{"run": {"command": "nx run ui-common:storybook", "startTime": "2025-06-12T13:23:03.962Z", "endTime": "2025-06-12T13:29:44.081Z", "inner": false}, "tasks": [{"taskId": "ui-common:storybook", "target": "storybook", "projectName": "ui-common", "hash": "11456119053590901530", "startTime": "2025-06-12T13:23:03.984Z", "endTime": "2025-06-12T13:29:44.080Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}