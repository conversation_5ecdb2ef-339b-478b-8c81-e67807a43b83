# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output

# dependencies
**/node_modules

# IDEs and editors
**/idea/
**/.vs/
**/.vscode/
.idea/
.git/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
npm-debug.log
yarn-error.log
testem.log

# backup files
*.bak
*.backup

# System Files
.DS_Store
Thumbs.db

.angular

# documentation build files
/libs/documentation/dist/

_devenv-tmp
.editorconfig
CreateBranch.*.cmd

.nx/installation
.nx/cache
.nx/workspace-data