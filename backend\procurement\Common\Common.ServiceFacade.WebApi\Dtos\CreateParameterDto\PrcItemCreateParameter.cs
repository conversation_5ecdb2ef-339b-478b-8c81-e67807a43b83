using RIB.Visual.Procurement.Common.BusinessComponents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Procurement.Common.ServiceFacade.WebApi
{
	/// <summary>
	/// PrcItemCreateParameter
	/// </summary>
	public class PrcItemCreateParameter : PrcItemCreateParameterEntity
	{
		/// <summary>
		/// gets and sets MainItemId
		/// </summary>
		public int? ParentId { get; set; }

		/// <summary>
		/// gets and sets Itemnos
		/// </summary>
		public IEnumerable<int> Itemnos { get; set; }

		/// <summary>
		/// create item from package module
		/// </summary>
		public bool IsPackage { get; set; }

		/// <summary>
		/// create item from contract module
		/// </summary>
		public bool IsContract { get; set; }

		/// <summary>
		///  item is  call off contract
		/// </summary>
		public bool IsCallOffContract { get; set; }

		/// <summary>
		///
		/// </summary>
		public CreateInsertItemDto InsertOptions { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public class PrcItemFromMaterialCreateParameter
	{
		/// <summary>
		///
		/// </summary>
		public int maxNo { get; set; }

		/// <summary>
		///
		/// </summary>
		public PrcItemCreateParameter prcItemCreateParameter { get; set; }

		/// <summary>
		///
		/// </summary>
		public int[] materialIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int[] priceListIds { get; set; }
	}

	/// <summary>
	/// CopyPrcItemValueParameter
	/// </summary>
	public class CopyPrcItemValueParameter
	{
		/// <summary>
		/// moduleName
		/// </summary>
		public string moduleName { set; get; }

		/// <summary>
		/// targetPrcItem
		/// </summary>
		public PrcItemEntity targetPrcItem { set; get; }

		/// <summary>
		/// originPrcItem
		/// </summary>

		public PrcItemEntity originPrcItem { set; get; }
	}
}