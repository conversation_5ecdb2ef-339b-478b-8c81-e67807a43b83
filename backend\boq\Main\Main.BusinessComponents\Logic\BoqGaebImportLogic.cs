using GAEB_Toolbox_33;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Currency.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents.Configurations;
using RIB.Visual.Boq.Main.BusinessComponents.Logic;
using RIB.Visual.Boq.Main.Common;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Boq.Main.Localization.Properties;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Cloud.Desktop.BusinessComponents.Logic.UserSettings;
using RIB.Visual.Cloud.Desktop.Common.UserSettings.WysiwygEditor;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using NLS = RIB.Visual.Boq.Main.Localization.Properties.Resources;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using DbContext = RIB.Visual.Platform.BusinessComponents.DbContext;
using EBoqItemType1 = RIB.Visual.Boq.Main.Core.BoqConstants.EBoqItemType1;
using RIB.Visual.Platform.BusinessComponents;
using System.Xml.Linq;
using static RIB.Visual.Boq.Main.Core.BoqConstants;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using static RIB.Visual.Basics.Core.Common.EntityIdentifier;

namespace RIB.Visual.Boq.Main.BusinessComponents
{
	#region GaebData

	/// <summary>
	///
	/// </summary>
	public class GaebData
	{
		/// <summary>
		/// Contains byte stream of GAEB tree
		/// </summary>
		public Byte[] Content { get; set; }

		/// <summary>
		///
		/// </summary>
		public String Path { get; set; }

		/// <summary>
		///
		/// </summary>
		public String Url { get; set; }

		/// <summary>
		///
		/// </summary>
		public String FileName { get; set; }

		/// <summary>
		///
		/// </summary>
		public String FileExtension { get; set; }
	}

	#endregion

	/// <summary>
	///
	/// </summary>
	[Export(typeof(IBoqGaebImportLogic))]
	[PartCreationPolicy(CreationPolicy.NonShared)]
	public partial class BoqGaebImportLogic : BoqGaebLogicBase, IBoqGaebImportLogic
	{
		#region Properties

		internal BoqStructureEntity BoqStructure { get; set; }
		internal IEnumerable<BoqStructureDetailEntity> BoqStructureDetails { get; set; }
		//internal IEnumerable<GX_BoQBkdn> GaebBoqStructureDetails;
		internal Boolean IsMatchingBoqStructure { get; private set; }
		private Boolean IsMatchingCatAssign { get; set; }
		internal Boolean IsHeaderCurrencyChange { get; private set; }

		// internal IEnumerable<UomEntity> AvailableUoms { get; set; }
		// internal IEnumerable<UomSynonymEntity> AvailableUomSynonyms { get; set; }
		internal IEnumerable<Basics.Currency.BusinessComponents.CurrencyEntity> AvailableCurrencies { get; set; }
		internal IEnumerable<BasicsCustomizeDivisionTypeEntity> AvailableDivisionType { get; set; }
		internal IEnumerable<BoqItemFlagEntity> AvailableBoqItemFlag { get; set; }
		internal IEnumerable<BasicsCustomizeBoqItemStatusEntity> AvailableItemStatuses { get; set; }

		//hack for "Final Remarks",
		private BoqItemEntity _lastFirstLeveltem;
		private Boolean _preFirstBoqBody = true;
		private Boolean _postFirstBoqBody;
		//hack for "Final Remarks"

		String _gaebDp = String.Empty;
		// internal Boolean IsBidderFile { get; private set; }
		// internal Boolean IsGaeb86 { get; private set; }

		internal class GaebImportSurchargedItem
		{
			public String GaebId { get; set; }
			public double? Quantity { get; set; }
		}

		//helper maps
		Dictionary<string, BoqItemEntity> _itemRefMap;

		//Dictionary<string, BoqItemEntity> _itemRefMapBaseBoq;
		//Dictionary<string, BoqItemEntity> _itemRefMapUpdateBoq;
		Dictionary<BoqItemEntity, List<GaebImportSurchargedItem>> _itemSurchargedItems;

		//statistics
		internal int DivisionCount = 0;
		internal int ItemCount = 0;
		internal int TextCount = 0;
		internal int TotalCount = 0;
		internal int ItemNotFound = 0;
		internal int TotalNotFound = 0;

		//private bool _IsFromBasline = false; // import boq from baseline
		private bool IsQtySplits = false;
		private bool IsCollectBoQDivision = false;
#pragma warning disable 414 // BH: For I don't know if this member is going to be used in the future again I just skip the 'member not being used' warning by using this pragma
		private bool IsBoQReferenceInBidEstimate = false;
#pragma warning restore 414 // BH: Feel free to remove the pragma statments when the member is in use again.
		//Work Category,Location,Catalog 1,Catalog 2,Catalog 3,Catalog 4
		private Dictionary<string, Dictionary<int, string>> _ctlgTypeandKey;
		private Dictionary<string, string> _ctlgIdandTypeDic;
		private Dictionary<IBoqItemEntity, IBoqQtySplitData> _boqItemDataDic = new Dictionary<IBoqItemEntity, IBoqQtySplitData>();
		internal List<BoqItem2CostGroupEntity> _boq2CostGroupsToSave = new List<BoqItem2CostGroupEntity>();
		// activity data
		private string _activities = "Activities";
		private string _activityNo = "No";
		private string _subItemNo = "SubItemNo";
		// AQ-Quantity in qtysplit
		private string _predQty = "PredQty";
		// controlling unit data
		private string _controllingUnit = "ControllingUnit";

		private string _locality = "locality";

		// user-defined infos -> NOT longer needed for revised code
		//private string _lblUserDef1 = "LblUserDef1";
		//private string _lblUserDef2 = "LblUserDef2";
		//private string _lblUserDef3 = "LblUserDef3";
		//private string _lblUserDef4 = "LblUserDef4";
		//private string _lblUserDef5 = "LblUserDef5";
		//private string _userDef1 = "UserDef1";
		//private string _userDef2 = "UserDef2";
		//private string _userDef3 = "UserDef3";
		//private string _userDef4 = "UserDef4";
		//private string _userDef5 = "UserDef5";

		// Wic No
		private Dictionary<string, List<Dictionary<string, List<BoqItemEntity>>>> _wicBoQDic = new Dictionary<string, List<Dictionary<string, List<BoqItemEntity>>>>();

		//blobs gaeb84 import (bidder textcomplements)
		private List<BlobEntity> _modifiedBlobs = null;
		//textcomplements (new 83,86 / modify 84)
		private List<BoqTextComplementEntity> _textComplementItems = new List<BoqTextComplementEntity>();
		private List<BoqTextConfigurationEntity> _textConfigurations = new List<BoqTextConfigurationEntity>();

		private BoqCharacterContentLogic characterLogic = new BoqCharacterContentLogic();

		// PriceRequest
		private string _PriceRequest = "PriceRequest";

		// buffer current root item
		private BoqItemEntity _boqRootItem = null;

		// surcharge type 4, assign rules parameter and division type
		// rules
		internal List<IEstimateRulePrjEstRuleEntity> _prjRules { get; set; }
		internal List<IEstRuleEntity> _estRules { get; set; }
		internal int _prjFk = 0;
		internal List<IEstEvaluationsequenceEntity> _estEvaluationsequenceEntity { get; set; }
		internal List<Boq2estRuleEntity> _boq2estRulesToSave = new List<Boq2estRuleEntity>();
		internal List<WicBoq2mdcRuleEntity> _wicBoq2mdcRulesToeSave = new List<WicBoq2mdcRuleEntity>();
		// parameters
		internal List<IEstParametergroupEntity> _estParameterGroups { get; set; }
		internal List<IEstimateRuleParameterValueEntity> _paramValues { get; set; }
		internal List<IRuleCommonParamEntity> _params2Lookup = new List<IRuleCommonParamEntity>();
		internal string _paramValueWarningMessage = "";
		internal List<IUnitEntity> _units { get; set; }
		internal List<BoqItemParamEntity> _boqParamsToSave = new List<BoqItemParamEntity>();
		internal List<IEstimateRuleParameterValueEntity> _paramValuesToSave = new List<IEstimateRuleParameterValueEntity>();
		// division type
		internal List<BasicsCustomizeDivisionTypeEntity> _boqDivisionTypes { get; set; }
		internal List<BoqItem2boqDivisiontypeEntity> _boqDivisionTypesToSave = new List<BoqItem2boqDivisiontypeEntity>();
		#endregion

		private List<int> _SplitEntityIdPool = null;

		private string _gaebFileVersion = "";

		private bool _isImportExternalCodeWihtUserDef = false;
		internal int _importExternalCodeOption = 0;

		#region included logic

		private readonly Lazy<BoqTypeLogic> _BoqTypeLogic = new Lazy<BoqTypeLogic>();
		private BoqTypeLogic BoqTypeLogic
		{
			get
			{
				return _BoqTypeLogic.Value;
			}
		}

		private Lazy<BoqCatalogLogic> _catalogLogic;
		private BoqCatalogLogic BoqCatalogLogic
		{
			get
			{
				return _catalogLogic.Value;
			}
		}
        private Lazy<TranslationLogic> _transLogic = new Lazy<TranslationLogic>();
        private TranslationLogic TransLogic
        {
            get
            {
                return _transLogic.Value;
            }
        }

		#endregion

		#region Import main

		internal Dictionary<string, Dictionary<string, int?>> _workCategoryCodeDic = new Dictionary<string, Dictionary<string, int?>>();
		internal bool _byPackage = false;
		internal string _cltgIDbyWorkCategory = string.Empty;
		internal List<Tuple<string, BoqItemEntity>> _controllingUnitCodeToBoqItemMap = new List<Tuple<string, BoqItemEntity>>();

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="gaebFile"></param>
		/// <param name="workCategoryCodeDic"></param>
		/// <param name="validationError"></param>
		/// <param name="warnings"></param>
		/// <returns></returns>
		public bool ImportByPackage(int boqHeaderId, string gaebFile, Dictionary<string, Dictionary<string, int?>> workCategoryCodeDic, out string validationError, out List<string> warnings)
		{
			_workCategoryCodeDic = workCategoryCodeDic;
			_byPackage = true;
			var flag = this.Import(boqHeaderId, gaebFile, out validationError, out warnings);
			return flag;
		}

		/// <summary>
		/// THE Import
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="gaebFile"></param>
		/// <param name="validationError"></param>
		/// <param name="retWarnings"></param>
		/// <param name="allowUpdate">Overrides setting of import options if not null</param>
		/// <param name="gaebFileContainsContent"></param>
		/// <param name="importOptions"></param>
		/// <returns></returns>
		public bool Import(int boqHeaderId, string gaebFile, out string validationError, out List<string> retWarnings, bool? allowUpdate = null, bool gaebFileContainsContent = false, GaebInfoData importOptions = null)
		{
			retWarnings = new List<string>();
			List<ImportWarning> warnings = new List<ImportWarning>();

			// store ImportOptions in a module variable
			if (importOptions == null)
			{
				importOptions = new GaebInfoData();
			}
			Clear();
			_ImportOptions = importOptions;

			// explicit setting of allowUpdate will override options!
			if (allowUpdate.HasValue)
			{
				_ImportOptions.AddNewItems = allowUpdate.Value;
				_ImportOptions.OverwriteExistingItems = allowUpdate.Value;
			}

			//assigned catalog import
			//if (_ImportOptions != null)
			_catalogLogic = new Lazy<BoqCatalogLogic>(() => new BoqCatalogLogic(_ImportOptions.ProjectId));

			CostGroupCatLogic _costGroupCatLogic = new CostGroupCatLogic();
			CostGroupLogic _costGroupLogic = new CostGroupLogic();
			var gaebCatalogInfoDic = new Dictionary<string, GaebCatalogInfo>();

			foreach (var cat in importOptions.Catalogs)
			{
				if (cat.BasCostgroupCatFk.HasValue && cat.BasCostgroupCatFk.Value != 0)
				{
					var catEntity = _costGroupCatLogic.GetCostGroupCatsByIds(new List<int>() { cat.BasCostgroupCatFk.Value }).FirstOrDefault();
					if (catEntity != null)
					{
						cat.CostGroupCat = catEntity;
						gaebCatalogInfoDic.Add(cat.CtlgID, cat);
					}
				}
				else if (cat.IsCostGroupCatalog)
				{
					var catEntity = _costGroupCatLogic.GetPrjCostGroupCatEntityByCode(cat.CostGroupCatalogCode, importOptions.ProjectId);
					if (catEntity != null)
					{
						cat.BasCostgroupCatFk = catEntity.Id;
						cat.CostGroupCat = catEntity;
						gaebCatalogInfoDic.Add(cat.CtlgID, cat);
					}
				}
			}

			if (gaebCatalogInfoDic.Any())
			{
				var catalogIds = gaebCatalogInfoDic.Values.Select(r => r.BasCostgroupCatFk).ToArray();
				Dictionary<int, Dictionary<string, CostGroupEntity>> dic1 = _costGroupLogic.GetByFilter(r => catalogIds.Contains(r.CostGroupCatFk))
					.GroupBy(r => r.CostGroupCatFk).ToDictionary(r => r.Key, s => s.ToDictionary(t => t.Code));

				foreach (var gaebCatalogInfo in gaebCatalogInfoDic)
				{
					if (dic1.TryGetValue(gaebCatalogInfo.Value.BasCostgroupCatFk.Value, out Dictionary<string, CostGroupEntity> outDic))
					{
						gaebCatalogInfo.Value.CostGroupDic = outDic;
					}
					else
					{
						gaebCatalogInfo.Value.CostGroupDic = new Dictionary<string, CostGroupEntity>();
					}
				}
			}

			MainItem2CostGroupLogic boqItem2cgLogic = new MainItem2CostGroupLogic(new BoqItem2CostGroupConfiguration().TableName);
			List<MainItem2CostGroupEntity> existingBoqItem2CostGroupEntities = boqItem2cgLogic.GetByFilter(e => e.RootItemId == boqHeaderId).ToList();

			MainItem2CostGroupLogic boqSplitQty2cgLogic = new MainItem2CostGroupLogic(new BoqSplitQty2CostGroupConfiguration().TableName);
			List<MainItem2CostGroupEntity> existingBoqSplitQty2CostGroupEntities = boqSplitQty2cgLogic.GetByFilter(e => e.RootItemId == boqHeaderId).ToList();



			validationError = String.Empty;
			//_boqUpdate = false;

			// pr
			// var gaebType = gaebFile.Substring(gaebFile.LastIndexOf(".", StringComparison.Ordinal) + 1, 3).ToLower();
			string gaebType;
			if (gaebFileContainsContent)
			{
				gaebType = "X84";
			}
			else
			{
				// gaebType = gaebFile.Substring(gaebFile.LastIndexOf(".", StringComparison.Ordinal) + 1, 3).ToLower();
				gaebType = Path.GetExtension(gaebFile).TrimStart('.');
			}

			//if (!String.IsNullOrEmpty(gaebType) && gaebType.Length == 3)
			//	_gaebDp = gaebType.Substring(1, 2);
			//else
			//{
			//	validationError = "File extension not valid\n"; //..normally not possible - what is normally! What about ribx83!
			//	return false;
			//}

			GaebXml = SetupGaebToolbox(true);

			// GaebXml.gRead(gaebFile);
			if (gaebFileContainsContent)
			{
				GaebXml.gRead(gaebFile.ToByteArray());
			}
			else
			{
				GaebXml.gRead(gaebFile);
			}

			var companyInfoProvider = this.GetCompanyInfoProvider();
			var mdcContextId = companyInfoProvider.GetLineItemContext(RVPARB.BusinessEnvironment.CurrentContext.ClientId);
			_boqItem2CostGroupLogic = new MainItem2CostGroupLogicWrapper(new BoqItem2CostGroupConfiguration(), importOptions.ProjectId, mdcContextId, gaebCatalogInfoDic, existingBoqItem2CostGroupEntities);
			_boqSplitQty2CostGroupLogic = new MainItem2CostGroupLogicWrapper(new BoqSplitQty2CostGroupConfiguration(), importOptions.ProjectId, mdcContextId, gaebCatalogInfoDic, existingBoqSplitQty2CostGroupEntities);

			if (GaebXml.Award == null || GaebXml.Award.DP_isEmpty())
			{
				_gaebDp = gaebType.Right(2);
			}
			else
			{
				_gaebDp = GaebXml.Award.DP;
			}

			if (_gaebDp.Length != 2)
			{
				validationError = String.Format("GAEB type {0} not valid!\n", _gaebDp);
				return false;
			}

			var gaebBoq = GaebXml.Award.BoQ;
			var gaebInfo = gaebBoq.BoQInfo;
			CollectPrcStructureAssignments(gaebInfo.gNodes.Where(node => node is GX_Ctlg).FirstOrDefault(), _ImportOptions);
			var locationCtlgData = GetLocationCtlgData(gaebInfo.gNodes.Where(node => node is GX_Ctlg)); // get the location ctlg

			CollectLocationCtlgDatas(locationCtlgData, _ImportOptions); // get list for location ctlg data: model object
																							// IsBidderFile = "84".Equals(_gaebDp);
																							// IsGaeb86 = "86".Equals(_gaebDp);

			Int32 boqItemCount;
			// BoqItemEntity boqRootItem;	--> moved to module vars
			BoqItemEntity baseBoqRootItem = null;
			BoqHeaderEntity boqHeader;
			BoqHeaderContextVEntity boqHeaderContext;
			EBoqType myBoqType;
			BoqHeaderEntity baseBoqHeader = null;
			BoqHeaderContextVEntity baseBoqHeaderContext = null;

			// current boq entities (header, root, structure)
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{

				boqItemCount = dbContext.Entities<BoqItemEntity>().Count(boqItem => boqItem.BoqHeaderFk == boqHeaderId);
				_targetHasChilds = boqItemCount > 1;

				// --- redirect to revised logic ---
				if (!IsGaeb84 && boqItemCount > 1 && _ImportOptions.PartialImport)
				{
					if (GaebTypeSupportsUpsert)   // using revised code for partial import
					{
						return RevisedImport(GaebXml, boqHeaderId, out validationError, ref warnings);
					}
					else
					{
						validationError += NLS.IMP_Gaeb_Children_exist;
						return false;
					}
				}

				//boqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(aBoqHeader => aBoqHeader.Id == boqHeaderId);
				boqHeader = BoqHeaderLogic.GetEntityById(boqHeaderId);
				myBoqType = BoqItemLogic.GetBoqType(boqHeaderId, out boqHeaderContext);
				_gaebFileVersion = gaebType.Substring(0, 1);

				if (boqHeader == null)
				{
					throw new Exception(String.Format("Target BoQ Header with Id={0} must exist!", boqHeaderId));
				}

				if (boqHeader.BoqHeaderFk != null)
				{
					// baseBoqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(aBoqHeader => aBoqHeader.Id == boqHeader.BoqHeaderFk);
					baseBoqHeader = BoqHeaderLogic.GetEntityById(boqHeader.BoqHeaderFk.Value);
					baseBoqHeaderContext = dbContext.Entities<BoqHeaderContextVEntity>().First(bhc => bhc.BoqHeaderId == baseBoqHeader.Id);
				}

				if (IsBidderFile)
					_boqRootItem = BoqItemLogic.GetBoqItemsHierchical(dbContext, boqHeaderId).FirstOrDefault(e => e.BoqHeaderFk == boqHeaderId && e.BoqItemFk == null);   //root with tree
				else
				{
					_boqRootItem = BoqItemLogic.GetBoqItemsHierchical(dbContext, boqHeaderId).FirstOrDefault(boqItem => boqItem.BoqHeaderFk == boqHeaderId && boqItem.BoqItemFk == null);      //only root (children should not exist)
					if (_boqRootItem != null && _boqRootItem.BoqItemPrjBoqFk != null)
						baseBoqRootItem = BoqItemLogic.GetBoqItemsHierchical(dbContext, (int)_boqRootItem.BoqItemPrjBoqFk).FirstOrDefault(e => e.BoqHeaderFk == (int)_boqRootItem.BoqItemPrjBoqFk && e.BoqItemFk == null);
				}

				BoqStructure = dbContext.Entities<BoqStructureEntity>().FirstOrDefault(aBoqStructure => aBoqStructure.Id == boqHeader.BoqStructureFk);
				BoqStructureDetails = dbContext.Entities<BoqStructureDetailEntity>().Where(aDetail => aDetail.BoqStructureFk == boqHeader.BoqStructureFk).ToList();

				if (BoqTypeLogic.IsCrbBoq(BoqStructure))
				{
					validationError += NLS.Crb_DisabledFunc;
					return false;
				}
				else if (BoqTypeLogic.IsOenBoq(BoqStructure))
				{
					validationError += NLS.Oen_DisabledFunc;
					return false;
				}
			}

			//// --- redirect to revised logic ---
			//// if (IsBidderFile)
			//if (IsBidderFileOrUpdate85)
			//{
			//	if (IsGaeb85)	// using revised code for 85 import
			//	{
			//		return RevisedImport(GaebXml, boqHeaderId, out validationError);
			//	}
			//}
			//else
			//{
			//	if (boqItemCount > 1)
			//	{
			//		if (_ImportOptions.PartialImport)	// using revised code for partial import
			//		{
			//			return RevisedImport(GaebXml, boqHeaderId, out validationError);
			//		}
			//		else
			//		{
			//			validationError += NLS.IMP_Gaeb_Children_exist;
			//			return false;
			//		}
			//	}
			//}
			//// ---

			////////////////////////////////////////////////////////////////////////////////
			// check structure
			// validationError += ValidateStructure(boqHeader, HasBaseBoqWithChildren(baseBoqRootItem)); --> done in revised code!

			////////////////////////////////////////////////////////////////////////////////
			// create new / update boq

			// map for gaebId/boqItem or referenceNo/boqItem (84)
			_itemRefMap = new Dictionary<string, BoqItemEntity>();

			if (IsBidderFile)
			{

				///////////////////////////////////////////////
				//update boq prices/free quantities

				validationError += Validate(boqHeader, HasBaseBoqWithChildren(baseBoqRootItem), ref warnings);

				//check structure mismatch
				if (!IsMatchingBoqStructure)
					return false;

				BuildReferenceMap(_boqRootItem, true);
				//update
				UpdateBoqTree(GaebXml, "", 0, ref warnings, _boqRootItem);

			}
			else
			{
				if (boqItemCount > 1)
				{
					validationError += NLS.IMP_Gaeb_Children_exist;
					return false;
				}

				validationError += Validate(boqHeader, HasBaseBoqWithChildren(baseBoqRootItem), ref warnings);

				if (!IsMatchingBoqStructure && HasBaseBoqWithChildren(baseBoqRootItem))
					return false;

				//CN flags
				AvailableDivisionType = null;
				AvailableBoqItemFlag = null;

				//refs
				_itemSurchargedItems = new Dictionary<BoqItemEntity, List<GaebImportSurchargedItem>>();

				//create tree
				CreateBoqTree(GaebXml, _boqRootItem, _boqRootItem, ref warnings);

				//sub quantities of items to be surcharged
				CreateSurchargedItemsType3();
			}

			// append the param value warning message
			if (!string.IsNullOrEmpty(_paramValueWarningMessage))
			{
				warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_NoMapping_ParamOrType), _paramValueWarningMessage));
			}

			if (warnings.Count > 0)
			{
				bool hasErrors = false;

				List<BasicsCustomizeBoQWarningConfigEntity> warningConfigs;

				using (var dbContext = new DbContext(Basics.Customize.BusinessComponents.ModelBuilder.DbModel))
				{
					warningConfigs = dbContext.Entities<BasicsCustomizeBoQWarningConfigEntity>().ToList();
				}

				foreach (var warning in warnings)
				{
					var config = warningConfigs.FirstOrDefault(r => r.Code == warning.Code);
					if (config != null)
					{
						if (config.WarningActionFk == 1) // Ignore
						{
							// ignore
						}
						else if (config.WarningActionFk == 2) // Warning
						{
							retWarnings.Add(String.Format("Warning: {0} {1}", config.DescriptionInfo.Description, warning.Text));
						}
						else if (config.WarningActionFk == 3) // Error
						{
							retWarnings.Add(String.Format("Error: {0} {1}", config.DescriptionInfo.Description, warning.Text));
							hasErrors = true;
						}
					}
					else
					{
						retWarnings.Add(String.Format("Warning: {0}", warning.Text));
					}
				}

				if (hasErrors)
				{
					return false;
				}
			}

			// calculate boq
			if (IsBidderFile)
				new BoqItemLogic().CalculateBoqTree(_boqRootItem); // Hint: To work properly the Id's of the boq items have to be set.

			///////////////////////////////////////////////////////////////////////////////
			// prepare and save
			//if (String.IsNullOrEmpty(validationError)) //todo: ...if severe error

			var specificationBlobs = new List<BlobEntity>();
			var surchargedItems = new List<BoqSurchargedItemEntity>();

			boqItemCount = 0;
			var blobCount = 0;
			var surchargedItemsCount = 0;
			var textComplementItemsCount = 0;
			var boqItem2CostGroupsCount = 0;
			var boqSplitQuantity2CostGroupsCount = 0;

			if (!IsBidderFile)
			{
				///////////////////////////////////////////////////////////////
				// count elements, generate ids, set ids, prepare blobs

				//count
				ForEachExceptTheRoot(_boqRootItem, boqItem =>
				{
					boqItemCount++;

					if (boqItem.SpecificationBlob != null)
						blobCount++;

					if (boqItem.BoqSurchardedItemEntities != null)
						surchargedItemsCount += boqItem.BoqSurchardedItemEntities.Count();

					if ((boqItem.BoqTextComplementEntities != null))
						textComplementItemsCount += boqItem.BoqTextComplementEntities.Count();

					if ((boqItem.BoqItem2CostGroupEntities != null))
						boqItem2CostGroupsCount += boqItem.BoqItem2CostGroupEntities.Count();

					if (boqItem.BoqSplitQuantityEntities != null)
						boqSplitQuantity2CostGroupsCount += boqItem.BoqSplitQuantityEntities.SelectMany(r => r.BoqSplitQuantity2CostGroupEntities).Count();
				});

				//generate Ids
				var sequenceManager = SequenceManager;
				IEnumerator<Int32> boqItemIds = new List<Int32>().GetEnumerator();
				IEnumerator<Int32> blobIds = new List<Int32>().GetEnumerator();
				IEnumerator<Int32> surchargedItemIds = new List<Int32>().GetEnumerator();
				IEnumerator<Int32> textComplementsItemIds = new List<Int32>().GetEnumerator();
				IEnumerator<Int32> boqItem2CostGroupIds = new List<Int32>().GetEnumerator();
				IEnumerator<Int32> boqSplitQuantity2CostGroupIds = new List<Int32>().GetEnumerator();

				var idCount = 1;
				if (HasBaseBoqWithoutChildren(baseBoqRootItem))
					idCount = 2;

				//items
				if (boqItemCount > 0)
					boqItemIds = sequenceManager.GetNextList<Int32>("BOQ_ITEM", idCount * boqItemCount, "ID").GetEnumerator();

				//blobs
				if (blobCount > 0)
					blobIds = sequenceManager.GetNextList<Int32>("BAS_BLOBS", idCount * blobCount, "ID").GetEnumerator();

				//surcharged items
				if (surchargedItemsCount > 0)
					surchargedItemIds = sequenceManager.GetNextList<Int32>("BOQ_SURCHARDED_ITEM", idCount * surchargedItemsCount, "ID").GetEnumerator();

				//text complements items
				if (textComplementItemsCount > 0)
					textComplementsItemIds = sequenceManager.GetNextList<Int32>("BOQ_TEXT_COMPLEMENT", idCount * textComplementItemsCount, "ID").GetEnumerator();

				//boqItem2CostGroups
				if (boqItem2CostGroupsCount > 0)
					boqItem2CostGroupIds = sequenceManager.GetNextList<Int32>("BOQ_ITEM2COSTGRP", idCount * boqItem2CostGroupsCount, "ID").GetEnumerator();

				//boqSplitQuantity2CostGroups
				if (boqSplitQuantity2CostGroupsCount > 0)
					boqSplitQuantity2CostGroupIds = sequenceManager.GetNextList<Int32>("BOQ_SPLIT_Q2COSTGRP", idCount * boqSplitQuantity2CostGroupsCount, "ID").GetEnumerator();


				// structure and structure  details
				if (BoqStructure != null && BoqStructureDetails != null)
				{
					BoqStructure.Id = sequenceManager.GetNext<Int32>("BOQ_STRUCTURE", 1, "ID");
					foreach (var boqStructureDetail in BoqStructureDetails)
					{
						boqStructureDetail.Id = sequenceManager.GetNext<Int32>("BOQ_STRUCTURE_DETAIL", 1, "ID");
						boqStructureDetail.BoqStructureFk = BoqStructure.Id;
					}
				}

				if (IsMatchingCatAssign == false)
				{
					// neue ids für cat_Assign cat_assign_details -> done later
				}

				// set Ids for parent/child, blobs, prepare blobs/surcharged entities
				ForEachExceptTheRoot(_boqRootItem, boqItem =>
				{
					boqItemIds.MoveNext();
					boqItem.Id = boqItemIds.Current;
					boqItem.BoqItemFk = boqItem.BoqItemParent.Id;

					if (boqItem.SpecificationBlob != null)
					{
						blobIds.MoveNext();
						boqItem.SpecificationBlob.Id = blobIds.Current;
						boqItem.BasBlobsSpecificationFk = blobIds.Current;
						specificationBlobs.Add(boqItem.SpecificationBlob);
					}
				});

				// loop again, every item has an Id now -> set Ids for referenced items (also used as predecessor reference for textelement position in tree) and surcharged items of Type3
				ForEachExceptTheRoot(_boqRootItem, boqItem =>
				{
					if (boqItem.BoqItemBasisParent != null)
						boqItem.BoqItemBasisFk = boqItem.BoqItemBasisParent.Id;

					if (boqItem.BoqItemReferenceParent != null)
						boqItem.BoqItemReferenceFk = boqItem.BoqItemReferenceParent.Id;

					if (boqItem.BoqSurchardedItemEntities != null && boqItem.BoqSurchardedItemEntities.Any())
					{
						foreach (var siEntity in boqItem.BoqSurchardedItemEntities)
						{
							surchargedItemIds.MoveNext();
							siEntity.BoqItemFk = boqItem.Id;
							siEntity.Id = surchargedItemIds.Current;
							siEntity.BoqSurcharedItemFk = siEntity.BoqSurchargedItem.Id;

							surchargedItems.Add(siEntity);
						}
					}

					if (boqItem.BoqTextComplementEntities != null && boqItem.BoqTextComplementEntities.Any())
					{
						foreach (var tcEntity in boqItem.BoqTextComplementEntities)
						{
							textComplementsItemIds.MoveNext();
							tcEntity.BoqHeaderFk = boqItem.BoqHeaderFk;
							tcEntity.BoqItemFk = boqItem.Id;
							tcEntity.Id = textComplementsItemIds.Current;
							_textComplementItems.Add(tcEntity);
						}
					}

					if (boqItem.BoqTextConfigurationEntities != null && boqItem.BoqTextConfigurationEntities.Any())
					{
						foreach (var tconfigEntity in boqItem.BoqTextConfigurationEntities)
						{
							tconfigEntity.BoqHeaderFk = boqItem.BoqHeaderFk;
							tconfigEntity.BoqItemFk = boqItem.Id;
							_textConfigurations.Add(tconfigEntity);
						}
					}

					// rules to surcharge type4
					if (boqItem.Boq2estRuleEntities != null && boqItem.Boq2estRuleEntities.Any())
					{
						foreach (var boq2estRule in boqItem.Boq2estRuleEntities)
						{
							boq2estRule.BoqHeaderFk = boqItem.BoqHeaderFk;
							boq2estRule.BoqItemFk = boqItem.Id;
							_boq2estRulesToSave.Add(boq2estRule);
						}
					}
					// wic boq rules to surcharge type4
					if (boqItem.WicBoq2mdcRuleEntities != null && boqItem.WicBoq2mdcRuleEntities.Any())
					{
						foreach (var wicBoq2estRule in boqItem.WicBoq2mdcRuleEntities)
						{
							wicBoq2estRule.BoqHeaderFk = boqItem.BoqHeaderFk;
							wicBoq2estRule.BoqItemFk = boqItem.Id;
							_wicBoq2mdcRulesToeSave.Add(wicBoq2estRule);
						}
					}
					// parameters
					if (boqItem.BoqItemParamEntities != null && boqItem.BoqItemParamEntities.Any())
					{
						foreach (var boqparam in boqItem.BoqItemParamEntities)
						{
							boqparam.BoqHeaderFk = boqItem.BoqHeaderFk;
							boqparam.BoqItemFk = boqItem.Id;
							_boqParamsToSave.Add(boqparam);
						}
					}
					// division type
					if (boqItem.BoqItem2boqDivisiontypeEntities != null && boqItem.BoqItem2boqDivisiontypeEntities.Any())
					{
						foreach (var boqDivisionType in boqItem.BoqItem2boqDivisiontypeEntities)
						{
							boqDivisionType.BoqHeaderFk = boqItem.BoqHeaderFk;
							boqDivisionType.BoqItemFk = boqItem.Id;
							_boqDivisionTypesToSave.Add(boqDivisionType);
						}
					}

					// set BoqItemFk's to BoqSplitQuantityEntities
					SetReferenceKeys(boqItem, boqSplitQuantity2CostGroupIds);

					//  boq2costgroup
					if (boqItem.BoqItem2CostGroupEntities != null && boqItem.BoqItem2CostGroupEntities.Any())
					{
						foreach (var boq2CostGroup in boqItem.BoqItem2CostGroupEntities)
						{
							if (boq2CostGroup.Id == 0)
							{
								boqItem2CostGroupIds.MoveNext();
								boq2CostGroup.Id = boqItem2CostGroupIds.Current;
							}
							boq2CostGroup.BoqHeaderFk = boqItem.BoqHeaderFk;
							boq2CostGroup.BoqItemFk = boqItem.Id;
							_boq2CostGroupsToSave.Add(boq2CostGroup);
						}
					}

				});

				// calculate boq
				BoqItemCalculationOption boqItemCalculationOption = new BoqItemCalculationOption() { TaxCodeId = boqHeaderContext.ContextMdcTaxCodeId };
				new BoqItemLogic().CalculateBoqTree(_boqRootItem, boqItemCalculationOption); // Hint: To work properly the Id's of the boq items have to be set.

				// sync base boq
				if (baseBoqRootItem != null && !IsBidderFile)
				{
					if (IsBaseBoqWithoutChildren(baseBoqRootItem))
					{
						var baseItemRefMap = new Dictionary<int, int> { { _boqRootItem.Id, baseBoqRootItem.Id } };

						baseBoqRootItem.SyncBoqItem(_boqRootItem, new SyncBoqItemOptions { LanguageId = this.UserLanguageId });

						var parent = baseBoqRootItem;

						ForEachExceptTheRoot(_boqRootItem, parent, (boqItem, boqParent) =>
						{
							var baseBoqItem = boqItem.Clone() as BoqItemEntity;

							boqItemIds.MoveNext();
							baseBoqItem.Id = boqItemIds.Current;
							baseBoqItem.BoqItemParent = boqParent;
							baseBoqItem.BoqHeaderFk = (int)_boqRootItem.BoqItemPrjBoqFk;
							boqItem.BoqItemPrjBoqFk = _boqRootItem.BoqItemPrjBoqFk;
							boqItem.BoqItemPrjItemFk = baseBoqItem.Id;

							//can refer onyl to items above/before current item, so IDs are already in the map
							baseBoqItem.BoqItemFk = GetBaseReference(boqItem.BoqItemFk, baseItemRefMap);
							baseBoqItem.BoqItemBasisFk = GetBaseReference(boqItem.BoqItemBasisFk, baseItemRefMap);
							baseBoqItem.BoqItemReferenceFk = GetBaseReference(boqItem.BoqItemReferenceFk, baseItemRefMap);

							baseItemRefMap.Add(boqItem.Id, baseBoqItem.Id);
							boqParent.BoqItemChildren.Add(baseBoqItem);

							if (boqItem.SpecificationBlob != null)
							{
								var blob = boqItem.SpecificationBlob.Clone() as BlobEntity;
								blobIds.MoveNext();
								blob.Id = blobIds.Current;
								baseBoqItem.BasBlobsSpecificationFk = blobIds.Current;
								specificationBlobs.Add(blob);
							}

							if (boqItem.BoqSurchardedItemEntities != null && boqItem.BoqSurchardedItemEntities.Any())
							{
								foreach (var siEntity in boqItem.BoqSurchardedItemEntities)
								{
									var baseSiEntity = siEntity.Clone() as BoqSurchargedItemEntity;
									surchargedItemIds.MoveNext();
									baseSiEntity.Id = surchargedItemIds.Current;
									baseSiEntity.BoqHeaderFk = baseBoqItem.BoqHeaderFk;
									baseSiEntity.BoqItemFk = baseBoqItem.Id;

									baseBoqItem.BoqSurchardedItemEntities.Add(baseSiEntity); //for calc, ...with wrong FKs
								}
							}


							if (boqItem.BoqTextComplementEntities != null && boqItem.BoqTextComplementEntities.Any())
							{
								foreach (var tcEntity in boqItem.BoqTextComplementEntities)
								{
									var baseTcEntity = tcEntity.Clone() as BoqTextComplementEntity;
									textComplementsItemIds.MoveNext();
									baseTcEntity.Id = textComplementsItemIds.Current;
									baseTcEntity.BoqHeaderFk = baseBoqItem.BoqHeaderFk;
									baseTcEntity.BoqItemFk = baseBoqItem.Id;

									baseBoqItem.BoqTextComplementEntities.Add(baseTcEntity); //for calc, ...with wrong FKs
								}
							}


							return baseBoqItem;
						});

						//second loop to correct ref IDs, can refer elsewhere in boq, so all IDs have to be in the map
						ForEachExceptTheRoot(baseBoqRootItem, baseBoqItem =>
						{
							if (baseBoqItem.BoqSurchardedItemEntities != null && baseBoqItem.BoqSurchardedItemEntities.Any())
							{
								foreach (var baseSiEntity in baseBoqItem.BoqSurchardedItemEntities)
								{
									baseSiEntity.BoqSurcharedItemFk = GetBaseReference(baseSiEntity.BoqSurcharedItemFk, baseItemRefMap);

									surchargedItems.Add(baseSiEntity); //for save
								}
							}
						});

						// calculate boq
						BoqItemCalculationOption calcOption = new BoqItemCalculationOption() { TaxCodeId = baseBoqHeaderContext.ContextMdcTaxCodeId };
						new BoqItemLogic().CalculateBoqTree(baseBoqRootItem, calcOption);
					}
					else
					{
						//if()

					}
				}
			}

			//save all
			try
			{
				using (var transactionScope = TransactionScopeFactory.Create())
				{
					BulkSaveHelper bulkSaveHelper = new BulkSaveHelper();
					List<BoqItemEntity> insertedBoqItems, updatedBoqItems;
					List<BoqItemEntity> addedBoqPositions = null, modifiedBoqPositions = null;

					/////////////////////////////////////
					// save specification blobs
					if (!IsBidderFile)
					{
						using (var dbContext = new DbContext(Cloud.Common.BusinessComponents.ModelBuilder.DbModel))
						{
							bulkSaveHelper.BulkInsert(dbContext, specificationBlobs);
						}
					}
					else
					{
						if (_modifiedBlobs != null && _modifiedBlobs.Any())
							new BlobLogic().UpdateBlobs(_modifiedBlobs);
					}

					////////////////////////////////////
					// save boq elements
					using (var dbContext = new DbContext(ModelBuilder.DbModel))
					{

						// structure and structure details
						if (IsBidderFile == false && (IsMatchingBoqStructure == false || IsMatchingCatAssign == false))
						{
							BoqStructure.BoqStructureDetailEntities = null; //#!# entity framework nav-props ?

							// The settings for LeadingZeros are not always easy to determine from the imported BoqStructure alone.
							// To have a better behavior we check the created reference numbers for occurrences of leading zeros.
							BoqStructure.LeadingZeros = DetermineLeadingZeros(_itemRefMap, BoqStructure, BoqStructureDetails);

							dbContext.Entities<BoqStructureEntity>().Add(BoqStructure);

							foreach (var boqStructureDetail in BoqStructureDetails)
								dbContext.Entities<BoqStructureDetailEntity>().Add(boqStructureDetail);

							// save cat_Assign, cat_assign_details
							if (IsMatchingCatAssign == false)
							{
								UpdateCatAssignments(BoqStructure, dbContext);
							}

							//{
							//	#region clone cat assignments

							//	BoqCatAssignConfEntity catAssignEntity = BoqStructure.BoqCatAssignFk.HasValue ? BoqTypeLogic.GetBoqCatAssignConfById(BoqStructure.BoqCatAssignFk.Value) : null;
							//	if (catAssignEntity == null) // create new entry
							//	{
							//		catAssignEntity = BoqTypeLogic.CreateBoqCatAssign();
							//		// catAssignEntity.DescriptionInfo = ?
							//	}
							//	else  // clone existing entry
							//	{
							//		catAssignEntity.Id = SequenceManager.GetNext<Int32>("BOQ_CAT_ASSIGN", 1, "ID");
							//		catAssignEntity.Version = 0;
							//	}
							//	dbContext.Entities<BoqCatAssignConfEntity>().Add(catAssignEntity);

							//	// später neuen BOQ_CAT_ASSIGN_CONFTYPE_FK setzen
							//	// BoqCatAssignConfTypeEntity catAssignConfTypeEntity = BoqTypeLogic.CreateBoqCatAssignConftype();	// wird immer neu erstellt?
							//	// catAssignConfTypeEntity.BoqCatAssignFk = catAssignEntity.Id;
							//	// catAssignConfTypeEntity.DescriptionInfo = ?
							//	// dbContext.Entities<BoqCatAssignConfTypeEntity>().Add(catAssignConfTypeEntity);
							//	if (ImportOptions != null && ImportOptions.Catalogs != null)
							//	{
							//		foreach (var importCat in ImportOptions.Catalogs)
							//		{
							//			if (importCat.MappedCatalogId.HasValue && (importCat.MappedCatalogId.Value > 0 && importCat.MappedCatalogId.Value <= 13))
							//			{
							//				BoqCatAssignDetailEntity catAssignDetailEntity = BoqTypeLogic.CreateBoqCatAssignDetail();
							//				catAssignDetailEntity.GaebId = (BoqCatalogLogic.GetGaebIdByCatalogType(importCat.CtlgType) ?? (int)BoqConstants.GaebType.Miscellaneous);
							//				catAssignDetailEntity.BoqCatalogFk = importCat.MappedCatalogId.Value;
							//				catAssignDetailEntity.BoqCatAssignFk = catAssignEntity.Id;
							//				catAssignDetailEntity.Description = importCat.CtlgName;
							//				catAssignDetailEntity.SearchMode = (int)importCat.CatalogAssignmentMode;
							//				dbContext.Entities<BoqCatAssignDetailEntity>().Add(catAssignDetailEntity);
							//			}
							//		}
							//	}
							//	BoqStructure.BoqCatAssignFk = catAssignEntity.Id;
							//	BoqStructure.BoqCatAssignConfTypeFk = null;	//  catAssignConfTypeEntity.Id;

							//	#endregion
							//}

							dbContext.SaveChanges(); // ...experimental development -> have to save before, otherwise error with BoqStructureFK <- ###
						}

						// Base BOQ items
						if (baseBoqRootItem != null)
						{
							insertedBoqItems = new List<BoqItemEntity>();
							updatedBoqItems = new List<BoqItemEntity>();
							ForEachExceptTheRoot(baseBoqRootItem, boqItem =>
							{
								boqItem.SpecificationBlob = null;
								boqItem.BoqSurchardedItemEntities.Clear();
								boqItem.BoqTextComplementEntities.Clear();
								boqItem.BoqSurchardedItemEntities = null;
								boqItem.BoqTextComplementEntities = null;

								if (boqItem.Version > 0) { updatedBoqItems.Add(boqItem); }
								else { insertedBoqItems.Add(boqItem); }
							});

							updatedBoqItems.Add(baseBoqRootItem);
							bulkSaveHelper.BulkUpdate(dbContext, updatedBoqItems);
							bulkSaveHelper.BulkInsert(dbContext, insertedBoqItems);

							addedBoqPositions = insertedBoqItems != null ? insertedBoqItems.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position).ToList() : null;
							modifiedBoqPositions = updatedBoqItems != null ? updatedBoqItems.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position).ToList() : null;
						}

						insertedBoqItems = new List<BoqItemEntity>();
						updatedBoqItems = new List<BoqItemEntity>();
						updatedBoqItems.Add(_boqRootItem);

						// BOQ items
						ForEachExceptTheRoot(_boqRootItem, boqItem =>
						{
							boqItem.SpecificationBlob = null;
							boqItem.BoqSurchardedItemEntities.Clear();
							boqItem.BoqTextComplementEntities.Clear();

							if (boqItem.Version == 0)
							{
								insertedBoqItems.Add(boqItem);
							}
							else if (!IsBidderFile || (IsBidderFile && boqItem.State == EntityState.Modified))
							{
								updatedBoqItems.Add(boqItem);
							}
						});

						#region checking ambiguous references
						var ambiguousReferences = insertedBoqItems.Where(r => r.BoqLineTypeFk != (int)EBoqLineType.Root && r.Reference.Length > 0)
									.GroupBy(r => r.Reference).Where(r => r.Count() > 1).Select(g => new { g.Key, Count = g.Count() }).OrderBy(r => r.Key).ToList();
						if (ambiguousReferences.Count > 0)
						{
							validationError = NLS.IMP_Gaeb_Ambiguous_References;
							foreach (var reference in ambiguousReferences)
							{
								validationError += String.Format("{0} x '{1}'\n", reference.Count, reference.Key);
							}
							return false;
						}
						#endregion

						var insertedBoqItem2CostGroupEntities = insertedBoqItems.SelectMany(r => r.BoqItem2CostGroupEntities).ToList();
						var insertedSplitQuantities = insertedBoqItems.SelectMany(bi => bi.BoqSplitQuantityEntities).ToList();
						var insertedBoqSplitQuantity2CostGroupEntities = insertedSplitQuantities.SelectMany(sq => sq.BoqSplitQuantity2CostGroupEntities).ToList();

						bulkSaveHelper.BulkInsert(dbContext, insertedBoqItems);
						bulkSaveHelper.BulkInsert(dbContext, insertedBoqItem2CostGroupEntities);
						bulkSaveHelper.BulkUpdate(dbContext, updatedBoqItems);
						bulkSaveHelper.BulkInsert(dbContext, insertedSplitQuantities);
						bulkSaveHelper.BulkInsert(dbContext, insertedBoqSplitQuantity2CostGroupEntities);

						if (baseBoqRootItem == null)
						{
							addedBoqPositions = insertedBoqItems != null ? insertedBoqItems.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position).ToList() : null;
							modifiedBoqPositions = updatedBoqItems != null ? updatedBoqItems.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position).ToList() : null;
						}

						//header (modified)
						if (!IsMatchingBoqStructure || IsHeaderCurrencyChange || !String.IsNullOrEmpty(gaebType) || !IsMatchingCatAssign)
						{
							if (!IsMatchingBoqStructure || !IsMatchingCatAssign)
							{
								boqHeader.BoqTypeFk = null;
								boqHeader.BoqStructureFk = BoqStructure.Id;
							}

							if (!String.IsNullOrEmpty(gaebType))
							{
								boqHeader.Gaebtype = gaebType.Substring(0, 3);
							}

							dbContext.Entry(boqHeader).State = EntityState.Modified;

							if (baseBoqHeader != null && !IsBidderFile)
							{
								baseBoqHeader.SyncItem(boqHeader);
								dbContext.Entry(baseBoqHeader).State = EntityState.Modified;
							}
						}

						//surcharged items && text complements
						if (!IsBidderFile)
						{
							bulkSaveHelper.BulkInsert(dbContext, surchargedItems.Where(si => si.Version == 0).ToList());
							bulkSaveHelper.BulkInsert(dbContext, _textComplementItems.Where(si => si.Version == 0).ToList());

							// bre: Not yet saved by the 'BulkSaveHelper' beacause of missing test data
							foreach (EntityBase entity in _textConfigurations.OfType<EntityBase>().Concat(_boq2estRulesToSave).Concat(_wicBoq2mdcRulesToeSave).Concat(_boqParamsToSave)
																															.Concat(_boqDivisionTypesToSave))
							{
								if (entity.Version == 0)
								{
									dbContext.Entry(entity).State = EntityState.Added;
								}
							}
						}
						else
						{
							foreach (var tcEntity in _textComplementItems)
								dbContext.Entry(tcEntity).State = tcEntity.Version == 0 ? EntityState.Added : EntityState.Modified;

							foreach (var tconfigEntity in _textConfigurations)
								dbContext.Entry(tconfigEntity).State = tconfigEntity.Version == 0 ? EntityState.Added : EntityState.Modified;

							foreach (var boq2estRuleToSave in _boq2estRulesToSave)
								dbContext.Entry(boq2estRuleToSave).State = boq2estRuleToSave.Version == 0 ? EntityState.Added : EntityState.Modified;
							foreach (var wicBoq2mdcRuleToSave in _wicBoq2mdcRulesToeSave)
								dbContext.Entry(wicBoq2mdcRuleToSave).State = wicBoq2mdcRuleToSave.Version == 0 ? EntityState.Added : EntityState.Modified;
							foreach (var boqParamToSave in _boqParamsToSave)
								dbContext.Entry(boqParamToSave).State = boqParamToSave.Version == 0 ? EntityState.Added : EntityState.Modified;
							foreach (var boqDivisionTypeToSave in _boqDivisionTypesToSave)
								dbContext.Entry(boqDivisionTypeToSave).State = boqDivisionTypeToSave.Version == 0 ? EntityState.Added : EntityState.Modified;
							foreach (var boq2CostGroupToSave in _boq2CostGroupsToSave)
								dbContext.Entry(boq2CostGroupToSave).State = boq2CostGroupToSave.Version == 0 ? EntityState.Added : EntityState.Modified;
						}

						// before the param save, save param values first
						if (_paramValuesToSave.Count > 0)
						{
							Injector.Get<IEstimateRuleParameterValueLogic>().SaveItems(_paramValuesToSave);
						}

						//deal the value detail parameters
						BoqItemLogic.DealValueDetailParamsAfterGaebImport(_boqRootItem, _ImportOptions.ProjectId);

						// save and run stored proc
						dbContext.SaveChanges();

						if (!IsBidderFile)
						{
							dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", boqHeader.Id);
							if (baseBoqRootItem != null)
								dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", baseBoqRootItem.BoqHeaderFk);
						}
					}

					transactionScope.Complete();

					//DEV-23300 - Create estimate line items for newly added/updated boq items from gaeb import for project boqs only and only if Is Estimate Boq Driven flag is true in project container
					if (new[] { EBoqType.Project, EBoqType.Bid, EBoqType.Ord, EBoqType.Wip, EBoqType.Bill }.Contains(myBoqType)
					    && ((addedBoqPositions != null && addedBoqPositions.Count > 0) || (modifiedBoqPositions != null && modifiedBoqPositions.Count > 0)))
					{
						var estBoqData = new EstimateMainBoqDrivenData() { TargetProjectId = (int)boqHeaderContext.ContextProjectId, GaebBoqItems = addedBoqPositions, GaebBoqItemsToUpdate = modifiedBoqPositions, IsFromBoqImport = true };
						Injector.Get<IEstimateMainLineItemLogic>().CreateEstLineItemsFromBoq(estBoqData);
					}

					if (!IsBidderFile)
						validationError += String.Format(NLS.IMP_Gaeb_Ok, gaebType, _boqRootItem.Reference, DivisionCount, ItemCount, TextCount);
					else
					{
						validationError += String.Format(NLS.IMP_Gaeb_84_Ok, gaebType, _boqRootItem.Reference, TotalCount, ItemCount);
						if (ItemNotFound > 0)
						{
							warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_Gaeb_84_ItemsNotFound),
								String.Format(NLS.GaebImportWarning_Gaeb_84_ItemsNotFound, ItemNotFound)));
						}
						if (TotalNotFound > 0)
						{
							warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_Gaeb_84_TotalsNotFound),
								String.Format(NLS.GaebImportWarning_Gaeb_84_TotalsNotFound, TotalNotFound)));
						}
					}

					if (_truncatedCounter > 0)
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_StringsTruncated),
							String.Format(NLS.GaebImportWarning_StringsTruncated, _truncatedCounter)));
					}
					if (_invalideLumpsums > 0)
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_LumpsumErrors),
							String.Format(NLS.GaebImportWarning_LumpsumErrors, _invalideLumpsums)));
					}
					if (_duplicateRefNo != null && _duplicateRefNo.Any())
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_DuplicateRefNo),
							String.Format(NLS.GaebImportWarning_DuplicateRefNo, String.Join(",", _duplicateRefNo))));
					}
				}
			}
			catch (Exception)
			{
				throw;
			}

			// Copy Unit Rate to Budget/Unit
			if (boqHeaderContext.ContextTable == "PRC_PACKAGE")
			{
				if (new SystemOptionLogic().GetLatestValueAsBool(SystemOption.PackageGaebImportCopyUnitRateToBudgetPerUnit, false))
				{
					BoqItemLogic.CopyUnitRateToBudgetUnit(boqHeaderId);
				}
			}

			return true;
		}

		/// <summary>
		/// The Import: return key:boqitem, value: Qty and Qtysplit(for create lineitem and reference lineitem)
		/// </summary>
		Dictionary<IBoqItemEntity, IBoqQtySplitData> Import(int boqHeaderId, string gaebFile, Dictionary<string, Dictionary<int, string>> ctlgTypeandKey, Dictionary<string, string> ctlgIdandTypeDic, Dictionary<int, string> prjCostGroupDic, Dictionary<int, string> enterpriseCostGroupDic, out string validationError, out bool isflag)
		{
			try
			{
				this.IsQtySplits = true;
				this.IsCollectBoQDivision = true;
				this.IsBoQReferenceInBidEstimate = true;
				this._isImportExternalCodeWihtUserDef = true;
				this._ctlgTypeandKey = ctlgTypeandKey;
				this._ctlgIdandTypeDic = ctlgIdandTypeDic;
				this._boqItemDataDic.Clear();
				this._wicBoQDic.Clear();

				// import from baseline with External Code
				var item = new SystemOptionLogic().GetSearchList(e => e.Id == SystemOption.ImportExternalCodeOption).FirstOrDefault();
				_importExternalCodeOption = item != null && !string.IsNullOrEmpty(item.ParameterValue) ? int.Parse(item.ParameterValue) : 0;

				Dictionary<IBoqItemEntity, IBoqQtySplitData> boqItemDataDic = new Dictionary<IBoqItemEntity, IBoqQtySplitData>();
				// get the gaeb info
				GaebInfoData gaebInfoData = new GaebInfoData();
				IBoqCompositeLogic boqCompositeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IBoqCompositeLogic>("Project");
				var boqHeader = boqCompositeLogic.GetBoqListEntityByBoqHeader(boqHeaderId);
				if (boqHeader != null)
				{
					gaebInfoData.ProjectId = boqHeader.MainItemId;
					gaebInfoData.BoqHeaderId = boqHeaderId;
					gaebInfoData.LocalFileName = gaebFile;
					gaebInfoData.AddNewItems = true;
					gaebInfoData.OverwriteExistingItems = true;
					BoqGaebImportLogic importLogic = new BoqGaebImportLogic();
					importLogic.GetGaebInfo(gaebFile, false, gaebInfoData);
					// set as enterprice costgroup
					if (enterpriseCostGroupDic.Any() && gaebInfoData.Catalogs.Any())
					{
						foreach (var costGroup in enterpriseCostGroupDic)
						{
							var catalog = gaebInfoData.Catalogs.FirstOrDefault(e => e.CtlgType.Equals("miscellaneous", StringComparison.OrdinalIgnoreCase) && e.CtlgName == costGroup.Value);
							if (catalog != null)
							{
								catalog.BoqCatalogFk = 14;
								catalog.BasCostgroupCatFk = costGroup.Key;
								catalog.CatalogAssignmentMode = BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Search;
							}
						}
					}

					// set as project costgroup
					if (prjCostGroupDic.Any() && gaebInfoData.Catalogs.Any())
					{
						foreach (var costGroup in prjCostGroupDic)
						{
							var catalog = gaebInfoData.Catalogs.FirstOrDefault(e => e.CtlgType.Equals("miscellaneous", StringComparison.OrdinalIgnoreCase) && e.CtlgName == costGroup.Value);
							if (catalog != null)
							{
								catalog.BoqCatalogFk = 15;
								catalog.BasCostgroupCatFk = costGroup.Key;
								catalog.CatalogAssignmentMode = BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Search;
							}
						}
					}
				}

				List<string> warnings = new List<string>();
				isflag = Import(boqHeaderId, gaebFile, out validationError, out warnings, null, false, importOptions: gaebInfoData);
				if (!isflag)
				{
					return null;
				}
				boqItemDataDic = this._boqItemDataDic;
				Clear(true);
				return boqItemDataDic;
			}
			catch (Exception ex)
			{
				Clear(true);
				throw new Exception(ex.Message);
			}
		}

		#endregion

		/// <summary>
		/// Clear
		/// </summary>
		private void Clear(bool isFromBasline = false)
		{
			if (isFromBasline) // TODO: if import from baseline, clear after import done.
			{
				//this._IsFromBasline = false;
				this.IsQtySplits = false;
				this.IsCollectBoQDivision = false;
				this.IsBoQReferenceInBidEstimate = false;
				_preFirstBoqBody = true;
				_postFirstBoqBody = false;
				this._ctlgTypeandKey = null;
				this._ctlgIdandTypeDic = null;
				this._wicBoQDic.Clear();
				this._textComplementItems.Clear();
				this._textConfigurations.Clear();
				this._isImportExternalCodeWihtUserDef = false;
			}

			_SplitEntityIdPool = null;

			_modifiedBlobs = new List<BlobEntity>();

			_forcePlainText = BoqCommonLogic.GetForcePlainTextSystemOption();

			_divisionsAdded = 0;
			_ItemsAdded = 0;
			_blobsAdded = 0;
			_divisionsUpdated = 0;
			_itemsUpdated = 0;
			_tcsAdded = 0;
			_tcsSkipped = 0;
			_sqsTotal = 0;
			_sqsAdded = 0;
			_truncatedCounter = 0;
			_duplicateRefNo.Clear();
		}

		#region IBoqGaebImportLogic

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="gaebFile"></param>
		/// <param name="ctlgTypeandKey"></param>
		/// <param name="ctlgIdandTypeDic"></param>
		/// <param name="prjCostGroupDic"></param>
		/// <param name="enterpriseCostGroupDic"></param>
		/// <param name="validationError"></param>
		/// <param name="isflag"></param>
		/// <returns></returns>
		public IDictionary<IBoqItemEntity, IBoqQtySplitData> ImportBoQ(int boqHeaderId, string gaebFile, Dictionary<string, Dictionary<int, string>> ctlgTypeandKey, Dictionary<string, string> ctlgIdandTypeDic, Dictionary<int, string> prjCostGroupDic, Dictionary<int, string> enterpriseCostGroupDic, out string validationError, out bool isflag)
		{
			var boqItemDataDic = Import(boqHeaderId, gaebFile, ctlgTypeandKey, ctlgIdandTypeDic, prjCostGroupDic, enterpriseCostGroupDic, out validationError, out isflag);
			return boqItemDataDic;
		}

		#endregion

		#region Check boq structure

		/// <summary>Validate</summary>
		private String Validate(BoqHeaderEntity boqHeader, bool hasBaseBoqWithChild, ref List<ImportWarning> warnings)
		{
			var retError = String.Empty;

			IsMatchingBoqStructure = false;

			var gaebAward = GaebXml.Award;

			if (gaebAward != null)
			{
				#region Currency
				IsHeaderCurrencyChange = false;
				var gaebBoqCurrency = String.Empty;
				var geabAwardInfo = gaebAward.AwardInfo;

				if (geabAwardInfo != null)
				{
					if (!geabAwardInfo.Cur_isEmpty())
						gaebBoqCurrency = geabAwardInfo.Cur;
				}

				if (String.IsNullOrEmpty(gaebBoqCurrency))
				{
					var gaebPrjInfo = GaebXml.PrjInfo;

					if (gaebPrjInfo != null)
					{
						if (!gaebPrjInfo.Cur_isEmpty())
							gaebBoqCurrency = gaebPrjInfo.Cur;
					}
				}

				// Currency check
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					var boqHeaderContext = dbContext.Entities<BoqHeaderContextVEntity>().First(bhc=>bhc.BoqHeaderId==boqHeader.Id);
					int gaebBoqCurrencyId = GetCurrencyFk(gaebBoqCurrency);
					if (gaebBoqCurrencyId <= 0)
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_Unknown_Currency),
							String.Format(NLS.GaebImportWarning_Unknown_Currency, gaebBoqCurrency)));
					}
					else if (boqHeaderContext.ContextTable!="PRJ_PROJECT" && boqHeaderContext.ContextCurrencyId.HasValue && boqHeaderContext.ContextCurrencyId!=gaebBoqCurrencyId)
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_Unexpected_Currency),
							String.Format(NLS.GaebImportWarning_Unexpected_Currency, gaebBoqCurrency, new BasicsCurrencyLogic().GetCurrencyById(boqHeaderContext.ContextCurrencyId.Value))));
					}
					else if (boqHeader.BasCurrencyFk != gaebBoqCurrencyId)
					{
						boqHeader.BasCurrencyFk = gaebBoqCurrencyId;
						IsHeaderCurrencyChange = true;
					}
				}
				#endregion

				String gaebBoqMask = String.Empty;
				GX_BoQ gaebBoq;
				GX_BoQInfo gaebBoqInfo;

				String urbName1 = String.Empty, urbName2 = String.Empty, urbName3 = String.Empty, urbName4 = String.Empty, urbName5 = String.Empty, urbName6 = String.Empty;

				// Builds the GAEB BOQ mask and checks if is matching structure and details
				if (((gaebBoq = gaebAward.BoQ) != null) && ((gaebBoqInfo = gaebBoq.BoQInfo) != null))
				{
					if (_byPackage)
					{
						Set_cltgIDbyWorkCategory(gaebBoqInfo);
					}
					// BoQ levels
					//var gaebLevel = 0;
					_GaebBoqStructureDetails = gaebBoqInfo.gNodes.Where(node => node is GX_BoQBkdn).Cast<GX_BoQBkdn>();
					var gxBoQBkdns = _GaebBoqStructureDetails as IList<GX_BoQBkdn> ?? _GaebBoqStructureDetails.ToList();
					//foreach (var gaebBoqStructureDetail in gxBoQBkdns)
					//{
					//    for (var j = 0; j < gaebBoqStructureDetail.Length; j++)
					//    {
					//        if (gaebBoqStructureDetail.Type.Equals("Item"))
					//            gaebBoqMask += "P";
					//        else if (gaebBoqStructureDetail.Type.Equals("BoQLevel"))
					//            gaebBoqMask += (gaebLevel + 1).ToString(CultureInfo.InvariantCulture);
					//        else if (gaebBoqStructureDetail.Type.Equals("Index"))
					//            gaebBoqMask += "I";
					//    }
					//    gaebLevel++;
					//}

					bool indexAdded;

					gaebBoqMask = GetGaebBoqMask(gaebBoqInfo, out indexAdded);

					//if (!gaebBoqInfo.NoUPComps_isEmpty())
					//{
					//	int upComps = System.Convert.ToInt32(gaebBoqInfo.NoUPComps);

					//	if (upComps >= 1 && gaebBoqInfo.LblUPComp1() != null)
					//		urbName1 = gaebBoqInfo.LblUPComp1().gValue;
					//	if (upComps >= 2 && gaebBoqInfo.LblUPComp2() != null)
					//		urbName2 = gaebBoqInfo.LblUPComp2().gValue;
					//	if (upComps >= 3 && gaebBoqInfo.LblUPComp3() != null)
					//		urbName3 = gaebBoqInfo.LblUPComp3().gValue;
					//	if (upComps >= 4 && gaebBoqInfo.LblUPComp4() != null)
					//		urbName4 = gaebBoqInfo.LblUPComp4().gValue;
					//	if (upComps >= 5 && gaebBoqInfo.LblUPComp5() != null)
					//		urbName5 = gaebBoqInfo.LblUPComp5().gValue;
					//	if (upComps >= 6 && gaebBoqInfo.LblUPComp6() != null)
					//		urbName6 = gaebBoqInfo.LblUPComp6().gValue;
					//}
					SetNameUrb(BoqStructure, gaebBoqInfo);

					// set user defined column
					// confusing code moved to separate method ...
					//var userDefinedInfo1 = gaebBoqInfo.Nodes.Where(node => node.gName().Equals(_lblUserDef1)).FirstOrDefault();
					//var userDefinedInfo2 = gaebBoqInfo.Nodes.Where(node => node.gName().Equals(_lblUserDef2)).FirstOrDefault();
					//var userDefinedInfo3 = gaebBoqInfo.Nodes.Where(node => node.gName().Equals(_lblUserDef3)).FirstOrDefault();
					//var userDefinedInfo4 = gaebBoqInfo.Nodes.Where(node => node.gName().Equals(_lblUserDef4)).FirstOrDefault();
					//var userDefinedInfo5 = gaebBoqInfo.Nodes.Where(node => node.gName().Equals(_lblUserDef5)).FirstOrDefault();
					//if (userDefinedInfo1 != null)
					//	definedNameUser1 = userDefinedInfo1.gValue;
					//if (userDefinedInfo2 != null)
					//	definedNameUser2 = userDefinedInfo2.gValue;
					//if (userDefinedInfo3 != null)
					//	definedNameUser3 = userDefinedInfo3.gValue;
					//if (userDefinedInfo4 != null)
					//	definedNameUser4 = userDefinedInfo4.gValue;
					//if (userDefinedInfo5 != null)
					//	definedNameUser5 = userDefinedInfo5.gValue;

					// check for match with default structure
					BoqStructureDetailEntity boqStructureDetail;
					bool structureEquals = gaebBoqMask.Equals(BoqStructure.Boqmask);
					if ((!structureEquals) && IsBidderFile && indexAdded)
					{
						string originalGaebBoQMask = gaebBoqMask.Substring(0, gaebBoqMask.Length - 1);
						structureEquals = originalGaebBoQMask.Equals(BoqStructure.Boqmask);
					}

					if (!structureEquals)
					{
						bool hasTargetBoqAnIndex = BoqStructure.Boqmask.Contains("I");
						bool hasImportBoqAnIndex = gaebBoqMask.Contains("I");
						var targetGaebBoqMask = BoqStructure.Boqmask.TrimEnd('I');
						var importGaebBoqMask = gaebBoqMask.TrimEnd('I');

						structureEquals = importGaebBoqMask.Equals(targetGaebBoqMask) && (hasTargetBoqAnIndex || !hasImportBoqAnIndex);
					}

					if (BoqStructure != null && BoqStructureDetails != null && structureEquals)
					{
						IsMatchingBoqStructure = true; //gaeb structure masks are equal

							// todo: validation skipped due to problems when import GAEB2000 p84 files
							//for (var j = 0; j < gxBoQBkdns.Count(); j++)
							//{
							//    GX_BoQBkdn gaebBoqStructureDetail = gxBoQBkdns.ElementAt(j);
							//    boqStructureDetail = BoqStructureDetails.ElementAt(j);

							//    if (!gaebBoqStructureDetail.Num_isEmpty())
							//    {
							//        if (gaebBoqStructureDetail.Num.Equals("Yes") && boqStructureDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric ||
							//             gaebBoqStructureDetail.Num.Equals("No") && boqStructureDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
							//        //|| gaebBoqStructureDetail.Num.Equals("No") && BoqStructure.LeadingZeros)
							//        {
							//            IsMatchingBoqStructure = false;
							//            break;
							//        }
							//    }
							//}

							//check URB names
							//if (IsMatchingBoqStructure && !IsBidderFile && !(BoqStructure.NameUrb1 == urbName1 && BoqStructure.NameUrb2 == urbName2 && BoqStructure.NameUrb3 == urbName3 &&
							//											 BoqStructure.NameUrb4 == urbName4 && BoqStructure.NameUrb5 == urbName5 && BoqStructure.NameUrb6 == urbName6))
							if (IsMatchingBoqStructure && !IsBidderFile && BoqStructure.State == EntityState.Modified)
							{
								IsMatchingBoqStructure = false;
							}
					}

					//Boq-Standard
					if (BoqStructure != null)
					{
						if (BoqStructure.BoqStandardFk != (int)EBoqStandard.Gaeb)
						{
							BoqStructure.BoqStandardFk = (int)EBoqStandard.Gaeb;
							IsMatchingBoqStructure = false;
						}
					}

					// Cat assign vergleichen
					// if ungleich: IsMatchingBoqStructure = false;
					//			    IsMatchingCatAssign = false
					// IsMatchingCatAssign = CatalogAssignmentsAreEqual(boqHeader);
					//if (!IsMatchingCatAssign)
					//{
					//	IsMatchingBoqStructure = false;
					//}

					if (!IsBidderFile)
					{
						IsMatchingCatAssign = CatalogAssignmentsAreEqual(boqHeader);
					}
					else
					{
						IsMatchingCatAssign = true;	// check not necessary
					}

					_isFreeBoq = BoqStructure.BoqStandardFk == (int)BoqConstants.EBoqStandard.Free;

					bool hasErrors = !IsMatchingBoqStructure || !IsMatchingCatAssign;
					// if (!IsMatchingBoqStructure)	--> same flag for all possible erros????
					if (hasErrors)
					{
						if (IsBidderFile && !IsMatchingBoqStructure)
						{
							retError = String.Format(NLS.IMP_Structure_mismatch, gaebBoqMask, BoqStructure.Boqmask);
							return retError;
						}

						if (!IsMatchingCatAssign && HasChildren(_boqRootItem))
						{
							warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_CatalogAssignmentMismatch), NLS.GaebImportWarning_CatalogAssignmentMismatch));
						}

						if (hasBaseBoqWithChild)
						{
							retError = String.Format(NLS.IMP_Gaeb_Base_Children_exist, gaebBoqMask, BoqStructure.Boqmask);
							return retError;
						}
					}

					if (!IsBidderFile && IsMatchingBoqStructure) // additionally check line type descriptions
					{
						IsMatchingBoqStructure = StructuresAreEqual(BoqStructure, gaebBoqInfo, indexAdded);
					}
					bool updateStructure = !IsBidderFile && (IsMatchingBoqStructure == false || IsMatchingCatAssign == false);
					if (updateStructure)
					{

						//BoqStructure.Boqmask = gaebBoqMask;
						//BoqStructure.Description = "GAEB " + gaebBoqMask;
						//BoqStructure.DescriptionTr = null;
						//BoqStructure.Sorting = Int16.MaxValue;
						//BoqStructure.Isdefault = false;
						//BoqStructure.PredefinedStructure = false;
						//BoqStructure.EnforceStructure = !_isFreeBoq; //#!# todo:...have to considere also the "left out hierarchy"
						//BoqStructure.Version = 0;
						SetBoqStructure(BoqStructure, gaebBoqMask);
						BoqStructureDetails = new List<BoqStructureDetailEntity>();

						bool indexFound = false;

						if (_GaebBoqStructureDetails != null)
						{
							var boqLevel = 0;
							foreach (var gaebBoqStructureDetail in gxBoQBkdns)
							{
								boqStructureDetail = new BoqStructureDetailEntity
								{
									DescriptionInfo = new DescriptionTranslateType(gaebBoqStructureDetail.LblBoQBkdn),
									LengthReference = (Int32)gaebBoqStructureDetail.Length,
									DataType = "Yes".Equals(gaebBoqStructureDetail.Num) ? (int)BoqConstants.EBoqStructureDetailDataType.Numeric : (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric,
									IsLeftAlignment = "No".Equals(gaebBoqStructureDetail.Num) && "left".Equals(gaebBoqStructureDetail.Alignment.ToLower()),
									DiscountAllowed = true
								};

								if ("Item".Equals(gaebBoqStructureDetail.Type))
								{
									boqStructureDetail.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Position;
									boqStructureDetail.Stepincrement = 10;
									boqStructureDetail.StartValue = "10";
									BoqStructure.LeadingZeros = false; // default, may be overwritten in DetermineLeadingZeros
									if (String.IsNullOrEmpty(boqStructureDetail.DescriptionInfo.Description))
										boqStructureDetail.DescriptionInfo.Description = NLS.IMP_Structure_Item;

								}
								else if ("BoQLevel".Equals(gaebBoqStructureDetail.Type) || "Lot".Equals(gaebBoqStructureDetail.Type))
								{
									boqStructureDetail.BoqLineTypeFk = boqLevel + 1;
									boqStructureDetail.Stepincrement = 1;
									boqStructureDetail.StartValue = "1";
								}
								else if ("Index".Equals(gaebBoqStructureDetail.Type))
								{
									boqStructureDetail.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Index;
									boqStructureDetail.Stepincrement = 1;
									boqStructureDetail.StartValue = "1";
									if (String.IsNullOrEmpty(boqStructureDetail.DescriptionInfo.Description))
									{
										boqStructureDetail.DescriptionInfo.Description = NLS.IMP_Structure_Index;
									}
									indexFound = true;
								}

								(BoqStructureDetails as List<BoqStructureDetailEntity>).Add(boqStructureDetail);
								boqLevel++;
							}

							if (!indexFound)
							{
								boqStructureDetail = new BoqStructureDetailEntity
								{
									DescriptionInfo = new DescriptionTranslateType(NLS.IMP_Structure_Index),
									LengthReference = BoqCommonLogic.GetForceBoqReferenceNoIndex() ? 1 : 0,
									DataType = (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric,
									DiscountAllowed = true,
									BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Index,
									Stepincrement = 1,
									StartValue = "1"
								};

								(BoqStructureDetails as List<BoqStructureDetailEntity>).Add(boqStructureDetail);
							}
						}

						//BoqStructure.NameUrb1 = urbName1;
						//BoqStructure.NameUrb2 = urbName2;
						//BoqStructure.NameUrb3 = urbName3;
						//BoqStructure.NameUrb4 = urbName4;
						//BoqStructure.NameUrb5 = urbName5;
						//BoqStructure.NameUrb6 = urbName6;
						// done above

						// user-defined
						//BoqStructure.NameUserdefined1 = definedNameUser1;
						//BoqStructure.NameUserdefined2 = definedNameUser2;
						//BoqStructure.NameUserdefined3 = definedNameUser3;
						//BoqStructure.NameUserdefined4 = definedNameUser4;
						//BoqStructure.NameUserdefined5 = definedNameUser5;
						SetUserDefinedLabels(BoqStructure, gaebBoqInfo);

						// copy cat assign
						// set BOQ_CAT_ASSIGN_CONFTYPE_FK ih Structure = NULL;
						// später neuen BOQ_CAT_ASSIGN_CONFTYPE_FK setzen
						/*if (_IsFromBasline)
						{
							BoqStructure.BoqCatAssignConfTypeFk = null;
							BoqStructure.BoqCatAssignFk = null;
						}*/
						if (IsMatchingCatAssign == false)
						{
							// copy cat assign --> see later
							BoqStructure.BoqCatAssignConfTypeFk = null;	// structure was overwritten by user
						}



					}
				}
			}

			return retError;
		}

		private void SetBoqStructure(BoqStructureEntity strucEntity, string gaebBoqMask)
		{
			strucEntity.Boqmask = gaebBoqMask;
			strucEntity.Description = "GAEB " + gaebBoqMask;
			strucEntity.DescriptionTr = null;
			strucEntity.Sorting = Int16.MaxValue;
			strucEntity.Isdefault = false;
			strucEntity.PredefinedStructure = false;
			strucEntity.EnforceStructure = !_isFreeBoq; //#!# todo:...have to considere also the "left out hierarchy"
			strucEntity.Version = 0;
		}

		#endregion

		#region Helper functions

		/// <summary>
		/// iterate BOQ tree and invoke the incoming 'action' for each item except the root one.
		/// </summary>
		/// <param name="boqItem"></param>
		/// <param name="action"></param>
		/// <param name="exitLoop"></param>
		private static void ForEachExceptTheRoot(BoqItemEntity boqItem, Action<BoqItemEntity> action, bool exitLoop = false)
		{
			if (boqItem == null || action == null || exitLoop)
				return;

			if (boqItem.BoqItemParent != null)
				action.Invoke(boqItem);

			foreach (var boqChildItem in boqItem.BoqItemChildren)
				ForEachExceptTheRoot(boqChildItem, action, exitLoop);
		}

		private static void ForEachExceptTheRoot(BoqItemEntity boqItem, BoqItemEntity parent, Func<BoqItemEntity, BoqItemEntity, BoqItemEntity> action)
		{
			if (boqItem == null || action == null)
				return;

			if (boqItem.BoqItemParent != null)
				parent = action.Invoke(boqItem, parent);

			foreach (var boqChildItem in boqItem.BoqItemChildren)
				ForEachExceptTheRoot(boqChildItem, parent, action);
		}


		private string GetDefiniteReferenceNo(string reference)
		{
			var result = reference;

			var refParts = reference.Split('.');
			if (refParts.Any())
			{
				for (var i = 0; i < refParts.Count(); i++)
				{
					if (i < BoqStructureDetails.Count())
					{
						var structDetail = BoqStructureDetails.ElementAt(i);
						int tmpValuePart;
						if (structDetail != null && structDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric && Int32.TryParse(refParts[i], out tmpValuePart))
						{
							refParts[i] = System.Convert.ToInt32(refParts[i]).ToString(CultureInfo.InvariantCulture);
						}
					}
				}
				result = String.Join(".", refParts);
			}
			return result;
		}

		/// <summary>
		/// GetBaseReference
		/// </summary>
		/// <returns>base boq fk</returns>
		private int? GetBaseReference(int? itemRef, Dictionary<int, int> refMap)
		{
			int? baseRef = null;

			if (itemRef != null && refMap.ContainsKey((int)itemRef))
				baseRef = refMap[(int)itemRef];

			return baseRef;
		}


		/// <summary>
		/// GetCurrencyFk (...only called one time)
		/// </summary>
		/// <returns></returns>
		private int GetCurrencyFk(String currency)
		{
			AvailableCurrencies = new BasicsCurrencyLogic().GetList();

			int ret = -1;

			if (!String.IsNullOrEmpty(currency))
			{
				foreach (var cur in AvailableCurrencies)
				{
					if (cur != null && !String.IsNullOrEmpty(cur.Currency))
					{
						if (cur.Currency.Trim().ToLower().Equals(currency.Trim().ToLower()))
						{
							ret = cur.Id;
							break;
						}
					}
				}
			}

			return ret;
		}

		private int GetDivisionTypeFk(String code)
		{
			int ret = -1;

			if (!String.IsNullOrEmpty(code))
			{
				if (AvailableDivisionType == null)
					AvailableDivisionType = new BasicsCustomizeDivisionTypeLogic().GetListByFilter(e => true);

				foreach (var divisionType in AvailableDivisionType)
				{
					if (divisionType != null && !String.IsNullOrEmpty(divisionType.Code))
					{
						if (divisionType.Code.Trim().ToLower().Equals(code.Trim().ToLower()))
						{
							ret = divisionType.Id;
							break;
						}
					}
				}
			}

			return ret;
		}

		private int GetBoqItemFlagFk(String code)
		{
			int ret = -1;

			if (!String.IsNullOrEmpty(code))
			{
				if (AvailableBoqItemFlag == null)
					AvailableBoqItemFlag = new BoqItemFlagLogic().GetList();

				foreach (var boqItemFlag in AvailableBoqItemFlag)
				{
					if (boqItemFlag != null && !String.IsNullOrEmpty(boqItemFlag.Code))
					{
						if (boqItemFlag.Code.Trim().ToLower().Equals(code.Trim().ToLower()))
						{
							ret = boqItemFlag.Id;
							break;
						}
					}
				}
			}

			return ret;
		}

		private int? GetItemStatusFk(String description)
		{
			int? ret = null;

			if (!String.IsNullOrEmpty(description))
			{
				if (AvailableItemStatuses == null)
				{
					using (var dbContext = new DbContext(Basics.Customize.BusinessComponents.ModelBuilder.DbModel))
					{
						AvailableItemStatuses = dbContext.Entities<BasicsCustomizeBoqItemStatusEntity>().ToList();
					}
				}

				var itemStatus = AvailableItemStatuses.FirstOrDefault(r => r.DescriptionInfo != null && r.DescriptionInfo.Description == description);

				if (itemStatus != null)
				{
					ret = itemStatus.Id;

				}
			}

			return ret;
		}


		private bool HasBaseBoqWithChildren(BoqItemEntity baseBoqRootItem)
		{
			return baseBoqRootItem != null && !IsBaseBoqWithoutChildren(baseBoqRootItem);
		}

		private bool HasBaseBoqWithoutChildren(BoqItemEntity baseBoqRootItem)
		{
			return baseBoqRootItem != null && IsBaseBoqWithoutChildren(baseBoqRootItem);
		}

		private bool IsBaseBoqWithoutChildren(BoqItemEntity baseBoqRootItem)
		{
			return (baseBoqRootItem.BoqItemChildren == null || (baseBoqRootItem.BoqItemChildren != null && !baseBoqRootItem.BoqItemChildren.Any()));
		}

		private bool HasChildren(BoqItemEntity boqItem)
		{
			return boqItem.BoqItemChildren != null && boqItem.BoqItemChildren.Any();
		}

		/// <summary>
		/// SetupGaebToolbox
		/// </summary>
		/// <returns></returns>
		static public GX_GAEB SetupGaebToolbox(Boolean isImport)
		{

			GX_GAEB gaebXml;

			gaebXml = new GX_GAEB();

			if (isImport)
			{
				gaebXml.gFlush();
				gaebXml.gSetOptions("loadMessageFile");
				gaebXml.gSetOptions("enableGaeb90");
				gaebXml.gSetOptions("enableGaeb2000");
				gaebXml.gSetOptions("xmlNotUseUnknownObjects");
			}
			else
			{
				//TODO: add RIB namespace for netcore version - lnt
				gaebXml.create_Object("RIB", "RIB");

				gaebXml.gSetOptions("disableGaeb90");
				gaebXml.gSetOptions("disableGaeb2000");
				gaebXml.gSetOptions("disableGaebXml");
				gaebXml.gSetOptions("xmlPrettyPrintOn");

				gaebXml.create_GAEBInfo();
				gaebXml.GAEBInfo.ProgName = "RIB 4.0";
			}

			gaebXml.gSetSerialNumber("NU98-HFNL-7DG8-AUAJ-7020");

			gaebXml.gSetOptions("konverterKey:211709");
			gaebXml.gSetOptions("xmlNotUseUnknownObjects");

			//if (!String.IsNullOrEmpty(DefaultFontName))
			//	gaebXml.gSetOptions(String.Format("xmlKonverterDefaultFontName:{0}", DefaultFontName));

			//gaebXml.gSetOptions(String.Format("xmlKonverterDefaultFontSize:{0}", DefaultFontSize));

			return gaebXml;
		}


		#endregion

		#region Update boq tree

		///
		///
		/// <returns></returns>
        private void UpdateBoqTree(G_Object gaebObject, String parentReferenceNo, int level, ref List<ImportWarning> warnings, BoqItemEntity boqParentItem = null)
        {
			var gaebChildObjects = gaebObject.gNodes;

			if (gaebChildObjects == null)
                return;

            BoqItemEntity boqItem = null;

            var referenceNo = parentReferenceNo;

            foreach (var gaebChildObject in gaebChildObjects)
            {
                if (gaebChildObject is GX_BoQCtgy)				// Division
                {
                    var gaebBoQCtgy = gaebChildObject as GX_BoQCtgy;
                    DivisionCount++;
                    referenceNo = BuildReferenceNo(parentReferenceNo, gaebBoQCtgy.att_RNoPart, "", level);

                    boqItem = GetMatchingBoqItem(GetDefiniteReferenceNo(referenceNo));
                    if (boqItem != null)
                    {
						SetTotals(gaebBoQCtgy.Totals, boqItem);
						UpdateBoqTree(gaebChildObject, referenceNo, ++level, ref warnings);	//one level deeper
                        level--;
                    }
                    else
                        TotalNotFound++;
                }
                else if (gaebChildObject is GX_Itemlist) // Item-List
                {
                    UpdateBoqTreeItemList(gaebChildObject as GX_Itemlist, referenceNo, level, ref warnings);	// not one level deeper for we're already coming from division level and have level here ?!
                }
                else if (gaebChildObject is GX_BoQInfo)			// boq root
                {
                    if (boqParentItem != null && boqItem != null)
                    {
                        var gaebBoQInfo = gaebChildObject as GX_BoQInfo;

                        if (String.IsNullOrEmpty(boqItem.Reference))
                            boqItem.Reference = gaebBoQInfo.Name;

						SetTotals(gaebBoQInfo.Totals, boqParentItem);
					}
				}
                else
                {
                    UpdateBoqTree(gaebChildObject, referenceNo, level, ref warnings);
                }
            }
        }

		/// <summary>
		/// elements of gaeb item list
		/// </summary>
		private void UpdateBoqTreeItemList(GX_Itemlist gaebObject, String parentReferenceNo, int level, ref List<ImportWarning> warnings)
		{
			var gaebChildObjects = gaebObject.gNodes;

			if (gaebChildObjects == null)
				return;

			BoqItemEntity boqItem;

			foreach (G_Object gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_Item)					// Item
				{
					var gaebItem = gaebChildObject as GX_Item;
					ItemCount++;

					var referenceNo = BuildReferenceNo(parentReferenceNo, gaebItem.att_RNoPart, gaebItem.att_RNoIndex, level, (int)BoqConstants.EBoqLineType.Position);

					boqItem = GetMatchingBoqItem(GetDefiniteReferenceNo(referenceNo));
					if (boqItem != null)
					{
						SetItem(boqItem, gaebItem, null, ref warnings);

						UpdateBoqTree(gaebItem, referenceNo, ref warnings);
					}
					else
						ItemNotFound++;
				}
				else if (gaebChildObject is GX_MarkupItem)		// Surcharge Item
				{
					var gaebMarkupItem = gaebChildObject as GX_MarkupItem;
					ItemCount++;

					boqItem = GetMatchingBoqItem(GetDefiniteReferenceNo(BuildReferenceNo(parentReferenceNo, gaebMarkupItem.att_RNoPart, gaebMarkupItem.att_RNoIndex, level)));
					if (boqItem != null)
					{
						bool isSurcharge4Item = !string.IsNullOrEmpty(gaebChildObject.Schema) && gaebChildObject.Schema.Equals(BoqConstants.RibGaebExtSchema) &&
							!string.IsNullOrEmpty(gaebChildObject.gName) && gaebChildObject.gName.Equals(BoqConstants.RibGaebExtMarkupItem) ? true : false;

						SetSurchargeItem(boqItem, gaebMarkupItem, null, ref warnings, isSurcharge4Item);
					}
					else
						ItemNotFound++;
				}
			}
		}

		/// <summary>
		/// gaeb item , if item is lead description, it have minimum one sub-description
		/// </summary>
		private void UpdateBoqTree(GX_Item gaebObject, String parentReferenceNo, ref List<ImportWarning> warnings)
		{
			var gaebChildObjects = gaebObject.gNodes;

			if (gaebChildObjects == null)
				return;

			foreach (var gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_SubDescr)			// TextElement
				{
					var gaebSubDescr = gaebChildObject as GX_SubDescr;

					var boqItem = GetMatchingBoqItem(GetDummySubDescRef(GetDefiniteReferenceNo(parentReferenceNo), StringExtension.Truncate(gaebSubDescr.SubDNo.ToString(CultureInfo.InvariantCulture), 4)));
					if (boqItem != null)
						SetSubDescription(boqItem, gaebSubDescr, boqItem.BoqItemParent, null, ref warnings);
					else
						ItemNotFound++;
				}
			}
		}

		#endregion



		#region Create new boq tree

		/// <summary>
		/// potential first level elements of gaeb boqbody
		/// </summary>
		private void CreateBoqTree(G_Object gaebObject, BoqItemEntity boqItem, BoqItemEntity preItem, ref List<ImportWarning> warnings, bool firstBoQLevel = false)
		{
			var gaebChildObjects = gaebObject.gNodes;
			if (gaebChildObjects == null)
				return;

			var predecessor = preItem;
			BoqItemEntity newBoqItem;

			foreach (var gaebChildObject in gaebChildObjects)
			{

				// import (not GAEB conform!) specification of divisions
				if (gaebChildObject is GX_BoQText)
				{
					GX_BoQText obj = gaebChildObject as GX_BoQText;
					SetSpecification(boqItem, System.Net.WebUtility.HtmlEncode(obj.gText), obj.gXml);
				}

				if (gaebChildObject is GX_BoQCtgy)				// Division
				{
					var gaebBoQCtgy = gaebChildObject as GX_BoQCtgy;
					DivisionCount++;

					newBoqItem = new BoqItemEntity();
					SetDivision(newBoqItem, gaebBoQCtgy, boqItem, ref warnings);
					predecessor = newBoqItem;
					if (firstBoQLevel)
						_lastFirstLeveltem = newBoqItem;

					SetTotals(gaebBoQCtgy.Totals, newBoqItem);

					SetBoqBudget(gaebBoQCtgy.gNodes, newBoqItem);

					SetUserDefinedInfos(gaebChildObject, newBoqItem); // set user defined

					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

					CreateBoqTree(gaebChildObject, newBoqItem, predecessor, ref warnings);	//one level deeper

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemDivisionData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemDivisionData, false);
					}

					var itemData = GetItemCatalogDataFromBoQCtgy(gaebBoQCtgy, newBoqItem);
					setCatalogFromBoQCtgy(newBoqItem, itemData, gaebBoQCtgy);
				}
				else if (gaebChildObject is GX_Itemlist)		// Item-List -> separate call without recursion
				{
					CreateBoqItems(gaebChildObject as GX_Itemlist, boqItem, predecessor, ref warnings);
				}
				else if (gaebChildObject is GX_AddText)			// TextElement
				{
					//special for "Final Remarks"
					if (_postFirstBoqBody)
						predecessor = _lastFirstLeveltem;
					//special for "Final Remarks"

					newBoqItem = new BoqItemEntity();
					SetTextElement(newBoqItem, gaebChildObject as GX_AddText, boqItem, predecessor);
					SetUserDefinedInfos(gaebChildObject, newBoqItem); // set user defined
					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined
					predecessor = newBoqItem;

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemTextElementData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemTextElementData, false);
					}
				}
				else if (gaebChildObject is GX_Remark)			// Note
				{
					newBoqItem = new BoqItemEntity();
					SetNote(newBoqItem, gaebChildObject as GX_Remark, boqItem, predecessor);
					SetUserDefinedInfos(gaebChildObject, newBoqItem); // set user defined
					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined
					predecessor = newBoqItem;

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemNoteData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemNoteData, false);
					}
				}
				else if (gaebChildObject is GX_PerfDescr)		// Design Description
				{
					newBoqItem = new BoqItemEntity();
					SetDesignDescription(newBoqItem, gaebChildObject as GX_PerfDescr, boqItem, predecessor);
					SetUserDefinedInfos(gaebChildObject, newBoqItem); // set user defined
					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined
					predecessor = newBoqItem;

					CreateBoqChildren(gaebChildObject as GX_PerfDescr, newBoqItem, predecessor);

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemPerfDescrData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemPerfDescrData, false);
					}
				}
				else if (gaebChildObject is GX_BoQBody)			// BoQ-Body, special for "Final Remarks"
				{
					var bFirstBoqBodyLevel = false;
					if (_preFirstBoqBody)
					{
						_preFirstBoqBody = false;
						bFirstBoqBodyLevel = true;
					}

					CreateBoqTree(gaebChildObject, boqItem, predecessor, ref warnings, bFirstBoqBodyLevel);

					if (bFirstBoqBodyLevel)
						_postFirstBoqBody = true;
				}
				else if (gaebChildObject is GX_BoQInfo)			// boq root: code, description, totals (mainly for discount or lumpsum)
				{
					if (boqItem.BoqItemParent == null)
					{
						var gaebBoQInfo = gaebChildObject as GX_BoQInfo;

						boqItem.ExternalCode = gaebBoQInfo.Name;
						if (String.IsNullOrEmpty(boqItem.Reference))
							boqItem.Reference = gaebBoQInfo.Name;

						if (!String.IsNullOrEmpty(boqItem.Reference) && boqItem.Reference.Length > 42)
							boqItem.Reference = boqItem.Reference.Substring(0, 42);

						boqItem.BriefInfo.Description = StringExtension.Truncate(gaebBoQInfo.LblBoQ, 2000);

						//Change order
						if (!gaebBoQInfo.CONo_isEmpty())
							SetChangeOrder(boqItem, gaebBoQInfo, System.Convert.ToUInt32(gaebBoQInfo.CONo), gaebBoQInfo.COStatus_isEmpty() ? string.Empty : gaebBoQInfo.COStatus);

						if (!String.IsNullOrEmpty(boqItem.BriefInfo.Description) && boqItem.BriefInfo.Description.Length > 2000)
						{
							boqItem.BriefInfo.Description = boqItem.BriefInfo.Description.Substring(0, 2000);
						}

						SetUserDefinedInfos(gaebChildObject, boqItem); // set user defined
						SetExternalCodeWithUserDef(gaebChildObject, boqItem); // set external code with user defined

						SetTotals(gaebBoQInfo.Totals, boqItem);

						SetBoqBudget(gaebBoQInfo.gNodes, boqItem);
					}
				}
				else if (!(gaebChildObject is GX_OWN || gaebChildObject is GX_Requester || gaebChildObject is GX_CTR
					|| gaebChildObject is GX_CnstSite || gaebChildObject is GX_NotifSite))
				{
					CreateBoqTree(gaebChildObject, boqItem, predecessor, ref warnings);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="itemData"></param>
		/// <param name="isCreateLineItem"></param>
		private void AddBoQItemData(BoqItemEntity item, BoqQtySplitData itemData, bool isCreateLineItem)
		{
			itemData.IsCreateLineItem = isCreateLineItem;
			if (!this._boqItemDataDic.ContainsKey(item))
				this._boqItemDataDic.Add(item, itemData);
		}

		/// <summary>
		/// elements of gaeb item list
		/// </summary>
		private void CreateBoqItems(GX_Itemlist gaebObject, BoqItemEntity boqItem, BoqItemEntity preItem, ref List<ImportWarning> warnings)
		{
			var gaebChildObjects = gaebObject.gNodes;

			if (gaebChildObjects == null)
				return;

			var predecessor = preItem;
			BoqItemEntity newBoqItem;

			foreach (var gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_Item)					// Item
				{
					newBoqItem = new BoqItemEntity();

					GX_Item gxItem = gaebChildObject as GX_Item;

					if (!string.IsNullOrEmpty(gxItem.QU) && gxItem.QU.ToUpper().Equals("TEXT")) //Bosch
					{
						SetNoteFromTextItem(newBoqItem, gxItem, boqItem, predecessor);
						predecessor = newBoqItem;

						if (IsCollectBoQDivision)
						{
							BoqQtySplitData itemNoteData = new BoqQtySplitData();
							AddBoQItemData(newBoqItem, itemNoteData, false);
						}

					}
					else
					{
						ItemCount++;
						Add2ReferenceMap(gxItem.att_ID, newBoqItem);	// not longer done in SetItem!

						SetItem(newBoqItem, gxItem, boqItem, ref warnings);
						predecessor = newBoqItem;

						//set PrjCharater
						SetItemPrjCharacter(newBoqItem, gaebChildObject);
						//set workcontent
						SetItemWorkContent(newBoqItem, gaebChildObject);
						// set text configuration detail
						SetTextConfigurations(newBoqItem, gaebChildObject);

						bool hasSplitQuantities;
						CreateBoqChildren(gaebChildObject as GX_Item, newBoqItem, out hasSplitQuantities, ref warnings);

						var itemData = GetItemDataQtyAndQtySplits(gaebChildObject as GX_Item, newBoqItem);
						if (IsQtySplits)	// only for chinese requirements
						{
							AddBoQItemData(newBoqItem, itemData, true);
						}
						SetSplitQuantities(newBoqItem, itemData, gxItem);

						// add user-defined infos
						SetUserDefinedInfos(gaebChildObject, newBoqItem);

						SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

						WriteRibExtensionControllingUnitToMap(gaebChildObject as GX_Item, newBoqItem);
					}

				}
				else if (gaebChildObject is GX_MarkupItem)		// Surcharge Item
				{
					ItemCount++;
					newBoqItem = new BoqItemEntity();
					bool isSurcharge4Item = !string.IsNullOrEmpty(gaebChildObject.Schema) && gaebChildObject.Schema.Equals(BoqConstants.RibGaebExtSchema) &&
						!string.IsNullOrEmpty(gaebChildObject.gName) && gaebChildObject.gName.Equals(BoqConstants.RibGaebExtMarkupItem) ? true : false;

					SetSurchargeItem(newBoqItem, gaebChildObject as GX_MarkupItem, boqItem, ref warnings, isSurcharge4Item);
					predecessor = newBoqItem;

					//set PrjCharater
					SetItemPrjCharacter(newBoqItem, gaebChildObject);
					//set workcontent
					SetItemWorkContent(newBoqItem, gaebChildObject);
					// set text configuration detail
					SetTextConfigurations(newBoqItem, gaebChildObject);
					// add user-defined infos
					SetUserDefinedInfos(gaebChildObject, newBoqItem);

					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

					CreateBoqChildren(gaebChildObject as GX_MarkupItem, newBoqItem);

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemSurchargeData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemSurchargeData, true);
					}

					var itemData = GetItemCatalogDataFromMarkupItem(gaebChildObject as GX_MarkupItem, newBoqItem);
					setCatalogFromMarkupItem(newBoqItem, itemData, gaebChildObject as GX_MarkupItem);
				}
				else if (gaebChildObject is GX_Remark)			// Note
				{
					newBoqItem = new BoqItemEntity();
					SetNote(newBoqItem, gaebChildObject as GX_Remark, boqItem, predecessor);
					// add user-defined infos
					SetUserDefinedInfos(gaebChildObject, newBoqItem);

					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

					predecessor = newBoqItem;

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemNoteData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemNoteData, false);
					}
				}
				else if (gaebChildObject is GX_PerfDescr)		// Design Description
				{
					newBoqItem = new BoqItemEntity();
					SetDesignDescription(newBoqItem, gaebChildObject as GX_PerfDescr, boqItem, predecessor);
					// add user-defined infos
					SetUserDefinedInfos(gaebChildObject, newBoqItem);

					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

					predecessor = newBoqItem;

					CreateBoqChildren(gaebChildObject as GX_PerfDescr, newBoqItem, predecessor);

					if (IsCollectBoQDivision)
					{
						BoqQtySplitData itemPerfDescrData = new BoqQtySplitData();
						AddBoQItemData(newBoqItem, itemPerfDescrData, false);
					}
				}
			}
			AssignRibExtensionControllingUnit();
		}

		/// <summary>
		/// Get QtySplit Info
		/// </summary>
		private BoqQtySplitData GetItemDataQtyAndQtySplits(GX_Item gaebObject, BoqItemEntity item)
		{
			BoqQtySplitData itemData = new BoqQtySplitData();

			// only the lumpsum uom will be seted as lumpsum
			itemData.IsLumpSum = IsLumpsumUnit(gaebObject.QU);

			// qtysplits
			foreach (var Node in gaebObject.gNodes)
			{
				#region QtySplits
				if (Node is GX_QtySplit)
				{
					if (itemData.QtySplits == null)
					{
						itemData.QtySplits = new List<IBoqQtySplit>();
					}

					BoqQtySplit qtySplist = new BoqQtySplit();
					qtySplist.Qty = (Node as GX_QtySplit).Qty;

					if (qtySplist.CtlgIDs == null)
						qtySplist.CtlgIDs = new List<string>();
					if (qtySplist.CtlgCodes == null)
						qtySplist.CtlgCodes = new List<string>();

					// locality and classification
					foreach (var Ctlg in Node.gNodes)
					{
						if (Ctlg is GX_CtlgAssign)
						{
							string ctlgCode = StringExtension.Truncate((Ctlg as GX_CtlgAssign).CtlgCode, 16);
							var ctlgId = (Ctlg as GX_CtlgAssign).CtlgID;
							qtySplist.CtlgIDs.Add(ctlgId);
							if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey(ctlgId))
							{
								string ctlgType = _ctlgIdandTypeDic[ctlgId].ToString();
								if (ctlgType == _locality)
								{
									var existItem = ImportOptions.LocationCtlgDatas != null ? ImportOptions.LocationCtlgDatas.Where(e => e.CtlgCode == ctlgCode).FirstOrDefault() : null;
									if (existItem != null)
										qtySplist.CpiID = existItem.CpiId;
								}
							}
							qtySplist.CtlgCodes.Add(ctlgCode);
						}

						#region Activity Data
						if (Ctlg.gName.Equals(_activities))
						{
							List<ActivityData> activities = new List<ActivityData>();
							foreach (var ctlgNode in Ctlg.gNodes)
							{
								ActivityData activity = new ActivityData();
								for (int i = 0; i < ctlgNode.gAttributeCount; i++)
								{
									string attributName = ctlgNode.gAttributeName(i);
									var array = attributName.Split(':');
									if (array.Length > 1)
									{
										if (array[1].Equals(_activityNo))
										{
											activity.No = ctlgNode.gAttributeValue(i);
										}
										else if (array[1].Equals(_subItemNo))
										{
											activity.SubItemNo = ctlgNode.gAttributeValue(i);
										}
									}
								}
								activities.Add(activity);
							}
							qtySplist.Activities = activities;
						}
						#endregion

						#region Controlling Unit Data

						if (Ctlg.gName.Equals(_controllingUnit))
						{
							ControllingUnitData controllingUnitData = new ControllingUnitData();
							controllingUnitData.controllingUnitNo = Ctlg.gValue;
							qtySplist.ControllingUnit = controllingUnitData;
						}

						#endregion

						#region AQ-Quantity in qtysplit
						if (Ctlg.gName.Equals(_predQty))
						{
							qtySplist.PredQty = System.Convert.ToDouble(Ctlg.gValue);
						}
						#endregion

						#region Comment
						if (!string.IsNullOrEmpty(Ctlg.Schema) && Ctlg.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(Ctlg.gName) && Ctlg.gName.Equals(BoqConstants.RibGaebExtComment))
						{
							qtySplist.CommentText = Ctlg.gValue;
						}
						#endregion


					}

					itemData.QtySplits.Add(qtySplist);
				}
				#endregion

				#region CtlgAssign

				// work category and classification
				if (Node is GX_CtlgAssign)
				{
					if (itemData.CtlgIDs == null)
						itemData.CtlgIDs = new List<string>();
					if (itemData.CtlgCodes == null)
						itemData.CtlgCodes = new List<string>();

					SetPrcStructureAssignment(item, Node as GX_CtlgAssign);

					if ((Node as GX_CtlgAssign).CtlgID != null)
					{
						itemData.CtlgIDs.Add((Node as GX_CtlgAssign).CtlgID);
					}

					if ((Node as GX_CtlgAssign).CtlgCode != null)
					{
						itemData.CtlgCodes.Add((Node as GX_CtlgAssign).CtlgCode);
					}

					if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey((Node as GX_CtlgAssign).CtlgID))
					{
						string ctlgType = _ctlgIdandTypeDic[(Node as GX_CtlgAssign).CtlgID].ToString();
						foreach (var ctlgKey in _ctlgTypeandKey.Keys)
						{
							if (ctlgKey.Contains(ctlgType))
							{
								foreach (KeyValuePair<int, string> ctlgIdandCode in _ctlgTypeandKey[ctlgKey])
								{
									if (ctlgIdandCode.Value != null && ctlgIdandCode.Value.Contains(","))
									{
										if (ctlgType == "work category")
										{
											var assignCodes = ctlgIdandCode.Value.ToUpper().Split(',').ToList();
											bool isExist = false;
											foreach (var assignCode in assignCodes)
											{
												if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == assignCode.ToUpper())
												{
													item.PrcStructureFk = (int?)ctlgIdandCode.Key;
													isExist = true;
													break;
												}
											}
											if (isExist)
												break;
										}
									}
									else
									{
										if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == ctlgIdandCode.Value.ToUpper())
										{
											if (ctlgType == "work category")
											{
												item.PrcStructureFk = (int?)ctlgIdandCode.Key;
											}
											break;
										}
									}
								}
							}
						}
					}
				}

				#endregion

				#region lump sum billing

				//if (Node.gName().Equals(_lumpSumBilling))
				//{
				//	itemData.IsLumpSum = Node.gValue.Equals("Yes") ? true : false;
				//}
				#endregion
			}

			return itemData;
		}

		/// <summary>
		/// Get QtySplit Info
		/// </summary>
		private BoqQtySplitData GetItemCatalogDataFromMarkupItem(GX_MarkupItem gaebObject, BoqItemEntity item)
		{
			BoqQtySplitData itemData = new BoqQtySplitData();

			// qtysplits
			foreach (var Node in gaebObject.gNodes)
			{
				if (Node is GX_CtlgAssign)
				{
					if (itemData.CtlgIDs == null)
						itemData.CtlgIDs = new List<string>();
					if (itemData.CtlgCodes == null)
						itemData.CtlgCodes = new List<string>();

					SetPrcStructureAssignment(item, Node as GX_CtlgAssign);

					if ((Node as GX_CtlgAssign).CtlgID != null)
					{
						itemData.CtlgIDs.Add((Node as GX_CtlgAssign).CtlgID);
					}

					if ((Node as GX_CtlgAssign).CtlgCode != null)
					{
						itemData.CtlgCodes.Add((Node as GX_CtlgAssign).CtlgCode);
					}

					if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey((Node as GX_CtlgAssign).CtlgID))
					{
						string ctlgType = _ctlgIdandTypeDic[(Node as GX_CtlgAssign).CtlgID].ToString();
						foreach (var ctlgKey in _ctlgTypeandKey.Keys)
						{
							if (ctlgKey.Contains(ctlgType))
							{
								foreach (KeyValuePair<int, string> ctlgIdandCode in _ctlgTypeandKey[ctlgKey])
								{
									if (ctlgIdandCode.Value != null && ctlgIdandCode.Value.Contains(","))
									{
										if (ctlgType == "work category")
										{
											var assignCodes = ctlgIdandCode.Value.ToUpper().Split(',').ToList();
											bool isExist = false;
											foreach (var assignCode in assignCodes)
											{
												if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == assignCode.ToUpper())
												{
													item.PrcStructureFk = (int?)ctlgIdandCode.Key;
													isExist = true;
													break;
												}
											}
											if (isExist)
												break;
										}
									}
									else
									{
										if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == ctlgIdandCode.Value.ToUpper())
										{
											if (ctlgType == "work category")
											{
												item.PrcStructureFk = (int?)ctlgIdandCode.Key;
											}
											break;
										}
									}
								}
							}
						}
					}
				}

				#endregion
			}

			return itemData;
		}

				/// <summary>
		/// Get QtySplit Info
		/// </summary>
		private BoqQtySplitData GetItemCatalogDataFromBoQCtgy(GX_BoQCtgy gaebObject, BoqItemEntity item)
		{
			BoqQtySplitData itemData = new BoqQtySplitData();

			// qtysplits
			foreach (var Node in gaebObject.gNodes)
			{
				if (Node is GX_CtlgAssign)
				{
					if (itemData.CtlgIDs == null)
						itemData.CtlgIDs = new List<string>();
					if (itemData.CtlgCodes == null)
						itemData.CtlgCodes = new List<string>();

					SetPrcStructureAssignment(item, Node as GX_CtlgAssign);

					if ((Node as GX_CtlgAssign).CtlgID != null)
					{
						itemData.CtlgIDs.Add((Node as GX_CtlgAssign).CtlgID);
					}

					if ((Node as GX_CtlgAssign).CtlgCode != null)
					{
						itemData.CtlgCodes.Add((Node as GX_CtlgAssign).CtlgCode);
					}

					if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey((Node as GX_CtlgAssign).CtlgID))
					{
						string ctlgType = _ctlgIdandTypeDic[(Node as GX_CtlgAssign).CtlgID].ToString();
						foreach (var ctlgKey in _ctlgTypeandKey.Keys)
						{
							if (ctlgKey.Contains(ctlgType))
							{
								foreach (KeyValuePair<int, string> ctlgIdandCode in _ctlgTypeandKey[ctlgKey])
								{
									if (ctlgIdandCode.Value != null && ctlgIdandCode.Value.Contains(","))
									{
										if (ctlgType == "work category")
										{
											var assignCodes = ctlgIdandCode.Value.ToUpper().Split(',').ToList();
											bool isExist = false;
											foreach (var assignCode in assignCodes)
											{
												if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == assignCode.ToUpper())
												{
													item.PrcStructureFk = (int?)ctlgIdandCode.Key;
													isExist = true;
													break;
												}
											}
											if (isExist)
												break;
										}
									}
									else
									{
										if ((Node as GX_CtlgAssign).CtlgCode != null && (Node as GX_CtlgAssign).CtlgCode.ToUpper() == ctlgIdandCode.Value.ToUpper())
										{
											if (ctlgType == "work category")
											{
												item.PrcStructureFk = (int?)ctlgIdandCode.Key;
											}
											break;
										}
									}
								}
							}
						}
					}
				}
			}

			return itemData;
		}

		private void WriteRibExtensionControllingUnitToMap(GX_Item gaebObject, BoqItemEntity newBoqItem)
		{
			foreach (var Node in gaebObject.gNodes)
			{
				if (Node.gName == BoqConstants.RibGaebExtAssignmentList)
				{
					G_Object controllingUnitToAssign = new G_Object();

					if (Node.gNodes != null && Node.gNodes.Any())
					{
						foreach (var assignNode in Node.gNodes)
						{
							if (assignNode.gName == BoqConstants.RibGaebExtAssign)
							{
								if (assignNode.gNodes != null && assignNode.gNodes.Any() && assignNode.gAttributes.Any())
								{
									if (assignNode.gAttributes.FirstOrDefault().gValue == BoqConstants.RibGaebExtControllingStructure && assignNode.gNodes.FirstOrDefault().gName == BoqConstants.RibGaebExtAssignItem)
									{
										controllingUnitToAssign = assignNode.gNodes.FirstOrDefault();
									}
								}
							}
						}
					}

					if (controllingUnitToAssign != null)
					{
						if (controllingUnitToAssign.gAttributes.Find(cuId => cuId.gName == "ID") != null)
						{
							string controllingUnitCode = controllingUnitToAssign.gAttributes.Find(cuId => cuId.gName == "ID").gValue;

							if (!string.IsNullOrEmpty(controllingUnitCode))
							{
								_controllingUnitCodeToBoqItemMap.Add(Tuple.Create(controllingUnitCode, newBoqItem));
							}
						}
					}
				}
			}
		}

		private void AssignRibExtensionControllingUnit()
		{
			if (_controllingUnitCodeToBoqItemMap != null && _controllingUnitCodeToBoqItemMap.Any())
			{
				Dictionary<string, int> controllingUnitsForRibGaebImport = new Dictionary<string, int>();
				ControllingunitLogic controllingUnitLogic = new ControllingunitLogic();

				List<string> controllingUnitsToAssignCodes = new List<String>();

				foreach (var tuple in _controllingUnitCodeToBoqItemMap)
				{
					controllingUnitsToAssignCodes.Add(tuple.Item1);
				}


				using (var dbcontext = new RIB.Visual.Platform.BusinessComponents.DbContext(controllingUnitLogic.GetDbModel()))
				{
					controllingUnitsForRibGaebImport = dbcontext.Entities<ControllingunitEntity>().Where(cu => controllingUnitsToAssignCodes.Contains(cu.Code)).ToDictionary(cu => cu.Code, cu => cu.Id);
				}


				if (controllingUnitsToAssignCodes.Distinct().Intersect(controllingUnitsForRibGaebImport.Keys).Count() == controllingUnitsToAssignCodes.Distinct().Count())
				{
					foreach (var controllingUnitCodeBoqItemPair in _controllingUnitCodeToBoqItemMap)
					{
						controllingUnitCodeBoqItemPair.Item2.MdcControllingUnitFk = controllingUnitsForRibGaebImport.Where(c => c.Key == controllingUnitCodeBoqItemPair.Item1).Select(c => c.Value).FirstOrDefault();
					}
				}
				else
				{
					throw new BusinessLayerException(NLS.IMP_RIBGaeb_Unexisting_Controlling_Unit, null);
				}
			}
		}

		/// <summary>
		/// (design description) and its sub text elements
		/// </summary>
		private void CreateBoqChildren(GX_PerfDescr gaebObject, BoqItemEntity boqItemParent, BoqItemEntity preItem)
		{
			var gaebChildObjects = gaebObject.gNodes;
			if (gaebChildObjects == null)
				return;

			var predecessor = preItem;

			foreach (G_Object gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_Description)          // as TextElement
				{
					TextCount++;

					var newBoqItem = new BoqItemEntity();
					SetItemBasics(newBoqItem, gaebChildObject as GX_Description, boqItemParent, predecessor, (int)BoqConstants.EBoqLineType.TextElement);
					SetUserDefinedInfos(gaebChildObject, newBoqItem); // set user defined

					SetExternalCodeWithUserDef(gaebChildObject, newBoqItem); // set external code with user defined

					predecessor = newBoqItem;
				}
			}
		}

		/// <summary>
		/// gaeb item , if item is lead description, it have minimum one sub-description
		/// </summary>
		private void CreateBoqChildren(GX_Item gaebObject, BoqItemEntity boqItemParent, out bool hasSplitQuantities, ref List<ImportWarning> warnings)
		{

			hasSplitQuantities = false;

			var gaebChildObjects = gaebObject.gNodes;

			if (gaebChildObjects == null)
				return;

			var predecessor = boqItemParent;

			foreach (G_Object gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_SubDescr)			// TextElement for lead-description
				{
					var newBoqItem = new BoqItemEntity();
					SetSubDescription(newBoqItem, gaebChildObject as GX_SubDescr, boqItemParent, predecessor, ref warnings);
					predecessor = newBoqItem;
				}

				// pr handle split quantities
				if (gaebChildObject is GX_QtySplit)
				{
					var obj = gaebChildObject as GX_QtySplit;
					hasSplitQuantities = true;
				}


			}
		}

		/// <summary>
		/// surcharged items for surcharge item type 3
		/// </summary>
		private void CreateBoqChildren(GX_MarkupItem gaebObject, BoqItemEntity boqItem)
		{
			if (boqItem.BoqLineTypeFk != (int)BoqConstants.EBoqLineType.SurchargeItemType3 && boqItem.BoqLineTypeFk != (int)BoqConstants.EBoqLineType.SurchargeItemType4)
				return;

			var gaebChildObjects = gaebObject.gNodes;
			if (gaebChildObjects == null)
				return;

			var listGisi = new List<GaebImportSurchargedItem>();

			foreach (G_Object gaebChildObject in gaebChildObjects)
			{
				if (gaebChildObject is GX_MarkupSubQty)
				{
					var gIsi = new GaebImportSurchargedItem();

					var gxMarkupSubQty = gaebChildObject as GX_MarkupSubQty;

					var gxRefItem = gxMarkupSubQty.RefItem;
					if (gxRefItem != null)
					{
						gIsi.GaebId = gxRefItem.att_IDRef; //can references to every item, so maybe item not exist yet
						gIsi.Quantity = null;

						if (!gxMarkupSubQty.SubQty_isEmpty())
							gIsi.Quantity = gxMarkupSubQty.SubQty;

						listGisi.Add(gIsi);
					}
				}
			}

			if (listGisi.Any())
				_itemSurchargedItems.Add(boqItem, listGisi);
		}

		/// <summary>
		/// create surcharged items for surcharge item type 3 (after all items are in the refmap)
		/// </summary>
		private void CreateSurchargedItemsType3()
		{
			foreach (var kvp in _itemSurchargedItems)
			{
				foreach (var si in kvp.Value)
				{
					string justifiedRefNr = BoqImExportHelper.JustifyReference(si.GaebId);
					if (_itemRefMap.ContainsKey(justifiedRefNr))
					{
						var siEntity = new BoqSurchargedItemEntity
						{
							BoqHeaderFk = kvp.Key.BoqHeaderFk,
							QuantitySplit = si.Quantity == null ? _itemRefMap[justifiedRefNr].Quantity : System.Convert.ToDecimal(si.Quantity),
							BoqSurchargedItem = _itemRefMap[justifiedRefNr]
						};

						if (kvp.Key.BoqSurchardedItemEntities == null)
							kvp.Key.BoqSurchardedItemEntities = new List<BoqSurchargedItemEntity>();

						kvp.Key.BoqSurchardedItemEntities.Add(siEntity);
					}
				}
			}
		}

		#region Set items from GAEB elements

		static private void SetBasItemTypeFk(BoqItemEntity boqItem, String provis)
		{
			if (String.IsNullOrEmpty(provis))
			{
				boqItem.BasItemTypeFk = (int)EBoqItemType1.Standard;
			}
			else
			{
				boqItem.BasItemTypeFk =  "WithTotal".Equals(provis) ? (int)EBoqItemType1.OptionalWithIT : (int)EBoqItemType1.OptionalWithoutIT;
			}
		}

		static private void SetBasItemType2Fk(BoqItemEntity item, bool isNormal, int serNo, int groupNo, bool isAcceptedEmpty, string accepted, ref List<ImportWarning> warnings)
		{
			if (!isNormal)
			{
				if (isAcceptedEmpty)
				{
					if (serNo == 0)
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Base;
					}
					else
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Alternative;
					}
				}
				else
				{
					if ("AltAccept".Equals(accepted))
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.AlternativeAwarded;
					}
					else // "BasReject"
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.BasePostponed;
					}
				}

				#region ALM 132145 check groupNo and serNo

				short? agn = null;
				try
				{
					agn = System.Convert.ToInt16(groupNo);
					if (agn < 0)
					{
						warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_AGNIsIncorrect),
						String.Format(NLS.GaebImportWarning_AGNIsIncorrect, item.Reference)));
					}
				}
				catch
				{
					warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_AGNIsIncorrect),
						String.Format(NLS.GaebImportWarning_AGNIsIncorrect, item.Reference)));
				}

				short? aan = null;
				try
				{
					aan = System.Convert.ToInt16(serNo);
					if (aan < 0)
					{
						throw new Exception(String.Format(NLS.GaebImportError_PleaseCheckAANandAGN, item.Reference));
					}
				}
				catch
				{
					throw new Exception(String.Format(NLS.GaebImportError_PleaseCheckAANandAGN, item.Reference));
				}
				#endregion ALM 132145 check groupNo and serNo

				item.AGN = (agn>=0 && agn<=999) ? agn.ToString() : null;		// the GAEB Toolbox converts invalid values in gaeb files to 2147483647???

				if (aan < 9999)
					item.AAN = aan.ToString();
				else
					item.AAN = "1";
			}
			else
			{
				item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Normal;
			}
		}

		private void SetUoM(BoqItemEntity item, string gaebQu, ref List<ImportWarning> warnings)
		{
			var uomFound = false;
			gaebQu = !string.IsNullOrEmpty(gaebQu) ? gaebQu.Trim() : "";
			foreach (var uom in AvailableUoms)
			{
				if (uom.Unit.Equals(gaebQu))
				{
					item.BasUomFk = uom.Id;
					uomFound = true;
					break;
				}
			}
			if (!uomFound)
			{
				item.ExternalUom = gaebQu;

				foreach (var uomSynonym in AvailableUomSynonyms)
				{
					if (uomSynonym.Synonym.ToLower().Equals(gaebQu.ToLower()) && AvailableUoms.Any(uom => uom.Id == uomSynonym.UnitFk))
					{
						item.BasUomFk = uomSynonym.UnitFk;
						uomFound = true;
						break;
					}
				}
			}
			if (!uomFound)
			{
				var fallbackUom = AvailableUoms.FirstOrDefault(uom => uom.Fallback);
				item.BasUomFk = fallbackUom == null ? 0 : fallbackUom.Id;
			}
			if (item.BasUomFk == 0 && !String.IsNullOrEmpty(item.Reference))
			{
				string warningText = String.Format("{0} - {1}", item.Reference, NLS.GaebImportWarning_UomIsEmpty);
				if (!String.IsNullOrEmpty(item.ExternalUom))
				{
					warningText = String.Format("{0} {1}", warningText, String.Format(NLS.GaebImportWarning_ExternalUomExists, item.ExternalUom));
				}
				warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_UomIsEmpty), warningText));
			}
		}

		private void SetItemReferenceNo2(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqRef2))
				{
					item.Reference2 = gaebObject.gValue;
					break;
				}
			}
		}

		private void SetItemWorkContent(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqWorkContent))
				{

					BoqCharacterContentEntity entityContent = characterLogic.Create();
					entityContent.Content = gaebObject.gValue;
					item.BoqCharacterContentWorkFk = entityContent.Id;
					characterLogic.Update(entityContent);
					break;
				}
			}
		}

		private void SetItemPrjCharacter(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqPrjCharacter))
				{
					BoqCharacterContentEntity entityPrjCharater = characterLogic.Create();
					entityPrjCharater.Content = gaebObject.gValue;
					item.BoqCharacterContentPrjFk = entityPrjCharater.Id;
					characterLogic.Update(entityPrjCharater);
					break;
				}
			}
		}

		private void SetTextConfigurations(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqTextConfigGroups))
				{
					var boqTextConfigLogic = new BoqTextConfigurationLogic();
					List<BoqTextConfigurationEntity> boqTextConfigList = new List<BoqTextConfigurationEntity>();
					foreach (var TextConfigNode in gaebObject.gNodes)
					{
						if (!string.IsNullOrEmpty(TextConfigNode.Schema) && TextConfigNode.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigNode.gName) && TextConfigNode.gName.Equals(BoqConstants.RibGaebExtBoqTextConfigGroup))
						{
							var entity = boqTextConfigLogic.Create();
							foreach (var TextConfigDetail in TextConfigNode.gNodes)
							{
								SetTextConfigurationDetail(entity, TextConfigDetail);

								if (!string.IsNullOrEmpty(TextConfigNode.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigNode.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtBoqTextConfigGroup))
								{
									SetTextConfigurationChildrens(item, TextConfigDetail, entity.Id, boqTextConfigList);
								}
							}
							boqTextConfigList.Add(entity);
						}
					}

					foreach (var boqTextConfig in boqTextConfigList)
					{
						item.BoqTextConfigurationEntities.Add(boqTextConfig);
					}

				}
			}
		}

		/// <summary>
		///
		/// </summary>
		private void SetTextConfigurationDetail(BoqTextConfigurationEntity entity, G_Object TextConfigDetail)
		{
			if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtConfigType))
				entity.ConfigType = TextConfigDetail.gValue == "PrjCharacter" ? 1 : 2;
			else if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtConfigCaption))
				entity.ConfigCaption = TextConfigDetail.gValue;
			else if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtConfigBody))
				entity.ConfigBody = TextConfigDetail.gValue;
			else if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtConfigTail))
				entity.ConfigTail = TextConfigDetail.gValue;
			else if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtIsoutput))
				entity.Isoutput = TextConfigDetail.gValue == "1" ? true : false;
			else if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtRemark))
			{
				entity.Remark = TextConfigDetail.gValue;
			}
		}

		/// <summary>
		///
		/// </summary>
		private void SetTextConfigurationChildrens(BoqItemEntity item, G_Object gaebObject, int parentId, List<BoqTextConfigurationEntity> boqTextConfigList)
		{
			var boqTextConfigLogic = new BoqTextConfigurationLogic();
			var entity = boqTextConfigLogic.Create();
			entity.BoqTextConfigurationFk = parentId > 0 ? (int?)parentId : null;
			foreach (var TextConfigDetail in gaebObject.gNodes)
			{
				SetTextConfigurationDetail(entity, TextConfigDetail);

				if (!string.IsNullOrEmpty(TextConfigDetail.Schema) && TextConfigDetail.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(TextConfigDetail.gName) && TextConfigDetail.gName.Equals(BoqConstants.RibGaebExtBoqTextConfigGroup))
				{
					SetTextConfigurationChildrens(item, TextConfigDetail, entity.Id, boqTextConfigList);
				}
			}
			boqTextConfigList.Add(entity);
		}

		private bool ContainsTextComplements(GX_CompleteText completeText)
		{
			if ((completeText.ComplTSA_isEmpty() && completeText.ComplTSB_isEmpty()) || completeText.DetailTxt.gNodes == null)
			{
				return false;
			}
			else
			{
				return true;
			}
		}

		private string GetSurcharge4ItemTextOutlineText(GX_Description gxDescription)
		{
			string newVal = "";
			var gxCompleteText = gxDescription.CompleteText;
			if (gxCompleteText != null)
			{
				var gxOutlineText = gxCompleteText.OutlineText;
				if (gxOutlineText != null)
				{
					var gxOutlTxt = gxOutlineText.OutlTxt;
					if (gxOutlTxt != null)
					{
						var gxTextOutlTxt = gxOutlTxt.TextOutlTxt;
						if (gxTextOutlTxt != null)
						{
							var gxSpan = gxTextOutlTxt.span;
							newVal = gxSpan != null ? gxSpan.gValue : "";
						}
					}
				}
			}
			return newVal;
		}

		private void SetItemBasics(BoqItemEntity item, GX_Description gxDescription, BoqItemEntity parent, BoqItemEntity predecessor, int lineTypeFk, bool isSurcharge4Item = false)
		{
			void SetTextComplement(GX_TextComplement gxTextComplement, ref String plainSpec)
			{
				if (gxTextComplement == null)
				{
					return;
				}

				BoqTextComplementEntity tcItem = null;

				if (IsBidderFile)
				{
					if (gxTextComplement.att_Kind.Equals("Owner"))
						return;

					int tcNr = System.Convert.ToInt32(gxTextComplement.att_MarkLbl);
					tcItem = item.BoqTextComplementEntities.FirstOrDefault(e => e.Sorting == tcNr) ?? new BoqTextComplementEntity { Version = 0 };

				}
				else
					tcItem = new BoqTextComplementEntity { Version = 0 };

				if (gxTextComplement.att_Kind.Equals("Owner"))
					tcItem.ComplType = (int)BoqConstants.EBoqTextComplementType.Owner;
				else
					tcItem.ComplType = (int)BoqConstants.EBoqTextComplementType.Bidder;

				tcItem.Sorting = System.Convert.ToInt32(gxTextComplement.att_MarkLbl);

				GX_MLText gxComplBody = gxTextComplement.ComplBody;
				if (gxComplBody != null)
				{
					tcItem.ComplBody = TruncateString4000(gxComplBody.gText.Trim('\''), ref _truncatedCounter);   // remove single-quotes
				}

				if (!IsBidderFile || tcItem.Version == 0)
				{
					GX_MLText gxComplCaption = gxTextComplement.ComplCaption;
					if (gxComplCaption != null)
					{
						tcItem.ComplCaption = TruncateString4000(gxComplCaption.gText, ref _truncatedCounter);
					}

					GX_MLText gyComplTail = gxTextComplement.ComplTail;
					if (gyComplTail != null)
					{
						tcItem.ComplTail = TruncateString4000(gyComplTail.gText, ref _truncatedCounter);
					}
				}

				// New tc's for bidder files are NOT supported!
				if (tcItem.Version == 0 && !IsBidderFile)
					item.BoqTextComplementEntities.Add(tcItem);

				if (IsBidderFile && tcItem.Version != 0)
					_textComplementItems.Add(tcItem);

				// remove TA/Bxx prefix added by GAEB dll only on plain text
				plainSpec = plainSpec.Replace(String.Format("[T{0}{1:00}{2}[",
					tcItem.ComplType == (int)BoqConstants.EBoqTextComplementType.Owner ? "A" : "B", tcItem.Sorting, tcItem.ComplCaption), "[" + tcItem.ComplCaption + "[");
			}

			bool newItem = item.Version == 0;

			// todo: should be done inside the calling method
			if (!IsBidderFile && !_revisedLogicIsUsed)
			{
				item.BoqItemParent = parent;
				if (item.BoqItemParent != null)
					item.BoqItemParent.AddChild(item);

				item.Reference = String.Empty;

				item.BoqItemBasisParent = predecessor;

				item.BoqHeaderFk = parent.BoqHeaderFk;
				item.BasItemTypeFk = 0;
				//item.BasItemType2Fk = 0; // ...add 0 to database

				item.BoqLineTypeFk = lineTypeFk;
			}

			if (gxDescription != null)
			{
				if (newItem)
				{
					string newVal = "";
					if (isSurcharge4Item)
						newVal = GetSurcharge4ItemTextOutlineText(gxDescription);
					else
						newVal = StringExtension.Truncate(gxDescription.gTextOutlineText, 2000);

					if (newVal != item.BriefInfo.Description)
					{
						item.BriefInfo.Description = newVal;
					}

					SetStlNo(item, gxDescription);
				}

				var gxCompleteText = gxDescription.CompleteText;
				if (gxCompleteText != null)
				{
					var gxBoqText = gxCompleteText.DetailTxt;
					// string plainSpec = gxBoQText.gText; --> gText could be null!
					if (gxBoqText != null)
					{
						string plainSpec = "";
						if (isSurcharge4Item)
							plainSpec = System.Net.WebUtility.HtmlEncode(gxBoqText.gValueInTree);
						else
						{
							try
							{
								plainSpec = System.Net.WebUtility.HtmlEncode(gxBoqText.gText);
							}
							catch (Exception) {}
						}

						// text complement
						if (!_revisedLogicIsUsed)   // --> done in revised logic!
						{
							if (gxBoqText.gNodes!=null && (ContainsTextComplements(gxCompleteText) || IsBidderFile))
							{
								foreach (GX_TextComplement gxBoqTextComplement in gxBoqText.gNodes.Where(n => n is GX_TextComplement))
								{
									SetTextComplement(gxBoqTextComplement, ref plainSpec);
								}
								foreach (GX_FTextTC gxText in gxBoqText.gNodes.Where(n => n is GX_FTextTC))
								{
									foreach (GX_TextComplement gxTextComplement in gxText.gNodes.Where(n => n is GX_TextComplement))
									{
										SetTextComplement(gxTextComplement, ref plainSpec);
									}
									foreach (GX_pTC gxSubText in gxText.gNodes.Where(n => n is GX_pTC))
									{
										foreach (GX_TextComplement gxSubTextComplement in gxSubText.gNodes.Where(n => n is GX_TextComplement))
										{
											SetTextComplement(gxSubTextComplement, ref plainSpec);
										}
									}
								}

								// paste text complements in specification
								if (IsBidderFile)
								{
									SetSpecificationTextComplements(item);
								}
							}

							// specification
							if (newItem || CanUpdateBoq)
							{
								SetSpecification(item, plainSpec, gxBoqText.gXml);
							}
						}
					}
				}

				// WICNo
				if (!gxDescription.WICNo_isEmpty() && !IsBidderFile)
				{
					SetWicNo(item, gxDescription.WICNo);
				}
			}
			else if (!IsBidderFile && !_revisedLogicIsUsed)
			{
				item.BriefInfo.Description = String.Empty;	// ?? Strange ...
			}
		}

		/// <summary>
		/// boq item wicno
		/// </summary>
		private void SetWicNo(BoqItemEntity item, string strWicNo)
		{
			var boqWicLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IWicBoqLogic>();

			var wicNoArray = strWicNo.Split('/');
			if (wicNoArray.Length == 3)
			{
				Dictionary<string, List<BoqItemEntity>> boqItemsDic = new Dictionary<string, List<BoqItemEntity>>();
				List<BoqItemEntity> boqItemList = new List<BoqItemEntity>();
				List<Dictionary<string, List<BoqItemEntity>>> wicBoQList = new List<Dictionary<string, List<BoqItemEntity>>>();
				// get the wic data
				var wicCatGroup = boqWicLogic.GetWicGroupByCode(wicNoArray[0]);
				if (wicCatGroup != null)
				{
					var wicBoQItems = boqWicLogic.GetBoqRootItems(wicCatGroup.Id);
					if (wicBoQItems != null)
					{
						if (!boqItemsDic.ContainsKey(wicNoArray[1]))
						{
							foreach (var wicBoQItem in wicBoQItems)
							{
								if (wicBoQItem.Reference == wicNoArray[1])
								{
									var boqItems = new BoqItemLogic().GetBoqItemsByHeaderId(wicBoQItem.BoqHeaderFk);
									boqItemList.AddRange(boqItems);
									boqItemsDic.Add(wicNoArray[1], boqItemList);
									if (!_wicBoQDic.ContainsKey(wicNoArray[0]))
									{
										wicBoQList.Add(boqItemsDic);
										_wicBoQDic.Add(wicNoArray[0], wicBoQList);
									}
									else
									{
										_wicBoQDic[wicNoArray[0]].Add(boqItemsDic);
									}
									break;
								}
							}
						}
					}
				}


				if (_wicBoQDic.ContainsKey(wicNoArray[0]))
				{
					foreach (var wicBoQs in _wicBoQDic[wicNoArray[0]])
					{
						foreach (var boqItems in wicBoQs.Values)
						{
							foreach (var boqItem in boqItems)
							{
								if (boqItem.Reference.Replace(" ", "") == wicNoArray[2].Replace(" ", ""))
								{
									item.BoqItemWicBoqFk = boqItem.BoqHeaderFk;
									item.BoqItemWicItemFk = boqItem.Id;
									break;
								}
							}
						}
					}
				}
			}
		}


		/// <summary>
		/// boq item specification
		/// </summary>
		private void SetSpecification(BoqItemEntity boqItem, String specificationPlain, String specificationXml)
		{
			byte[] newContent;

			if (_forcePlainText || String.IsNullOrEmpty(specificationXml))
			{
				specificationXml = String.Format("<p>{0}</p>", Regex.Replace(specificationPlain, @"\n {2,}\n", "\n\n")
					                                                 .Replace("\r\n\r\n\r\n", "</p><br><br><p>").Replace("\r\n\r\n", "</p><br><p>").Replace("\r\n","</p><p>")
					                                                 .Replace("\n\n\n",       "</p><br><br><p>").Replace("\n\n",     "</p><br><p>").Replace("\n",  "</p><p>"));
			}

			if (!String.IsNullOrEmpty(specificationXml) && !String.IsNullOrEmpty(specificationPlain))
			{
				_specsTotal++;

				if (boqItem.BasBlobsSpecificationFk.HasValue)
				{
					boqItem.SpecificationBlob = BlobLogic.GetItemByKey(boqItem.BasBlobsSpecificationFk.Value);
				}
				else
				{
					boqItem.SpecificationBlob = new BlobEntity();
					_blobsAdded++;
				}

				if (!_forcePlainText)
				{
					if (new[]{"d","D"}.Contains(_gaebFileVersion))
					{
						specificationXml = SetDefaultFontForGaeb90(specificationXml);
					}

					specificationXml = BoqHelper.ImageGaebXml2Html(specificationXml, TransLogic.Context.CultureInfo.NumberFormat.NumberDecimalSeparator);

					specificationXml = specificationXml.Replace("Color:", "color:");
				}

				newContent = new UTF8Encoding().GetBytes(specificationXml);
				if (boqItem.SpecificationBlob.Version==0 || boqItem.SpecificationBlob.Content==null || boqItem.SpecificationBlob.Content.Length!=newContent.Length) // todo: compare content not only length
				{
					boqItem.SpecificationBlob.Content = newContent;
					Add2ModifiedBlobs(boqItem.SpecificationBlob);

					if (boqItem.SpecificationBlob.Version != 0)
					{
						_specsUpdated++;
					}
				}
			}
		}

		class Gaeb90Default
		{
			internal string Font { get; set; }
			internal int FontSize { get; set; }
		}
		Gaeb90Default _gaeb90Default;

		private string SetDefaultFontForGaeb90(string specificationXml)
		{
			if (_gaeb90Default == null)
			{
				Dictionary<string,object> defaultUserSettings = new UserSettingsLogic().GetAllSettings();
				_gaeb90Default = new Gaeb90Default
				{
					Font		= (defaultUserSettings["wysiwygEditorSettings"] as WysiwygEditorDtoCollection).System.DefaultFont,
					FontSize	= (defaultUserSettings["wysiwygEditorSettings"] as WysiwygEditorDtoCollection).System.DefaultFontSize,
				};
			}

			string defaultFontSize = _gaeb90Default.FontSize.ToString();
			string htmlTag = String.Format("<p style=\"text-align: left; margin-top: 0pt; margin-bottom: 0pt; font-size: {0}; font-family:{1};\">", defaultFontSize, _gaeb90Default.Font);
			specificationXml = specificationXml.Replace("<p style=\"text-align:left;margin-top:0pt;margin-bottom:0pt;\">", htmlTag);

			return specificationXml;
		}

		/// <summary>
		/// replace empty textcomplements placeholder with just imported text complements (body part)
		/// </summary>
		/// <param name="item"></param>
		private void SetSpecificationTextComplements(BoqItemEntity item)
		{
			var specification = string.Empty;

			if (item != null && item.BoqTextComplementEntities != null && item.BoqTextComplementEntities.Any())
			{
				BlobEntity specificationBlob = null;

				if (item.BasBlobsSpecificationFk.HasValue)
				{
					specificationBlob = new BlobLogic().GetItemByKey((int)item.BasBlobsSpecificationFk);
					if (specificationBlob != null && specificationBlob.Content != null)
						specification = new UTF8Encoding().GetString(specificationBlob.Content);
				}

				if (!string.IsNullOrEmpty(specification))
				{
					if (specificationBlob != null)
					{
						// paste textcomplements to spec
						var newContent = new BoqTextComplementLogic().AdjustBlobBody2Table(specification, item.BoqTextComplementEntities.ToList());
						specificationBlob.Content = new UTF8Encoding().GetBytes(newContent);
						if (!_revisedLogicIsUsed)
						{
							// _modifiedBlobs.Add(specificationBlob);
							Add2ModifiedBlobs(specificationBlob);
						}
					}
				}
			}
		}

		/// <summary>
		/// Set division, GX_BoQCtgy
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxBoqCtgy"></param>
		/// <param name="parent"></param>
		/// <param name="warnings"></param>
		private void SetDivision(BoqItemEntity item, GX_BoQCtgy gxBoqCtgy, BoqItemEntity parent, ref List<ImportWarning> warnings)
		{
			// DivisionCount++;	--> moved to recursive case loop
			bool isNewitem = item.Version == 0;

			// Text, Parent, Item-Type
			SetItemBasics(item, null, parent, null, parent.BoqItemParent == null ? 1 : parent.BoqLineTypeFk + 1);

			if (!IsBidderFile && !_revisedLogicIsUsed)
			{
				//Ref.-no.
				SetItemReferenceNo(item, parent, gxBoqCtgy.att_RNoPart);
				//Ref.-no2.
				SetItemReferenceNo2(item, gxBoqCtgy);
			}

			// Outline Spec
			if (gxBoqCtgy.LblTx != null)
			{
				SetBriefInfo(item, gxBoqCtgy.LblTx.gValueInTree);
			}


			// division flags
			if (!gxBoqCtgy.NotApplBoQ_isEmpty() && gxBoqCtgy.NotApplBoQ.Equals("Yes"))
			{
				item.IsNotApplicable = true;
			}

			SetBasItemType2Fk(item, gxBoqCtgy.ALNBSerNo_isEmpty(),gxBoqCtgy.ALNBSerNo, gxBoqCtgy.ALNBGroupNo, gxBoqCtgy.Accepted_isEmpty(), gxBoqCtgy.Accepted, ref warnings);

			//DivisionType (not GAEB)
			foreach (var gaebObject in gxBoqCtgy.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtDivisionType))
				{
					var divisionTypeId = GetDivisionTypeFk(gaebObject.gValue);
					if (divisionTypeId >= 0)
						item.BoqDivisionTypeFk = divisionTypeId;

					break;
				}
			}
		}

		/// <summary>
		/// Set item, GX_Item
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxItem"></param>
		/// <param name="parent"></param>
		/// <param name="warnings"></param>
		private void SetItem(BoqItemEntity item, GX_Item gxItem, BoqItemEntity parent, ref List<ImportWarning> warnings)
		{
			// ItemCount++;	--> moved to recursive case loop
			bool isNewItem = item.Id == 0;

			//ref map
			//if (!IsBidderFile)
			//	_itemRefMap.Add(gxItem.att_ID, item);

			// Text, Textcomplements, Parent, Item-Type
			SetItemBasics(item, gxItem.Description, parent, null, (int)BoqConstants.EBoqLineType.Position);

			//Ref.-no.
			if (!IsBidderFile && !_revisedLogicIsUsed)
			{
				SetItemReferenceNo(item, parent, gxItem.att_RNoPart, gxItem.att_RNoIndex);
				//Ref.-no2.
				SetItemReferenceNo2(item, gxItem);
			}

			// quantity WQ
			// if (!gxItem.Qty_isEmpty() && (!IsBidderFile || (IsBidderFile && item.IsFreeQuantity)))
			if (isNewItem || (IsBidderFile && item.IsFreeQuantity))
			{
				SetValueIfNotEmpty(item, gxItem.Qty_isEmpty(), e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxItem.Qty));

				var gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtQtyDetail)).FirstOrDefault();

				if (gxSubItem != null)
                {
                    item.QuantityDetail = gxSubItem.gValue;
                }

				gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtFactorDetail)).FirstOrDefault();

				if (gxSubItem != null)
                {
                    item.FactorDetail = gxSubItem.gValue;
                }

				gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtFactor)).FirstOrDefault();
				if (gxSubItem != null)
                {
                    var numberDecimalSep = TransLogic.Context.CultureInfo.NumberFormat.NumberDecimalSeparator;
                    var tempValue =  numberDecimalSep == "," ? gxSubItem.gValue.Replace(".", numberDecimalSep) : gxSubItem.gValue.Replace(",", numberDecimalSep);
                    item.Factor = Decimal.Parse(tempValue);
                }
			}


			if (_byPackage)
			{
				SetPrcStructureFkByPackage(item, gxItem, ref warnings);
			}

			// quantity AQ
			// if (!gxItem.PredQty_isEmpty() && !IsBidderFile)
			if (isNewItem)
			{
				if (gxItem.PredQty == 0 && new SystemOptionLogic().GaebImportTakeoverQuantityToQuantityAdj())
				{
					gxItem.PredQty = gxItem.Qty;
				}
				SetValueIfNotEmpty(item, gxItem.PredQty_isEmpty(), e => e.QuantityAdj, item.QuantityAdj, System.Convert.ToDecimal(gxItem.PredQty));
				SetQuantityAdjDetail(gxItem, item);
			}

			// hours
			//if (!gxItem.TimeQu_isEmpty())
			//{
			//	// item.HoursUnit = System.Convert.ToDecimal(gxItem.TimeQu);
			//	item.SetValue(e => e.HoursUnit, item.HoursUnit, System.Convert.ToDecimal(gxItem.TimeQu));
			//}
			SetValueIfNotEmpty(item, gxItem.TimeQu_isEmpty(), e => e.HoursUnit, item.HoursUnit, System.Convert.ToDecimal(gxItem.TimeQu));

			if (!IsGaeb84)
			{
				bool isUom4Lumpsum = IsLumpsumUnit(gxItem.QU);
				if (isUom4Lumpsum || IsLumpsumItem(gxItem))  //unit of the imported item is marked as lumpsum or imported item is marked as lumpsum
				{
					// treat imported item as lumpsum (even if it's not marked as lumpsum item!)
					item.SetValue(e => e.IsLumpsum, item.IsLumpsum, true);
					if (IsLumpsumItem(gxItem)) // take IT as price
					{
						SetPriceItIfNotEmpty(item, gxItem);
						if (System.Convert.ToDecimal(gxItem.Qty) != 1)
						{
							_invalideLumpsums++;
						}
						item.SetValue(e => e.Quantity, item.Quantity, 1.0M);  // set qty to 1 (instructed by savic Aug. 6, 2019)
					}
					else // take UP as price
					{
						SetPriceIfNotEmpty(item, gxItem);
						if (System.Convert.ToDecimal(gxItem.Qty) != 1)
						{
							_invalideLumpsums++;
						}
						item.SetValue(e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxItem.Qty));
					}
				}
				else
				{
					SetPriceIfNotEmpty(item, gxItem);
				}
			}
			else
			{
				if (item.IsLumpsum)
				{
					SetPriceItIfNotEmpty(item, gxItem);
					if (System.Convert.ToDecimal(gxItem.Qty) != 1)
					{
						_invalideLumpsums++;
					}
					item.SetValue(e => e.Quantity, item.Quantity, 1.0M);  // set qty to 1 (instructed by savic Aug. 6, 2019)
				}
				else
				{
					SetPriceIfNotEmpty(item, gxItem);
				}
			}

			// discount %
			SetValueIfNotEmpty(item, gxItem.DiscountPcnt_isEmpty(), e => e.DiscountPercent, item.DiscountPercent, System.Convert.ToDecimal(gxItem.DiscountPcnt));

			// handling of "Nebenangebot"
			if (!gxItem.AlterBidStatus_isEmpty())
			{
				switch (gxItem.AlterBidStatus.ToLower())
				{
					case "new":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.NewlyOffered);
						break;

					case "modified":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.ModifiedOffered);
						break;

					case "identical":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.IdenticalOffered);
						break;

					default:

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.NotRequired);
						break;
				}

			}

			// ur breakdown
			SetValueIfNotEmpty(item, gxItem.UPBkdn_isEmpty(), e => e.IsUrb, item.IsUrb, true);
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp1_isEmpty(), e => e.Urb1, item.Urb1, System.Convert.ToDecimal(gxItem.UPComp1));
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp2_isEmpty(), e => e.Urb2, item.Urb2, System.Convert.ToDecimal(gxItem.UPComp2));
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp3_isEmpty(), e => e.Urb3, item.Urb3, System.Convert.ToDecimal(gxItem.UPComp3));
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp4_isEmpty(), e => e.Urb4, item.Urb4, System.Convert.ToDecimal(gxItem.UPComp4));
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp5_isEmpty(), e => e.Urb5, item.Urb5, System.Convert.ToDecimal(gxItem.UPComp5));
			SetMonetaryValueIfNotEmpty(item, gxItem.UPComp6_isEmpty(), e => e.Urb6, item.Urb6, System.Convert.ToDecimal(gxItem.UPComp6));

			// Bidder comment
			SetCommentContractor(item, gxItem);

			//Change order
			if (!gxItem.CONo_isEmpty())
				SetChangeOrder(item, gxItem, System.Convert.ToUInt32(gxItem.CONo), gxItem.COStatus_isEmpty() ? string.Empty : gxItem.COStatus);

			//if (!IsBidderFile)
			if (isNewItem)
			{
				// up from/to
				SetMonetaryValueIfNotEmpty(item, gxItem.UPFrom_isEmpty(), e => e.UnitRateFrom, item.UnitRateFrom, System.Convert.ToDecimal(gxItem.UPFrom));
				SetMonetaryValueIfNotEmpty(item, gxItem.UPTo_isEmpty(), e => e.UnitRateTo, item.UnitRateTo, System.Convert.ToDecimal(gxItem.UPTo));

				SetCommentClient(item, gxItem);

				// item flags
				if (!gxItem.NotAppl_isEmpty() && gxItem.NotAppl.Equals("Yes"))
				{
					item.IsNotApplicable = true;
				}
				if (!gxItem.KeyIt_isEmpty() && gxItem.KeyIt.Equals("Yes"))
				{
					item.IsKeyitem = true;
				}
				//if (!gxItem.LumpSumItem_isEmpty() && gxItem.LumpSumItem.Equals("Yes"))	--> see above
				//{
				//	item.IsLumpsum = true;
				//}
				if (!gxItem.SumDescr_isEmpty() && gxItem.SumDescr.Equals("Yes"))
				{
					item.IsLeadDescription = true;
				}
				if (!gxItem.QtyTBD_isEmpty() && gxItem.QtyTBD.Equals("Yes"))
				{
					item.IsFreeQuantity = true;
				}
				if (!gxItem.MarkupIt_isEmpty() && gxItem.MarkupIt.Equals("Yes"))
				{
					item.IsSurcharged = true;
				}
				if (!gxItem.HourIt_isEmpty() && gxItem.HourIt.Equals("Yes"))
				{
					item.IsDaywork = true;
				}

				SetBasItemTypeFk(item, gxItem.Provis);
				SetPriceRequest(item, gxItem);
				SetBasItemType2Fk(item, gxItem.ALNSerNo_isEmpty(), gxItem.ALNSerNo, gxItem.ALNGroupNo, gxItem.Accepted_isEmpty(), gxItem.Accepted, ref warnings);

				// uom
				SetUoM(item, gxItem.QU, ref warnings);

				//Bezug-/Wiederholungsbeschreibung ??
				//public bool RefDescr_isEmpty(); public string RefDescr { get; set; }   <RefDescr>Ref</RefDescr> <RefDescr>Rep</RefDescr>

				// ref to design description / item
				//SetReferenceDd(item, gxItem);
				SetReferenceDd(item, gxItem.RefPerfNo, gxItem.RefRNo);

				//BoqItemFlag (not GAEB)
				foreach (var gaebObject in gxItem.gNodes)
				{
					if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqItemFlag))
					{
						var boqItemFlagId = GetBoqItemFlagFk(gaebObject.gValue);
						if (boqItemFlagId >= 0)
							item.BoqItemFlagFk = boqItemFlagId;

						break;
					}
				}
			}

			//Included (not GAEB)
			var gInclude = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebIncluded));
			if (gInclude != null)
			{
				item.Included = gInclude.gValue == "Yes" ? true : false;
			}

			//NotSubmitted (not GAEB)
			var gNotSubmitted = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebNotSubmitted));
			if (gNotSubmitted != null && item.Price == 0)
			{
				item.NotSubmitted = gNotSubmitted.gValue == "Yes" ? true : false;
			}

			SetBoqBudget(gxItem.gNodes, item);

			//ItemStatus (not GAEB)
			var gItemStatus = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtItemStatus));
			if (gItemStatus != null)
			{
				item.BasItemStatusFk = GetItemStatusFk(gItemStatus.gValue);
			}
		}

		private void SetValueIfNotEmpty<T>(BoqItemEntity item, bool isEmpty, Expression<Func<BoqItemEntity, T>> expression, T oldValue, T newValue) where T : System.IComparable<T>
		{
			if (!isEmpty)
			{
				item.SetValue(expression, oldValue, newValue);
			}
		}

		private void SetMonetaryValueIfNotEmpty<T>(BoqItemEntity item, bool isEmpty, Expression<Func<BoqItemEntity, T>> expression, T oldValue, T newValue) where T : System.IComparable<T>
		{
			if (!isEmpty)
			{
				item.SetMonetaryValue(expression, oldValue, newValue);
			}
		}

		private void SetPriceIfNotEmpty(BoqItemEntity boqItem, GX_Item gxItem)
		{
			decimal price = System.Convert.ToDecimal(gxItem.UP);
			SetMonetaryValueIfNotEmpty(boqItem, gxItem.UP_isEmpty(), bi=>bi.Price, boqItem.Price, price);
			if (price != 0)
			{
				boqItem.NotSubmitted = false;
			}
		}

		private void SetPriceItIfNotEmpty(BoqItemEntity boqItem, GX_Item gxItem)
		{
			decimal price = System.Convert.ToDecimal(gxItem.IT);
			SetMonetaryValueIfNotEmpty(boqItem, gxItem.IT_isEmpty(), bi => bi.Price, boqItem.Price, price);
			if (price != 0)
			{
				boqItem.NotSubmitted = false;
			}
		}

		/// <summary>
		/// Set surcharge item, GX_MarkupItem
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxMarkupItem"></param>
		/// <param name="parent"></param>
		/// <param name="warnings"></param>
		/// <param name="isSurcharge4Item"></param>
		private void SetSurchargeItem(BoqItemEntity item, GX_MarkupItem gxMarkupItem, BoqItemEntity parent, ref List<ImportWarning> warnings,  bool isSurcharge4Item = false)
		{
			if (!IsBidderFile)
			{
				//_itemRefMap.Add(gxMarkupItem.att_ID, item);
				Add2ReferenceMap(gxMarkupItem.att_ID, item);
				int surchargeType;

				if ("IdentAsMark".Equals(gxMarkupItem.MarkupType))
					surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType1;
				else if ("AllInCat".Equals(gxMarkupItem.MarkupType))
					surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType2;
				else if ("ListInSubQty".Equals(gxMarkupItem.MarkupType))
					surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType3;
				else if ("ListInEstRule".Equals(gxMarkupItem.MarkupType))
					surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType4;
				else //error
					surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType1;

				// Text, Parent, Item-Type
				SetItemBasics(item, gxMarkupItem.Description, parent, null, surchargeType, isSurcharge4Item);

				//Ref.-no.
				SetItemReferenceNo(item, parent, gxMarkupItem.att_RNoPart, gxMarkupItem.att_RNoIndex);
				//Ref.-no2.
				SetItemReferenceNo2(item, gxMarkupItem);
			}

			// markup %
			SetMonetaryValueIfNotEmpty(item, gxMarkupItem.Markup_isEmpty(), e => e.Price, item.Price, System.Convert.ToDecimal(gxMarkupItem.Markup));
			// total
			SetMonetaryValueIfNotEmpty(item, gxMarkupItem.IT_isEmpty(), e => e.Finalprice, item.Finalprice, System.Convert.ToDecimal(gxMarkupItem.IT));

			// ITMarkup sum of to be surcharged
			if (!gxMarkupItem.ITMarkup_isEmpty())
				item.Quantity = System.Convert.ToDecimal(gxMarkupItem.ITMarkup);

			// discount %
			if (!gxMarkupItem.DiscountPcnt_isEmpty())
				item.DiscountPercent = System.Convert.ToDecimal(gxMarkupItem.DiscountPcnt);

			if (!IsBidderFile)
			{
				// item flags
				if (!gxMarkupItem.NotAppl_isEmpty() && gxMarkupItem.NotAppl.Equals("Yes"))
					item.IsNotApplicable = true;
				if (!gxMarkupItem.KeyIt_isEmpty() && gxMarkupItem.KeyIt.Equals("Yes"))
					item.IsKeyitem = true;
				if (!gxMarkupItem.HourIt_isEmpty() && gxMarkupItem.HourIt.Equals("Yes"))
					item.IsDaywork = true;

				SetBasItemTypeFk( item, gxMarkupItem.Provis);
				SetBasItemType2Fk(item, gxMarkupItem.ALNSerNo_isEmpty(), gxMarkupItem.ALNSerNo, gxMarkupItem.ALNGroupNo, gxMarkupItem.Accepted_isEmpty(), gxMarkupItem.Accepted, ref warnings);

				// ref to design description / item
				//SetReferenceDd(item, gxMarkupItem);
				SetReferenceDd(item, gxMarkupItem.RefPerfNo, gxMarkupItem.RefRNo);

				// surcharge type 4, assign rules
				#region assign rules, parameter and division type to surcharge 4
				if ("ListInEstRule".Equals(gxMarkupItem.MarkupType))
				{
					foreach (var surcharge4Object in gxMarkupItem.gNodes)
					{

						if (!string.IsNullOrEmpty(surcharge4Object.Schema) && surcharge4Object.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(surcharge4Object.gName) && surcharge4Object.gName.Equals(BoqConstants.RibGaebExtRules))
							{
#region rules
							// get the project rule first time
							//get project rules
							var estPrjRuleLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateRulePrjEstRuleLogic>();
							var estRuleLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateRuleLogic>();
							var prjBoqLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectBoqLogic>();
							if (_prjRules == null && _estRules == null)
							{
								var prjBoqItem = prjBoqLogic.GetPrjBoqItem((int?)item.BoqHeaderFk);
								_prjFk = prjBoqItem != null ? prjBoqItem.PrjProjectFk : 0;
								_prjRules = estPrjRuleLogic.GetListByProjectId(_prjFk).ToList();
								_prjRules = _prjRules.Where(e => e.IsForBoq).ToList();
								_estRules = estRuleLogic.GetEstRules().ToList();
								_estRules = _estRules.Where(e => e.IsForBoq).ToList();

								// Estimate Evaluationsequence Entities
								var estEvaluationsequenceLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstEvaluationsequenceLogic>();
								_estEvaluationsequenceEntity = estEvaluationsequenceLogic.GetItems().ToList();
							}

							foreach (var ruleObject in surcharge4Object.gNodes)
							{
								if (!string.IsNullOrEmpty(ruleObject.Schema) && ruleObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(ruleObject.gName) && ruleObject.gName.Equals(BoqConstants.RibGaebExtRule))
								{
									bool IsMapRule = false;
									int ruleId = 0;
									Boq2estRuleEntity ruleItem = null;
									WicBoq2mdcRuleEntity wicBoqRuleItem = null;
									if (ruleObject.gNodes != null)
									{
										foreach (var ruleDetailObject in ruleObject.gNodes)
										{
											if ((_prjRules.Count > 0 || _estRules.Count > 0) && !string.IsNullOrEmpty(ruleDetailObject.Schema) && ruleDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) &&
												!string.IsNullOrEmpty(ruleDetailObject.gName) && ruleDetailObject.gName.Equals(BoqConstants.RibGaebExtRuleCode))
											{
												var mapPrjRule = _prjRules.Where(e => e.Code == ruleDetailObject.gValue).FirstOrDefault();
												if (mapPrjRule != null && mapPrjRule.Id > 0)
												{
													IsMapRule = true;
													ruleId = mapPrjRule.Id;
													ruleItem = new ProjectBoq2EstRuleLogic().CreateEntity(0, null);
													ruleItem.BoqHeaderFk = item.BoqHeaderFk;
													ruleItem.BoqItemFk = item.Id;
													ruleItem.PrjEstRuleFk = ruleId;
												}
												else
												{
													var mapEstRules = _estRules.Where(e => e.Code == ruleDetailObject.gValue);
													var mapEstRuleIds = mapEstRules.Select(e => e.Id);
													if(mapEstRuleIds != null && mapEstRuleIds.Any())
													{
														if (_prjFk > 0)
														{
															var ruleIdDic = estPrjRuleLogic.CopyMasterRuleToProjectRule(mapEstRuleIds, _prjFk);
															if (ruleIdDic != null && ruleIdDic.Count > 0)
															{
																var mapEstRuleId = ruleIdDic.FirstOrDefault().Value;
																IsMapRule = true;
																ruleId = mapEstRuleId;
																ruleItem = new ProjectBoq2EstRuleLogic().CreateEntity(0, null);
																ruleItem.BoqHeaderFk = item.BoqHeaderFk;
																ruleItem.BoqItemFk = item.Id;
																ruleItem.PrjEstRuleFk = ruleId;
															}
														}
														else
														{
															foreach (var mapEstRuleId in mapEstRuleIds)
															{
																wicBoqRuleItem = new WicBoq2MdcRuleLogic().Create();
																wicBoqRuleItem.EstRuleFk = mapEstRuleId;
																wicBoqRuleItem.BoqHeaderFk = item.BoqHeaderFk;
																wicBoqRuleItem.BoqItemFk = item.Id;
															}

														}
													}
												}
											}

											if (IsMapRule && ruleItem != null)
											{
												if (!string.IsNullOrEmpty(ruleDetailObject.Schema) && ruleDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(ruleDetailObject.gName) && ruleDetailObject.gName.Equals(BoqConstants.RibGaebExtEstEvaluationSequence))
												{
													var mapEstEvalItem = _estEvaluationsequenceEntity.Where(e => e.DescriptionInfo.Description == ruleDetailObject.gValue).FirstOrDefault();
													if (_prjFk > 0)
														ruleItem.EstEvaluationSequenceFk = mapEstEvalItem != null ? (int?)mapEstEvalItem.Id : null;
													else
														wicBoqRuleItem.EstEvaluationSequenceFk = mapEstEvalItem != null ? (int?)mapEstEvalItem.Id : null;
												}
												else if (!string.IsNullOrEmpty(ruleDetailObject.Schema) && ruleDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(ruleDetailObject.gName) && ruleDetailObject.gName.Equals(BoqConstants.RibGaebExtIsExecution))
												{
													if (_prjFk > 0)
														ruleItem.IsExecution = ruleDetailObject.gValue == "1" ? true : false;
													else
														wicBoqRuleItem.IsExecution = ruleDetailObject.gValue == "1" ? true : false;
												}
											}
										}
									}

									if (ruleItem != null)
										item.Boq2estRuleEntities.Add(ruleItem);
									if (wicBoqRuleItem != null)
										item.WicBoq2mdcRuleEntities.Add(wicBoqRuleItem);
								}
							}
							#endregion
						}
						else if (!string.IsNullOrEmpty(surcharge4Object.Schema) && surcharge4Object.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(surcharge4Object.gName) && surcharge4Object.gName.Equals(BoqConstants.RibGaebExtParameters))
						{
							#region parameters
							var estRuleParamValueLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateRuleParameterValueLogic>();
							var prjBoqLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectBoqLogic>();
							var prjBoqEntity = prjBoqLogic.GetPrjBoqItem((int?)item.BoqHeaderFk);
							if (_estParameterGroups == null)
							{
								// parameter gruops
								var estParametergroupLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstParametergroupLogic>();
								_estParameterGroups = estParametergroupLogic.GetItems().ToList();
								// parameters
								var prjId = prjBoqEntity != null ? prjBoqEntity.PrjProjectFk : 0;
								_paramValues = estRuleParamValueLogic.GetByPrjId((int?)prjId).ToList();
								if (prjId > 0)
								{
									var estRulePrjParamLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateRulePrjEstRuleParamLogic>();
									_params2Lookup = estRulePrjParamLogic.GetLookupParametersByPrjId(prjId).ToList();

								}
								else
								{
									var estRuleParam4BoqLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateRuleParameter4BoqLogic>();
									_params2Lookup = estRuleParam4BoqLogic.GetLookupParameters().ToList();
								}
								// parameter units
								var getUnitLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetUnitLogic>();
								_units = getUnitLogic.IGetList().ToList();
							}

							if (surcharge4Object.gNodes != null)
							{
								foreach (var paramObject in surcharge4Object.gNodes)
								{
									if (!string.IsNullOrEmpty(paramObject.Schema) && paramObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramObject.gName) && paramObject.gName.Equals(BoqConstants.RibGaebExtParameter))
									{
										#region mapping parameter value with the param
										BoqItemParamEntity boqItemParame = new BoqItemParamEntity();
										string paramValueCode ="";
										bool isCanCreate = true;
										foreach (var paramDetailObject in paramObject.gNodes)
										{
											if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterCode))
												boqItemParame.Code = paramDetailObject.gValue;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValueType))
											{
												string strValueType = paramDetailObject.gValue;
												int typeId = -1;
												BoqCommonLogic.GetParameterType(ref typeId, ref strValueType);
												boqItemParame.ValueType = typeId;
											}
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterIsLookup))
												boqItemParame.IsLookup = paramDetailObject.gValue == "lookup" ? true : false;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValueCode))
												paramValueCode = paramDetailObject.gValue;
										}

										if (boqItemParame.IsLookup)
										{
											var existParamValue = _paramValues.Where(e => e.Code == boqItemParame.Code && e.DescriptionInfo.Description == paramValueCode).FirstOrDefault();
											var hasLookupItem = _params2Lookup.Where(e => e.Code == boqItemParame.Code && e.ValueType == boqItemParame.ValueType).FirstOrDefault();
											if (existParamValue == null && hasLookupItem == null)
											{
												_paramValueWarningMessage += String.Format(NLS.GaebImportWarning_NoMapping_ParamOrType, boqItemParame.Code, paramValueCode, item.Reference);
												isCanCreate = false;
												continue;
											}
										}
										#endregion

										// if no mapping code or type, don't create parameter
										if (!isCanCreate)
											continue;

										#region create parameter
										BoqItemParamEntity paramItem = new BoqItemParamLogic().CreateEntity(0, null);
										paramItem.BoqHeaderFk = item.BoqHeaderFk;
										paramItem.BoqItemFk = item.Id;
										foreach (var paramDetailObject in paramObject.gNodes)
										{
											if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterCode))
												paramItem.Code = paramDetailObject.gValue;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterDesc))
												paramItem.DescriptionInfo.Description = paramDetailObject.gValue;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterGroup))
											{
												var mapGroup = _estParameterGroups.Where(e => e.DescriptionInfo.Description == paramDetailObject.gValue).FirstOrDefault();
												if (mapGroup != null && mapGroup.Id > 0)
													paramItem.EstParameterGroupFk = mapGroup.Id;
											}
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterDetail))
												paramItem.ValueDetail = paramDetailObject.gValue;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterDetail))
												paramItem.ParameterValue = System.Convert.ToDecimal(paramDetailObject.gValue, CultureInfo.InvariantCulture);
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterUnit))
											{
												var mapUnit = _units.Where(e => e.UnitInfo.Description == paramDetailObject.gValue).FirstOrDefault();
												if (mapUnit != null && mapUnit.Id > 0)
													paramItem.UomFk = mapUnit.Id;
											}
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterDefaultValue))
												paramItem.DefaultValue = System.Convert.ToDecimal(paramDetailObject.gValue, CultureInfo.InvariantCulture);
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValueType))
											{
												string strValueType = paramDetailObject.gValue;
												int typeId = -1;
												BoqCommonLogic.GetParameterType(ref typeId, ref strValueType);
												paramItem.ValueType = typeId;
											}
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValue))
												paramItem.ParameterValue = System.Convert.ToDecimal(paramDetailObject.gValue, CultureInfo.InvariantCulture);
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterIsLookup))
												paramItem.IsLookup = paramDetailObject.gValue == "lookup" ? true : false;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValueCode))
											{
												if (paramItem.IsLookup)
												{
													var existParamValue = _paramValues.Where(e => e.Code == paramItem.Code && e.DescriptionInfo.Description == paramDetailObject.gValue).FirstOrDefault();
													if (existParamValue != null)
														paramItem.EstRuleParamValueFk = (int?)existParamValue.Id;
													else
													{
														var prjProjectFk = prjBoqEntity != null ? prjBoqEntity.PrjProjectFk : 0;
														var paramValueEntity = estRuleParamValueLogic.CreateItem(paramItem.Id, prjProjectFk, paramItem.Code, paramItem.ParameterValue, paramDetailObject.gValue, 1);
														if (paramValueEntity != null)
														{
															_paramValuesToSave.Add(paramValueEntity);
															paramItem.EstRuleParamValueFk = (int?)paramValueEntity.Id;
														}
													}
												}
											}
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterText))
												paramItem.ParameterText = paramDetailObject.gValue;
											else if (!string.IsNullOrEmpty(paramDetailObject.Schema) && paramDetailObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(paramDetailObject.gName) && paramDetailObject.gName.Equals(BoqConstants.RibGaebExtParameterValueText))
												paramItem.ValueText = paramDetailObject.gValue;
										}
										#endregion

										if (paramItem != null && !string.IsNullOrEmpty(paramItem.Code))
											item.BoqItemParamEntities.Add(paramItem);
									}
								}
							}
							#endregion
						}
						else if (!string.IsNullOrEmpty(surcharge4Object.Schema) && surcharge4Object.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(surcharge4Object.gName) && surcharge4Object.gName.Equals(BoqConstants.RibGaebExtBoqDivisionTypes))
						{
							#region division type
							if (_boqDivisionTypes == null)
							{
								//get boqDivisionType
								_boqDivisionTypes = new BasicsCustomizeDivisionTypeLogic().GetListByFilter(e => e.IsLive == true).ToList();
							}

							if (surcharge4Object.gNodes != null)
							{
								foreach (var divisionTypeObject in surcharge4Object.gNodes)
								{
									if (!string.IsNullOrEmpty(divisionTypeObject.Schema) && divisionTypeObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(divisionTypeObject.gName) && divisionTypeObject.gName.Equals(BoqConstants.RibGaebExtBoqDivisionType))
									{
										var mapDivisionType = _boqDivisionTypes.Where(e => e.Code == divisionTypeObject.gValue).FirstOrDefault();
										if (mapDivisionType != null && mapDivisionType.Id > 0)
										{
											BoqItem2boqDivisiontypeEntity divisionTypeItem = new BoqItem2BoqDivisionTypeLogic().CreateEntity(0, null);
											divisionTypeItem.BoqHeaderFk = item.BoqHeaderFk;
											divisionTypeItem.BoqItemFk = item.Id;
											divisionTypeItem.BoqDivisionTypeFk = mapDivisionType.Id;
											item.BoqItem2boqDivisiontypeEntities.Add(divisionTypeItem);
										}

									}
								}
							}
							#endregion
						}

					}
				}
				#endregion
			}
		}

		/// <summary>
		/// Set text element Item with text
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxAddText"></param>
		/// <param name="parent"></param>
		/// <param name="predecessor"></param>
		private void SetTextElement(BoqItemEntity item, GX_AddText gxAddText, BoqItemEntity parent, BoqItemEntity predecessor)
		{
			TextCount++;

			item.BoqItemBasisParent = predecessor;

			item.BoqItemParent = parent;
			if (item.BoqItemParent != null)
				item.BoqItemParent.AddChild(item);

			item.BoqHeaderFk = parent.BoqHeaderFk;
			item.BasItemTypeFk = 0;
			item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.TextElement;

			item.Reference = String.Empty;

			GX_MLText gaebOutlineAddText = gxAddText.OutlineAddText;
			GX_FText gaebDetailAddText = gxAddText.DetailAddText;

			if (gaebOutlineAddText != null)
			{
				SetBriefInfo(item, gaebOutlineAddText.gText);
			}

			String specificationXml = "";
			if (gaebDetailAddText != null)
				specificationXml = gaebDetailAddText.gXml;

			SetSpecification(item, System.Net.WebUtility.HtmlEncode(gxAddText.gTextCompleteText), specificationXml);
		}

		/// <summary>
		/// Set design description, GX_PerfDescr
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gaebPerfDescr"></param>
		/// <param name="parent"></param>
		/// <param name="predecessor"></param>
		private void SetDesignDescription(BoqItemEntity item, GX_PerfDescr gaebPerfDescr, BoqItemEntity parent, BoqItemEntity predecessor)
		{
			TextCount++;

			// _itemRefMap.Add(gaebPerfDescr.att_ID, item);
			Add2ReferenceMap(gaebPerfDescr.att_ID, item);

			SetItemBasics(item, null, parent, predecessor, (int)BoqConstants.EBoqLineType.DesignDescription);

			if (!gaebPerfDescr.PerfNo_isEmpty())
				item.DesignDescriptionNo = StringExtension.Truncate(gaebPerfDescr.PerfNo.ToString(CultureInfo.InvariantCulture), 4);

			if (!gaebPerfDescr.PerfLbl_isEmpty())
			{
				SetBriefInfo(item, gaebPerfDescr.PerfLbl);
			}

			//Change order
			if (!gaebPerfDescr.CONo_isEmpty())
				SetChangeOrder(item, gaebPerfDescr, System.Convert.ToUInt32(gaebPerfDescr.CONo), string.Empty);
		}

		/// <summary>
		/// Constructor Note
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxRemark"></param>
		/// <param name="parent"></param>
		/// <param name="predecessor"></param>
		private void SetNote(BoqItemEntity item, GX_Remark gxRemark, BoqItemEntity parent, BoqItemEntity predecessor)
		{
			TextCount++;
			if (gxRemark.att_ID.Length > 32)
			{
				throw new Exception(String.Format("Remark ID must not be longer than 32 characters: {0}", gxRemark.att_ID));
			}
			item.GaebId = gxRemark.att_ID;
			SetItemBasics(item, gxRemark.Description, parent, predecessor, (int)BoqConstants.EBoqLineType.Note);

			//Change order
			if (!gxRemark.CONo_isEmpty())
				SetChangeOrder(item, gxRemark, System.Convert.ToUInt32(gxRemark.CONo), string.Empty);
		}

		private void SetNoteFromTextItem(BoqItemEntity item, GX_Item gxItem, BoqItemEntity parent, BoqItemEntity predecessor)
		{
			TextCount++;
			SetItemBasics(item, gxItem.Description, parent, predecessor, (int)BoqConstants.EBoqLineType.Note);

		}

		/// <summary>
		/// Set sub description, GX_SubDescr
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxSubDescr"></param>
		/// <param name="parent"></param>
		/// <param name="predecessor"></param>
		/// <param name="warnings"></param>
		private void SetSubDescription(BoqItemEntity item, GX_SubDescr gxSubDescr, BoqItemEntity parent, BoqItemEntity predecessor, ref List<ImportWarning> warnings)
		{
			bool isNewitem = item.Version == 0;
			TextCount++;

			// if (!IsBidderFile)
			if (isNewitem || CanUpdateBoq)
			{
				SetItemBasics(item, gxSubDescr.Description, parent, predecessor, (int)BoqConstants.EBoqLineType.SubDescription);

				if (!gxSubDescr.SubDNo_isEmpty())
				{
					item.DesignDescriptionNo = StringExtension.Truncate(gxSubDescr.SubDNo.ToString(CultureInfo.InvariantCulture), 4);
				}

				// ref to design description / subdescription
				//SetReferenceDd(item, gxSubDescr); // ?? ..not gaeb
			}

			// quantity
			SetValueIfNotEmpty(item, gxSubDescr.Qty_isEmpty(), e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxSubDescr.Qty));

			// uom
			if (!IsBidderFile && !gxSubDescr.QU_isEmpty())
			{
				SetUoM(item, gxSubDescr.QU, ref warnings);
			}

			// ur (up)
			if (!gxSubDescr.UP_isEmpty())
			{
				// item.Price = System.Convert.ToDecimal(gxSubDescr.UP);
				item.SetMonetaryValue(e => e.Price, item.Price, System.Convert.ToDecimal(gxSubDescr.UP));
			}

			if (!IsBidderFile)
			{
				// falgs
				if (!gxSubDescr.QtyTBD_isEmpty() && gxSubDescr.QtyTBD.Equals("Yes"))
					item.IsFreeQuantity = true;
				if (!gxSubDescr.UPSpec_isEmpty() && gxSubDescr.UPSpec.Equals("Yes"))
					item.IsUrFromSd = true;
			}

			// ur breakdown
			if (!gxSubDescr.UPBkdn_isEmpty())
			{
				item.SetValue(e => e.IsUrb, item.IsUrb, true);
			}

			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp1_isEmpty(), e => e.Urb1, item.Urb1, System.Convert.ToDecimal(gxSubDescr.UPComp1));
			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp2_isEmpty(), e => e.Urb2, item.Urb2, System.Convert.ToDecimal(gxSubDescr.UPComp2));
			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp3_isEmpty(), e => e.Urb3, item.Urb3, System.Convert.ToDecimal(gxSubDescr.UPComp3));
			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp4_isEmpty(), e => e.Urb4, item.Urb4, System.Convert.ToDecimal(gxSubDescr.UPComp4));
			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp5_isEmpty(), e => e.Urb5, item.Urb5, System.Convert.ToDecimal(gxSubDescr.UPComp5));
			SetMonetaryValueIfNotEmpty(item, gxSubDescr.UPComp6_isEmpty(), e => e.Urb6, item.Urb2, System.Convert.ToDecimal(gxSubDescr.UPComp6));

			//UserDefined1-5 (not GAEB)
			if (parent.IsLeadDescription)
			{
				SetUserDefinedInfos(gxSubDescr, item);
			}
		}

		/// <summary>
		/// Set totals GX_Totals
		/// </summary>
		private void SetTotals(GX_Totals totals, BoqItemEntity boqItem)
		{
			TotalCount++;

			if (totals != null && boqItem != null)
			{
				SetMonetaryValueIfNotEmpty(boqItem, totals.DiscountAmt_isEmpty(), e => e.Discount, boqItem.Discount, System.Convert.ToDecimal(totals.DiscountAmt));

				SetValueIfNotEmpty(boqItem, totals.DiscountPcnt_isEmpty(), e => e.DiscountPercentIt, boqItem.DiscountPercentIt, System.Convert.ToDecimal(totals.DiscountPcnt));

				SetMonetaryValueIfNotEmpty(boqItem, totals.TotAfterDisc_isEmpty(), e => e.Finalprice, boqItem.Finalprice, System.Convert.ToDecimal(totals.TotAfterDisc));

				SetMonetaryValueIfNotEmpty(boqItem, totals.Total_isEmpty(), e => e.DiscountedPrice, boqItem.DiscountedPrice, System.Convert.ToDecimal(totals.Total));

				if (!totals.TotalLSUM_isEmpty())
				{
					boqItem.SetMonetaryValue(e => e.LumpsumPrice, boqItem.LumpsumPrice, System.Convert.ToDecimal(totals.TotalLSUM));

					boqItem.SetMonetaryValue(e => e.Finalprice, boqItem.Finalprice, boqItem.LumpsumPrice);

					if (boqItem.LumpsumPrice <= 0)
					{
						boqItem.IsLumpsum = false;
					}
					else if (boqItem.LumpsumPrice > 0)
					{
						boqItem.IsLumpsum = true;
					}
				}

				boqItem.SetMonetaryValue(e => e.Finalprice, boqItem.Finalprice, boqItem.DiscountedPrice);
			}
		}

		private void SetBoqBudget(G_List<G_Object> gNodes, BoqItemEntity item)
		{
			if (gNodes == null)
			{
				return;
			}

			//BoqBudget (not GAEB)
			item.BudgetFixedTotal = false;
			item.BudgetFixedUnit = false;
			item.BudgetTotal = 0;
			item.BudgetPerUnit = 0;

			var gBoqBudget = gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebBoqBudget));
			var gFixedBudget = gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebFixedBudget));
			if (gBoqBudget != null && gFixedBudget != null)
			{
				if (gFixedBudget.gValue == "Yes")
				{
					item.BudgetFixedTotal = true;
					item.BudgetTotal = System.Convert.ToDecimal(gBoqBudget.gValue, CultureInfo.InvariantCulture);
				}
				else if (item.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position)
				{
					item.BudgetFixedUnit = true;
					item.BudgetPerUnit = System.Convert.ToDecimal(gBoqBudget.gValue, CultureInfo.InvariantCulture);
				}
			}
		}


		/// <summary>
		///
		/// </summary>
		private void SetChangeOrder(BoqItemEntity boqItem, G_Object gxObject, uint coNo, string sCoStatus)
		{
			boqItem.PrjChangeFk = GetChangeIdByGaebNo((int) coNo);
			if(boqItem.PrjChangeFk == null)
            {
				//TODO add to info List

				return;
            }

			boqItem.PrjChangeStatusFk = GetCoStatusId(sCoStatus);

			if (boqItem.PrjChangeStatusFk != null)
			{
				boqItem.PrjChangeStatusFactorByAmount = GetRibCoFactor(gxObject, BoqConstants.RibGaebCoFactorAmount);
				boqItem.PrjChangeStatusFactorByReason = GetRibCoFactor(gxObject, BoqConstants.RibGaebCoFactorReason);
			}
		}

		private Decimal GetRibCoFactor(G_Object gxObject, string sRibExt)
        {
			Decimal dFactor = -1;
			var gxFactor = gxObject.gNodes.ToList().FirstOrDefault(e => null != e.Schema && null != e.gName && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && e.gName.Equals(sRibExt));

			if (gxFactor != null)
				dFactor = System.Convert.ToDecimal(gxFactor.gValue, CultureInfo.InvariantCulture);

			return dFactor;
		}

		private int? GetChangeIdByGaebNo(int gaebNO)
		{
			int? id = null;

			var changeMainLogic = Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity");
			var changeEntities = new List<IChangeEntity>();
			changeEntities = changeMainLogic.GetIdsByChangeStatus(_ImportOptions.ProjectId).ToList();
			if (changeEntities.Any())
			{
				IChangeEntity ent = changeEntities.FirstOrDefault(e => e.GermanGaebNr.Equals(gaebNO));
				if (ent != null)
					id = ent.Id;
			}

			return id;
		}

		private int? GetCoStatusId(string sCoStatus)
        {
			int? id = null;

			BasicsCustomizeProjectChangeStatusEntity entity = null;

			/*
			private bool _isIdentified;
			private bool _isAnnounced;
			private bool _isSubmitted;
			private bool _isWithDrawn;
			private bool _isRejected;
			private bool _isRejectedWithProtest;
			private bool _isAcceptedInPrinciple;
			private bool _isAccepted;
			public const string GaebConstCoStatus1 = "Recog";         // erkannt
			public const string GaebConstCoStatus2 = "Filed";        // angemeldet
			public const string GaebConstCoStatus3 = "Offered";      // angeboten
			public const string GaebConstCoStatus4 = "Withdrawn";    // zurückgezogen
			public const string GaebConstCoStatus5 = "Rejected";      // abgelehnt
			public const string GaebConstCoStatus6 = "ObjToRecj";     // Widerspruch zur Ablehnung
			public const string GaebConstCoStatus7 = "FormAckn";      // sachlich anerkannt
			public const string GaebConstCoStatus8 = "Approved";      // genehmigt
			*/

			if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus1)) 		entity = AvailableChangeStatus.FirstOrDefault(e => e.IsIdentified == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus2)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsAnnounced == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus3)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsSubmitted == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus4)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsWithDrawn == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus5)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsRejected == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus6)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsRejectedWithProtest == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus7)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsAcceptedInPrinciple == true);
			else if (sCoStatus.Equals(BoqConstants.GaebConstCoStatus8)) entity = AvailableChangeStatus.FirstOrDefault(e => e.IsAccepted == true);

			if (entity != null)
				id = entity.Id;

			return id;
		}

#endregion

#region catalog assign

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeader"></param>
		private bool CatalogAssignmentsAreEqual(BoqHeaderEntity boqHeader)
		{

			int countOptionAssigns = (ImportOptions == null || ImportOptions.Catalogs == null) ? 0 : ImportOptions.Catalogs.Where(e => e.BoqCatalogFk.HasValue).Count();

			IEnumerable<BoqCatAssignDetailEntity> catAssignList = BoqTypeLogic.GetBoqCatAssignDetailsByHeaderId(boqHeader.Id);
			int countCatAssigns = catAssignList == null ? 0 : catAssignList.ToList().Count;

			if (countCatAssigns != countOptionAssigns)
			{
				return false;
			}

			// To Do: when the ImportOptions is null, it will throw error.
			if (ImportOptions != null)
			{
				foreach (var importCat in ImportOptions.Catalogs.Where(e => e.BoqCatalogFk.HasValue))
				{
					// search GAEB_ID by catalog type
					int gaebId = (BoqCatalogLogic.GetGaebIdByCatalogType(importCat.CtlgType) ?? (int)BoqConstants.GaebType.Miscellaneous);
					BoqCatAssignDetailEntity catAssignEntity = catAssignList.FirstOrDefault(e => e.GaebId == gaebId);
					if (catAssignEntity == null
						|| catAssignEntity.BoqCatalogFk != importCat.BoqCatalogFk
						|| catAssignEntity.BasCostgroupCatFk != importCat.BasCostgroupCatFk
						|| catAssignEntity.SearchMode != (int)importCat.CatalogAssignmentMode)
					{
						return false;
					}
				}
			}

			return true;
		}

#endregion

		private bool StructuresAreEqual(BoqStructureEntity strucEntity, GX_BoQInfo gaebBoqInfo, bool indexAdded)
		{

			// if (gaebBoqInfo.NoUPComps_isEmpty()) return;
			bool result = true;

			int offset = 0;

			if (indexAdded && "I".Equals(strucEntity.Boqmask.Right(1)))
				offset = -1;

			var gaebBoqStructureDetails = gaebBoqInfo.gNodes.Where(node => node is GX_BoQBkdn).Cast<GX_BoQBkdn>();

			var gxBoQBkdns = _GaebBoqStructureDetails.ToList() ?? new List<GX_BoQBkdn>();

			if (strucEntity.BoqStructureDetailEntities.Count + offset == gxBoQBkdns.Count)
			{
				for (var i = 0; i < strucEntity.BoqStructureDetailEntities.Count + offset; i++)
				{
					if (strucEntity.BoqStructureDetailEntities.ElementAt(i).DescriptionInfo.Description != gxBoQBkdns[i].LblBoQBkdn)
					{
						result = false;
					}
				}
			}
			else
			{
				result = false;
			}

			return result;
		}

#region merged from partial classes

		private void BuildReferenceMap(BoqItemEntity boqRootItem, bool ignoreException = false)
		{
			ForEachExceptTheRoot(boqRootItem, boqItem =>
			{
				if (String.IsNullOrEmpty(boqItem.Reference))
				{
					if (boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.SubDescription)
					{
						Add2ReferenceMap(GetDummySubDescRef(GetDefiniteReferenceNo(boqItem.BoqItemParent.Reference), boqItem.DesignDescriptionNo), boqItem, ignoreException);
					}
					else if (boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.DesignDescription)
					{
						Add2ReferenceMap(GetDummyDesignDescRef(GetDefiniteReferenceNo(boqItem.BoqItemParent.Reference), boqItem.DesignDescriptionNo), boqItem, ignoreException);
					}
				}
				else
				{
					Add2ReferenceMap(GetDefiniteReferenceNo(boqItem.Reference), boqItem, ignoreException);
				}
			});
		}

		private void SetBriefInfo(BoqItemEntity item, string spec)
		{

			bool isNewitem = item.Version == 0;
			string newSpec = StringExtension.Truncate(spec, 2000);
			if (isNewitem || item.BriefInfo.Description != newSpec)
			{
				item.BriefInfo.Description = newSpec;
			}

		}

		private void SetUserDefinedInfos(G_Object gObject, BoqItemEntity item)
		{
			//Userdefined1-5 (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName))
				{
					if (gaebObject.gName.Equals(BoqConstants.RibGaebExtUserDef1))
					{
						item.Userdefined1 = StringExtension.Truncate(gaebObject.gValue, 252);
					}
					else if (gaebObject.gName.Equals(BoqConstants.RibGaebExtUserDef2))
					{
						item.Userdefined2 = StringExtension.Truncate(gaebObject.gValue, 252);
					}
					else if (gaebObject.gName.Equals(BoqConstants.RibGaebExtUserDef3))
					{
						item.Userdefined3 = StringExtension.Truncate(gaebObject.gValue, 252);
					}
					else if (gaebObject.gName.Equals(BoqConstants.RibGaebExtUserDef4))
					{
						item.Userdefined4 = StringExtension.Truncate(gaebObject.gValue, 252);
					}
					else if (gaebObject.gName.Equals(BoqConstants.RibGaebExtUserDef5))
					{
						item.Userdefined5 = StringExtension.Truncate(gaebObject.gValue, 252);
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="gaebObject"></param>
		/// <param name="item"></param>
		private void SetExternalCodeWithUserDef(G_Object gaebObject, BoqItemEntity item)
		{
			if (_isImportExternalCodeWihtUserDef)
			{
				switch (_importExternalCodeOption)
				{
					case 1:
						var userDef1 = gaebObject.gNodes.Where(node => node.gName.Equals("UserDef1")).FirstOrDefault();
						if (userDef1 != null)
							item.ExternalCode = userDef1.gValue;
						break;
					case 2:
						var userDef2 = gaebObject.gNodes.Where(node => node.gName.Equals("UserDef2")).FirstOrDefault();
						if (userDef2 != null)
							item.ExternalCode = userDef2.gValue;
						break;
					case 3:
						var userDef3 = gaebObject.gNodes.Where(node => node.gName.Equals("UserDef3")).FirstOrDefault();
						if (userDef3 != null)
							item.ExternalCode = userDef3.gValue;
						break;
					case 4:
						var userDef4 = gaebObject.gNodes.Where(node => node.gName.Equals("UserDef4")).FirstOrDefault();
						if (userDef4 != null)
							item.ExternalCode = userDef4.gValue;
						break;
				}
			}
		}

		private void SetNameUrb(BoqStructureEntity strucEntity, GX_BoQInfo gaebBoqInfo)
		{

			if (gaebBoqInfo.NoUPComps_isEmpty()) return;

			String[] urbName = new String[6];
			int upComps = System.Convert.ToInt32(gaebBoqInfo.NoUPComps);

			if (upComps >= 1 && gaebBoqInfo.LblUPComp1 != null)
			{
				urbName[0] = gaebBoqInfo.LblUPComp1.gValue;
			}
			if (upComps >= 2 && gaebBoqInfo.LblUPComp2 != null)
			{
				urbName[1] = gaebBoqInfo.LblUPComp2.gValue;
			}
			if (upComps >= 3 && gaebBoqInfo.LblUPComp3 != null)
			{
				urbName[2] = gaebBoqInfo.LblUPComp3.gValue;
			}
			if (upComps >= 4 && gaebBoqInfo.LblUPComp4 != null)
			{
				urbName[3] = gaebBoqInfo.LblUPComp4.gValue;
			}
			if (upComps >= 5 && gaebBoqInfo.LblUPComp5 != null)
			{
				urbName[4] = gaebBoqInfo.LblUPComp5.gValue;
			}
			if (upComps >= 6 && gaebBoqInfo.LblUPComp6 != null)
			{
				urbName[5] = gaebBoqInfo.LblUPComp6.gValue;
			}

			//BoqStructure.NameUrb1 = urbName1;
			//BoqStructure.NameUrb2 = urbName2;
			//BoqStructure.NameUrb3 = urbName3;
			//BoqStructure.NameUrb4 = urbName4;
			//BoqStructure.NameUrb5 = urbName5;
			//BoqStructure.NameUrb6 = urbName6;

			for (int i = 0; i < upComps; i++)
			{
				if (!String.IsNullOrEmpty(urbName[i]))
				{
					strucEntity.SetValue("NameUrb" + (i + 1).ToString(), urbName[i]);
				}
			}
		}

		private void SetUserDefinedLabels(BoqStructureEntity strucEntity, GX_BoQInfo gxGaebBoqInfo)
		{
			for (int i = 1; i <= 5; i++)
			{
				G_Object lbl = gxGaebBoqInfo.gNodes.Where(node => node.gName.Equals(String.Format("LblUserDef{0}", i))).FirstOrDefault();
				if (lbl != null)
				{
					strucEntity.SetNameUserdefined(i, lbl.gValue);
				}
			}
		}


		private void SetReferenceKeys(BoqItemEntity boqItem, IEnumerator<Int32> boqSplitQuantity2CostGroupIds)
		{
			if (boqItem.BoqSplitQuantityEntities != null && boqItem.BoqSplitQuantityEntities.Any())
			{
				if (_SplitEntityIdPool == null)
				{
					_SplitEntityIdPool = SplitQuantityLogic.Value.GetNextIds(_sqsTotal);
				}

				if (_SplitEntityIdPool != null && _SplitEntityIdPool.Count > 0)
				{
					foreach (var entity in boqItem.BoqSplitQuantityEntities)
					{
						entity.Id = _SplitEntityIdPool[0];
						_SplitEntityIdPool.RemoveAt(0);
						entity.BoqItemFk = boqItem.Id;
						entity.BoqHeaderFk = boqItem.BoqHeaderFk;

						if (entity.BoqSplitQuantity2CostGroupEntities != null && entity.BoqSplitQuantity2CostGroupEntities.Any())
						{
							foreach (var sq2cgEntity in entity.BoqSplitQuantity2CostGroupEntities)
							{
								if (sq2cgEntity.Id == 0)
								{
									boqSplitQuantity2CostGroupIds.MoveNext();
									sq2cgEntity.Id = boqSplitQuantity2CostGroupIds.Current;
								}
								sq2cgEntity.BoqHeaderFk = entity.BoqHeaderFk;
								sq2cgEntity.BoqItemFk = entity.BoqItemFk;
								sq2cgEntity.BoqSplitQuantityFk = entity.Id;
							}
						}

					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="splitData"></param>
		/// <param name="gaebObject"></param>
		private void SetSplitQuantities(BoqItemEntity item, BoqQtySplitData splitData, GX_Item gaebObject)
		{

			bool isNewitem = item.Version == 0;
			bool isLocationUnique = true;
			int prjLocationId = -1;
			List<int> ids = new List<int>();

			if (splitData != null && splitData.QtySplits != null && splitData.QtySplits.Count > 1)
			{
				foreach (var splitEntity in splitData.QtySplits)
				{
					BoqSplitQuantityEntity entity = new BoqSplitQuantityEntity()
					{
						BoqHeaderFk = item.BoqHeaderFk,
						Quantity = System.Convert.ToDecimal(splitEntity.Qty),
						QuantityAdj = System.Convert.ToDecimal(splitEntity.PredQty),
						CommentText = (splitEntity as BoqQtySplit).CommentText
					};

					if (ImportOptions != null && ImportOptions.Catalogs != null)
					{
						for (int i = 0; i < splitEntity.CtlgIDs.Count; i++)
						{
							// get catalog mappings
							GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == splitEntity.CtlgIDs[i]);
							if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
							{
								if (catalogInfo.IsCostGroupCatalog)
								{
									int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
									entity.SetCostGroup(_boqSplitQty2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, splitEntity.CtlgCodes[i], (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
									catalogInfo.BasCostgroupCatFk = costgroupCatFk;
								}
								else
								{
									BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
									int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, splitEntity.CtlgCodes[i]);
									if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
									{
										referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, splitEntity.CtlgCodes[i]);
									}
									entity.SetPropertyValue(boqCatalogEntity.AssignedCol, referenceId);
								}

								// #97701 no assignment for boq item
								//// set catalog references also to boq item
								//if (i == 0)
								//{
								//	item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
								//}

							}
						}
					}

					if (IsQtySplits) // bid estimate interface
					{
						// set locations in qty splits
						SetBoqSplitLocationFk(entity, splitEntity, ids, ref prjLocationId, ref isLocationUnique);
					}

					if (isNewitem) // Merging of split Quantities not yet supported!
					{
						item.BoqSplitQuantityEntities.Add(entity);
						_sqsAdded++;
					}
					else
					{
						_sqsSkipped++;
					}

					_sqsTotal++;
				}

				// if the location are same, set as boq item locaiton
				if (IsQtySplits && ids.Count > 1 && isLocationUnique && prjLocationId > 0)  // bid estimate interface
				{
					item.PrjLocationFk = (int?)prjLocationId;
				}
			}

			else
			{
				// item has no split quantities but the user has assigned catalog(s).
				if (ImportOptions != null && ImportOptions.HasAssignedCatalog)
				{
					Dictionary<string, string> itemCatalogs = GetItemDataCtlgAssign(gaebObject);
					if (itemCatalogs.Count > 0)
					{
						foreach (var catalog in itemCatalogs)
						{
							// get catalog mappings
							GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == catalog.Key);
							// if (catalogInfo != null && catalogInfo.MappedCatalogId.HasValue && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
							if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
							{
								if (catalogInfo.IsCostGroupCatalog)
								{
									int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
									item.SetCostGroup(_boqItem2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, catalog.Value, (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
									catalogInfo.BasCostgroupCatFk = costgroupCatFk;
								}
								else
								{
									BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
									int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, catalog.Value);
									if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
									{
										referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, catalog.Value);
									}
									item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
								}
							}
						}
					}
				}

				if (IsQtySplits && splitData.QtySplits != null && splitData.QtySplits.Count == 1)  // bid estimate interface
				{
					// set locations in qty splits
					SetBoqSplitLocationFk(null, splitData.QtySplits[0], ids, ref prjLocationId, ref isLocationUnique);

					// set as boq item locaiton
					if (prjLocationId > 0)
					{
						item.PrjLocationFk = (int?)prjLocationId;
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="splitEntity"></param>
		/// <param name="ids"></param>
		/// <param name="prjLocationId"></param>
		/// <param name="isLocationUnique"></param>
		private void SetBoqSplitLocationFk(BoqSplitQuantityEntity entity, IBoqQtySplit splitEntity, List<int> ids, ref int prjLocationId, ref bool isLocationUnique)
		{
			for (int i = 0; i < splitEntity.CtlgIDs.Count; i++)
			{
				if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey(splitEntity.CtlgIDs[i]))
				{
					string ctlgType = _ctlgIdandTypeDic[splitEntity.CtlgIDs[i]].ToString();
					foreach (var ctlgKey in _ctlgTypeandKey.Keys)
					{
						if (ctlgKey.Contains(ctlgType))
						{
							foreach (KeyValuePair<int, string> ctlgIdandCode in _ctlgTypeandKey[ctlgKey])
							{
								if (splitEntity.CtlgCodes[i].ToUpper() == ctlgIdandCode.Value.ToUpper())
								{
									if (ctlgType == "locality")
									{
										if (entity != null)
										{
											entity.PrjLocationFk = (int?)ctlgIdandCode.Key;
										}

										if (prjLocationId > 0)
										{
											if (prjLocationId != ctlgIdandCode.Key)
											{
												isLocationUnique = false;
											}
										}
										else
										{
											prjLocationId = ctlgIdandCode.Key;
										}
										ids.Add(ctlgIdandCode.Key);
									}
									break;
								}
							}
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="splitData"></param>
		/// <param name="gaebObject"></param>
		public void setCatalogFromMarkupItem(BoqItemEntity item, BoqQtySplitData splitData, GX_MarkupItem gaebObject)
		{
			// item has no split quantities but the user has assigned catalog(s).
			if (ImportOptions != null && ImportOptions.HasAssignedCatalog)
			{
				Dictionary<string, string> itemCatalogs = GetItemDataCtlgAssign(gaebObject);
				if (itemCatalogs.Count > 0)
				{
					foreach (var catalog in itemCatalogs)
					{
						// get catalog mappings
						GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == catalog.Key);
						// if (catalogInfo != null && catalogInfo.MappedCatalogId.HasValue && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
						if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
						{
							if (catalogInfo.IsCostGroupCatalog)
							{
								int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
								item.SetCostGroup(_boqItem2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, catalog.Value, (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
								catalogInfo.BasCostgroupCatFk = costgroupCatFk;
							}
							else
							{
								BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
								int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, catalog.Value);
								if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
								{
									referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, catalog.Value);
								}

								item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
							}
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="splitData"></param>
		/// <param name="gaebObject"></param>
		public void setCatalogFromBoQCtgy(BoqItemEntity item, BoqQtySplitData splitData, GX_BoQCtgy gaebObject)
		{
			// item has no split quantities but the user has assigned catalog(s).
			if (ImportOptions != null && ImportOptions.HasAssignedCatalog)
			{
				Dictionary<string, string> itemCatalogs = GetItemDataCtlgAssign(gaebObject);
				if (itemCatalogs.Count > 0)
				{
					foreach (var catalog in itemCatalogs)
					{
						// get catalog mappings
						GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == catalog.Key);
						// if (catalogInfo != null && catalogInfo.MappedCatalogId.HasValue && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
						if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
						{
							if (catalogInfo.IsCostGroupCatalog)
							{
								int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
								item.SetCostGroup(_boqItem2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, catalog.Value, (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
								catalogInfo.BasCostgroupCatFk = costgroupCatFk;
							}
							else
							{
								BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
								int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, catalog.Value);
								if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
								{
									referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, catalog.Value);
								}

								item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
							}
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxItem"></param>
		/// <param name="warnings"></param>
		private void SetPrcStructureFkByPackage(BoqItemEntity item, GX_Item gxItem, ref List<ImportWarning> warnings)
		{
			string _warning = string.Empty;
			foreach (var gaebObject in gxItem.gNodes)
			{
				if (gaebObject.gName.Equals(BoqConstants.RibGaebExtCtlgAssign))
				{
					var ctlgAssign = gaebObject as GX_CtlgAssign;
					if (ctlgAssign.CtlgID == _cltgIDbyWorkCategory)
					{
						var ctlgCode = ctlgAssign.CtlgCode;
						if (!String.IsNullOrEmpty(ctlgCode))
						{
							var temp = _workCategoryCodeDic.Where(e => e.Key == ctlgCode);
							if (temp.Count() > 0)
							{
								var val = temp.FirstOrDefault().Value.Where(e => e.Value.HasValue);
								if (val.Count() > 0)
								{
									item.PrcStructureFk = val.FirstOrDefault().Value;
									break;
								}
								else
								{
									int? Id = getprcStructureId(ctlgCode);
									if (Id == null)
									{
										_warning = string.Format(Resources.GaebImportWarning_StructureNoFound, ctlgCode);
									}
									else
									{
										item.PrcStructureFk = Id;
										break;
									}
								}
							}
							else
							{
								int? Id = getprcStructureId(ctlgCode);
								if (Id == null)
								{
									_warning = string.Format(Resources.GaebImportWarning_StructureNoFound, ctlgCode);
								}
								else
								{
									item.PrcStructureFk = Id;
									break;
								}
							}

						}
					}
				}
			}
			if (item.PrcStructureFk == null && !String.IsNullOrEmpty(_warning))
			{
				warnings.Add(new ImportWarning(nameof(Resources.GaebImportWarning_StructureNoFound), _warning));
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="gaebBoqInfo"></param>
		private void Set_cltgIDbyWorkCategory(GX_BoQInfo gaebBoqInfo)
		{
			foreach (var gaebObject in gaebBoqInfo.gNodes)
			{
				if (gaebObject.gName.Equals(BoqConstants.RibGaebExtCtlg))
				{
					var ctlgType = (gaebObject as GX_Ctlg).CtlgType;
					if (ctlgType == "work category")
					{
						_cltgIDbyWorkCategory = (gaebObject as GX_Ctlg).CtlgID;
						break;
					}
				}
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="key"></param>
		/// <returns></returns>
		private int? getprcStructureId(string key)
		{
			var prcStructure = Injector.Get<IPrcStructureLogic>().GetItemByCode(key);
			if (prcStructure != null)
			{
				return prcStructure.Id;
			}
			return null;
		}

		/// <summary>
		/// Determine if the references of the given items in the itemRefMap have leading zeros.
		/// </summary>
		/// <param name="itemRefMap"></param>
		/// <param name="boqStructure"></param>
		/// <param name="boqStructureDetails"></param>
		/// <returns>bool telling if items in map have leading zeros in reference numbers</returns>
		private bool DetermineLeadingZeros(Dictionary<string, BoqItemEntity> itemRefMap, BoqStructureEntity boqStructure, IEnumerable<BoqStructureDetailEntity> boqStructureDetails)
		{
			if (!boqStructure.LeadingZeros && _itemRefMap.Any())
			{
				// The settings for LeadingZeros are not always easy to determine from the imported BoqStructure alone.
				// To have a better behavior we check the created reference numbers for occurrences of leading zeros.
				// return _itemRefMap.Where(e => !string.IsNullOrEmpty(e.Value.Reference)).Any(e =>
				return _itemRefMap.Where(e => !string.IsNullOrEmpty(BoqImExportHelper.JustifyReference(e.Value.Reference))).Any(e =>
				{
					var detailInfo = boqStructureDetails.FirstOrDefault(i => i.BoqLineTypeFk == e.Value.BoqLineTypeFk);

					if (detailInfo == null)
						return false;

					var myReference = new BoqItemLogic().RemoveDotAtEnd(e.Value.Reference);
					var refParts = myReference.Split('.');
					var myRefPart = refParts.Last();

					if (myRefPart.Length == detailInfo.LengthReference)
					{
						// The reference part on this level has the maximum length
						// -> look if it has leading zeros
						if (myRefPart.Length > 1 && myRefPart[0] == '0')
							return true;
					}

					return false;
				});
			}

			return boqStructure.LeadingZeros;
		}

		#endregion

	}

	// ImportWarning
	class ImportWarning
	{
		// Code
		public string Code
		{
			get;
		}

		// Text
		public string Text
		{
			get;
		}

		/// <summary>
		/// Constructor
		/// </summary>
		/// <param name="code"></param>
		/// <param name="text"></param>
		public ImportWarning(string code, string text)
		{
			Code = code;
			Text = text;
		}
	}
}
