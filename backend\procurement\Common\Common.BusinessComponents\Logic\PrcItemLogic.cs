/*
 * $Id: PrcItemLogic.cs 633775 2021-04-25 07:10:29Z chi $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.Caching;
using System.Transactions;
using Jint.Parser;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Basics.MaterialCatalog.BusinessComponents;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Basics.TaxCode.BusinessComponents;
using RIB.Visual.Basics.Unit.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Procurement.Common.Core;
using COMMON_NLS = RIB.Visual.Procurement.Common.Localization.Properties.Resources;
using LookupBiz = RIB.Visual.Basics.LookupData.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Procurement.Common.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class ValidateAndUpdateItemQuantityParams
	{
		/// <summary>
		///
		/// </summary>
		public int PrcurementItemId { get; set; }
		/// <summary>
		///
		/// </summary>
		public string ValidateAndUpdateScope { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public enum FrmStyle
	{
		/// <summary>
		///
		/// </summary>
		FromReq = 1,
		/// <summary>
		///
		/// </summary>
		FromContract = 2,
		/// <summary>
		///
		/// </summary>
		FromPackage = 3,
		/// <summary>
		///
		/// </summary>
		FromQuote = 4,

		/// <summary>
		///
		/// </summary>
		FromPriceComparisonQuoteReq = 5
	}

	/// <summary>
	/// prcitemlogic
	/// </summary>
	[Export(typeof(IGetPrcItemLogic))]
	[Export(typeof(IPrcItemLogic))]
	[Export("prcitem", typeof(IChangeStatus))]
	[Export("prcitemprojectchange", typeof(IChangeStatus))]
	[Export("procurement.prcitem", typeof(IOwnerMappingReadLogic))]
	[EntityStatus("PRC_ITEMSTATUS", "procurement.common", "Package Item Status")]
	public class PrcItemLogic : HeaderSubDataLogicBase<PrcItemEntity, long>, IGetPrcItemLogic, ICreateEntityFacade, IChangeStatus, IEntityAggregator, IPrcItemLogic, IOwnerMappingReadLogic
	{
		private static readonly object _lock = new Object();

		private static readonly MemoryCache _itemNoCache = new MemoryCache("itemNOCache");// used to create a unique item number
		private string itemNOCacheKey = "itemNOCacheKey";

		/// <summary>
		/// Holds the GetSubItemLogic logic
		/// </summary>
		private IGetSubItemLogic<IIdentifyable> _GetSubItemLogic;

		/// <summary>
		///
		/// </summary>
		public PrcItemLogic()
		{
			this.PermissionGUID = "94c2bdf0c9824b8c876f81c9fb2ef127";
			this.EnablePermissionCheck = false;
		}

		/// <summary>
		/// Returns the extended logic
		/// </summary>
		public IGetSubItemLogic<IIdentifyable> GetSubItemLogic
		{
			get
			{
				if (_GetSubItemLogic == null)
				{
					_GetSubItemLogic = new GetSubItemLogic<PrcItemEntity>(ModelBuilder.DbModel, "PrcHeaderFk");
				}
				return _GetSubItemLogic;
			}
		}

		/// <summary>
		/// Retrieve item number increment step from system option id = 500, default is 10.
		/// </summary>
		/// <returns></returns>
		public int GetItemNumberIncrementStep()
		{
			var defaultStep = 10;

			var systemOption = new SystemOptionLogic().GetItemByKey(500);
			if (systemOption == null)
			{
				return defaultStep;
			}

			if (!int.TryParse(systemOption.ParameterValue, out var result) || result <= 0)
			{
				result = defaultStep;
			}
			return result;
		}

		/// <summary>
		/// GetMaxItemNo
		/// </summary>
		/// <returns></returns>
		public int GetMaxItemNo(int prcHeaderFk)
		{
			var list = this.GetSearchList(p => p.PrcHeaderFk == prcHeaderFk);
			return GetMaxItemNo(list.Select(e => e.Itemno));
		}

		/// <summary>
		/// GetMaxItemNo
		/// </summary>
		/// <param name="itemNos"></param>
		/// <param name="providedStep"></param>
		/// <returns></returns>
		public int GetMaxItemNo(IEnumerable<int> itemNos, int? providedStep = null)
		{
			var step = providedStep.HasValue ? providedStep.Value : this.GetItemNumberIncrementStep();
			return itemNos != null && itemNos.Any() ? itemNos.Max() + step : step;
		}

		/// <summary>
		/// GetMaxItemNo
		/// </summary>
		/// <param name="maxItemNo"></param>
		/// <param name="count"></param>
		/// <returns></returns>
		public List<int> GetMaxItemNo(int maxItemNo, int count = 1)
		{
			var result = new List<int>();

			int step = this.GetItemNumberIncrementStep();
			for (var i = 0; i < count; i++)
			{
				maxItemNo += step;
				result.Add(maxItemNo);
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="PrcHeaderFk"></param>
		/// <param name="itemNos"></param>
		/// <param name="step"></param>
		/// <returns></returns>
		private int GetAndUpdate(int PrcHeaderFk, IEnumerable<int> itemNos, int step)
		{
			int finalResult = step;
			lock (_lock)
			{
				IDictionary<int, int> itemNoDictionary = null;
				var maxItemNoMap = _itemNoCache.Get(itemNOCacheKey);
				if (maxItemNoMap != null)
				{
					itemNoDictionary = (Dictionary<int, int>)maxItemNoMap;
					if (itemNoDictionary.ContainsKey(PrcHeaderFk))
					{
						if (itemNos != null && itemNos.Any())
						{
							finalResult = itemNos.Max() + step;
						}
						else
						{
							finalResult = step;
						}
						itemNoDictionary[PrcHeaderFk] = finalResult;
					}
					else
					{
						finalResult = (itemNos != null && itemNos.Any()) ? itemNos.Max() + step : step;
						itemNoDictionary[PrcHeaderFk] = finalResult;
					}
				}
				else
				{
					itemNoDictionary = new Dictionary<int, int>();
					finalResult = (itemNos != null && itemNos.Any()) ? itemNos.Max() + step : step;
					itemNoDictionary[PrcHeaderFk] = finalResult;
				}
				CacheItemPolicy cacheItemPolicy = new CacheItemPolicy() { AbsoluteExpiration = new DateTimeOffset(DateTime.Now.AddDays(1)) };
				_itemNoCache.Set(itemNOCacheKey, itemNoDictionary, cacheItemPolicy);
			}
			return finalResult;
		}

		/// <summary>
		/// GetMaxItemNo
		/// </summary>
		/// <returns></returns>
		public int GetMaxItemNo(int PrcHeaderFk, IEnumerable<int> itemNos, int systemOptionId = 500)//systemOptionDefaultId = 500
		{
			var step = GetPrcItemIncreaseStep(systemOptionId);

			return GetAndUpdate(PrcHeaderFk, itemNos, step);
		}

		/// <summary>
		/// GetMaxItemNo by Step.
		/// Note: use this in loop and providing step value for better performance.
		/// </summary>
		/// <returns></returns>
		public int GetMaxItemNoByStep(int PrcHeaderFk, IEnumerable<int> itemNos, int step)
		{
			return GetAndUpdate(PrcHeaderFk, itemNos, step);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="systemOptionId"></param>
		/// <returns></returns>
		public int GetPrcItemIncreaseStep(int systemOptionId = 500)
		{
			var step = 0;
			var systemOption = new SystemOptionLogic().GetItemByKey(systemOptionId);
			if (systemOption != null)
			{
				int.TryParse(systemOption.ParameterValue, out step);
			}
			if (step == 0)
			{
				step = 10;//systemOptionDefaultIncrement = 10
			}
			return step;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int GetDefaultItemTypeId()
		{
			var itemType = new ItemTypeLogic().GetDefault();

			if (itemType == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type"));
			}

			return itemType.Id;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int GetDefaultItemType2Id()
		{
			var itemType2 = new ItemType2Logic().GetDefault();

			if (itemType2 == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type2"));
			}

			return itemType2.Id;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int? GetDefaultItemType85Id()
		{
			var itemType85 = new ItemType85Logic().GetDefault();

			if (itemType85 != null && itemType85.IsDefault)
			{
				return itemType85.Id;
			}

			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int? GetDefaultPrcIncotermId()
		{
			var defaultIncoterm = new PrcIncotermLogic().GetDefault();
			if (defaultIncoterm == null)
			{
				return null;
			}

			return defaultIncoterm.Id;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public IPrcItemEntity CreateNew()
		{
			return new PrcItemEntity();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="configurationId"></param>
		/// <param name="projectId"></param>
		/// <param name="itemNumber"></param>
		/// <param name="isSelfRef"></param>
		/// <returns></returns>
		public IPrcItemEntity Create(int prcHeaderFk, int configurationId, int projectId, int itemNumber, bool isSelfRef = false)
		{
			return CreateNew(prcHeaderFk, configurationId, projectId, itemNumber, isSelfRef, null);
		}

		/// <summary>
		/// Create new prcitem with defaultvalue
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="configurationId"></param>
		/// <param name="projectId"></param>
		/// <param name="itemNumber"></param>
		/// <param name="isSelfRef"></param>
		/// <param name="packageId"></param>
		/// <param name="newId"></param>
		/// <param name="providedConfiguration"></param>
		/// <param name="defaultInfo"></param>
		/// <param name="package"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public PrcItemEntity CreateNew(int prcHeaderFk, int configurationId, int projectId, int itemNumber, bool isSelfRef = false, int? packageId = null,
			long? newId = null, PrcConfigurationEntity providedConfiguration = null, PrcItemDefualtInfo defaultInfo = null, IPrcPackageEntity package = null)
		{
			var entity = new PrcItemEntity
			{
				Id = newId.HasValue ? newId.Value : GetNextId(),
				PrcHeaderFk = prcHeaderFk
			};
			if (isSelfRef)
			{
				entity.PrcItemFk = entity.Id;
			}
			entity.Itemno = itemNumber;

			entity.PrcIncotermFk = (defaultInfo != null) ? defaultInfo.PrcIncotermFk : GetDefaultPrcIncotermId();
			entity.BasItemTypeFk = (defaultInfo != null) ? defaultInfo.BasItemTypeFk : GetDefaultItemTypeId();
			entity.BasItemType2Fk = (defaultInfo != null) ? defaultInfo.BasItemType2Fk : GetDefaultItemType2Id();

			var itemType85 = (defaultInfo != null) ? defaultInfo.ItemType85Entity : new PrcItemType85Logic().GetDefault();
			if (itemType85 != null && itemType85.IsDefault)
			{
				entity.BasItemType85Fk = itemType85.Id;
			}

			//Get Configuration by configurationId
			var configuration = (providedConfiguration != null && providedConfiguration.Id == configurationId) ? providedConfiguration : new PrcConfigurationLogic().GetItemByKey(configurationId);
			entity.BasPaymentTermFiFk = configuration.PaymentTermFiFk;
			entity.BasPaymentTermPaFk = configuration.PaymentTermPaFk;

			entity.Quantity = 0;
			entity.AlternativeQuantity = 0;
			entity.PriceUnit = 1;
			entity.FactorPriceUnit = 1;
			entity.SafetyLeadTime = 0;
			entity.BufferLeadTime = 0;

			//entity.Onhire = DateTime.Now;
			//entity.Offhire = DateTime.Now;

			//temp, which value should set into InstanceId??
			//entity.InstanceId = (int)entity.Id;

			entity.DateRequired = null;
			entity.Hasdeliveryschedule = false;
			entity.Hastext = false;
			var prcItemStatus = (defaultInfo != null) ? defaultInfo.PrcItemstatusEntity : new PrcItemstatusLogic().GetDefault();
			if (prcItemStatus == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Prc Items"));
			}
			entity.PrcItemstatusFk = prcItemStatus.Id;
			entity.PrcPriceConditionFk = null;

			var packageHeader = package != null ?
				package :
				packageId.HasValue ? Injector.Get<IPrcPackageLogic>().GetPackageById(packageId.Value) : null;
			entity.MdcControllingunitFk = package != null ? package.MdcControllingUnitFk : entity.MdcControllingunitFk;
			if (packageHeader != null)
			{
				entity.MdcControllingunitFk = packageHeader.MdcControllingUnitFk;
			}
			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="frmHeaderFk"></param>
		/// <param name="frmStyle"></param>
		/// <param name="entity"></param>
		public void CreateAddress(int? frmHeaderFk, int? frmStyle, IPrcItemEntity entity)
		{
			CreateAddress(frmHeaderFk, frmStyle, entity as PrcItemEntity);
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="frmHeaderFk"></param>
		/// <param name="frmStyle"></param>
		/// <param name="entity"></param>
		public void CreateAddress(int? frmHeaderFk, int? frmStyle, PrcItemEntity entity)
		{
			//Default Address
			if (frmHeaderFk.HasValue && frmStyle.HasValue)
			{
				int? AddressFk = GetHeaderAddressFk(frmStyle.Value, frmHeaderFk.Value);
				if (AddressFk.HasValue)
				{
					var addressEntity = new AddressLogic().GetItemByKey(AddressFk.Value);
					if (addressEntity != null)
					{
						var adddressCopy = addressEntity.Clone() as AddressEntity;
						adddressCopy.Id = this.SequenceManager.GetNext("BAS_ADDRESS");
						adddressCopy.Version = 0;
						adddressCopy.InsertedBy = entity.InsertedBy;
						adddressCopy.InsertedAt = entity.InsertedAt;
						adddressCopy.UpdatedAt = null;
						adddressCopy.UpdatedBy = null;
						UpdateAddressByAddressEntity(entity, adddressCopy);
					}

				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="frmStyle"></param>
		/// <param name="frmHeaderFk"></param>
		/// <returns></returns>
		private int? GetHeaderAddressFk(int frmStyle, int frmHeaderFk)
		{
			int? AddressFk = null;
			if (frmStyle == (int)FrmStyle.FromReq)
			{
				var reqHeader = Injector.Get<IGetReqHeaderLogic>().GetReqHeaderByPrcHederIds(new int[] { frmHeaderFk }).FirstOrDefault();
				if (reqHeader != null)
				{
					AddressFk = reqHeader.AddressFk;
				}
			}
			else if (frmStyle == (int)FrmStyle.FromContract)
			{
				var contractHeader = Injector.Get<IContractHeaderInfoProvider>().GetConHeadersByKey(new int?[] { frmHeaderFk }).FirstOrDefault();
				if (contractHeader != null)
				{
					AddressFk = contractHeader.AddressFk;
				}
			}
			else
			{
				var packageHeader = Injector.Get<IPrcPackageLogic>().GetPackageById(frmHeaderFk);
				if (packageHeader != null)
				{
					AddressFk = packageHeader.AddressFk;
				}
			}
			return AddressFk;
		}

		/// <summary>
		/// Create new prcitem with defaultvalue
		/// </summary>
		/// <param name="prcItemId"></param>
		/// <param name="prcHeaderFk"></param>
		/// <param name="cfgEntity"></param>
		/// <param name="prcItemstatusEntity"></param>
		/// <param name="prcIncotermEntity"></param>
		/// <param name="addressEntity"></param>
		/// <param name="maxNo"></param>
		/// <param name="isSelfRef"></param>
		/// <returns></returns>
		public PrcItemEntity CreateNew(int prcItemId, int prcHeaderFk, PrcConfigurationEntity cfgEntity, PrcItemstatusEntity prcItemstatusEntity,
			PrcIncotermEntity prcIncotermEntity, AddressEntity addressEntity, int maxNo, bool isSelfRef = false)
		{
			var entity = new PrcItemEntity();
			entity.Id = prcItemId;
			entity.PrcHeaderFk = prcHeaderFk;
			if (isSelfRef == true)
			{
				entity.PrcItemFk = entity.Id;
			}
			entity.Itemno = maxNo;

			//DefaultIncoterm
			entity.PrcIncotermFk = prcIncotermEntity.Id;

			//Get Configuration by configurationId
			entity.BasPaymentTermFiFk = cfgEntity.PaymentTermFiFk;
			entity.BasPaymentTermPaFk = cfgEntity.PaymentTermPaFk;

			entity.Quantity = 0;
			entity.PriceUnit = 1;
			entity.FactorPriceUnit = 1;
			entity.SafetyLeadTime = 0;
			entity.BufferLeadTime = 0;

			//Default Address
			if (addressEntity != null)
			{
				entity.AddressEntity = addressEntity;
				entity.BasAddressFk = addressEntity.Id;
			}

			entity.DateRequired = null;
			entity.Hasdeliveryschedule = false;
			entity.Hastext = false;
			if (prcItemstatusEntity == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Prc Items"));
			}
			entity.PrcItemstatusFk = prcItemstatusEntity.Id;
			entity.PrcPriceConditionFk = null;

			entity.BasItemTypeFk = getDefaultValueForItemTypeFk();
			entity.BasItemType2Fk = getDefaultValueForItemType2Fk();

			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private int getDefaultValueForItemTypeFk()
		{
			var itemType = new ItemTypeLogic().GetDefault();
			if (itemType == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type"));
			}

			return itemType.Id;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private int getDefaultValueForItemType2Fk()
		{
			var itemType2 = new ItemType2Logic().GetDefault();
			if (itemType2 == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type2"));
			}
			return itemType2.Id;
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public decimal GetLeadTimeExtraByPrcHeader(int prcHeaderId)
		{
			var prcItems = new PrcItemLogic().GetSearchList(x => x.PrcHeaderFk == prcHeaderId);
			var materialLogic = new MaterialLogic();
			foreach (var suItem in prcItems)
			{
				decimal leadTimeExtra = 0;

				if (suItem.MdcMaterialFk.HasValue)
				{
					var material = materialLogic.GetItemByKey(suItem.MdcMaterialFk);
					if (material != null)
					{
						leadTimeExtra = material.LeadTimeExtra;
						suItem.MaterialStockFk = material.MdcMaterialStockFk;
					}
				}
				suItem.TotalLeadTime = suItem.BufferLeadTime + leadTimeExtra + suItem.SafetyLeadTime;
			}

			if (prcItems.Any())
			{
				var prcItem = prcItems.OrderByDescending(o => o.TotalLeadTime).FirstOrDefault();
				if (prcItem != null)
				{
					return prcItem.LeadTimeExtra;
				}
				return 0;
			}
			return 0;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetPrcItemsByPrcHeader(IEnumerable<int> prcHeaderIds)
		{
			var prcItems = new PrcItemLogic().GetSearchList(x => prcHeaderIds.Contains(x.PrcHeaderFk));

			var materialIds = prcItems.Select(e => e.MdcMaterialFk).ToList();
			var materials = new MaterialLogic().GetItemsByKey(materialIds).ToList();

			foreach (var suItem in prcItems)
			{
				decimal leadTime = 0;

				if (suItem.MdcMaterialFk.HasValue)
				{
					var material = materials.FirstOrDefault(e => e.Id == suItem.MdcMaterialFk);
					if (material != null)
					{
						leadTime = material.LeadTime;
						suItem.MaterialStockFk = material.MdcMaterialStockFk;
					}
				}
				suItem.TotalLeadTime = suItem.BufferLeadTime + leadTime + suItem.SafetyLeadTime + suItem.LeadTimeExtra;
			}
			if (prcItems.Any())
			{
				prcItems = prcItems.OrderByDescending(o => o.TotalLeadTime);
			}
			return prcItems;
		}

		/// <summary>
		/// Material Alternative By AI
		/// </summary>
		/// <param name="companyCurrencyId"></param>
		/// <param name="prcHeaderFk"></param>
		/// <returns></returns>
		public Dictionary<string, object> ItemMaterialMapping(int companyCurrencyId, int prcHeaderFk)
		{
			var prcItems = GetSearchList(x => (x.PrcHeaderFk == prcHeaderFk) && x.MdcMaterialFk.HasValue);
			if (prcItems == null || !prcItems.Any())
			{
				Dictionary<string, object> mainData = new Dictionary<string, object>();
				mainData["PrcItems"] = new List<PrcItemEntity>();
				mainData["Materials"] = new List<MaterialEntity>();
				mainData["Alternatives"] = new List<AIReplaceMaterialEntity>();
				return mainData;
			}

			var materialLogic = new MaterialLogic();
			var materialVLogic = new MaterialLookupVLogic();
			IPrcPackageLogic packageLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPrcPackageLogic>();
			IPackage2HeaderInfoProvider Package2HeaderLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPackage2HeaderInfoProvider>();

			var package2header = Package2HeaderLogic.GetPackage2HeaderByPrcHeaderIds(new int[] { prcHeaderFk }).FirstOrDefault();

			if(package2header == null)
			{
				throw new InvalidOperationException(COMMON_NLS.ERR_NoSubPackageFound);
			}

			IPrcPackageEntity packageEntity = packageLogic.GetPackageById(package2header.PrcPackageFk);

			// get item's material in MaterialCatalog
			var materialIds = prcItems.Select(x => x.MdcMaterialFk);
			var itemMaterials = materialVLogic.GetSearchList(x => materialIds.Contains(x.Id));
			var catalogIds = itemMaterials.Select(x => x.MaterialCatalogFk);

			// AI
			var threshold = AIConfigHelper.GetAIMaterialAlternativesThreshold();
			var maxSuggestedCount = AIConfigHelper.GetAIMaterialAlternativesCount();

			Dictionary<string, PrcItemEntity> dictPrcItem = new Dictionary<string, PrcItemEntity>();
			List<MostSimilarInput> inputs = new List<MostSimilarInput>();
			var dictMaterialEntites = new Dictionary<int, MaterialEntity>();
			var lookupMaterialIds = new List<int>();

			// For original material lookup
			foreach (int? materialId in materialIds)
			{
				if (!dictMaterialEntites.ContainsKey(materialId.Value))
				{
					dictMaterialEntites[materialId.Value] = materialLogic.GetItemByKey(materialId.Value);
					lookupMaterialIds.Add(materialId.Value);
				}
			}

			#region construct data for AI

			foreach (var catalogId in catalogIds)
			{
				MostSimilarInput input = new MostSimilarInput();
				// Sources
				List<Text> sources = new List<Text>();
				foreach (var prcItem in prcItems)
				{
					// to use in mapping reuslt
					dictPrcItem[prcItem.Id.ToString()] = prcItem;
					var itemMaterialCatalogId = itemMaterials.First(x => x.Id == prcItem.MdcMaterialFk).MaterialCatalogFk;
					string itemDesc = (prcItem.Description1 + " " + prcItem.Description2).Trim();
					if (itemMaterialCatalogId == catalogId && itemDesc.Length > 0 && itemDesc != null)
					{
						// Source
						Text source = new Text()
						{
							Id = prcItem.Id.ToString(),
							Content = itemDesc,
						};
						sources.Add(source);
					}
				}
				// Targets
				List<Text> targets = new List<Text>();
				var materials = materialVLogic.GetSearchList(x => x.MaterialCatalogFk == catalogId);
				foreach (var material in materials)
				{
					if (material != null)
					{
						string materialDesc = (material.DescriptionInfo.Description + " " + material.DescriptionInfo2.Description).Trim();
						if (materialDesc.Length > 0 && materialDesc != null)
						{
							Text target = new Text()
							{
								Id = material.Id.ToString(),
								Content = materialDesc,
							};
							targets.Add(target);
						}
					}
				}
				input.Application = AIApplicationEnum.package_item_alternative;
				input.Source = sources;
				input.Target = targets;
				input.TopN = 10;
				input.Threshold = threshold;
				inputs.Add(input);
			}

			#endregion

			var modelVersionLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IModelVersionProvider>();
			var modelVersionInfo = modelVersionLogic.GetActivatedVersion(AIModelCase.package_item_alternative);
			AIGeneralInputEntity inputEntity = new AIGeneralInputEntity();

			inputEntity.GUID = modelVersionInfo.Guid;
			inputEntity.Platform_Type = "ON-PREM";
			inputEntity.Cases_Name = AIModelCase.package_item_alternative;
			inputEntity.Model_Params_Input = Newtonsoft.Json.JsonConvert.SerializeObject(inputs);
			inputEntity.Model_Type = modelVersionInfo.ModelType;

			Others others = new Others();
			others.Params_Input_Format = modelVersionInfo.Params_Input_Format;
			others.Params_Output_Format = modelVersionInfo.Params_Output_Format;
			inputEntity.Others = others;
			inputEntity.Ops = "run";
			inputEntity.Shared_Folder = "/var/rib/app/resources/models";

			// sent to AI
			AILogic aiLogic = new AILogic(AIModelCase.package_item_alternative);
			string aiResult = aiLogic.GeneralInput(inputEntity);
			var results = Newtonsoft.Json.JsonConvert.DeserializeObject<List<MostSimilarResult>>(aiResult);

			// recaculate
			var alternatives = new List<AIReplaceMaterialEntity>();

			// many MostSimilarResult
			foreach (var result in results)
			{
				foreach (var mapping in result)
				{
					// one SimilarResult
					if (mapping.Source != null
						&& dictPrcItem.ContainsKey(mapping.Source.Id)
						&& mapping.Matches != null
						&& mapping.Matches.Count > 0)
					{
						PrcItemEntity entity = dictPrcItem[mapping.Source.Id];
						var itemAlternatives = new List<AIReplaceMaterialEntity>();

						entity.SuggestedMaterialFks = new List<int>();

						#region calculate total currency

						foreach (var match in mapping.Matches)
						{
							// get AI match MaterialEntities
							try
							{
								var materialId = int.Parse(match.Id);

								if (match.Id != entity.MdcMaterialFk.ToString())
								{
									// get AI support MatrialEntity
									if (!dictMaterialEntites.ContainsKey(materialId))
									{
										dictMaterialEntites[materialId] = materialLogic.GetItemByKey(materialId);
									}
									var material = dictMaterialEntites[materialId];

									// recaculate
									AIReplaceMaterialEntity alternative = this.CalculateTotalCurrency(packageEntity, companyCurrencyId, entity, material);
									// alternative.UomConversionStatus = 3 indicates the conversion between UoMs is not applicable.
									if (alternative.TotalCurrency < entity.TotalOc && alternative.UomConversionStatus != 3)
									{
										itemAlternatives.Add(alternative);
									}
								}
							}
							catch (Exception ex)
							{
								throw new BusinessLayerException("Cannot convert Id from string to int", ex);
							}
						}
						#endregion

						#region itemAlternatives
						if (itemAlternatives.Any())
						{
							var suggestedAlternatives = itemAlternatives.OrderBy(x => x.TotalCurrency).ToArray();
							if (suggestedAlternatives.Length > maxSuggestedCount)
							{
								suggestedAlternatives = suggestedAlternatives.Take(maxSuggestedCount).ToArray();
							}
							alternatives.AddRange(suggestedAlternatives);
							entity.SuggestedMaterialFks.AddRange(suggestedAlternatives.Select(e => e.MaterialFk));
							entity.SuggestedTotalCurrency = suggestedAlternatives[0].TotalCurrency;
							lookupMaterialIds.AddRange(entity.SuggestedMaterialFks);
						}
						else
						{
							// AI does not suggest any alternatives
							entity.SuggestedTotalCurrency = entity.TotalOc;
						}
						#endregion
					}
				}
			}

			List<MaterialEntity> materialEntites = dictMaterialEntites.Values.Where(e => lookupMaterialIds.Contains(e.Id)).ToList();
			Dictionary<string, object> mainDatas = new Dictionary<string, object>();
			mainDatas["PrcItems"] = prcItems;
			mainDatas["Materials"] = materialEntites;
			mainDatas["Alternatives"] = alternatives;
			return mainDatas;
		}

		/// <summary>
		/// Material Alternative By AI -- two
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="selectList"></param>
		/// <returns></returns>
		public Dictionary<string, object> MaterialAlternativeMapping(int prcHeaderFk, List<long> selectList)
		{
			IEnumerable<PrcItemEntity> prcItems;
			if (selectList != null && selectList.Count > 0 && selectList[0] != 0)
			{
				prcItems = GetSearchList(x => selectList.Contains(x.Id) && (x.BasItemType2Fk == 1 || x.BasItemType2Fk == 2));
			}
			else
			{
				//ItemType2: Normal=1, Base=2, Alternative=5
				prcItems = GetSearchList(x => (x.PrcHeaderFk == prcHeaderFk) && x.MdcMaterialFk.HasValue && (x.BasItemType2Fk == 1 || x.BasItemType2Fk == 2));
			}

			if (prcItems == null || !prcItems.Any())
			{
				Dictionary<string, object> mainData = new Dictionary<string, object>();
				mainData["Main"] = new List<AIUpdateMaterialEntity>();
				return mainData;
			}

			var materialCatalogLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetMaterialCatalogLogic>();
			var materialLogic = new MaterialLogic();
			var materialVLogic = new MaterialLookupVLogic();
			var unitLogic = new UnitConversionLogic();

			// AI
			var threshold = AIConfigHelper.GetAIMaterialAlternativesThreshold();
			var maxSuggestedCount = AIConfigHelper.GetAIMaterialAlternativesCount();

			Dictionary<string, PrcItemEntity> dictPrcItem = new Dictionary<string, PrcItemEntity>();
			var dictMaterialEntites = new Dictionary<int, MaterialEntity>();
			List<MostSimilarInput> inputs = new List<MostSimilarInput>();

			#region construct data for AI
			IEnumerable<MaterialLookupVEntity> allMaterials = null;
			var materialIds = prcItems.Select(x => x.MdcMaterialFk);
			var itemMaterials = materialVLogic.GetSearchList(e => materialIds.Contains(e.Id));

			//all material catalogs
			IEnumerable<IGroupEntity> materialCatalogEntities = materialCatalogLogic.GetTreeList();
			var materialCatalogIds = materialCatalogEntities.Select(e => e.Id).Distinct();
			allMaterials = materialVLogic.GetSearchList(e => materialCatalogIds.Contains(e.MaterialCatalogFk)).Where(e => e.Islive).Distinct();

			foreach (var prcItem in prcItems)
			{
				if (!string.IsNullOrEmpty(prcItem.Description1) || string.IsNullOrEmpty(prcItem.Description2))
				{
					MostSimilarInput input = new MostSimilarInput();
					List<Text> sources = new List<Text>();
					dictPrcItem[prcItem.Id.ToString()] = prcItem;
					string itemDesc = (prcItem.Description1 + " " + prcItem.Description2).Trim();

					// Source
					Text source = new Text()
					{
						Id = prcItem.Id.ToString(),
						Content = itemDesc,
					};
					sources.Add(source);

					// Targets
					List<Text> targets = new List<Text>();
					IEnumerable<MaterialLookupVEntity> basicMaterials = null;

					var catalogId = itemMaterials.Where(e => e.Id == prcItem.MdcMaterialFk).Select(e => e.MaterialCatalogFk).FirstOrDefault();
					var prcStructureId = prcItem.PrcStructureFk;
					var prcItemMaterial = allMaterials.FirstOrDefault(e => e.Id == prcItem.MdcMaterialFk);
					var neutralMaterialId = prcItemMaterial != null ? prcItemMaterial.MdcMaterialFk : null;

					basicMaterials = allMaterials.Where(e =>
						(catalogId == e.MaterialCatalogFk)  // same material catalog
						|| (prcStructureId != null && prcStructureId == e.PrcStructureFk) // same procurement structure
						|| (neutralMaterialId != null && neutralMaterialId == e.MdcMaterialFk));   // same neutral material

					if (basicMaterials != null)
					{
						foreach (var material in basicMaterials)
						{
							if (material.Id == prcItem.MdcMaterialFk)
							{
								// Do not recommendate current item material
								continue;
							}
							//unit can be converted or equal.
							if (material != null && (unitLogic.CanConvert(prcItem.BasUomFk, material.BasUomFk) || prcItem.BasUomFk == material.BasUomFk))
							{
								string materialDesc = (material.DescriptionInfo.Description + " " + material.DescriptionInfo2.Description).Trim();
								if (!string.IsNullOrEmpty(materialDesc))
								{
									Text target = new Text()
									{
										Id = material.Id.ToString(),
										Content = materialDesc,
									};
									targets.Add(target);
								}
							}
						}
					}

					if (targets != null && targets.Count > 0)
					{
						//original prcItem's material
						if (prcItem.MdcMaterialFk.HasValue)
						{
							if (!dictMaterialEntites.ContainsKey(prcItem.MdcMaterialFk.Value))
							{
								dictMaterialEntites[prcItem.MdcMaterialFk.Value] = materialLogic.GetItemByKey(prcItem.MdcMaterialFk.Value);
							}
						}

						input.Application = AIApplicationEnum.package_item_alternative;
						input.Source = sources;
						input.Target = targets;
						input.TopN = maxSuggestedCount;
						input.Threshold = threshold;
						inputs.Add(input);
					}
				}
			}

			#endregion

			// all prcItem doesn't have desc1 and desc2 || all prcItem's PrcStructure doesn't have materials
			if (inputs == null || !inputs.Any())
			{
				Dictionary<string, object> mainData = new Dictionary<string, object>();
				mainData["Main"] = new List<AIUpdateMaterialEntity>();
				return mainData;
			}

			var modelVersionLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IModelVersionProvider>();
			var modelVersionInfo = modelVersionLogic.GetActivatedVersion(AIModelCase.package_item_alternative);
			AIGeneralInputEntity inputEntity = new AIGeneralInputEntity();

			inputEntity.GUID = modelVersionInfo.Guid;
			inputEntity.Platform_Type = "ON-PREM";
			inputEntity.Cases_Name = AIModelCase.package_item_alternative;
			inputEntity.Model_Params_Input = Newtonsoft.Json.JsonConvert.SerializeObject(inputs);
			inputEntity.Model_Type = modelVersionInfo.ModelType;

			Others others = new Others();
			others.Params_Input_Format = modelVersionInfo.Params_Input_Format;
			others.Params_Output_Format = modelVersionInfo.Params_Output_Format;
			inputEntity.Others = others;
			inputEntity.Ops = "run";
			inputEntity.Shared_Folder = "/var/rib/app/resources/models";

			// sent to AI
			AILogic aiLogic = new AILogic(AIModelCase.package_item_alternative);
			string aiResult = aiLogic.GeneralInput(inputEntity);
			var results = Newtonsoft.Json.JsonConvert.DeserializeObject<List<MostSimilarResult>>(aiResult);

			List<AIUpdateMaterialEntity> displayEntities = new List<AIUpdateMaterialEntity>();

			#region many MostSimilarResult from AI
			int j = 0; //id
			foreach (var result in results)
			{
				foreach (var mapping in result)
				{
					if (mapping.Source != null
						&& dictPrcItem.ContainsKey(mapping.Source.Id)
						&& mapping.Matches != null
						&& mapping.Matches.Count > 0)
					{
						int i = 0; // AI recommend rank
						PrcItemEntity entity = dictPrcItem[mapping.Source.Id];
						var existAlternativeMaterials = GetSearchList(x => x.PrcItemAltFk == entity.Id).Select(x => x.MdcMaterialFk).ToList();
						foreach (var match in mapping.Matches)
						{
							// get AI match MaterialEntities
							try
							{
								var materialId = int.Parse(match.Id);

								if (match.Id != entity.MdcMaterialFk.ToString() && !existAlternativeMaterials.Contains(materialId))
								{
									j++;  //id
									i++;  //top
									var originalMaterial = dictMaterialEntites[entity.MdcMaterialFk.Value];

									AIUpdateMaterialEntity displayEntity = new AIUpdateMaterialEntity();
									displayEntity.PrcItemId = entity.Id;
									displayEntity.ItemNo = entity.Itemno;
									displayEntity.ItemStatus = "Alternative";
									displayEntity.ItemDescription = entity.Description1;
									displayEntity.OriginalMaterialCode = originalMaterial.Code;
									displayEntity.OrignalMaterialDescription = originalMaterial.DescriptionInfo1.Description;
									// get AI support MatrialEntity
									var supportMaterial = materialLogic.GetItemByKey(materialId);
									displayEntity.SuggestedMaterialCode = supportMaterial.Code;
									displayEntity.SuggestedMaterialId = supportMaterial.Id;
									displayEntity.SuggestedMaterialDescription = supportMaterial.DescriptionInfo1.Description;
									displayEntity.Top = "AI Recommend Rank #" + i.ToString();
									displayEntity.IsCheckAi = true;
									displayEntity.Id = j;

									displayEntities.Add(displayEntity);
								}
							}
							catch (Exception ex)
							{
								throw new BusinessLayerException("Cannot convert Id from string to int", ex);
							}
						}
					}
				}
			}
			#endregion

			Dictionary<string, object> mainDatas = new Dictionary<string, object>();
			mainDatas["Main"] = displayEntities;
			return mainDatas;
		}

		/// <summary>
		/// Material Alternative caculate and save -- two
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="prcHeaderFk"></param>
		/// <param name="entity"></param>
		/// <param name="materialId"></param>
		/// <returns></returns>
		public bool AIUpdateMaterial(int projectId, int companyCurrencyId, int prcHeaderFk, PrcItemEntity entity, int materialId)
		{
			var materialLogic = new MaterialLogic();
			IPrcPackageLogic packageLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPrcPackageLogic>();
			IPackage2HeaderInfoProvider Package2HeaderLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPackage2HeaderInfoProvider>();

			//save's logic
			ProcurementCommonWizardLogic updateLogic = new ProcurementCommonWizardLogic();
			List<PrcReplaceNautralMaterialEntity> updateEntities = new List<PrcReplaceNautralMaterialEntity>();

			var package2header = Package2HeaderLogic.GetPackage2HeaderByPrcHeaderIds(new int[] { prcHeaderFk }).FirstOrDefault();

			if (package2header == null)
			{
				throw new InvalidOperationException(COMMON_NLS.ERR_NoSubPackageFound);
			}

			IPrcPackageEntity packageEntity = packageLogic.GetPackageById(package2header.PrcPackageFk);
			MaterialEntity material = materialLogic.GetItemByKey(materialId);

			// recaculate
			AIReplaceMaterialEntity alternative = this.CalculateTotalCurrency(packageEntity, companyCurrencyId, entity, material);
			List<AIReplaceMaterialEntity> alternatives = new List<AIReplaceMaterialEntity>();
			alternatives.Add(alternative);

			//save
			return AIReplaceItemMaterial(companyCurrencyId, projectId, alternatives);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcPackageEntity"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="prcItem"></param>
		/// <param name="material"></param>
		/// <returns></returns>
		public AIReplaceMaterialEntity CalculateTotalCurrency(IPrcPackageEntity prcPackageEntity, int companyCurrencyId, PrcItemEntity prcItem, MaterialEntity material)
		{
			var unitLogic = new UnitConversionLogic();
			var materialPriceConditionLogic = new MaterialPriceConditionLogic();

			var replaceEntity = new AIReplaceMaterialEntity();
			replaceEntity.PrcItemId = prcItem.Id;
			replaceEntity.MaterialFk = material.Id;

			decimal exchangeRate = prcPackageEntity.ExchangeRate;
			int docCurrencyFk = prcPackageEntity.CurrencyFk;
			int projectId = prcPackageEntity.ProjectFk;

			prcItem = prcItem.Clone() as PrcItemEntity; // Clone one. Do not change original prcItem.

			prcItem.MdcMaterialFk = material.Id;
			prcItem.PriceUnit = material.PriceUnit;
			prcItem.BasUomPriceUnitFk = material.BasUomPriceUnitFk;
			if (material.FactorPriceUnit.HasValue)
			{
				prcItem.FactorPriceUnit = material.FactorPriceUnit.Value;
			}

			decimal vatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, prcPackageEntity.BpdVatGroupFk);
			RecalculateFourPriceFieldsByMaterial(prcItem, material, projectId, docCurrencyFk, companyCurrencyId, exchangeRate, vatPercent, false);
			if (prcItem.BasUomFk == material.UomFk)
			{
				replaceEntity.UomConversionStatus = 1;
			}
			else if (unitLogic.CanConvert(prcItem.BasUomFk, material.UomFk))
			{
				replaceEntity.UomConversionStatus = 2;
				var conversion = unitLogic.GetConversionFactor(prcItem.BasUomFk, material.UomFk);
				prcItem.Quantity = PrcCalculationHelper.RoundingQuantityField(prcItem.Quantity * conversion);
				prcItem.MinQuantity = PrcCalculationHelper.RoundingQuantityField(material.MinQuantity * conversion);
			}
			else
			{
				replaceEntity.UomConversionStatus = 3;
			}

			prcItem.AlternativeUomFk = prcItem.BasUomFk = material.UomFk;
			prcItem.PrcPriceConditionFk = material.PrcPriceconditionFk;
			prcItem.AlternativeQuantity = prcItem.Quantity;

			var currItemPriceConditions = materialPriceConditionLogic.GetSearchList(x => (x.MdcMaterialFk == material.Id), true, null);
			RecalculatePriceCondition(prcItem, currItemPriceConditions, exchangeRate);

			// After above calculation, we can now calculate the TotalCurrency
			replaceEntity.TotalCurrency = prcItem.TotalOc;

			return replaceEntity;
		}

		/// <summary>
		/// Replace the material of procurement items with AI suggestion
		/// </summary>
		/// <param name="companyCurrencyId"></param>
		/// <param name="projectId"></param>
		/// <param name="alternatives"></param>
		/// <returns></returns>
		public bool AIReplaceItemMaterial(int companyCurrencyId, int projectId, IEnumerable<AIReplaceMaterialEntity> alternatives)
		{
			ProcurementCommonWizardLogic updateLogic = new ProcurementCommonWizardLogic();
			List<PrcReplaceNautralMaterialEntity> updateEntities = new List<PrcReplaceNautralMaterialEntity>();

			foreach (var alternative in alternatives)
			{
				PrcReplaceNautralMaterialEntity updateEntity = new PrcReplaceNautralMaterialEntity();
				updateEntity.Id = alternative.PrcItemId;
				updateEntity.Status = alternative.UomConversionStatus;
				updateEntity.MathingMaterialCode = alternative.MaterialFk;
				updateEntities.Add(updateEntity);
			}

			int fromFlg = 0; // From "package" module
			return updateLogic.ReplaceSimulationData(fromFlg, companyCurrencyId, projectId, updateEntities);
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <param name="prcStructureEnentOption"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetPrcItemsTotalLeadTimeByStructureOption(IEnumerable<int> prcHeaderIds, int? prcStructureEnentOption = null)
		{
			var prcItems = new PrcItemLogic().GetSearchList(x => prcHeaderIds.Contains(x.PrcHeaderFk));
			var materialLogic = new MaterialLogic();
			foreach (var suItem in prcItems)
			{
				decimal leadTime = 0;

				if (suItem.MdcMaterialFk.HasValue)
				{
					var material = materialLogic.GetItemByKey(suItem.MdcMaterialFk);
					if (material != null)
					{
						leadTime = material.LeadTime;
						suItem.MaterialStockFk = material.MdcMaterialStockFk;
					}
				}
				if (prcStructureEnentOption.HasValue)
				{
					switch (prcStructureEnentOption)
					{
						case 1: //Supplier Lead Time
							suItem.TotalLeadTime = leadTime;
							break;
						case 2://Supplier lead Time + Safety Lead Time
							suItem.TotalLeadTime = leadTime + suItem.SafetyLeadTime;
							break;
						case 3://Procurement Lead Time + Supplier Lead Time + Safety Lead Time
							suItem.TotalLeadTime = suItem.BufferLeadTime + leadTime + suItem.SafetyLeadTime;
							break;
						case 4://Procurement Lead Time
							suItem.TotalLeadTime = suItem.BufferLeadTime;
							break;
						case 5://Safety Lead Time
							suItem.TotalLeadTime = suItem.SafetyLeadTime;
							break;
					}
				}
				else
				{
					suItem.TotalLeadTime = 0;
				}
			}
			if (prcItems.Any())
			{
				prcItems = prcItems.OrderByDescending(o => o.TotalLeadTime);
			}
			return prcItems;
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetPrcItemsByPrcHeader(int prcHeaderId)
		{
			return GetPrcItemsByPrcHeader(new List<int> { prcHeaderId });
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetFillRelatedPrcItemsByPrcHeader(int prcHeaderId)
		{
			var items = GetPrcItemsByPrcHeader(new List<int> { prcHeaderId });
			return this.FillRelated(items);
		}

		/// <summary>
		/// get project address
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public AddressEntity GetAddressByProject(int projectId)
		{
			IProjectInfoProvider projectInfo = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
			int? addressId = projectInfo.GetAddress(projectId);
			return addressId == null
				? null
				: new AddressLogic().GetItemByKey(addressId);
		}

		/// <summary>
		/// FillRelated
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		protected override IEnumerable<PrcItemEntity> FillRelated(IEnumerable<PrcItemEntity> items)
		{
			var materialLogic = new MaterialLogic();
			bool inPrcContractModule = false;
			var firstItem = items.FirstOrDefault();
			var relatedConHeaders = new List<IContractHeaderData>();
			var conHeadersOfItems = new List<IContractHeaderData>();
			var relatedItems = new List<PrcItemEntity>();
			if (firstItem != null)
			{
				var rubricFk = GetHeaderRubric(firstItem.PrcHeaderFk);
				if (rubricFk == RubricConstant.Contract)
				{
					inPrcContractModule = true;
				}
				if (inPrcContractModule)
				{
					GetRelatedContractsAndItems(items, out conHeadersOfItems, out relatedConHeaders, out relatedItems);
				}
			}
			foreach (var item in items)
			{
				//var material = materialLogic.GetItemByKey(item.MdcMaterialFk);
				//if (material != null)
				//{
				//	//item.LeadTime = material.LeadTime;
				//	//item.MinQuantity = material.MinQuantity;
				//	//item.SellUnit = material.SellUnit;
				//	item.TotalLeadTime = item.LeadTime + item.SafetyLeadTime + item.BufferLeadTime + item.LeadTimeExtra;
				//}
				//else
				//{
				//	item.TotalLeadTime = item.SafetyLeadTime + item.BufferLeadTime;
				//}
				// change the calculation formula, requirements by eric
				item.TotalLeadTime = item.LeadTime + item.SafetyLeadTime + item.BufferLeadTime + item.LeadTimeExtra;

				if (inPrcContractModule)
				{
					SetContractGrandQuantityTotalCallOffQuantity(item, conHeadersOfItems, relatedConHeaders, relatedItems);
				}
			}

			#region basAddress
			var addressIdList = items.Where(k => k.BasAddressFk.HasValue).CollectIds(e => e.BasAddressFk.Value);
			if (!addressIdList.Any())
			{
				return items;
			}

			var addressList = new AddressLogic().GetSearchList(e => addressIdList.Contains(e.Id), addressIdList.Any());
			#endregion

			if (addressIdList == null || !addressIdList.Any())
			{
				return items;
			}

			foreach (PrcItemEntity entity in items)
			{
				if (entity.BasAddressFk.HasValue)
				{
					entity.AddressEntity = addressList.SingleOrDefault(e => e.Id == entity.BasAddressFk.Value);
				}
			}
			return items;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="SaveList"></param>
		/// <param name="updateAddress"></param>
		/// <param name="isHandleInstanceId"></param>
		/// <param name="updateGrpSet"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> Save(IEnumerable<IPrcItemEntity> SaveList, bool updateAddress = true, bool isHandleInstanceId = true, bool updateGrpSet = false)
		{
			return Save(SaveList.Select(x=> x as PrcItemEntity).ToList(), updateAddress, isHandleInstanceId, updateGrpSet);
		}

		/// <summary>
		/// Save
		/// </summary>
		/// <param name="SaveList"></param>
		/// <param name="updateAddress"></param>
		/// <param name="isHandleInstanceId"></param>
		/// <param name="updateGrpSet"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> Save(IEnumerable<PrcItemEntity> SaveList, bool updateAddress = true, bool isHandleInstanceId = true, bool updateGrpSet = false)
		{
			var blobLogic = new BlobLogic();
			SaveList = SaveList.ToList();
			var _prcItemHaveBs = SaveList.Where(x => x.BlobSpecificationToSave != null).ToList();
			var blobSpecificationIds = _prcItemHaveBs.CollectIds(e => e.BlobSpecificationToSave.Id).ToList();
			var blobSpecificationEntities = blobLogic.GetBlobs(blobSpecificationIds);

			foreach (var prcItem in SaveList.Where(x => x.BasBlobsSpecificationFk != null && x.Version == 0).ToList())
			{
				if (prcItem.BasBlobsSpecificationFk.HasValue)
				{
					var blobId = blobLogic.CopyBlob((int)prcItem.BasBlobsSpecificationFk);
					prcItem.BasBlobsSpecificationFk = blobId;
				}
			}
			foreach (var prcItem in _prcItemHaveBs)
			{
				if (prcItem.BlobSpecificationToSave.Version > 0)
				{
					var _blobEntity = blobSpecificationEntities.FirstOrDefault(e => e.Id == prcItem.BlobSpecificationToSave.Id);
					if (_blobEntity != null)
					{
						prcItem.BlobSpecificationToSave.Version = _blobEntity.Version;
					}
				}

				var blobSpecificationList = new List<SyncBlobEntity>();
				blobSpecificationList.Add(new SyncBlobEntity(prcItem.BlobSpecificationToSave, prcItem.BlobSpecificationToSave.Id > 0 ? (int?)prcItem.BlobSpecificationToSave.Id : null));
				blobLogic.UpdateBlobs(blobSpecificationList);
				prcItem.BasBlobsSpecificationFk = prcItem.BlobSpecificationToSave.Id;
			}
			if (updateAddress)
			{
				AddressLogic addressLogic = new AddressLogic();

				var addressKeysToDelete = SaveList.Where(x => x.BasAddressFk.HasValue && x.AddressEntity == null).Select(x => x.BasAddressFk).ToList();
				var addressiesToUpdate = SaveList.Where(x => x.BasAddressFk.HasValue && x.AddressEntity != null).Select(x => x.AddressEntity).ToList();

				//save all addressies in savelist where addressEntity is not null.
				if (addressiesToUpdate.Any())
				{
					addressLogic.Save(addressiesToUpdate);
				}

				//if address entity is null, set address_fk to null too.
				//get the deleted address from database.
				if (addressKeysToDelete.Any())
				{
					//update address fk in prc item as null.
					foreach (var prcItem in SaveList.Where(x => addressKeysToDelete.Contains(x.BasAddressFk)).ToList())
					{
						prcItem.BasAddressFk = null;
					}
					base.Save(SaveList);

					//delete address
					var prcItem4addressIds = GetSearchList(e => addressKeysToDelete.Contains(e.BasAddressFk)).Select(x => x.BasAddressFk);
					addressKeysToDelete = addressKeysToDelete.Except(prcItem4addressIds).ToList();//Exclude those that already have prcitems.BasAddressFk

					var addressEntities = addressLogic.GetSearchList(x => addressKeysToDelete.Contains(x.Id), true);
					if (addressEntities != null && addressEntities.Any())
					{
						addressLogic.Delete(addressLogic.GetDbModel(), addressEntities);
					}

				}

				//update address fk in prc item.
				foreach (var prcItem in SaveList.Where(x => x.AddressEntity != null).ToList())
				{
					prcItem.BasAddressFk = prcItem.AddressEntity.Id;
				}

			}

			//(1) Package: create 3 PrcItem (no item existed in database), InstanceId should be 1, 2, 3;
			//(2) Package: create 2 PrcItem (already 3 items in database), InstanceId should be 4, 5 (maxInstanceId + 1);
			//(3) Requisition to Quote: copy PrcHeader -> copy PrcItem, already set InstanceId.
			//    If here reset InstanceId again, it will result in the data of price comparison not correct!
			//    the added parameter 'isHandleInstanceId' is to deal with this case (see: wizard 'Create Quote' logic in module 'quote').
			if (isHandleInstanceId)
			{
				SetInstanceId(SaveList);
			}

			if (updateGrpSet)
			{
				var _logic = new PrcItemGrpSetLogic();
				foreach (var prcItem in SaveList)
				{
					if (prcItem.ControllinggrpsetFk != null)
					{
						prcItem.ControllinggrpsetFk = _logic.CopyBysetFk(prcItem.ControllinggrpsetFk.Value, (int)prcItem.Id);
					}
				}
			}
			//save prcItems
			base.Save(SaveList);
			return SaveList;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="SaveList"></param>
		/// <returns></returns>
		public List<PrcItemEntity> BulkSaveItems(List<PrcItemEntity> SaveList)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				this.BulkSave(ModelBuilder.DbModel, SaveList);
			}
			return SaveList;
		}

		/// <summary>
		/// to auto increment Instance Id
		/// </summary>
		/// <param name="items"></param>
		private void SetInstanceId(IEnumerable<PrcItemEntity> items)
		{
			if (!items.Any())
			{
				return;
			}

			//ToDo: Is there a problem if there are two different PrcHeaders?
			int prcHeaderFk = items.First().PrcHeaderFk;
			var maxInstanceId = 1;

			var itemsInDatabase = GetSearchList(p => p.PrcHeaderFk == prcHeaderFk).ToList();
			if (itemsInDatabase != null && itemsInDatabase.Count > 0)
			{
				maxInstanceId = itemsInDatabase.Max(e => e.InstanceId) + 1;
			}

			foreach (var prcItem in items)
			{
				if (prcItem.Version == 0 && prcItem.InstanceId == 0)
				{
					prcItem.InstanceId = maxInstanceId++;
				}
			}
		}

		/// <summary>
		/// override save
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public override PrcItemEntity Save(PrcItemEntity entity)
		{

			AddressLogic addressLogic = new AddressLogic();
			AddressEntity address = null;

			if (entity.AddressEntity != null)
			{
				addressLogic.Save(entity.AddressEntity);
			}

			//if address entity is null, set address_fk to null too.
			//get the deleted address from database.
			if (entity.AddressEntity == null && entity.BasAddressFk.HasValue && !entity.IsFromApi)
			{
				address = addressLogic.GetItemByKey(entity.BasAddressFk.Value);
				entity.BasAddressFk = null;
				addressLogic.Delete(addressLogic.GetDbModel(), address);

			}

			if (entity.BasAddressFk != null && entity.IsFromApi)
			{
				address = addressLogic.GetItemByKey(entity.BasAddressFk.Value);
				if (address != null)
				{
					entity.BasAddressFk = address.Id;
					entity.AddressEntity = address;
				}
			}
			if (entity.AddressEntity != null)
			{
				entity.BasAddressFk = entity.AddressEntity.Id;
			}
			//if updated the address need to delete  the old address, because one item refference one addess
			if (entity.Version != 0)
			{
				var oldPrcEntity = this.GetSearchList(x => x.Id == entity.Id).FirstOrDefault();
				if (oldPrcEntity != null && oldPrcEntity.BasAddressFk != entity.BasAddressFk)
				{
					var oldAddress = addressLogic.GetItemByKey(oldPrcEntity.BasAddressFk.Value);
					if (oldAddress != null)
					{
						addressLogic.Delete(addressLogic.GetDbModel(), oldAddress);
					}
				}
			}

			var headerInfoItemsFromApi = new HeaderInfo();
			var recalcItemResultFromApi = new PrcItemComplete();
			if (entity.IsFromApi && entity.needRecalculateFromApi)
			{
				headerInfoItemsFromApi = GetHeaderInfoByPrcHeader(entity.PrcHeaderFk);
				var vatGroupId = headerInfoItemsFromApi.vatGroupId;
				var exchangeRate = headerInfoItemsFromApi.exchangeRate;
				recalcItemResultFromApi = RecalculatePrcItemTotalAndGrossBeforeUpdateItemsFromApi(entity, exchangeRate, vatGroupId);
			}
			var blobLogic = new BlobLogic();
			if (entity.BasBlobsSpecificationFk != null && entity.BasBlobsSpecificationFk != 0)
			{
				var hasBlobs = this.GetSearchList(t => t.Id != entity.Id && t.BasBlobsSpecificationFk == entity.BasBlobsSpecificationFk).Any();
				if (hasBlobs)
				{
					entity.BasBlobsSpecificationFk = blobLogic.CopyBlob((int)entity.BasBlobsSpecificationFk);
				}
			}
			if (entity.BlobSpecificationToSave != null)
			{
				var hasBlobs = this.GetSearchList(t => t.Id != entity.Id && t.BasBlobsSpecificationFk == entity.BlobSpecificationToSave.Id).Any();
				if (hasBlobs || entity.BlobSpecificationToSave.Id == 0)
				{
					var blobSpecificationEntity = blobLogic.GetItemByKey(entity.BlobSpecificationToSave.Id);
					if (blobSpecificationEntity != null)
					{
						entity.BlobSpecificationToSave.Version = blobSpecificationEntity.Version;
					}

					var blobSpecificationList = new List<SyncBlobEntity>();
					blobSpecificationList.Add(new SyncBlobEntity(entity.BlobSpecificationToSave, entity.BlobSpecificationToSave.Id > 0 ? (int?)entity.BlobSpecificationToSave.Id : null));
					blobLogic.UpdateBlobs(blobSpecificationList);
					entity.BasBlobsSpecificationFk = entity.BlobSpecificationToSave.Id;
				}
			}
			//defect:124735 start
			if (entity.PrcStructureFk != null)
			{
				var PrcStructureLogic = new PrcStructureTaxCodeLogic();
				var prcItemStructure = PrcStructureLogic.GetSearchList(e => e.PrcStructureFk == entity.PrcStructureFk);
				if (prcItemStructure.Any())
				{
					int ledger = this.GetCompanyInfoProvider().GetLedgerContext(RVPARB.BusinessEnvironment.CurrentContext.ClientId);
					foreach (var item in prcItemStructure)
					{
						if (item.MdcLedgerContextFk == ledger)
						{
							entity.MdcSalesTaxGroupFk = item.MdcSalesTaxGroupFk;
							break;
						}
					}

				}
			}
			//defect:124735 end

			if (entity.IsFromApi && entity.needRecalculateFromApi && recalcItemResultFromApi.PriceConditionToSave != null && recalcItemResultFromApi.PriceConditionToSave.Any())
			{
				new PrcItemPriceConditionCalcLogic().Save(recalcItemResultFromApi.PriceConditionToSave);
			}
			base.Save(entity);
			if (entity.IsFromApi && entity.needRecalculateFromApi && headerInfoItemsFromApi.rubricFk.HasValue && headerInfoItemsFromApi.headerId.HasValue)
			{
				RecalculateTotalsAfterUpdateItemsFromApi(headerInfoItemsFromApi.rubricFk.Value, headerInfoItemsFromApi.headerId.Value);
			}
			return entity;
		}

		/// <summary>
		/// DeleteComplete
		/// </summary>
		/// <param name="entities"></param>
		public void DeleteComplete(IEnumerable<PrcItemComplete> entities)
		{
			Delete(entities.Select(x => x.PrcItem));
		}

		/// <summary>
		/// Delete
		/// </summary>
		/// <param name="entities"></param>
		public override void Delete(IEnumerable<PrcItemEntity> entities)
		{
			try
			{
				RVPARB.BusinessEnvironment.DisablePermissionCheck();
			}
			catch (InvalidOperationException)
			{
				// permission is already disabled.
			}

			var items = entities.Where(x => x != null).Distinct(new Comparer<PrcItemEntity>()).ToList();
			var subitemids = items.Select(e => e.Id).ToList();
			var subentities = this.GetSearchListComplete(e => e.PrcReplacementItemFk != null && subitemids.Contains(e.PrcReplacementItemFk.Value)).ToList();
			if (subentities.Count > 0)
			{
				Delete(subentities);
			}
			//delete address
			var addressLogic = new AddressLogic();
			var _prcHeaderLogic = new PrcHeaderLogic();

			var deliveryLogic = new PrcItemdeliveryLogic();
			var itemBlobLogic = new PrcItemblobLogic();
			var priceConditionLogic = new PrcItemPriceConditionNewLogic();
			var scopeLogic = new PrcItemScopeLogic();
			var itemInfoBlLogic = new PrcItemInfoBLLogic();
			var itemKeys = items.Select(x => x.Id);
			var prcHeaderTempIds = items.Where(l => l.PrcReplacementItemFk != null).Select(l => l.PrcHeaderFk);


			var jobTaskLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobTaskLogic>();
			foreach (var prcItem in entities.Where(p => p.JobFk.HasValue))
			{
				jobTaskLogic.Delete(prcItem.Id, null, prcItem.JobFk.Value);
			}

			//delete sub properties
			if (itemKeys.Any())
			{
				var deilveries = deliveryLogic.GetSearchList(x => itemKeys.Contains(x.PrcItemFk), true);
				var itemBlobs = itemBlobLogic.GetSearchList(x => itemKeys.Contains(x.PrcItemFk), true);
				var priceConditions = priceConditionLogic.GetSearchList(x => itemKeys.Contains(x.PrcItemFk), true);
				var scopes = scopeLogic.GetListByFilter(e => itemKeys.Contains(e.PrcItemFk));
				var mainItem2CostGroups = DuplicateObjectEntityExtension.GetMainItem2CostGroups(itemKeys);

				if (deilveries.Any())
				{
					deliveryLogic.Delete(deilveries);
				}
				if (itemBlobs.Any())
				{
					itemBlobLogic.Delete(itemBlobs);
				}
				if (priceConditions.Any())
				{
					priceConditionLogic.Delete(priceConditions);
				}
				if (scopes.Any())
				{
					scopeLogic.Delete(scopes);
				}
				if (mainItem2CostGroups.Any())
				{
					new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP").Delete(mainItem2CostGroups);
				}

				itemInfoBlLogic.DeleteCompleteEntities(itemKeys);
			}

			var addresses = items.Select(x => x.AddressEntity).Where(x => x != null && x.Version > 0);
			var prcHeaders = _prcHeaderLogic.GetSearchList(e => prcHeaderTempIds.Contains(e.Id), prcHeaderTempIds.Any());

			try
			{
				this.Delete<PrcItemEntity>(this.GetDbModel(), items);
			}
			catch (Exception e) when (LogicBaseExtension.IsSpecificException(e))
			{
				//Some special exceptions should not be handled here; they will be handled on the frontend to show more meaningful messages
				//exampple:DataUpdateVersionConflictException,DbUpdateConcurrencyException, ResourceAccessLayerException,DataUpdateConcurrencyException .
				throw; // Re-throw specific exceptions
			}
			catch (Exception ex)
			{

				Exception innerMostEx = ex;
				while (innerMostEx.InnerException != null)
				{
					innerMostEx = innerMostEx.InnerException;
				}

				string errorMsg = innerMostEx.Message;
				if (innerMostEx.Message.Contains("PRC_ITEM_FK03") && innerMostEx.Message.Contains("dbo.PRC_ITEM"))
				{
					errorMsg = string.Format(COMMON_NLS.Err_DeleteEntity_General_Reference);
				}
				if (innerMostEx.Message.Contains("PRC_ITEM_FK21") && innerMostEx.Message.Contains("dbo.RES_RESOURCE"))
				{
					errorMsg = string.Format(COMMON_NLS.Err_DeleteEntity_resource_Reference);
				}
				throw new BusinessLayerException(errorMsg, ex);
			}

			//delete addresses
			if (addresses != null && addresses.Any())
			{
				addressLogic.Delete(addressLogic.GetDbModel(), addresses);
			}
			//delete temp prcHeader about Replacement item
			if (prcHeaders != null && prcHeaders.Any())
			{
				_prcHeaderLogic.Delete(prcHeaders);
			}

			#region controllingGrpSetLogic
			PrcItemGrpSetLogic _logic = new PrcItemGrpSetLogic();
			_logic.DeleteEntities(entities);
			#endregion

			#region BasBlobsSpecificationFk
			if (entities.Any(x => x.BasBlobsSpecificationFk != null && x.BasBlobsSpecificationFk > 0))
			{
				var blobLogic = new BlobLogic();
				var delBlobSpecifications = entities.Where(x => x.BasBlobsSpecificationFk != null && x.BasBlobsSpecificationFk > 0).ToList();
				delBlobSpecifications.ForEach(t =>
				{
					blobLogic.DeleteBlobById((int)t.BasBlobsSpecificationFk);
				});
			}
			#endregion
		}



		/// <summary>
		/// get subproperties
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="items"></param>
		/// <param name="selector"></param>
		/// <returns></returns>
		private IEnumerable<T> GetFlattedSubItems<T>(IEnumerable<PrcItemComplete> items, Func<PrcItemComplete, IEnumerable<T>> selector)
		{
			IEnumerable<T> result = new List<T>();
			foreach (var item in items)
			{
				var target = selector(item);
				if (target != null && target.Any())
				{
					result = result.Concat<T>(target);
				}
			}
			result = result.Distinct();
			return result;

		}

		/// <summary>
		/// get prc item by id.
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public override PrcItemEntity GetItemByKey(int id)
		{
			return this.GetSearchList(x => x.Id == (long)id).FirstOrDefault();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="exchangeRate"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemComplete> ValidateStringFieldsAndUpdate(IEnumerable<PrcItemComplete> entities, decimal exchangeRate)
		{
			foreach (var entity in entities)
			{
				StringFieldsValidationHelper.TruncateStringFieldsIfOutOfRange(entity.PrcItem);
			}

			return Update(entities, exchangeRate);
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<PrcItemComplete> Update(IEnumerable<PrcItemComplete> entities, decimal exchangeRate,
			string headerName = "", int headerId = -1, int prjId = -1, int headerTaxCodeFk = -1, int? headerVatGroupFk = null, bool? excelImport = false)
		{
			if (!entities.Any()) { return entities; }

			try
			{
				RVPARB.BusinessEnvironment.DisablePermissionCheck();
			}
			catch (InvalidOperationException)
			{
				// permission is already disabled.
			}

			var deliveryLogic = new PrcItemdeliveryLogic();
			var itemBlobLogic = new PrcItemblobLogic();
			var priceConditionLogic = new PrcItemPriceConditionCalcLogic();
			var prcItemScopeLogic = new PrcItemScopeLogic();
			var prcItemPriceConditionLogic = new PrcItemPriceConditionLogic();

			//move it to save function and add a function to create it
			//int PrcHeaderFk = 0;
			//if (entities.First().PrcItem == null)
			//{
			//	var prcItem = GetItemByKey(entities.First().MainItemId) as PrcItemEntity;
			//	PrcHeaderFk = prcItem.PrcHeaderFk;
			//}
			//else
			//{
			//	PrcHeaderFk = entities.First().PrcItem.PrcHeaderFk;
			//}

			//var maxInstanceId = GetMaxInstanceId(PrcHeaderFk);

			var PrcItemDeliveryToDelete = GetFlattedSubItems<PrcItemdeliveryEntity>(entities, x => x.PrcItemDeliveryToDelete);
			var PrcItemblobToDelete = GetFlattedSubItems<PrcItemblobEntity>(entities, x => x.PrcItemblobToDelete);
			var PrcItemPriceConditionToDelete = GetFlattedSubItems<PrcItemPriceConditionEntity>(entities, x => x.PriceConditionToDelete);
			var PrcItemDeliveryToSave = GetFlattedSubItems<PrcItemdeliveryEntity>(entities, x => x.PrcItemDeliveryToSave);
			var PrcItemblobToSave = GetFlattedSubItems<PrcItemblobEntity>(entities, x => x.PrcItemblobToSave);
			var PrcItemPriceConditionToSave = GetFlattedSubItems<PrcItemPriceConditionEntity>(entities, x => x.PriceConditionToSave).ToList();
			var PrcItemScopeToDelete = GetFlattedSubItems<PrcItemScopeEntity>(entities, x => x.PrcItemScopeToDelete).ToList();
			var PrcItemScopeToSave = GetFlattedSubItems<PrcItemScopeComplete>(entities, x => x.PrcItemScopeToSave).ToList();
			var prcItemCostGroupToDelete = GetFlattedSubItems<MainItem2CostGroupEntity>(entities, x => x.CostGroupToDelete);
			var prcItemCostGroupToSave = GetFlattedSubItems<MainItem2CostGroupEntity>(entities, x => x.CostGroupToSave);

			deliveryLogic.Delete(PrcItemDeliveryToDelete);
			itemBlobLogic.Delete(PrcItemblobToDelete);
			var prcItemPriceConditionToDeleteIds = PrcItemPriceConditionToDelete.Select(e => e.Id);
			var PrcItemPriceConditionToDeleteList = priceConditionLogic.GetSearchList(e => prcItemPriceConditionToDeleteIds.Contains(e.Id));
			priceConditionLogic.DeleteEntities(PrcItemPriceConditionToDeleteList);
			if (PrcItemScopeToDelete != null && PrcItemScopeToDelete.Any())
			{
				prcItemScopeLogic.Delete(PrcItemScopeToDelete);
			}

			/* CostGroup */
			if (prcItemCostGroupToDelete != null && prcItemCostGroupToDelete.Any())
			{
				new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP").Delete(prcItemCostGroupToDelete);
			}

			#region controllingGrpSetLogic

			var _logic = new PrcItemGrpSetLogic();
			var _entities = _logic.Get(entities.ToList());
			var prcItems = _entities["Header"] as List<PrcItemEntity>;

			var prcItemIds = prcItems.Select(e => e.Id).ToList();
			var oldJobValues = this.GetSearchList(e => prcItemIds.Contains(e.Id) && e.JobFk.HasValue).ToDictionary(e => e.Id, e => e.JobFk.Value);

			Save(prcItems);

			// set jobtask
			var jobTaskLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobTaskLogic>();
			foreach (var prcItem in prcItems)
			{
				int oldJobId = -1;
				if (!oldJobValues.TryGetValue(prcItem.Id, out oldJobId))
				{
					oldJobId = -1;//Make sure the value is not set to another value in between
				}

				if (prcItem.JobFk.HasValue && prcItem.JobFk.Value != oldJobId)
				{//Job Id has changed, adjust the connection
					jobTaskLogic.CreateJobTaskByJob(prcItems.First(p => p.JobFk.HasValue).JobFk.Value, prcItem.Id, prcItem.Quantity, null, prcItem.PrcHeaderFk, null);//Save new
				}

				if (oldJobId > 0 && (!prcItem.JobFk.HasValue || prcItem.JobFk.Value != oldJobId))
				{
					jobTaskLogic.Delete(prcItem.Id, null, oldJobId);
				}
			}

			var AfterDeleteset = _entities["AfterDeleteset"] as IEnumerable<int>;
			if (AfterDeleteset.Any() && AfterDeleteset != null)
			{
				_logic.DeleteSet(AfterDeleteset);
			}
			#endregion
			deliveryLogic.Update(PrcItemDeliveryToSave);
			itemBlobLogic.Save(PrcItemblobToSave);
			prcItemScopeLogic.Update(PrcItemScopeToSave);

			var itemIds = entities.Where(e => e.PrcItem != null).Select(e => e.PrcItem.Id);
			var scopeDetailPcs = new PrcItemScopeDetailPcLogic().ReCalculateScopeDetailPriceCondition(itemIds, headerName, headerId, prjId, exchangeRate);
			foreach (var prcItemScope in PrcItemScopeToSave)
			{
				var scopeDetails = prcItemScope.PrcItemScopeDetailToSave;
				if (null != scopeDetails)
				{
					foreach (var scopeDetail in scopeDetails)
					{
						if (null != scopeDetail.PrcItemScopeDetail)
						{
							scopeDetail.PrcItemScopeDetailPcToSave = scopeDetailPcs.Where(e => e.PrcItemScopeDetailFk == scopeDetail.PrcItemScopeDetail.Id);
						}
					}
				}
			}

			priceConditionLogic.Save(PrcItemPriceConditionToSave);

			/* CostGroup */
			if (prcItemCostGroupToSave != null && prcItemCostGroupToSave.Any())
			{
				new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP").Save(prcItemCostGroupToSave);
			}

			var itemids = entities.Select(x => (long)x.MainItemId).ToList();
			var itemPriceConditions = priceConditionLogic.GetSearchList(x => itemids.Contains(x.PrcItemFk), itemids.Any(), null);

			var priceConditions = new PrcItemPriceConditionNewLogic().GetHaveLeadTimeFormulaPriceConditions(itemids);

			//#128601 sysc calculate price condition and network slow ,the version will be error
			var dbPrcItems = this.GetSearchList(e => itemids.Contains(e.Id)).ToList();
			var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
			var headerInfoDic = new Dictionary<int, HeaderInfo>();
			var vatEvaluator = new VatEvaluator();
			//map the reculated price conditions to save data.
			foreach (var entity in entities)
			{
				var dbPrcItem = dbPrcItems.FirstOrDefault(e => e.Id == entity.Id);
				if (null != dbPrcItem && entity.PrcItem != null && (dbPrcItem.Version > entity.PrcItem.Version))
				{
					entity.PrcItem.Version = dbPrcItem.Version;
				}
				//recalculate items
				var currItemPriceConditions = itemPriceConditions.Where(x => x.MainItemId == entity.MainItemId).ToList();
				//TODO-Young Optimization is needed, taking 0.2 seconds to complete
				var result = RecalculatePriceCondition(entity.PrcItem, currItemPriceConditions, exchangeRate, headerName, headerId, prjId);

				bool hasPrcItemAndIsNotInputTotal = entity.PrcItem != null && entity.PrcItem.IsInputTotal != true;
				bool hasNoPrcItemButHasPriceCondi = entity.PrcItem == null && (
						(entity.PriceConditionToSave != null && entity.PriceConditionToSave.Any()) ||
						(entity.PriceConditionToDelete != null && entity.PriceConditionToDelete.Any()));
				if (hasPrcItemAndIsNotInputTotal || hasNoPrcItemButHasPriceCondi)
				{
					var relPrcItem = entity.PrcItem != null ? entity.PrcItem : dbPrcItems.FirstOrDefault(e => e.Id == entity.MainItemId);
					if (relPrcItem != null)
					{
						if (hasNoPrcItemButHasPriceCondi)
						{
							RecalculatePriceExtra(currItemPriceConditions, ref relPrcItem);
						}
						var vatPercent = vatEvaluator.EvaluateVatPercent(relPrcItem.MdcTaxCodeFk, headerVatGroupFk);
						RecalculatePrcItemTotalOverPriceOrGross(relPrcItem, isCalculateOverGross, vatPercent, headerInfoDic);
						var hasLeadTimePriceConditions = priceConditions.Where(e => e.PrcItemFk == relPrcItem.Id);
						if (hasLeadTimePriceConditions.Any())
						{
							relPrcItem.HasLeadTimeFormula = true;
						}
						else
						{
							relPrcItem.HasLeadTimeFormula = false;
						}
						if (!(entity.PrcItem is PrcItemEntity))
						{
							entity.PrcItem = relPrcItem;
						}
					}
				}
				else if (entity.PrcItem != null && entity.PrcItem.IsInputTotal == true)
				{
					entity.PrcItem.IsInputTotal = false;
				}
				entity.PriceConditionToSave = currItemPriceConditions.Select(x => x as PrcItemPriceConditionEntity).ToList();
				if (entity.BulkEditPriceConditionToSave != null)
				{
					prcItemPriceConditionLogic.BulkEditToSave(entity);
					var currentPrcItem = entity.PrcItem;
					RecalculatePriceExtra(entity.PriceConditionToSave, ref currentPrcItem);
					var vatPercent = vatEvaluator.EvaluateVatPercent(entity.PrcItem.MdcTaxCodeFk, headerVatGroupFk);
					RecalculatePrcItemTotalOverPriceOrGross(entity.PrcItem, isCalculateOverGross, vatPercent, headerInfoDic);
				}
			}
			//save recalucated items
			PrcItemPriceConditionToSave = GetFlattedSubItems<PrcItemPriceConditionEntity>(entities, x => x.PriceConditionToSave).ToList();
			priceConditionLogic.Save(PrcItemPriceConditionToSave);
			SetPrcItemsIsContracted(prcItems);
			Save(prcItems.Where(x => x != null)); //save recalcuated items

			return entities;
		}

		/// <summary>
		///
		/// </summary>
		public PriceConditionCalculationEntity RecalculatePriceCondition(PrcItemEntity item, IEnumerable<IMaterialPriceConditionEntity> priceConditions, decimal exchangeRate,
			string headerName = "", int headerId = -1, int prjId = -1)
		{
			if (item == null || priceConditions == null || !priceConditions.Any())
			{
				if (item != null)
				{
					item.PriceExtraOc = 0;
					item.PriceExtra = 0;
				}
				return null;
			}

			var priceConditionCalcLogic = new PrcItemPriceConditionCalcLogic();

			var recalculator = new PriceConditionCalculationEntity();

			recalculator.PriceConditions = priceConditions;
			recalculator.MainItem = item;
			recalculator.ExchangeRate = exchangeRate;
			recalculator.HeaderName = headerName;
			recalculator.HeaderId = headerId;
			recalculator.ProjectFk = prjId;
			//TODO-Young Optimization is needed, taking 0.2 seconds to complete
			var result = priceConditionCalcLogic.RecalculateNew(recalculator);

			item.PriceExtraOc = priceConditions.Where(x => x.IsPriceComponent && x.IsActivated).Sum(x => x.TotalOc);
			item.PriceExtra = priceConditions.Where(x => x.IsPriceComponent && x.IsActivated).Sum(x => x.Total);

			return result;
		}

		/// <summary>
		/// Note: provides better performance.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="isCalculateOverGross"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="headerExchangeRate"></param>
		/// <param name="vatEvaluator"></param>
		/// <returns></returns>
		public List<PrcItemEntity> RecalculatePrcItemsTotalOverPriceOrGross(List<PrcItemEntity> prcItems, bool isCalculateOverGross, int? headerVatGroupFk, decimal headerExchangeRate, VatEvaluator vatEvaluator)
		{
			return isCalculateOverGross
				? RecalculatePrcItemsTotalOverGross(prcItems, headerVatGroupFk, headerExchangeRate, vatEvaluator)
				: RecalculatePrcItemsTotalOverPrice(prcItems, headerVatGroupFk, vatEvaluator);
		}

		/// <summary>
		/// Note: provides better performance.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="headerExchangeRate"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="vatEvaluator"></param>
		/// <returns></returns>
		private List<PrcItemEntity> RecalculatePrcItemsTotalOverGross(List<PrcItemEntity> prcItems, int? headerVatGroupFk, decimal headerExchangeRate, VatEvaluator vatEvaluator)
		{
			if (prcItems == null || prcItems.Count == 0)
			{
				return prcItems;
			}

			foreach (var prcItem in prcItems)
			{
				var vatPercent = vatEvaluator.EvaluateVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk);
				RecalculatePrcItemTotalOverGross(prcItem, vatPercent, headerExchangeRate);
			}
			return prcItems;
		}

		/// <summary>
		/// Note: provides better performance.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="vatEvaluator"></param>
		/// <returns></returns>
		private List<PrcItemEntity> RecalculatePrcItemsTotalOverPrice(List<PrcItemEntity> prcItems, int? headerVatGroupFk, VatEvaluator vatEvaluator)
		{
			if (prcItems == null || prcItems.Count == 0)
			{
				return prcItems;
			}

			foreach (var prcItem in prcItems)
			{
				var vatPercent = vatEvaluator.EvaluateVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk);
				RecalculatePrcItemTotalOverPrice(prcItem, vatPercent);
			}
			return prcItems;
		}

		/// <summary>
		/// Caution: poor performance with large data. Please try to use <see cref="RecalculatePrcItemTotalOverPriceOrGross"/> instead.
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="providedHeaderInfo"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculatePrcItemTotal(PrcItemEntity prcItem, int? headerVatGroupFk, HeaderInfo providedHeaderInfo = null)
		{
			var headerInfo = providedHeaderInfo == null ? GetHeaderInfoByPrcHeader(prcItem.PrcHeaderFk) : providedHeaderInfo;
			var rate = headerInfo.exchangeRate;
			var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
			var vatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk);
			if (isCalculateOverGross)
			{
				RecalculatePrcItemTotalOverGross(prcItem, vatPercent, rate);
			}
			else
			{
				RecalculatePrcItemTotalOverPrice(prcItem, vatPercent);
			}
			return prcItem;
		}

		/// <summary>
		/// Note: provides better performance. Not access db.
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="isCalculateOverGross"></param>
		/// <param name="vatPercent"></param>
		/// <param name="headerInfoDic"></param>
		public void RecalculatePrcItemTotalOverPriceOrGross(PrcItemEntity prcItem, bool isCalculateOverGross, decimal vatPercent, Dictionary<int, HeaderInfo> headerInfoDic)
		{
			if (isCalculateOverGross)
			{
				if (!headerInfoDic.TryGetValue(prcItem.PrcHeaderFk, out var headerInfo))
				{
					headerInfo = GetHeaderInfoByPrcHeader(prcItem.PrcHeaderFk);
					headerInfoDic[prcItem.PrcHeaderFk] = headerInfo;
				}
				var headerExchangeRate = headerInfo.exchangeRate;
				RecalculatePrcItemTotalOverGross(prcItem, vatPercent, headerExchangeRate);
			}
			else
			{
				RecalculatePrcItemTotalOverPrice(prcItem, vatPercent);
			}
		}

		/// <summary>
		/// Note: provides better performance. Not access db.
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="vatPercent"></param>
		/// <param name="rate"></param>
		private void RecalculatePrcItemTotalOverGross(PrcItemEntity prcItem, Decimal vatPercent, Decimal rate)
		{
			const bool isCalculateOverGross = true;

			if (prcItem.PriceGrossOc == 0 || prcItem.PriceGross == 0)
			{
				prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
				prcItem.PriceGross = PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
			}
			prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);
			prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross, rate);
			prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
			prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);

			if (NeedsCalculateTotalValues(prcItem))
			{
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			else
			{
				prcItem.Total = 0;
				prcItem.TotalOc = 0;
				prcItem.TotalGross = 0;
				prcItem.TotalGrossOc = 0;
				prcItem.TotalNoDiscount = 0;
				prcItem.TotalCurrencyNoDiscount = 0;
			}
		}

		/// <summary>
		/// Note: provides better performance. Not access db.
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="vatPercent"></param>
		private void RecalculatePrcItemTotalOverPrice(PrcItemEntity prcItem, Decimal vatPercent)
		{
			const bool isCalculateOverGross = false;

			prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
			prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);
			prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross);
			prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);

			if (NeedsCalculateTotalValues(prcItem))
			{
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			else
			{
				prcItem.Total = 0;
				prcItem.TotalOc = 0;
				prcItem.TotalGross = 0;
				prcItem.TotalGrossOc = 0;
				prcItem.TotalNoDiscount = 0;
				prcItem.TotalCurrencyNoDiscount = 0;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <returns></returns>
		private bool NeedsCalculateTotalValues(PrcItemEntity prcItem)
		{
			return !(prcItem.BasItemTypeFk == 2 || prcItem.BasItemType2Fk == 3 || prcItem.BasItemType2Fk == 5 || prcItem.PriceUnit == 0);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="material"></param>
		/// <param name="projectId"></param>
		/// <param name="docCurrencyFk"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="vatPercent"></param>
		/// <param name="calculatePriceGrossFields"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculateFourPriceFieldsByMaterial(PrcItemEntity prcItem, MaterialEntity material, int? projectId, int docCurrencyFk, int companyCurrencyId, decimal exchangeRate, decimal vatPercent = 0m, bool calculatePriceGrossFields = true)
		{
			var materialCurrencyFk = material.BasCurrencyFk;
			if (materialCurrencyFk.HasValue)
			{
				ResetPriceByMaterial(prcItem, companyCurrencyId, docCurrencyFk, projectId, material, exchangeRate, vatPercent);

				if (calculatePriceGrossFields)
				{
					prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
					prcItem.PriceGross = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceGrossOc, exchangeRate);
				}
			}
			return prcItem;
		}

		/// <summary>
		/// update Price, Charge, Discount, DiscountAbsolute fields by material
		/// </summary>
		/// <param name="item"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="headerCurrencyFk"></param>
		/// <param name="projectId"></param>
		/// <param name="material"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="vatPercent"></param>
		/// <returns></returns>
		public void ResetPriceByMaterial(PrcItemEntity item, int companyCurrencyId, int headerCurrencyFk, int? projectId, MaterialEntity material, decimal exchangeRate, decimal vatPercent)
		{
			ResetPriceByMaterial(
				item, companyCurrencyId, headerCurrencyFk, projectId,
				material.BasCurrencyFk, material.ListPrice, material.Charges, material.Discount,
				exchangeRate, vatPercent);
		}

		/// <summary>
		/// update Price, Charge, Discount, DiscountAbsolute fields by material priceList
		/// </summary>
		/// <param name="item"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="headerCurrencyFk"></param>
		/// <param name="projectId"></param>
		/// <param name="priceList"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="vatPercent"></param>
		/// <returns></returns>
		public void ResetPriceByMaterialPriceList(PrcItemEntity item, int companyCurrencyId, int headerCurrencyFk, int? projectId, MaterialPriceListEntity priceList, decimal exchangeRate, decimal vatPercent)
		{
			ResetPriceByMaterial(
				item, companyCurrencyId, headerCurrencyFk, projectId,
				priceList.CurrencyFk, priceList.ListPrice, priceList.Charges, priceList.Discount,
				exchangeRate, vatPercent);
		}

		/// <summary>
		///  update Price, Charge, Discount, DiscountAbsolute fields
		/// </summary>
		/// <param name="item"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="headerCurrencyFk"></param>
		/// <param name="projectId"></param>
		/// <param name="materialCurrencyFk"></param>
		/// <param name="materialListPrice"></param>
		/// <param name="materialCharges"></param>
		/// <param name="materialDiscount"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="vatPercent"></param>
		private void ResetPriceByMaterial(PrcItemEntity item, int companyCurrencyId, int headerCurrencyFk, int? projectId, int? materialCurrencyFk, decimal materialListPrice, decimal materialCharges, decimal materialDiscount, decimal exchangeRate, decimal vatPercent)
		{
			if (companyCurrencyId == materialCurrencyFk || !materialCurrencyFk.HasValue)
			{
				item.Price = PrcCalculationHelper.CalculatePriceByMdcListPrice(materialListPrice);
				item.PriceOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.Price, exchangeRate);
				item.Charge = PrcCalculationHelper.CalculateChargeByMdcCharge(materialCharges);
				item.ChargeOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.Charge, exchangeRate);
			}
			else
			{
				decimal? rate = new ProcurementCommonExchangeRateLogic().GetExchangeRateOc(headerCurrencyFk, materialCurrencyFk.Value, projectId);
				item.PriceOc = (!rate.HasValue || rate.Value == 0m) ? 0m : PrcCalculationHelper.CalculatePriceByMdcListPrice(materialListPrice, rate.Value);
				item.Price = exchangeRate == 0m ? 0m : PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.PriceOc, exchangeRate);
				item.ChargeOc = (!rate.HasValue || rate.Value == 0m) ? 0m : PrcCalculationHelper.CalculatePriceByMdcListPrice(materialCharges, rate.Value);
				item.Charge = exchangeRate == 0m ? 0m : PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.ChargeOc, exchangeRate);
			}
			RecalculatePricGrossAndPriceGrossOcByPrice(item, null, vatPercent);

			item.Discount = materialDiscount;
			UpdateDiscountNAbsoluteAfterPriceOrDiscountUpdate(item, vatPercent, false);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="providedVatPercent"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculatePricGrossAndPriceGrossOcByPrice(PrcItemEntity prcItem, int? headerVatGroupFk, decimal? providedVatPercent = null)
		{
			var vatPercent = providedVatPercent ?? (new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk));

			prcItem.PriceGross = PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
			prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
			return prcItem;
		}

		/// <summary>
		/// Caution: poor performance with large data. Please try to use <see cref="RecalculatePrcItemsTotalOverPriceOrGross"/> instead.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <returns></returns>
		public List<PrcItemEntity> RecalculatePrcItemsTotal(List<PrcItemEntity> prcItems, int? headerVatGroupFk)
		{
			foreach (var prcItem in prcItems)
			{
				RecalculatePrcItemTotal(prcItem, headerVatGroupFk);
			}
			return prcItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="validTime"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="headerName"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculatePrcItemTotalAndGross(PrcItemEntity prcItem = null, int? headerVatGroupFk = null, string headerName = "",DateTime? validTime = null)
		{
			if (prcItem == null)
			{
				return prcItem;
			}
			var headerInfo = GetHeaderInfoByPrcHeader(prcItem.PrcHeaderFk);
			var rate = headerInfo.exchangeRate;
			var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
			var vatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk, validTime);
			RecalculateOneItemTotalAndGross(prcItem, isCalculateOverGross, vatPercent, rate, headerName);
			return prcItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="isCalculateOverGross"></param>
		/// <param name="vatPercent"></param>
		/// <param name="rate"></param>
		/// <param name="headerName"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculateOneItemTotalAndGross(PrcItemEntity prcItem, bool isCalculateOverGross, decimal vatPercent, decimal rate, string headerName = "")
		{
			if (prcItem == null)
			{
				return prcItem;
			}

			if (isCalculateOverGross)
			{
				if (prcItem.PriceGross == 0 && prcItem.PriceGrossOc == 0 && prcItem.Price != 0 && prcItem.PriceOc != 0)
				{
					prcItem.PriceGross = PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
					prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
				}
				else
				{
					prcItem.Price = PrcCalculationHelper.CalculatePriceByPriceGross(prcItem.PriceGross, vatPercent);
					prcItem.PriceOc = PrcCalculationHelper.CalculatePriceOcByPriceGrossOc(prcItem.PriceGrossOc, vatPercent);
				}
				prcItem.DiscountAbsoluteOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByGrossOc(prcItem.DiscountAbsoluteGrossOc, vatPercent);
				prcItem.DiscountAbsolute = PrcCalculationHelper.CalculateDiscountAbsoluteByGross(prcItem, vatPercent);
				prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross, rate);
				prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			else
			{
				prcItem.PriceGross = PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
				prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
				prcItem.DiscountAbsoluteGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByOc(prcItem.DiscountAbsoluteOc, vatPercent);
				prcItem.DiscountAbsoluteGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByDA(prcItem.DiscountAbsolute, vatPercent);
				prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			if (prcItem.BasItemTypeFk == 2 || prcItem.BasItemType2Fk == 3 || prcItem.BasItemType2Fk == 5 || prcItem.PriceUnit == 0)
			{
				prcItem.Total = 0;
				prcItem.TotalOc = 0;
				prcItem.TotalGross = 0;
				prcItem.TotalGrossOc = 0;
				prcItem.TotalNoDiscount = 0;
				prcItem.TotalCurrencyNoDiscount = 0;
			}
			return prcItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <param name="headerName"></param>
		/// <param name="headerRate"></param>
		/// <param name="isOverGross"></param>
		/// <param name="prcHeaderFk"></param>
		/// <returns></returns>
		public List<PrcItemEntity> RecalculatePrcItemsTotalAndGross(List<PrcItemEntity> prcItems, int? headerVatGroupFk, string headerName = "", decimal? headerRate = null, bool? isOverGross = null, int? prcHeaderFk = null)
		{
			if (prcItems == null || !prcItems.Any())
			{
				return prcItems;
			}
			var vatEvaluator = new VatEvaluator();
			var prcHeaderId = prcHeaderFk.HasValue ? prcHeaderFk.Value : prcItems.First().PrcHeaderFk;
			var rate = headerRate.HasValue ? headerRate.Value : GetHeaderInfoByPrcHeader(prcHeaderId).exchangeRate;
			var isCalculateOverGross = this.CalculateOverGross(isOverGross);
			foreach (var prcItem in prcItems)
			{
				var vatPercent = vatEvaluator.EvaluateVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk);
				RecalculateOneItemTotalAndGross(prcItem, isCalculateOverGross, vatPercent, rate, headerName);
			}

			return prcItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="headerVatGroupFk"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculatePreTaxValueAfterChangeVatPercent(PrcItemEntity prcItem, int? headerVatGroupFk)
		{
			var headerInfo = GetHeaderInfoByPrcHeader(prcItem.PrcHeaderFk);
			var rate = headerInfo.exchangeRate;
			var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
			var vatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, headerVatGroupFk);
			if (isCalculateOverGross)
			{
				prcItem.Price = PrcCalculationHelper.CalculatePriceByPriceGross(prcItem.PriceGross, vatPercent);
				prcItem.PriceOc = PrcCalculationHelper.CalculatePriceOcByPriceGrossOc(prcItem.PriceGrossOc, vatPercent);
				prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross, rate);
				prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			else
			{
				prcItem.PriceGross = PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
				prcItem.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
				prcItem.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.Total = PrcCalculationHelper.CalculateTotal(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalOc = PrcCalculationHelper.CalculateTotalOc(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGross = PrcCalculationHelper.CalculateTotalGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
			}
			if (prcItem.BasItemTypeFk == 2 || prcItem.BasItemType2Fk == 3 || prcItem.BasItemType2Fk == 5 || prcItem.PriceUnit == 0)
			{
				prcItem.Total = 0;
				prcItem.TotalOc = 0;
				prcItem.TotalGross = 0;
				prcItem.TotalGrossOc = 0;
				prcItem.TotalNoDiscount = 0;
				prcItem.TotalCurrencyNoDiscount = 0;
			}
			return prcItem;
		}

		/// <summary>
		/// GetNextId
		/// </summary>
		/// <returns></returns>
		public override string GetSequenceName()
		{
			return "PRC_ITEM";
		}

		/// <summary>
		/// Get latest currency rate
		/// </summary>
		/// <param name="currencyForeignId">currencyForeignId</param>
		/// <returns>Lastest  Currency Rate</returns>
		public decimal GetLatestCurrencyRate(int currencyForeignId)
		{
			decimal currencyRate = 0;
			int currencyHomeId = -1;

			var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var companyEntity = new BasicsCompanyLogic().GetCompanyById(companyId);
			currencyHomeId = companyEntity.CurrencyFk;

			if (currencyHomeId != currencyForeignId)
			{
				//var currencyRateLogic = new CurrencyRateLogic();
				//var currencyRateEntity = currencyRateLogic.GetLastestCurrencyRate(currencyHomeId, currencyForeignId);
				//currencyRate = currencyRateEntity.Rate;
			}
			else
			{
				currencyRate = 1;
			}

			return currencyRate;
		}



		/// <summary>
		/// Update the status
		/// </summary>
		/// <param name="identification"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public PrcItemEntity UpdateStatus(IStatusIdentifyable identification, int statusId)
		{
			var id = identification.Id;
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var item = dbcontext.Entities<PrcItemEntity>().Where(e => e.Id == id).FirstOrDefault();
				if (item == null)
				{
					return null;
				}
				if (identification.StatusField == "PrjChangeStatusFk")
				{
					item.PrjChangeStatusFk = statusId;
				}
				else
				{
					item.PrcItemstatusFk = statusId;
				}

				return dbcontext.Save(item);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="toSave"></param>
		/// <param name="toDelete"></param>
		/// <returns></returns>
		public bool Validator(IEnumerable<PrcItemComplete> toSave, IEnumerable<PrcItemEntity> toDelete)
		{
			if (!toSave.Any())
			{
				return true;
			}
			//TODO Optimization is needed, 2000 item taking 325 seconds to complete
			var allItems = GetAllItemByPrcHeader(toSave.Select(e => e.PrcItem).Where(e => e != null).ToList(), e => !(toDelete.Any(l => l.Id == e.Id) || toSave.Any(l => l.PrcItem.Id == e.Id)));

			// todo: The logic of following 2 lines are quite strange, not sure why any is used rather than foreach and validation result is ignored. currently it is only be used by procuremrnt quote module, need them to fix it
			//The code should be unique.
			allItems.Any(list => ValidationHelper.IsUnique(COMMON_NLS.ItemNo_should_be_unique, list, toSave.Select(e => e.PrcItem), (x, y) => x.Id == y.Id, (x, y) => x.Itemno == y.Itemno));

			//The total quantity for all the replacement items are not same as the quantity of the old item
			allItems.Any(list => ValidationHelper.IsSum(COMMON_NLS.Quantity_not_same_old_item, list, list.Where(parent => !parent.PrcReplacementItemFk.HasValue), (p, l) => l.PrcReplacementItemFk == p.Id, e => e.Quantity));

			return true;
		}


		private IEnumerable<IEnumerable<PrcItemEntity>> GetAllItemByPrcHeader(IEnumerable<PrcItemEntity> toSave, Func<PrcItemEntity, bool> filter)
		{
			var oldItemIds = toSave.Where(e => e.PrcReplacementItemFk.HasValue).Select(e => e.PrcReplacementItemFk);
			var prcIds = GetSearchList(e => oldItemIds.Contains(e.Id), oldItemIds.Any()).Concat(toSave.Where(e => !e.PrcReplacementItemFk.HasValue)).Select(e => e.PrcHeaderFk).ToList();
			var allItems = GetSearchList(e => prcIds.Contains(e.PrcHeaderFk) && !e.PrcReplacementItemFk.HasValue, prcIds.Any()).Where(filter).ToList();
			var prcItemIds = allItems.Select(e => e.Id);
			var allReplacementItems = GetSearchList(e => e.PrcReplacementItemFk.HasValue && prcItemIds.Contains(e.PrcReplacementItemFk.Value), prcItemIds.Any()).Where(filter).ToList();

			return prcIds.Select(e =>
			{
				var items = allItems.Where(l => l.PrcHeaderFk == e).Concat(toSave.Where(x => x.PrcHeaderFk == e && !x.PrcReplacementItemFk.HasValue));
				var replacementFiter = new Func<PrcItemEntity, bool>(x => items.Any(y => y.Id == x.PrcReplacementItemFk));
				var replacementItems = allReplacementItems.Where(replacementFiter).Concat(toSave.Where(replacementFiter));
				return items.Concat(replacementItems).ToList();
			}).ToList();
		}

		/// <summary>
		/// Get items.
		/// </summary>
		public List<PrcItemEntity> GetItemsByIds(List<long> ids)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var prcItems = dbcontext.Entities<PrcItemEntity>().Include(e => e.PrcHeaderEntity).Where(e => ids.Contains(e.Id));

				return prcItems.ToList();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> GetItemsByIds(IEnumerable<long> ids)
		{
			return GetItemsByIds(ids.ToList());
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="packageId"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetItemByPackageId(int packageId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var prcItems = dbcontext.Entities<PrcItemEntity>().Include(e => e.PrcHeaderEntity).Where(e => e.PrcPackageFk == packageId);

				return prcItems.ToList();
			}
		}

		/// <summary>
		/// Get items.
		/// </summary>
		public override IEnumerable<PrcItemEntity> GetItemsByKey(IEnumerable<int?> ids)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				List<long> ids2 = ids.Where(e => e.HasValue == true).ToList().ConvertAll(i => (long)i);
				return dbcontext.Entities<PrcItemEntity>().Include(e => e.PrcHeaderEntity).Where(e => ids2.Contains(e.Id)).ToList();
			}
		}

		/// <summary>
		/// Get items.
		/// </summary>
		public IEnumerable<IPrcItemEntity> GetItemsFromPackage(List<int> projectPackegeIds)
		{
			IPrcPackage2HeaderLogic Package2HeaderLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPrcPackage2HeaderLogic>();
			IEnumerable<int> PrcHeaderFkIds = Package2HeaderLogic.GetPrcPackage2HeaderIds();
			return this.GetSearchList(e => PrcHeaderFkIds.Contains(e.PrcHeaderFk) && e.PrcPackageFk.HasValue).Where(e => projectPackegeIds.Contains(e.PrcPackageFk.Value));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="isConsiderMaterial"></param>
		/// <param name="PriceConditionIds"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> Recalculate(int prcHeaderId, decimal exchangeRate, bool isConsiderMaterial = false, List<int> PriceConditionIds = null)
		{
			var list = this.GetSearchListComplete(e => e.PrcHeaderFk == prcHeaderId, true);
			if (!list.Any())
			{
				return list;
			}
			var priceConditionLogic = new PrcItemPriceConditionCalcLogic();
			var priceConditions = new List<PrcItemPriceConditionEntity>();
			var headerInfo = GetHeaderInfoByPrcHeader(prcHeaderId);
			if (isConsiderMaterial)
			{
				foreach (var item in list)
				{
					var itemPriceConditions = priceConditionLogic.Reload(item, exchangeRate, PriceConditionIds);
					var currItemPriceConditions = itemPriceConditions.PriceConditions;

					var result = RecalculatePriceCondition(item, currItemPriceConditions, exchangeRate);
					priceConditions.AddRange(currItemPriceConditions.Select(x => x as PrcItemPriceConditionEntity));
				}
				RecalculatePrcItemsTotalAndGross(list.ToList(), headerInfo.vatGroupId, "", headerInfo.exchangeRate);
				priceConditionLogic.Save(priceConditions);

			}
			else
			{
				#region old logic
				var itemids = list.Select(e => e.Id);
				var itemPriceConditions = (IEnumerable<IMaterialPriceConditionEntity>)priceConditionLogic.GetSearchList(x => itemids.Contains(x.PrcItemFk), itemids.Any());
				//map the reculated price conditions to save data.
				foreach (var entity in list)
				{
					//recalculate items
					var currItemPriceConditions = itemPriceConditions.Where(x => x.MainItemId == entity.Id);
					var result = RecalculatePriceCondition(entity, currItemPriceConditions, exchangeRate);
					priceConditions.AddRange(currItemPriceConditions.Select(x => x as PrcItemPriceConditionEntity));
				}
				RecalculatePrcItemsTotalAndGross(list.ToList(), headerInfo.vatGroupId, "", headerInfo.exchangeRate);
				//save recalucated items
				priceConditionLogic.Save(priceConditions);
				#endregion
			}
			return Save(list);
		}

		/// <summary>
		///  For #93642 - replace material price  from price version
		/// Used by feature of place order in ticket system.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="materialCondIds"></param>
		/// <param name="priceListCondIds"></param>
		/// <param name="isFromMaterial"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemPriceConditionEntity> Recalculate(IEnumerable<PrcItemEntity> prcItems, decimal exchangeRate, IEnumerable<int> materialCondIds = null, IEnumerable<int> priceListCondIds = null, bool isFromMaterial = false)
		{
			var priceConditions = new List<PrcItemPriceConditionEntity>();

			if (prcItems == null || !prcItems.Any())
			{
				return priceConditions;
			}

			var priceConditionLogic = new PrcItemPriceConditionCalcLogic();

			foreach (var prcItem in prcItems)
			{
				//recalculate items
				var itemPriceConditions = priceConditionLogic.Reload(prcItem, exchangeRate, materialCondIds, prcItem.MdcMatPriceListFk, priceListCondIds, isFromMaterial);
				var currItemPriceConditions = itemPriceConditions.PriceConditions;
				var result = RecalculatePriceCondition(prcItem, currItemPriceConditions, exchangeRate);
				priceConditions.AddRange(currItemPriceConditions.Select(x => x as PrcItemPriceConditionEntity));
			}

			return priceConditions;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="prcHeaderFk"></param>
		/// <param name="configurationFk"></param>
		/// <param name="projectFk"></param>
		/// <param name="packageHeaderId"></param>
		/// <returns></returns>
		public PrcItemEntity ReGeneratePrcItem(PrcItemEntity item, int prcHeaderFk, int configurationFk, int projectFk, int packageHeaderId)
		{
			var entity = CreateNew(prcHeaderFk, configurationFk, projectFk, item.Itemno, true);
			entity.PrcPackageFk = packageHeaderId;
			entity.Description1 = item.Description1;
			entity.Description2 = item.Description2;
			entity.PrcStructureFk = item.PrcStructureFk;
			entity.Specification = item.Specification;
			entity.MdcMaterialFk = item.MdcMaterialFk;
			entity.PriceUnit = item.PriceUnit;
			entity.BasUomPriceUnitFk = item.BasUomPriceUnitFk;
			entity.FactorPriceUnit = item.FactorPriceUnit;
			entity.AlternativeQuantity = entity.Quantity = item.Quantity;
			entity.MdcTaxCodeFk = item.MdcTaxCodeFk;
			entity.PriceExtra = item.PriceExtra;
			entity.PrcPriceConditionFk = item.PrcPriceConditionFk;
			entity.Hasdeliveryschedule = item.Hasdeliveryschedule;
			if (item.BasUomFk > 0)
			{
				var uomEntity = new UomLogic().GetItemByKey(item.BasUomFk);
				entity.BasUom = uomEntity.Uom;
				entity.AlternativeUomFk = entity.BasUomFk = item.BasUomFk;
			}
			if (!string.IsNullOrEmpty(item.BasUom))
			{
				var uomEntites = new UomLogic().GetSearchList(o => o.Uom == item.BasUom);
				if (uomEntites.Any() && uomEntites.FirstOrDefault() != null)
				{
					entity.AlternativeUomFk = entity.BasUomFk = uomEntites.FirstOrDefault().Id;
				}
				entity.BasUom = item.BasUom;
			}
			if (item.BasBlobsSpecificationFk.HasValue)
			{
				var blobLogic = new BlobLogic();
				var blobId = blobLogic.CopyBlob((int)item.BasBlobsSpecificationFk);
				entity.BasBlobsSpecificationFk = blobId;
			}
			else if (entity.MdcMaterialFk.HasValue && !entity.BasBlobsSpecificationFk.HasValue)
			{
				var mdcCommoditySearchLogic = new MdcCommoditySearchVLogic();
				var mdcCommodity = mdcCommoditySearchLogic.GetCommodityById((int)entity.MdcMaterialFk);
				if (mdcCommodity != null && mdcCommodity.BasBlobsSpecificationFk != null)
				{
					var blobLogic = new BlobLogic();
					var blobId = blobLogic.CopyBlob((int)mdcCommodity.BasBlobsSpecificationFk);
					entity.BasBlobsSpecificationFk = blobId;
				}
			}
			return entity;
		}

		#region IEntityFacade Methods

		private static ConvertProperties _entityProperties = new ConvertProperties()
			.Add("Id")
			.Add("PrcHeaderFk")
			.Add("PrcItemFk")
			.Add("Itemno")
			.Add("PrcPackageFk")
			.Add("PrcStructureFk")
			.Add("MdcMaterialFk")
			.Add("Description1")
			.Add("Description2")
			.Add("Specification")
			.Add("Quantity")
			.Add("BasUomFk")
			.Add("Price")
			.Add("PriceOc")
			.Add("PriceGross")
			.Add("PriceGrossOc")
			.Add("PrcPriceConditionFk")
			.Add("PriceExtra")
			.Add("PriceExtraOc")
			.Add("PriceUnit")
			.Add("BasUomPriceUnitFk")
			.Add("FactorPriceUnit")
			.Add("MdcControllingunitFk")
			.Add("TargetPrice")
			.Add("TargetTotal")
			.Add("Hasdeliveryschedule")
			.Add("Onhire")
			.Add("DateRequired")
			.Add("Offhire")
			.Add("MdcTaxCodeFk")
			.Add("BasPaymentTermFiFk")
			.Add("BasPaymentTermPaFk")
			.Add("PrcIncotermFk")
			.Add("BasAddressFk")
			.Add("Hastext")
			.Add("Supplierreference")
			.Add("Userdefined1")
			.Add("Userdefined2")
			.Add("Userdefined3")
			.Add("Userdefined4")
			.Add("Userdefined5")
			.Add("PrcItemstatusFk")
			.Add("InstanceId")
			.Add("QuantityAskedfor")
			.Add("QuantityDelivered")
			.Add("Batchno")
			.Add("BpdAgreementFk")
			.Add("SafetyLeadTime")
			.Add("BufferLeadTime")
			.Add("LeadTimeExtra")
			.Add("PrcReplacementItemFk")
			.Add("PrcItemEvaluationFk")
			.Add("QuantityConfirm")
			.Add("DeliverDateConfirm")
			.Add("BasItemTypeFk")
			.Add("BasItemType2Fk")
			.Add("PrcItemAltFk")
			.Add("LicCostGroup1Fk")
			.Add("LicCostGroup2Fk")
			.Add("LicCostGroup3Fk")
			.Add("LicCostGroup4Fk")
			.Add("LicCostGroup5Fk")
			.Add("PrjCostGroup1Fk")
			.Add("PrjCostGroup2Fk")
			.Add("PrjCostGroup3Fk")
			.Add("PrjCostGroup4Fk")
			.Add("PrjCostGroup5Fk")
			.Add("IsFreeQuantity")
			.Add("Discount")
			.Add("DiscountAbsolute")
			.Add("DiscountAbsoluteOc")
			.Add("DiscountAbsoluteGross")
			.Add("DiscountAbsoluteGrossOc")
			.Add("DiscountComment")
			.Add("BasItemType85Fk")
			.Add("SellUnit")
			.Add("LeadTime")
			.Add("MinQuantity")
			.Add("HasScope")
			.Add("ExternalCode")
			.Add("CommentContractor")
			.Add("CommentClient")
			.Add("PrjStockFk")
			.Add("PrjStockLocationFk")
			.Add("MaterialExternalCode")
			.Add("MdcSalesTaxGroupFk")
			.Add("AlternativeUomFk")
			.Add("AlternativeQuantity")
			.Add("MaterialStockFk")
			.Add("Co2Project")
			.Add("Co2ProjectTotal")
			.Add("Co2Source")
			.Add("Co2SourceTotal")
			.Add("Charge")
			.Add("ChargeOc");
		/// <summary>
		/// Name of the entity
		/// </summary>
		string IEntityFacade.Name
		{
			get { return "PrcItemEntity"; }
		}
		/// <summary>
		/// UUID to clearly determine the entity provider
		/// </summary>
		string IEntityFacade.Id
		{
			get { return "198FDF7473AB43B29A273C9978529690"; }
		}
		/// <summary>
		/// Module name
		/// </summary>
		string IEntityFacade.ModuleName
		{
			get { return "procurement.common"; }
		}
		/// <summary>
		/// Get a PrcItemEntity by Id
		/// </summary>
		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetItemByKey(id);
			var objectDic = ToDictionary(entity);

			return objectDic;
		}

		private IDictionary<string, object> ToDictionary(PrcItemEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);
			return objectDic;
		}

		/// <summary>
		/// Get entity by the header Id
		/// </summary>
		/// <param name="mainId"></param>
		/// <returns></returns>
		public IDictionary<string, object>[] GetEntities(int mainId)
		{
			var items = GetSearchList(e => e.PrcHeaderFk == mainId);

			return items.Select(item => ToDictionary(item)).ToArray();
		}


		/// <summary>
		/// Save a ConHeaderEntity
		/// </summary>
		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			try
			{
				int id = entityDictionary.GetId<int>();

				var newEntity = this.GetItemByKey(id);

				if (newEntity == null)
				{
					newEntity = new PrcItemEntity() { Id = this.SequenceManager.GetNext("PRC_ITEM") };
				}
				newEntity.SetObject(entityDictionary, _entityProperties);

				var headerInfo = new PrcItemLogic().GetHeaderInfoByPrcHeader(newEntity.PrcHeaderFk);
				SetDiscountNAbsolute(entityDictionary, newEntity, headerInfo.exchangeRate, headerInfo.vatGroupId);
				UpdateAlterNative(newEntity);
				RecalculatePrcItemTotal(newEntity, headerInfo.vatGroupId, headerInfo);
				return ToDictionary(this.Save(newEntity));
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		IDictionary<string, object> ICreateEntityFacade.Create()
		{
			var entity = new PrcItemEntity() { Id = this.SequenceManager.GetNext("PRC_ITEM") };
			//DefaultIncoterm
			var defaultIncoterm = new PrcIncotermLogic().GetDefault();
			entity.PrcIncotermFk = defaultIncoterm.Id;
			var itemType = new ItemTypeLogic().GetDefault();
			if (itemType == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type"));
			}
			entity.BasItemTypeFk = itemType.Id;

			var itemType2 = new ItemType2Logic().GetDefault();
			if (itemType2 == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type2"));
			}
			entity.BasItemType2Fk = itemType2.Id;
			var itemType85 = new PrcItemType85Logic().GetDefault();
			if (itemType85 == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Base Item Type85"));
			}
			entity.BasItemType85Fk = itemType85.Id;
			entity.Quantity = 0;
			entity.PriceUnit = 1;
			entity.FactorPriceUnit = 1;
			entity.SafetyLeadTime = 0;
			entity.BufferLeadTime = 0;
			entity.DateRequired = null;
			entity.Hasdeliveryschedule = false;
			entity.Hastext = false;
			var prcItemStatus = new PrcItemstatusLogic().GetDefault();
			if (prcItemStatus == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Prc Items"));
			}
			entity.PrcItemstatusFk = prcItemStatus.Id;
			entity.PrcPriceConditionFk = null;
			UpdateAlterNative(entity);
			return ToDictionary(entity);
		}

		private void UpdateAlterNative(PrcItemEntity item)
		{
			var material = new MaterialLogic().GetItemByKey(item.MdcMaterialFk);
			if (material != null && material.MdcMaterialStockFk.HasValue)
			{
				var stockUoms = new Material2basUomLogic().GetMaterial2UomItems(material.Id);
				var stockMaterial = new MaterialLogic().GetItemByKey(material.MdcMaterialStockFk);
				var stockUom = stockUoms.FirstOrDefault(e => e.UomFk == stockMaterial.UomFk);

				item.MaterialStockFk = material.MdcMaterialStockFk;
				item.AlternativeUomFk = stockMaterial.UomFk;

				if (stockUom != null)
				{
					item.AlternativeQuantity = CalculationHelper.QuantityRound(item.Quantity * stockUom.Quantity);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		public void UpdateAlternativeQuantity(List<PrcItemEntity> items)
		{
			if (items?.Any() != true)
			{
				return;
			}

			var materialIds = items.CollectIds(e => e.MdcMaterialFk);
			var mdc2UomsDic = materialIds?.Any() == true ? new Material2basUomLogic().GetMaterialIds2UomItems(materialIds) : null;
			foreach (var item in items)
			{
				item.AlternativeQuantity = item.Quantity;
				if (mdc2UomsDic != null && item.MdcMaterialFk.HasValue && mdc2UomsDic.ContainsKey(item.MdcMaterialFk.Value) && item.AlternativeUomFk.HasValue)
				{
					var material2UomItems = mdc2UomsDic[item.MdcMaterialFk.Value];
					UpdateAlternativeQuantity(item, material2UomItems);
				}
			}
		}

		private void UpdateAlternativeQuantity(PrcItemEntity item, List<Material2UomItems> material2UomItems)
		{
			if (item == null)
			{
				return;
			}

			var material2UomItem = material2UomItems?.FirstOrDefault(e => e.UomFk == item.AlternativeUomFk.Value);
			item.AlternativeQuantity = (material2UomItem != null) ?
				CalculationHelper.QuantityRound(item.Quantity * material2UomItem.Quantity) :
				item.Quantity;
		}

		/// <summary>
		/// Get Entity Properties
		/// </summary>
		string[] IEntityFacade.Properties
		{
			get
			{
				return _entityProperties.GetPropertyNames();
			}
		}

		#endregion

		/// <summary>
		/// Change the status of entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <param name="statusId">new status id</param>
		public RVPBizComp.EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			return UpdateStatus(identification, statusId);
		}

		/// <summary>
		/// Get the status of the entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <returns>Current status id</returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var item = this.GetItemByKey(identification.Id);
			if (item == null)
			{
				return 0;
			}
			if (identification.StatusField == "PrjChangeStatusFk")
			{
				return item.PrjChangeStatusFk != null ? item.PrjChangeStatusFk.Value : 0;
			}
			return item.PrcItemstatusFk;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}

		#region IEntityCopier

		/// <summary>
		///
		/// </summary>
		/// <param name="responsible"></param>
		/// <param name="entity"></param>
		/// <param name="intermediate"></param>
		/// <returns></returns>
		protected override IEnumerable<IIdentifyable> ProvideAggregatedForDeepCopy(IEntityRelationInfo responsible, IIdentifyable entity, IEntityRelationInfo intermediate)
		{
			if (responsible.GetIdentifier() == RelationInfoIdentifier.PrcHeader)
			{
				return this.GetSearchList(e => e.PrcHeaderFk == entity.Id);
			}

			return Array.Empty<IIdentifyable>();
		}

		/// <summary>
		/// Should get children of TEntity, if there any. Base implementation does nothing
		/// </summary>
		/// <param name="parent">Possible parent of other entities</param>
		/// <returns>null - and the method may not be overwritten</returns>
		protected override IEnumerable<PrcItemEntity> GetChildren(PrcItemEntity parent)
		{
			// todo-wui: because copy logic error if return null
			return new List<PrcItemEntity>();
		}

		/// <summary>
		/// Should initializes aggregating owner and if possbile the parent of the passed entity. Base implementation does nothing
		/// </summary>
		/// <param name="entity">Entity to which owner and parent must be set</param>
		/// <param name="newOwner">TEntiy (parent in hier. structures) or different type not known here</param>
		/// <param name="ownerMapping"></param>
		protected override void InitNewOwner(PrcItemEntity entity, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			entity.PrcHeaderFk = newOwner.Id;
		}

		/// <summary>
		/// Returns itself, as a parent instance it knows how to build up a complete entity
		/// </summary>
		/// <returns>this</returns>
		protected override IEntityAggregator ProvideAggregator()
		{
			return this;
		}

		IIdentifyable IEntityAggregator.ProvideCompleteEntity()
		{
			return new PrcItemComplete();
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IIdentifyable toBeAggregated)
		{
			var updateEntity = (PrcItemComplete)to;
			if (updateEntity != null && toBeAggregated != null)
			{
				updateEntity.PrcItem = (PrcItemEntity)toBeAggregated;
				if (updateEntity.PrcItem != null)
				{
					if (updateEntity.PrcItem.PrcItemFk != null && updateEntity.PrcItem.PrcItemFk > 0)
					{
						long oldId = (long)updateEntity.PrcItem.PrcItemFk;
						updateEntity.CostGroupToSave = DuplicateObjectEntityExtension.GetNewMainItem2CostGroup(oldId, updateEntity.PrcItem.Id);
					}
					updateEntity.Id = (int)updateEntity.PrcItem.Id;
					updateEntity.MainItemId = (int)updateEntity.PrcItem.Id;
				}
				if (updateEntity.PrcItem.BasAddressFk != null)
				{
					var addressLogic = new AddressLogic();
					var addressEntity = addressLogic.Clone(updateEntity.PrcItem.BasAddressFk.Value);
					updateEntity.PrcItem.AddressEntity = addressEntity;
					updateEntity.PrcItem.BasAddressFk = addressEntity.Id;
				}
			}
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			var updateEntity = (PrcItemComplete)to;
			if (updateEntity != null && toBeAggregated.Any())
			{
				DoAggregate(updateEntity, relInfo, toBeAggregated);
			}
		}

		/// <summary>
		/// Takes over a list of entities and appends it to the passed update entity
		/// </summary>
		/// <param name="updateEntity">Complete entity to build up</param>
		/// <param name="relInfo">Information about content of toBeAggregated</param>
		/// <param name="toBeAggregated">Entities which need to be added to updateEntity</param>
		protected void DoAggregate(PrcItemComplete updateEntity, IEntityRelationInfo relInfo,
			IEnumerable<IIdentifyable> toBeAggregated)
		{
			switch (relInfo.GetIdentifier())
			{
				case RelationInfoIdentifier.PrcItemDelivery:
					updateEntity.PrcItemDeliveryToSave = toBeAggregated.Where(e => (PrcItemdeliveryEntity)e != null).Select(d => (PrcItemdeliveryEntity)d);
					break;
				case RelationInfoIdentifier.PrcItemBlob:
					updateEntity.PrcItemblobToSave = toBeAggregated.Where(e => (PrcItemblobEntity)e != null).Select(d => (PrcItemblobEntity)d);
					break;
				case RelationInfoIdentifier.PrcItemPriceCondition:
					updateEntity.PriceConditionToSave = toBeAggregated.Where(e => (PrcItemPriceConditionEntity)e != null).Select(d => (PrcItemPriceConditionEntity)d);
					break;
				case RelationInfoIdentifier.PrcItemScope:
					updateEntity.PrcItemScopeToSave = toBeAggregated.Where(e => (PrcItemScopeComplete)e != null).Select(d => (PrcItemScopeComplete)d);
					break;
			}
		}

		#endregion

		/// <summary>
		/// extend LogicBase, it need override it, it is different from EntityLogic
		/// </summary>
		/// <returns></returns>
		protected override string GetDbTable()
		{
			return "PRC_ITEM";
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="status"></param>
		/// <param name="externalSourceId"></param>
		/// <param name="companyId"></param>
		/// <returns></returns>
		public string GetItemStatusDesp(string status, int? externalSourceId, int? companyId)
		{
			var id = GetItemStatusByExternalCode(status, externalSourceId);
			var itemStatusEntity = new PrcItemstatusLogic().GetItemByKey(id);
			if (itemStatusEntity != null)
			{
				if (itemStatusEntity.DescriptionInfo.DescriptionTr != null && companyId != null)
				{
					var companyLogic = new BasicsCompanyLogic();
					var company = companyLogic.GetCompanyById((int)companyId);
					var languageId = company.LanguageFk;
					TranslationLogic _translationLogic = new TranslationLogic();
					var statusTranslated = _translationLogic.GetTranslationByIdAndLanguageId((int)itemStatusEntity.DescriptionInfo.DescriptionTr, languageId);
					if (statusTranslated != null)
					{
						return statusTranslated.Description;
					}
				}
				return itemStatusEntity.DescriptionInfo.Description;
			}
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="statusFk"></param>
		/// <param name="externalSourceId"></param>
		/// <returns></returns>
		public List<string> getItemExternalCodeByFk(int statusFk, int? externalSourceId)
		{
			List<string> result = new List<string>();
			var itemStatus2external = new PrcItemstatus2externalLogic().GetSearchList(e => e.PrcItemstatusFk == statusFk && e.BasExternalsourceFk == externalSourceId);
			if (itemStatus2external != null && itemStatus2external.Any())
			{
				foreach (var oneItem in itemStatus2external)
				{
					result.Add(oneItem.ExtCode);
				}
				return result;
			}
			return new List<string>();
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="ItemStatus"></param>
		/// <param name="externalSourceId"></param>
		/// <returns></returns>
		public int GetItemStatusByExternalCode(string ItemStatus, int? externalSourceId)
		{
			PrcItemstatusLogic logic = new PrcItemstatusLogic();

			var itemStatus2external = new PrcItemstatus2externalLogic().GetSearchList(e => e.ExtCode.ToUpper() == ItemStatus.ToUpper() && e.BasExternalsourceFk == externalSourceId);
			if (itemStatus2external != null && itemStatus2external.Any())
			{
				return itemStatus2external.FirstOrDefault().PrcItemstatusFk;
			}
			else
			{
				return logic.GetDefault().Id;
			}
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="itemStatus"></param>
		/// <returns></returns>
		public int GetItemStatus(string itemStatus)
		{
			var statusLogic = new PrcItemstatusLogic();
			PrcItemstatusEntity statueEntity = null;
			if (String.IsNullOrEmpty(itemStatus))
			{
				statueEntity = statusLogic.GetItemByPredicate(e => e.IsAccepted == true);
			}
			else
			{
				if (itemStatus == "AD")
				{
					statueEntity = statusLogic.GetItemByPredicate(e => e.IsAccepted == true);
				}
				else if (itemStatus == "AC")
				{
					statueEntity = statusLogic.GetItemByPredicate(e => e.IsPartAccepted == true);
				}
				else if (itemStatus == "RD")
				{
					statueEntity = statusLogic.GetItemByPredicate(e => e.IsRejected == true);
				}
				else
				{
					statueEntity = null;
				}
			}
			if (statueEntity != null)
			{
				PrcItemStatus itemstatus = new PrcItemStatus();
				itemstatus.IsAccepted = statueEntity.IsAccepted;
				itemstatus.Iscanceled = statueEntity.IsCanceled;
				itemstatus.IsDefault = statueEntity.IsDefault;
				itemstatus.IsDelivered = statueEntity.IsDelivered;
				itemstatus.IsPartAccepted = statueEntity.IsPartAccepted;
				itemstatus.IsPartDelivered = statueEntity.IsPartDelivered;
				itemstatus.IsRejected = statueEntity.IsRejected;
				return itemstatus.GetStatusValue();
			}
			else
			{
				return 0;
			}
		}

		private decimal GetQuantity(decimal sellUnit, decimal minQuantity, decimal userInputValue)
		{
			decimal result = 0;
			decimal tempMultiplier = 0;
			if (userInputValue < 0)
			{
				userInputValue = 0;
			}
			if (sellUnit == 0)
			{
				if (userInputValue < minQuantity)
				{
					result = minQuantity;
				}
				else
				{
					result = userInputValue;
				}
			}
			else
			{
				if (userInputValue < minQuantity)
				{
					tempMultiplier = minQuantity / sellUnit;
					if (Math.Floor(tempMultiplier) == tempMultiplier)
					{
						result = minQuantity;
					}
					else
					{
						result = (Math.Floor(tempMultiplier) + 1) * sellUnit;
					}
				}
				else
				{
					tempMultiplier = userInputValue / sellUnit;
					if (Math.Floor(tempMultiplier) == tempMultiplier)
					{
						result = userInputValue;
					}
					else
					{
						result = (Math.Floor(tempMultiplier) + 1) * sellUnit;
					}
				}
			}
			return result;
		}

		private int ValidateAndUpdateItemQuantity(decimal sellUnit, decimal minQuantity, PrcItemEntity prcItem)
		{
			int result = 0;
			decimal lastInputedValue = prcItem.Quantity;
			bool blnChanged = false;
			if (sellUnit == 0)
			{
				if (minQuantity != 0 && lastInputedValue < minQuantity)
				{
					prcItem.Quantity = minQuantity;
					blnChanged = true;
				}
			}
			else
			{
				if (minQuantity == 0)
				{
					if (!(Math.Floor(lastInputedValue / sellUnit) == (lastInputedValue / sellUnit)))
					{
						prcItem.Quantity = GetQuantity(sellUnit, minQuantity, lastInputedValue);
						blnChanged = true;
					}
				}
				else
				{
					if (!((lastInputedValue >= minQuantity) && (Math.Floor(lastInputedValue / sellUnit) == (lastInputedValue / sellUnit))))
					{
						prcItem.Quantity = GetQuantity(sellUnit, minQuantity, lastInputedValue);
						blnChanged = true;
					}
				}
			}
			if (blnChanged)
			{
				base.Save(prcItem);
				result = 1;
			}
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemList"></param>
		/// <returns></returns>
		public int ValidateAndUpdateItemQuantity(List<PrcItemEntity> prcItemList)
		{
			int result = 0;
			for (int i = 0; i < prcItemList.Count; i++)
			{
				int? materialId = prcItemList[i].MdcMaterialFk;
				if (materialId.HasValue)
				{
					MaterialLogic materialLogic = new MaterialLogic();
					MaterialEntity materialEntity = materialLogic.GetItemByKey(materialId);
					if (materialEntity != null)
					{
						decimal sellUnit = materialEntity.SellUnit;
						decimal minQuantity = materialEntity.MinQuantity;
						result += ValidateAndUpdateItemQuantity(sellUnit, minQuantity, prcItemList[i]);
					}
				}
			}
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemIds"></param>
		/// <returns></returns>
		public IPrcItemEntity GetItemsFromPrcItem(long? prcItemIds)
		{
			return this.GetSearchList(e => e.Id == prcItemIds).FirstOrDefault();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemIds"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> GetItemsByPrcItemIds(IEnumerable<long> prcItemIds)
		{
			return this.GetItemsByIds(prcItemIds.ToList());
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetMixPrcItemsByFilter(IEnumerable<long> ids, Expression<Func<PrcItemLookupVEntity, bool>> LookupPredicate)
		{
			if (ids == null || !ids.Any())
			{
				return new List<PrcItemEntity>();
			}

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<PrcItemEntity> query = null;
				IQueryable<PrcItemEntity> query1 = dbContext.Entities<PrcItemEntity>().Where(e => ids.Contains(e.Id));

				IQueryable<PrcItemEntity> query2 = dbContext.Entities<PrcItemEntity>();

				if (LookupPredicate != null)
				{
					query2 = dbContext.Entities<PrcItemLookupVEntity>().Where(LookupPredicate).Join(query2, outter => outter.Id, inner => inner.Id, (outter, inner) => inner);
				}

				query = query1.Join(query2, outter => new { prcItemFk = outter.PrcItemFk.HasValue ? outter.PrcItemFk.Value : -outter.Id, outter.PriceOc }, inner => new { prcItemFk = inner.PrcItemFk.HasValue ? inner.PrcItemFk.Value : -inner.Id, inner.PriceOc }, (outter, inner) => inner).Distinct();

				// TODO chi: translation?
				return query.ToList();
			}
		}
		/// <summary>
		/// implement from IGetPrcItemLogic
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <param name="materialIds"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> GetPrcItemsByForeignKeys(IEnumerable<int> prcHeaderIds, IEnumerable<int> materialIds)
		{
			return this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk) && e.MdcMaterialFk.HasValue && materialIds.Contains(e.MdcMaterialFk.Value));
		}

		/// <summary>
		/// Get prc items by prc header id
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> GetPrcItemsByPrcHeaderId(int prcHeaderId)
		{
			return this.GetSearchList(e => e.PrcHeaderFk == prcHeaderId);
		}

		/// <summary>
		/// Get prc items by prc header ids
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<IPrcItemEntity> GetPrcItemsByPrcHeaderIds(IEnumerable<int> prcHeaderIds)
		{
			return this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));
		}

		/// <summary>
		/// Recalculate Item's Price Extra with Price Conditions
		/// </summary>
		/// <param name="conditions"></param>
		/// <param name="item"></param>
		public void RecalculatePriceExtra(IEnumerable<PrcItemPriceConditionEntity> conditions, ref PrcItemEntity item)
		{
			if (conditions != null && conditions.Any())
			{
				item.PriceExtraOc = conditions.Where(x => x.IsPriceComponent && x.IsActivated).Sum(x => x.TotalOc);
				item.PriceExtra = conditions.Where(x => x.IsPriceComponent && x.IsActivated).Sum(x => x.Total);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="materialGroupId"></param>
		/// <returns></returns>
		public materialCatalogInfo GetMaterialCatalogByMaterialGroupId(int materialGroupId)
		{
			var materialGroupIds = new List<int>();
			materialGroupIds.Add(materialGroupId);
			var result = GetMaterialCatalogsByMaterialGroupIds(materialGroupIds);
			if (result != null && result.Any())
			{
				return result.First();
			}
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="materialGroupIds"></param>
		/// <param name="groups"></param>
		/// <returns></returns>
		public List<materialCatalogInfo> GetMaterialCatalogsByMaterialGroupIds(List<int> materialGroupIds, List<MaterialGroupEntity> groups = null)
		{
			List<materialCatalogInfo> materialCatalogs = new List<materialCatalogInfo>();
			var materialGroups = groups != null ? groups : new MaterialGroupLogic().GetItemsByKey(materialGroupIds).ToList();
			var catalogIds = (materialGroups != null && materialGroups.Any()) ? materialGroups.Select(e => e.MaterialCatalogFk).Distinct().ToList() : new List<int>();

			MaterialCatalogLogic materialCatalogLogic = new MaterialCatalogLogic();
			var businessPartnerLogic = Injector.Get<IGetBusinessPartnerLogic>();
			var materialCatalogEntities = materialCatalogLogic.GetItemsByKey(catalogIds).ToList();
			if (materialCatalogEntities != null && materialCatalogEntities.Any())
			{
				var bpIds = materialCatalogEntities.Where(e => e.BusinessPartnerFk.HasValue).Select(e => e.BusinessPartnerFk.Value).Distinct().ToList();
				var bpName1sDic = businessPartnerLogic.GetBusinessPartnerName1ByKeys(bpIds);
				foreach (var materialCatalogEntity in materialCatalogEntities)
				{
					materialCatalogInfo materialCatalog = new materialCatalogInfo();
					materialCatalog.MaterialCatalogId = materialCatalogEntity.Id;
					materialCatalog.MaterialCatalogCode = materialCatalogEntity.Code;
					materialCatalog.MaterialCatalogDescription = materialCatalogEntity.DescriptionInfo.Translated;
					materialCatalog.MaterialCatalogTypeFk = materialCatalogEntity.MaterialCatalogTypeFk;
					if (materialCatalogEntity.BusinessPartnerFk.HasValue)
					{
						materialCatalog.MaterialCatalogSupplier = bpName1sDic.ContainsKey(materialCatalogEntity.BusinessPartnerFk.Value) ? bpName1sDic[materialCatalogEntity.BusinessPartnerFk.Value] : "";
					}
					materialCatalogs.Add(materialCatalog);
				}
			}

			return materialCatalogs;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="commonQuery"></param>
		/// <param name="request"></param>
		/// <param name="context"></param>
		/// <returns></returns>
		protected override IQueryable<PrcItemEntity> DoBuildLookupSearchFilter(IQueryable<PrcItemEntity> commonQuery, LookupSearchRequest request, RVPBizComp.DbContext context)
		{
			switch (request.FilterKey)
			{

				case "procurement-common-item-filter":
					var projectFk = request.TryGetParameterValueAsInt("projectFk");
					var packageFk = request.TryGetParameterValueAsInt("packageFk");
					var conHeaderFk = request.TryGetParameterValueAsInt("conHeaderFk");
					var packageIds = new List<int>();

					if (packageFk.HasValue)
					{//When the package is known, directly and only use this package
						var id = packageFk.Value;
						commonQuery = commonQuery.Where(o => o.PrcPackageFk.Value == id);
						packageIds.Add(id);
					}
					else if (projectFk.HasValue)
					{//Else try to use the project to determine the filtering packages
						var packages = Injector.Get<IPackageLogic>().GetPackageByProjectId(projectFk.Value).Select(x => x.Id);

						commonQuery = commonQuery.Where(o => packages.Contains(o.PrcPackageFk.Value));

						packageIds.AddRange(packages);
					}
					if (conHeaderFk.HasValue)
					{//If contract header is known use it directly as a filter for the procurement header
						var contractHeader = Injector.Get<IContractHeaderInfoProvider>().GetConHeadersByKey(new int?[] { conHeaderFk.Value }).FirstOrDefault();
						if (contractHeader != null)
						{
							commonQuery = commonQuery.Where(o => o.PrcHeaderFk == contractHeader.PrcHeaderFk);
						}
					}
					else
					{// find other, not that efficient ways to determine a filter for the procurement header
						var contracHeadertLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IContractHeaderInfoProvider>();
						IEnumerable<int> prcHeaderIdList;
						if (packageIds.Any())
						{//We know already the packages
							prcHeaderIdList = contracHeadertLogic.GetContractByPackageIds(packageIds).Select(e => e.PrcHeaderFk).ToArray();

							commonQuery = commonQuery.Where(e => prcHeaderIdList.Contains(e.PrcHeaderFk));
						}
						else
						{//Shit, only the company is left. Hope it will not crash at the end
							var clientId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
							var companyFk = clientId;
							prcHeaderIdList = contracHeadertLogic.GetValidContractsByCompanyId(companyFk).Select(e => e.PrcHeaderFk).ToArray();

							commonQuery = commonQuery.Where(e => prcHeaderIdList.Contains(e.PrcHeaderFk));
						}
					}

					break;
				default:
					break;

			}

			if (!string.IsNullOrEmpty(request.SearchText))
			{
				var opts = new QueryFilterOptions<PrcItemEntity>()
				{
					SearchText = request.SearchText,
					IgnoreSearchPattern = true
				};
				opts.Add(request.SearchFields);

				commonQuery = commonQuery.JoinTrAndFilter<PrcItemEntity, TranslationEntity>(context.Set<TranslationEntity>(), opts);
			}
			return commonQuery;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="exchangeRate"></param>
		/// <param name="priceConditions"></param>
		/// <param name="prcItem"></param>
		public void RecalculatePrices(decimal exchangeRate, IEnumerable<PrcItemPriceConditionEntity> priceConditions, ref PrcItemEntity prcItem)
		{
			RecalculatePriceExtra(priceConditions, ref prcItem);

			if (exchangeRate != 1 && exchangeRate != 0)
			{
				prcItem.Price = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceOc, exchangeRate);
				prcItem.PriceExtra = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceExtraOc, exchangeRate);
			}
			else
			{
				prcItem.Price = prcItem.PriceOc;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="exchangeRate"></param>
		/// <param name="priceConditions"></param>
		/// <param name="prcItem"></param>
		public void RecalculatePriceGross(decimal exchangeRate, IEnumerable<PrcItemPriceConditionEntity> priceConditions, ref PrcItemEntity prcItem)
		{
			RecalculatePriceExtra(priceConditions, ref prcItem);

			if (exchangeRate != 1 && exchangeRate != 0)
			{
				prcItem.PriceGross = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceGrossOc, exchangeRate);
				prcItem.PriceExtra = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceExtraOc, exchangeRate);
			}
			else
			{
				prcItem.PriceGross = prcItem.PriceGrossOc;
			}
		}
		/// <summary>
		/// This is new approach to deal with GetSearchList request from front end and dynamic linq is not suggested to use because of possible misuse to cause security issue.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public override LookupSearchResponse<PrcItemEntity> GetLookupSearchList(LookupSearchRequest request)
		{
			var response = new LookupSearchResponse<PrcItemEntity>();

			if (request == null) //get all
			{
				response.SearchList = GetList();
				response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
			}
			else
			{
				if (request.RequestIds != null && request.RequestIds.Any()) //get by ids
				{
					List<long> ids2 = request.RequestIds.Select(e => e.Id).ToList().ConvertAll(i => (long)i);

					response.SearchList = this.GetItemsByIds(ids2);
					response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
				}
				else //if (request.SearchFields != null && request.SearchFields.Any()) //search by text
				{
					using (var dbcontext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
					{
						IQueryable<PrcItemEntity> query = dbcontext.Set<PrcItemEntity>();

						query = DoBuildLookupSearchFilter(query, request, dbcontext);

						if (query != null)
						{
							response.RecordsFound = query.Count();

							//deal with paging.
							if (this.OrderByExpressions != null && this.OrderByExpressions.Any())
							{
								query = OrderTerm.OrderQuery(query, OrderByExpressions);
							}
							else
							{
								query = query.OrderBy(e => e.Id);
							}

							query = query.Skip(request.PageState.PageNumber * request.PageState.PageSize).Take(request.PageState.PageSize);

							response.SearchList = DoTranslate(query.ToList());

							response.RecordsRetrieved = response.SearchList.Count();
						}
					}
				}
			}

			return response;
		}

		/// <summary>
		///
		/// </summary>
		public class materialCatalogInfo
		{
			/// <summary>
			///
			/// </summary>
			public int MaterialCatalogId { get; set; }
			/// <summary>
			///
			/// </summary>
			public string MaterialCatalogCode { get; set; }

			/// <summary>
			///
			/// </summary>
			public string MaterialCatalogDescription { get; set; }

			/// <summary>
			///
			/// </summary>
			public int MaterialCatalogTypeFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public string MaterialCatalogSupplier { get; set; }
		}
		/// <summary>
		/// Calculates the delivered and asked-for quantities for a given set of PrcItems.
		/// Applies either consolidated or direct matching logic depending on the flag.
		/// </summary>
		/// <param name="prcItemIds">List of PrcItem IDs to process</param>
		/// <param name="isConsolidateChange">Indicates whether consolidated mode is enabled</param>
		public void CalculateQuantities(IEnumerable<long> prcItemIds, bool isConsolidateChange)
		{
			var pesItemInfoProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IPesItemInfoProvider>();
			var callOffLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ICallOffItemLogic>();
			var prcItemLogic = new PrcItemLogic();

			var prcItemList = prcItemLogic.GetItemsByIds(prcItemIds.ToList());
			var entitiesToSave = new List<PrcItemEntity>();
			var deliveredByBaseId = new Dictionary<long, decimal>();

			// Load related PrcItems if consolidated mode is on
			var allRelatedItems = isConsolidateChange ? this.GetRelatedPrcItems(prcItemList) : [];

			foreach (var item in prcItemList)
			{
				// Get base ID (self or parent)
				var baseItemId = item.PrcItemFk ?? item.Id;

				// Collect all items related to the same base for quantity calculation
				var relatedItems = allRelatedItems
					.Where(e => e.Id == baseItemId || e.PrcItemFk == baseItemId)
					.Append(item)
					.Distinct()
					.Cast<IPrcItemEntity>()
					.ToList();

				// Update the current item
				item.QuantityDelivered = pesItemInfoProvider.CalculateDeliveredQuantity(item.Id, null, isConsolidateChange, relatedItems); ;
				item.QuantityAskedfor = pesItemInfoProvider.GetQuantityAskedforByPrcItemId(item.Id);

				// Cache the delivered value for this base item ID
				if (!deliveredByBaseId.ContainsKey(baseItemId))
				{
					deliveredByBaseId[baseItemId] = item.QuantityDelivered;
				}

				entitiesToSave.Add(item);
			}

			if (isConsolidateChange)
			{
				// Update related items in the same base group with the calculated delivered value
				foreach (var relatedItem in allRelatedItems)
				{
					var baseItemId = relatedItem.PrcItemFk ?? relatedItem.Id;
					if (deliveredByBaseId.TryGetValue(baseItemId, out var delivered))
					{
						relatedItem.QuantityDelivered = delivered;
						entitiesToSave.Add(relatedItem);
					}
				}
			}
			// Save all changed entities in a single transaction
			prcItemLogic.Save(entitiesToSave);

			if (!isConsolidateChange)
			{
				// If not consolidated, match each call-off item to its base/change item and update delivered quantity
				var matchedMap = callOffLogic.MatchCallOffPrcItemsToBaseOrChange(prcItemIds);
				if (matchedMap.Any())
				{
					// Load matched base/change entities
					var matchedIds = matchedMap.Values.CollectIds(e => (long)e.Id).ToList();
					var matchedEntities = prcItemLogic.GetItemsByIds(matchedIds).ToDictionary(e => e.Id);

					var matchedEntitiesToSave = new List<PrcItemEntity>();
					foreach (var callOffId in prcItemIds)
					{
						if (matchedMap.TryGetValue(callOffId, out var matchedBase) &&
							matchedEntities.TryGetValue(matchedBase.Id, out var matchedEntity))
						{
							var relatedItems = allRelatedItems.Where(e => e.Id == matchedEntity.Id).Append(matchedEntity).Distinct().Cast<IPrcItemEntity>().ToList();
							matchedEntity.QuantityDelivered = pesItemInfoProvider.CalculateDeliveredQuantity(matchedEntity.Id, null, isConsolidateChange, relatedItems);
							matchedEntitiesToSave.Add(matchedEntity);
						}
					}
					prcItemLogic.Save(matchedEntitiesToSave);


				}

			}
		}



		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="prcItemFk"></param>
		/// <returns></returns>
		public List<IControllingGrpSetDTLEntity> getGrpSet(int mainItemId, int prcItemFk)
		{
			var _prcItem = new PrcItemLogic().GetSearchList(e => e.Id == prcItemFk).FirstOrDefault();

			if (_prcItem != null && _prcItem.ControllinggrpsetFk != null)
			{
				return Injector.Get<IControllingGrpSetDTLELogic>().CopyBysetFk(_prcItem.ControllinggrpsetFk.Value, mainItemId);
			}
			return new List<IControllingGrpSetDTLEntity>();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="headerExchangeRate"></param>
		/// <param name="headerVatGroupId"></param>
		public PrcItemComplete RecalculatePrcItemTotalAndGrossBeforeUpdateItemsFromApi(PrcItemEntity entity, decimal headerExchangeRate, int? headerVatGroupId)
		{
			var vatGroupId = headerVatGroupId;
			var exchangeRate = headerExchangeRate;
			if (entity.Price == 0 && entity.PriceOc != 0 && exchangeRate != 0)
			{
				entity.Price = PrcCalculationHelper.CalculateUnitRateNotOcByOc(entity.PriceOc, exchangeRate);
			}
			else if (entity.Price != 0 && entity.PriceOc == 0 && exchangeRate != 0)
			{
				entity.PriceOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(entity.Price, exchangeRate);
			}
			var result = new PrcItemComplete();
			result.PrcItem = entity;
			IEnumerable<PrcItemPriceConditionEntity> itemPriceConditions = new PrcItemPriceConditionCalcLogic().GetSearchList(x => entity.Id == x.PrcItemFk);
			if (itemPriceConditions != null && itemPriceConditions.Any())
			{
				RecalculatePriceCondition(entity, itemPriceConditions, exchangeRate);
				result.PriceConditionToSave = itemPriceConditions;
			}
			RecalculatePrcItemTotalAndGross(entity, vatGroupId);
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public int? GetHeaderRubric(int prcHeaderId)
		{
			var prcHeader = new PrcHeaderLogic().GetItemById(prcHeaderId);
			int? rubricFk = null;

			if (prcHeader != null)
			{
				var configuration = new PrcConfigurationLogic().GetItemByKey(prcHeader.ConfigurationFk);
				if (configuration != null)
				{
					var rubcatfk = configuration.RubricCategoryFk;
					var rubricCategory = new LookupBiz.RubricCategoryLogic().GetItemByKey(rubcatfk);
					rubricFk = rubricCategory != null ? rubricCategory.RubricFk : 0;
				}
			}

			return rubricFk;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public HeaderInfo GetHeaderInfoByPrcHeader(int prcHeaderId)
		{
			var headerInfoItemsFromApi = new HeaderInfo();
			headerInfoItemsFromApi.vatGroupId = null;
			headerInfoItemsFromApi.exchangeRate = 1;
			headerInfoItemsFromApi.rubricFk = null;
			headerInfoItemsFromApi.companyId = -1;

			var rubricFk = GetHeaderRubric(prcHeaderId);

			switch (rubricFk)
			{
				case RubricConstant.Package:
					IPrcPackageLogic prcPackageLogic = Injector.Get<IPrcPackageLogic>();
					var package = prcPackageLogic.GetPackageByPrcHederId(prcHeaderId).FirstOrDefault();
					headerInfoItemsFromApi.rubricFk = RubricConstant.Package;
					if (package != null)
					{
						headerInfoItemsFromApi.vatGroupId = package.BpdVatGroupFk;
						headerInfoItemsFromApi.exchangeRate = package.ExchangeRate;
						headerInfoItemsFromApi.headerId = package.Id;
						headerInfoItemsFromApi.companyId = package.CompanyFk;
						headerInfoItemsFromApi.currencyFk = package.CurrencyFk;
					}
					break;
				case RubricConstant.Requistion:
					IGetReqHeaderLogic reqHeaderLogic = Injector.Get<IGetReqHeaderLogic>();
					//TODO-Young Need optimization, takes 0.36 seconds, filterByPermission is slow
					var req = reqHeaderLogic.GetRequistionByFilter("PrcHeaderFk = " + prcHeaderId).FirstOrDefault();
					headerInfoItemsFromApi.rubricFk = RubricConstant.Requistion;
					if (req != null)
					{
						headerInfoItemsFromApi.vatGroupId = req.BpdVatGroupFk;
						headerInfoItemsFromApi.exchangeRate = req.ExchangeRate;
						headerInfoItemsFromApi.headerId = req.Id;
						headerInfoItemsFromApi.companyId = req.CompanyFk;
						headerInfoItemsFromApi.currencyFk = req.BasCurrencyFk;
					}
					else
					{
						IQuoteHeaderLogic quoteHeaderLogic = Injector.Get<IQuoteHeaderLogic>();
						var quote = quoteHeaderLogic.GetQuoteHeadersByPrcHederId(prcHeaderId).FirstOrDefault();
						headerInfoItemsFromApi.rubricFk = RubricConstant.Quotation;
						if (quote != null)
						{
							headerInfoItemsFromApi.vatGroupId = quote.BpdVatGroupFk;
							headerInfoItemsFromApi.exchangeRate = quote.ExchangeRate;
							headerInfoItemsFromApi.headerId = quote.Id;
							headerInfoItemsFromApi.companyId = quote.CompanyFk;
							headerInfoItemsFromApi.currencyFk = quote.CurrencyFk;
						}
					}
					break;
				case RubricConstant.Quotation:
					IQuoteHeaderLogic qtnHeaderLogic = Injector.Get<IQuoteHeaderLogic>();
					var qtn = qtnHeaderLogic.GetQuoteHeadersByPrcHederId(prcHeaderId).FirstOrDefault();
					headerInfoItemsFromApi.rubricFk = RubricConstant.Quotation;
					if (qtn != null)
					{
						headerInfoItemsFromApi.vatGroupId = qtn.BpdVatGroupFk;
						headerInfoItemsFromApi.exchangeRate = qtn.ExchangeRate;
						headerInfoItemsFromApi.headerId = qtn.Id;
						headerInfoItemsFromApi.companyId = qtn.CompanyFk;
						headerInfoItemsFromApi.currencyFk = qtn.CurrencyFk;
					}
					break;
				case RubricConstant.Contract:
					IContractHeaderInfoProvider contractHeaderLogic = Injector.Get<IContractHeaderInfoProvider>();
					var con = contractHeaderLogic.GetConHeaderSearchList("PrcHeaderFk = " + prcHeaderId).FirstOrDefault();
					headerInfoItemsFromApi.rubricFk = RubricConstant.Contract;
					if (con != null)
					{
						headerInfoItemsFromApi.vatGroupId = con.BpdVatGroupFk;
						headerInfoItemsFromApi.exchangeRate = con.ExchangeRate;
						headerInfoItemsFromApi.headerId = con.Id;
						headerInfoItemsFromApi.companyId = con.CompanyFk;
						headerInfoItemsFromApi.currencyFk = con.BasCurrencyFk;
					}
					break;
				default:
					break;
			}
			return headerInfoItemsFromApi;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rubric"></param>
		/// <param name="headerId"></param>
		public void RecalculateTotalsAfterUpdateItemsFromApi(int rubric, int headerId)
		{
			switch (rubric)
			{
				case RubricConstant.Package:
					var recalculatePackageTotalsLogic = Injector.Get<IPrcRecalculateTotalsLogic>("procurement.package");
					recalculatePackageTotalsLogic.RecalculateTotalsByHeaderId(headerId);

					break;
				case RubricConstant.Requistion:
					var recalculateReqTotalsLogic = Injector.Get<IPrcRecalculateTotalsLogic>("procurement.requisition");
					recalculateReqTotalsLogic.RecalculateTotalsByHeaderId(headerId);

					break;
				case RubricConstant.Quotation:
					var recalculateQtnTotalsLogic = Injector.Get<IPrcRecalculateTotalsLogic>("procurement.quote");
					recalculateQtnTotalsLogic.RecalculateTotalsByHeaderId(headerId);
					break;
				case RubricConstant.Contract:
					var recalculateConTotalsLogic = Injector.Get<IPrcRecalculateTotalsLogic>("procurement.contract");
					recalculateConTotalsLogic.RecalculateTotalsByHeaderId(headerId);
					break;
				default:
					break;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemIds"></param>
		/// <returns></returns>
		public Dictionary<long, decimal> GetPrcItemEstimateBudget(IEnumerable<long> prcItemIds)
		{
			var itemAssignments = new PrcItemAssignmentLogic().GetSearchList(e => e.PrcItemFk.HasValue && prcItemIds.Contains(e.PrcItemFk.Value) && e.EstResourceFk.HasValue);
			var lineItemResourceIds = new List<Tuple<int, int, int>>();
			List<IEstResourceData> resourceList = new List<IEstResourceData>();
			var prcItemId2EstimateBudget = new Dictionary<long, decimal>();

			foreach (var assign in itemAssignments)
			{
				if (!assign.EstResourceFk.HasValue)
				{
					continue;
				}

				lineItemResourceIds.Add(new Tuple<int, int, int>(assign.EstHeaderFk, assign.EstLineItemFk, assign.EstResourceFk.Value));
			}
			var resourceLogic = Injector.Get<IEstimateMainResourceLogic>();

			var resources = resourceLogic.GetResourcesByIdsAndHeaderId(lineItemResourceIds);
			if (resources != null && resources.Any())
			{
				resourceList.AddRange(resources);
			}

			foreach (var assign in itemAssignments)
			{
				if (!assign.EstResourceFk.HasValue || !assign.PrcItemFk.HasValue)
				{
					continue;
				}

				var tempResources = resourceList.Where(e => e.EstHeaderFk == assign.EstHeaderFk && e.EstLineItemFk == assign.EstLineItemFk && e.Id == assign.EstResourceFk && !e.IsDisabledPrc).ToList();
				var sumupBudget = tempResources.Sum(e => e.Budget);
				if (!prcItemId2EstimateBudget.ContainsKey(assign.PrcItemFk.Value))
				{
					prcItemId2EstimateBudget.Add(assign.PrcItemFk.Value, sumupBudget);
				}
				else
				{
					prcItemId2EstimateBudget[assign.PrcItemFk.Value] += sumupBudget;
				}
			}

			return prcItemId2EstimateBudget;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="moduleName"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> SetPrcItemsIsContracted(IEnumerable<PrcItemEntity> prcItems, string moduleName = "")
		{
			PrcItemAssignmentLogic prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			if ("procurement.package" == moduleName)
			{
				var prcItemIds = prcItems.Select(e => e.Id).ToList();
				var prcItemAssignments = prcItemAssignmentLogic.GetSearchList(e => e.PrcItemFk.HasValue && prcItemIds.Contains(e.PrcItemFk.Value)).ToList();
				var resIds = prcItemAssignments.Select(e => e.EstResourceFk).Distinct().ToList();
				var ids = prcItemAssignments.Select(e => e.Id);
				var basePrcItemAssignments = prcItemAssignmentLogic.GetSearchList(e => resIds.Contains(e.EstResourceFk) && !ids.Contains(e.Id)).ToList();
				prcItemAssignmentLogic.SetIsContractEntities(basePrcItemAssignments);
				var prcItemAssignmentsDic = prcItemAssignments.Where(e => e.PrcItemFk.HasValue).GroupBy(e => e.PrcItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());
				foreach (var prcItem in prcItems)
				{
					var currentPrcItemAssignments = prcItemAssignmentsDic.ContainsKey(prcItem.Id) ? prcItemAssignmentsDic[prcItem.Id] : null;
					if (currentPrcItemAssignments != null && currentPrcItemAssignments.Any())
					{
						var currentResIds = currentPrcItemAssignments.Where(e => e.EstResourceFk.HasValue).Select(e => e.EstResourceFk.Value).Distinct().ToList();
						var hasIsContractedAssignments = basePrcItemAssignments.Where(e => e.EstResourceFk.HasValue && currentResIds.Contains(e.EstResourceFk.Value) && e.IsContracted);
						if (hasIsContractedAssignments.Any())
						{
							prcItem.IsContracted = true;
						}
					}
				}
			}
			else
			{
				var parentPrcItemIds = prcItems.Select(e => e.PrcItemFk);
				var prcItemAssignments = prcItemAssignmentLogic.GetSearchList(e => e.PrcItemFk.HasValue && parentPrcItemIds.Contains(e.PrcItemFk.Value)).ToList();
				var resIds = prcItemAssignments.Select(e => e.EstResourceFk).Distinct().ToList();
				var ids = prcItemAssignments.Select(e => e.Id);
				var basePrcItemAssignments = prcItemAssignmentLogic.GetSearchList(e => resIds.Contains(e.EstResourceFk) && !ids.Contains(e.Id)).ToList();
				prcItemAssignmentLogic.SetIsContractEntities(basePrcItemAssignments);
				var prcItemAssignmentsDic = prcItemAssignments.Where(e => e.PrcItemFk.HasValue).GroupBy(e => e.PrcItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());
				foreach (var prcItem in prcItems)
				{
					var currentPrcItemAssignments = prcItemAssignmentsDic.ContainsKey(prcItem.Id) ? prcItemAssignmentsDic[prcItem.Id] : null;
					if (currentPrcItemAssignments != null && currentPrcItemAssignments.Any())
					{
						var currentResIds = currentPrcItemAssignments.Where(e => e.EstResourceFk.HasValue).Select(e => e.EstResourceFk.Value).Distinct().ToList();
						var hasIsContractedAssignments = basePrcItemAssignments.Where(e => e.EstResourceFk.HasValue && currentResIds.Contains(e.EstResourceFk.Value) && e.IsContracted);
						if (hasIsContractedAssignments.Any())
						{
							prcItem.IsContracted = true;
						}
					}
				}
			}
			return prcItems;
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="prcItems"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> SetPrcItemsTotalNoDiscount(IEnumerable<PrcItemEntity> prcItems)
		{
			if (!prcItems.Any())
			{
				return prcItems;
			}

			var companyLogic = new BasicsCompanyLogic();
			var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var company = companyLogic.GetCompanyById(loginCompanyId);
			var isCalculateOverGross = company != null ? company.IsCalculateOverGross : false;

			var headerInfosDic = new Dictionary<int, HeaderInfo>();
			var vatEvaluator = new VatEvaluator();
			foreach (var prcItem in prcItems)
			{
				HeaderInfo headerInfo = null;
				if (headerInfosDic.ContainsKey(prcItem.PrcHeaderFk))
				{
					headerInfo = headerInfosDic[prcItem.PrcHeaderFk];
				}
				else
				{
					headerInfo = GetHeaderInfoByPrcHeader(prcItem.PrcHeaderFk);
					headerInfosDic.Add(prcItem.PrcHeaderFk, headerInfo);
				}

				if (prcItem.BasItemTypeFk == 2 || prcItem.BasItemType2Fk == 3 || prcItem.BasItemType2Fk == 5 || prcItem.PriceUnit == 0)
				{
					prcItem.TotalNoDiscount = 0;
					prcItem.TotalCurrencyNoDiscount = 0;
				}
				else if (prcItem.Discount == 0 && prcItem.DiscountSplit == 0)
				{
					prcItem.TotalNoDiscount = prcItem.Total;
					prcItem.TotalCurrencyNoDiscount = prcItem.TotalOc;
				}
				else
				{
					var vatPercent = vatEvaluator.EvaluateVatPercent(prcItem.MdcTaxCodeFk, headerInfo.vatGroupId);
					prcItem.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(prcItem, vatPercent, isCalculateOverGross);
					prcItem.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(prcItem, vatPercent, isCalculateOverGross);
				}
			}
			return prcItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> UpdatePrcItemExtras(List<int> prcHeaderIds)
		{
			if (prcHeaderIds.Any())
			{
				var prcItems = this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));
				if (prcItems.Any())
				{
					foreach (var item in prcItems)
					{
						var prcItemPriceConditions = new PrcItemPriceConditionLogic().GetSearchList(e => e.PrcItemFk == item.Id && e.IsPriceComponent && e.IsActivated);
						if (prcItemPriceConditions != null && prcItemPriceConditions.Any())
						{
							var extraPriceConditions = prcItemPriceConditions.Where(e => e.IsPriceComponent && e.IsActivated);
							item.PriceExtra = extraPriceConditions.Sum(e => e.Total);
							item.PriceExtraOc = extraPriceConditions.Sum(e => e.TotalOc);
						}
					}
					this.Save(prcItems);
				}
				return prcItems;
			}
			return Array.Empty<PrcItemEntity>();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="moduleName"></param>
		public void UpdatePrcItemAfterUpdateDateEffective(int mainItemId, string moduleName)
		{
			IEnumerable<int> prcHeaderIds;
			switch (moduleName)
			{
				case "procurement.package":
					var package = Injector.Get<IPrcPackageLogic>().GetPackageById(mainItemId);
					var package2Headers = Injector.Get<IPackage2HeaderInfoProvider>().GetPackage2Headers(mainItemId);
					if (package2Headers != null && package2Headers.Any())
					{
						prcHeaderIds = package2Headers.CollectIds(e => e.PrcHeaderFk);
						UpdatePrcItemByHeaderIds(prcHeaderIds, package.BpdVatGroupFk);
					}
					break;
				case "procurement.requisition":
					var reqheader = Injector.Get<IGetReqHeaderLogic>().GetReqHeaderByIds(new int[] { mainItemId });
					if (reqheader != null && reqheader.Any())
					{
						prcHeaderIds = reqheader.CollectIds(e => e.PrcHeaderFk);
						var header = reqheader.First();
						UpdatePrcItemByHeaderIds(prcHeaderIds, header.BpdVatGroupFk);
					}
					break;
				case "procurement.quote":
					var qtnRequisitions = Injector.Get<IQuoteRequisitionLogic>().GetQuoteRequisitionsByQtnHeaderFk(mainItemId);
					if (qtnRequisitions != null && qtnRequisitions.Any())
					{
						prcHeaderIds = qtnRequisitions.CollectIds(e => e.PrcHeaderFk);

						var objectHeader = Injector.Get<IPrcCommonLogic>(moduleName).GetPrcDataByPrcHeaderFk(prcHeaderIds.First());
						if (objectHeader != null)
						{
							UpdatePrcItemByHeaderIds(prcHeaderIds, objectHeader.BpdVatGroupFk);
						}
						else
						{
							UpdatePrcItemByHeaderIds(prcHeaderIds, null);
						}
					}
					break;
				case "procurement.contract":
					var contractHeader = Injector.Get<IContractHeaderInfoProvider>().GetConHeadersByKey(new int?[] { mainItemId });
					if (contractHeader != null && contractHeader.Any())
					{
						prcHeaderIds = contractHeader.CollectIds(e => e.PrcHeaderFk);
						var currentContract = contractHeader.First();

						UpdatePrcItemByHeaderIds(prcHeaderIds, currentContract.BpdVatGroupFk);
					}
					break;
				default: break;
			}
		}

		private void UpdatePrcItemByHeaderIds(IEnumerable<int> prcHeaderIds, int? headerVatGroupFk)
		{
			if (prcHeaderIds.Any())
			{
				var prcItems = this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));
				if (prcItems.Any())
				{

					foreach (var item in prcItems)
					{
						var prcItemPriceConditions = new PrcItemPriceConditionLogic().GetSearchList(e => e.PrcItemFk == item.Id && e.IsPriceComponent && e.IsActivated);
						if (prcItemPriceConditions != null && prcItemPriceConditions.Any())
						{
							var extraPriceConditions = prcItemPriceConditions.Where(e => e.IsPriceComponent && e.IsActivated);
							item.PriceExtra = extraPriceConditions.Sum(e => e.Total);
							item.PriceExtraOc = extraPriceConditions.Sum(e => e.TotalOc);

							RecalculatePrcItemTotal(item, headerVatGroupFk);

						}
					}
					this.Save(prcItems);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <param name="exchangeRate"></param>
		/// <param name="remainNet"></param>
		/// <param name="moduleName"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> UpdateItemsAfterUpdateExchangeRate(List<int> prcHeaderIds, decimal exchangeRate, bool remainNet, string moduleName = "")
		{
			if (prcHeaderIds.Any())
			{
				var items = this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));
				if (items.Any())
				{
					foreach (var item in items)
					{
						if (remainNet)
						{
							item.PriceOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.Price, exchangeRate);
							item.PriceGrossOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.PriceGross, exchangeRate);
							item.TotalPriceOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.TotalPrice, exchangeRate);
							item.TotalPriceGrossOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.TotalPriceGross, exchangeRate);
							item.PriceExtraOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.PriceExtra, exchangeRate);

							item.TotalOc = PrcCalculationHelper.CalculateAmountOcByNotOc(item.Total, exchangeRate);
							item.TotalGrossOc = PrcCalculationHelper.CalculateAmountOcByNotOc(item.TotalGross, exchangeRate);

							item.DiscountSplitOc = PrcCalculationHelper.CalculateAmountOcByNotOc(item.DiscountSplit, exchangeRate);
							item.DiscountAbsoluteOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByDA(item, exchangeRate);
							item.DiscountAbsoluteGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByGross(item, exchangeRate);

							item.ChargeOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.Charge, exchangeRate);
						}
						else
						{
							item.Price = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.PriceOc, exchangeRate);
							item.PriceGross = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.PriceGrossOc, exchangeRate);
							item.TotalPrice = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.TotalPriceOc, exchangeRate);
							item.TotalPriceGross = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.TotalPriceGrossOc, exchangeRate);
							item.PriceExtra = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.PriceExtraOc, exchangeRate);

							item.Total = PrcCalculationHelper.CalculateAmountNotOcByOc(item.TotalOc, exchangeRate);
							item.TotalGross = PrcCalculationHelper.CalculateAmountNotOcByOc(item.TotalGrossOc, exchangeRate);

							item.DiscountSplit = PrcCalculationHelper.CalculateAmountNotOcByOc(item.DiscountSplitOc, exchangeRate);
							item.DiscountAbsolute = PrcCalculationHelper.CalculateDiscountAbsoluteByOc(item.DiscountAbsoluteOc, exchangeRate);
							item.DiscountAbsoluteGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByGrossOc(item.DiscountAbsoluteGrossOc, exchangeRate);

							item.Charge = PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.ChargeOc, exchangeRate);
						}
					}
					this.Save(items);
				}
				return items;
			}
			return Array.Empty<PrcItemEntity>();
		}

		/// <summary>
		/// get deleted prcItems of quote which from requisition originally.
		/// </summary>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetRequisitionPrcItemsItems(int mainItemId)
		{
			var prcHeaderFks = new List<int>() { mainItemId };
			IQuoteRequisitionLogic quoteReqLogic = RVPARB.BusinessEnvironment.GetExportedValue<IQuoteRequisitionLogic>();
			IGetReqHeaderLogic reqLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetReqHeaderLogic>();
			var quoteRequisition = quoteReqLogic.GetQuoteRequisitionByPrcHeaderFks(prcHeaderFks).FirstOrDefault();
			if (quoteRequisition != null)
			{
				var reqHeaderFk = quoteRequisition.ReqHeaderFk;
				int[] reqIds = { reqHeaderFk };
				var requisition = reqLogic.GetReqHeaderByIds(reqIds).FirstOrDefault();
				var reqItems = this.GetSearchList(e => e.PrcHeaderFk == requisition.PrcHeaderFk);
				return reqItems;
			}
			return new List<PrcItemEntity>();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="targetItem"></param>
		/// <param name="originItem"></param>
		public PrcItemEntity CopyPrcItemValues(PrcItemEntity targetItem, PrcItemEntity originItem)
		{
			var tempItem = originItem.Clone() as PrcItemEntity;
			tempItem.Id = targetItem.Id;
			tempItem.PrcHeaderFk = targetItem.PrcHeaderFk;
			tempItem.PrcItemFk = targetItem.PrcItemFk;
			tempItem.Version = targetItem.Version;
			tempItem.InsertedBy = targetItem.InsertedBy;
			tempItem.InsertedAt = targetItem.InsertedAt;
			tempItem.UpdatedBy = targetItem.UpdatedBy;
			tempItem.UpdatedAt = targetItem.UpdatedAt;
			targetItem = tempItem;
			return targetItem;
		}

		/// <summary>
		/// Copy item values from a new item.
		/// </summary>
		/// <param name="target">target item.</param>
		/// <param name="newItem">source item.</param>
		/// <returns></returns>
		public PrcItemEntity CopyValuesFromNewItem(PrcItemEntity target, PrcItemEntity newItem)
		{
			target.Id = newItem.Id;
			target.PrcHeaderFk = newItem.PrcHeaderFk;
			target.PrcItemFk = newItem.PrcItemFk;
			target.Itemno = newItem.Itemno;
			target.InstanceId = newItem.InstanceId;
			target.PrcPackageFk = newItem.PrcPackageFk;
			target.Version = newItem.Version;

			return target;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		public void UpdateItemsAfterUpdateMaterials(List<IPrcItemEntity> items)
		{
			if (items != null && items.Any())
			{
				var prcItems = new List<PrcItemEntity>();
				foreach (var item in items)
				{
					var prcItem = this.GetItemByKey(item.Id);
					prcItem.MdcMaterialFk = item.MdcMaterialFk;
					prcItems.Add(prcItem);
				}
				base.Save(prcItems);
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		/// <param name="conHeadersOfItems"></param>
		/// <param name="relatedConHeaders"></param>
		/// <param name="relatedItems"></param>
		public void GetRelatedContractsAndItems(IEnumerable<PrcItemEntity> items, out List<IContractHeaderData> conHeadersOfItems, out List<IContractHeaderData> relatedConHeaders, out List<PrcItemEntity> relatedItems)
		{
			var prcContractLogic = Injector.Get<IContractHeaderInfoProvider>();
			var prcHeaderFks = items.Select(e => e.PrcHeaderFk).Distinct().ToArray();
			var contractsOfItems = prcContractLogic.GetConHeaderByPrcHeaderIds(prcHeaderFks).ToList();
			conHeadersOfItems = contractsOfItems;
			var mainConIdsOfItems = new List<int>();
			foreach (var contract in contractsOfItems)
			{
				if (contract.ConHeaderFk.HasValue)
				{
					mainConIdsOfItems.Add(contract.ConHeaderFk.Value);
				}
				else
				{
					mainConIdsOfItems.Add(contract.Id);
				}
			}
			mainConIdsOfItems = mainConIdsOfItems.Distinct().ToList();
			var mainNChangeOrders = prcContractLogic.GetMainNChangeOrdersByBasicsIds(mainConIdsOfItems);
			var callOffs = prcContractLogic.GetCallOffByBasicsIds(mainConIdsOfItems);
			var allRelateConHeaders = new List<IContractHeaderData>();
			allRelateConHeaders.AddRange(mainNChangeOrders);
			allRelateConHeaders.AddRange(callOffs);
			allRelateConHeaders = allRelateConHeaders.DistinctBy(e => e.Id).ToList();
			relatedConHeaders = allRelateConHeaders;
			var allPrcHeaderFks = relatedConHeaders.Select(e => e.PrcHeaderFk).ToList();
			var isCanceledPrcItemStatus = new PrcItemstatusLogic().GetSearchList(e => e.IsCanceled).ToList();
			var isCanceledPrcItemStatusIds = (isCanceledPrcItemStatus != null && isCanceledPrcItemStatus.Any()) ? isCanceledPrcItemStatus.Select(e => e.Id).ToList() : new List<int>();
			relatedItems = this.GetSearchList(e => allPrcHeaderFks.Contains(e.PrcHeaderFk) && !isCanceledPrcItemStatusIds.Contains(e.PrcItemstatusFk)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="conHeadersOfItems"></param>
		/// <param name="relatedConHeaders"></param>
		/// <param name="relatedItems"></param>
		/// <returns></returns>
		public PrcItemEntity SetContractGrandQuantityTotalCallOffQuantity(PrcItemEntity item, List<IContractHeaderData> conHeadersOfItems, List<IContractHeaderData> relatedConHeaders, List<PrcItemEntity> relatedItems)
		{
			item.ContractGrandQuantity = 0;
			item.TotalCallOffQuantity = 0;
			item.RemainingQuantityForCallOff = 0;
			var conHeader = conHeadersOfItems.FirstOrDefault(e => e.PrcHeaderFk == item.PrcHeaderFk);
			if (conHeader != null)
			{
				var basicsContractId = conHeader.ConHeaderFk.HasValue ? conHeader.ConHeaderFk.Value : conHeader.Id;
				var basicsContract = relatedConHeaders.FirstOrDefault(e => e.Id == basicsContractId);
				var changeOrders = relatedConHeaders.Where(e => e.ConHeaderFk.HasValue && e.ConHeaderFk.Value == basicsContractId && e.ProjectChangeFk.HasValue).OrderBy(e => e.DateOrdered).ToList();
				var calloffs = relatedConHeaders.Where(e => e.ConHeaderFk.HasValue && e.ConHeaderFk.Value == basicsContractId && !e.ProjectChangeFk.HasValue).OrderBy(e => e.DateOrdered).ToList();
				var basicsAndChangeOrders = new List<IContractHeaderData>();
				if (basicsContract != null)
				{
					basicsAndChangeOrders.Add(basicsContract);
				}
				basicsAndChangeOrders.AddRange(changeOrders);
				var takenBasicsAndChangeOrders = basicsAndChangeOrders.DistinctBy(e => e.Id).ToList();
				var takenCallOffs = calloffs.DistinctBy(e => e.Id).ToList();
				var takenBasicsAndChangeOrderItems = new List<PrcItemEntity>();
				var takenCallOffItems = new List<PrcItemEntity>();
				var takenPrcHeaderFks = new List<int>();
				if (takenBasicsAndChangeOrders.Any())
				{
					takenPrcHeaderFks = takenBasicsAndChangeOrders.Select(e => e.PrcHeaderFk).ToList();
					var tt = relatedItems.Where(e => takenPrcHeaderFks.Contains(e.PrcHeaderFk) && e.Itemno == item.Itemno).ToList();
					takenBasicsAndChangeOrderItems.AddRange(tt);
				}
				if (takenCallOffs.Any())
				{
					takenPrcHeaderFks = takenCallOffs.Select(e => e.PrcHeaderFk).ToList();
					var tt = relatedItems.Where(e => takenPrcHeaderFks.Contains(e.PrcHeaderFk) && e.Itemno == item.Itemno).ToList();
					takenCallOffItems.AddRange(tt);
				}
				item.ContractGrandQuantity = takenBasicsAndChangeOrderItems.Sum(e => e.Quantity);
				item.TotalCallOffQuantity = takenCallOffItems.Sum(e => e.Quantity);
				item.RemainingQuantityForCallOff = item.ContractGrandQuantity - item.TotalCallOffQuantity;

			}
			return item;
		}

		/// <summary>
		/// Try create item number according selected item and insert position flag.
		/// </summary>
		/// <param name="selectedItem"></param>
		/// <param name="prcItems"></param>
		/// <param name="insertBefore"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentException">When selectedItem or prcItems is null or empty will throw exception.</exception>
		public int TryCreateInsertItemNumber(PrcItemEntity selectedItem, IEnumerable<PrcItemEntity> prcItems, bool insertBefore = false)
		{

			if (selectedItem == null || prcItems == null)
			{
				throw new ArgumentException($"Parameter {nameof(selectedItem)} and {nameof(prcItems)} can not be null.");
			}

			if (!prcItems.Any())
			{
				throw new ArgumentException($"Parameter {nameof(prcItems)} can not be empty.");
			}

			if (!prcItems.Any(p => p.Id == selectedItem.Id))
			{
				throw new ArgumentException($"The {nameof(selectedItem)} must be included in {nameof(prcItems)}.");
			}

			var selectedIndex = prcItems.TakeWhile(p => p.Id != selectedItem.Id).Count();
			var step = this.GetItemNumberIncrementStep();

			if (insertBefore)
			{
				var prevItem = selectedIndex > 0 ? prcItems.ElementAt(selectedIndex - 1) : null;
				return prevItem != null
					? selectedItem.Itemno - prevItem.Itemno > 1 ? selectedItem.Itemno - ((selectedItem.Itemno - prevItem.Itemno) / 2) : prcItems.Max(e => e.Itemno) + step
					: selectedItem.Itemno > 1 ? selectedItem.Itemno / 2 : prcItems.Max(e => e.Itemno) + step;
			}
			else
			{
				var nextItem = selectedIndex < prcItems.Count() - 1 ? prcItems.ElementAt(selectedIndex + 1) : null;
				return nextItem != null
					? nextItem.Itemno - selectedItem.Itemno > 1 ? selectedItem.Itemno + ((nextItem.Itemno - selectedItem.Itemno) / 2) : prcItems.Max(e => e.Itemno) + step
					: prcItems.Max(e => e.Itemno) + step;
			}
		}

		IDictionary<Int32, Int32> IOwnerMappingReadLogic.Read(IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			IDictionary<int, int> valuePairs = new Dictionary<int, int>();

			foreach (var item in ownerMapping)
			{
				if (item.Key is PrcItemEntity)
				{
					valuePairs.Add(item.Key.Id, item.Value.Id);
				}
			}
			return valuePairs;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entityDictionary"></param>
		/// <param name="prcItem"></param>
		/// <param name="rate"></param>
		/// <param name="vatGroupFk"></param>
		public void SetDiscountNAbsolute(IDictionary<string, object> entityDictionary, PrcItemEntity prcItem, decimal rate = 1m, int? vatGroupFk = null)
		{
			object discountObj = null;
			object discountAbObj = null;
			object discountAbOcObj = null;
			object discountAbGrossObj = null;
			object discountAbGrossOcObj = null;
			string discountStr = "Discount";
			string discountAbStr = "DiscountAbsolute";
			string discountAbOcStr = "DiscountAbsoluteOc";
			string discountAbGrossStr = "DiscountAbsoluteGross";
			string discountAbGrossOcStr = "DiscountAbsoluteGrossOc";
			var inputDiscount = entityDictionary.TryGetValue(discountStr, out discountObj);
			var inputDiscountAb = entityDictionary.TryGetValue(discountAbStr, out discountAbObj);
			var inputDiscountAbOc = entityDictionary.TryGetValue(discountAbOcStr, out discountAbOcObj);
			var inputDiscountAbGross = entityDictionary.TryGetValue(discountAbGrossStr, out discountAbGrossObj);
			var inputDiscountAbGrossOc = entityDictionary.TryGetValue(discountAbGrossOcStr, out discountAbGrossOcObj);
			decimal sumPrice = CalculationHelper.RoundAwayFromZero(prcItem.PriceOc + prcItem.PriceExtraOc + prcItem.Price + prcItem.PriceExtra + prcItem.PriceGross + prcItem.PriceGrossOc);
			if (!(inputDiscount || inputDiscountAb || inputDiscountAbOc || inputDiscountAbGross || inputDiscountAbGrossOc) || (sumPrice == 0m))
			{
				prcItem.Discount = 0m;
				prcItem.DiscountAbsolute = 0m;
				prcItem.DiscountAbsoluteOc = 0m;
				prcItem.DiscountAbsoluteGross = 0m;
				prcItem.DiscountAbsoluteGrossOc = 0m;
			}
			else
			{
				decimal? inputDiscountVal = inputDiscount ? Convert.ToDecimal(discountObj) : null;
				decimal? inputDiscountAbVal = inputDiscountAb ? Convert.ToDecimal(discountAbObj) : null;
				decimal? inputDiscountAbOcVal = inputDiscountAbOc ? Convert.ToDecimal(discountAbOcObj) : null;
				decimal? inputDiscountAbGrossVal = inputDiscountAbGross ? Convert.ToDecimal(discountAbGrossObj) : null;
				decimal? inputDiscountAbGrossOcVal = inputDiscountAbGrossOc ? Convert.ToDecimal(discountAbGrossOcObj) : null;
				if ((inputDiscount && inputDiscountAb && inputDiscountAbOc && inputDiscountAbGross && inputDiscountAbGrossOc))
				{
					prcItem.Discount = inputDiscountVal.Value;
					prcItem.DiscountAbsolute = inputDiscountAbVal.Value;
					prcItem.DiscountAbsoluteOc = inputDiscountAbOcVal.Value;
					prcItem.DiscountAbsoluteGross = inputDiscountAbGrossVal.Value;
					prcItem.DiscountAbsoluteGrossOc = inputDiscountAbGrossOcVal.Value;
				}
				else
				{
					var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
					decimal vatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(prcItem.MdcTaxCodeFk, vatGroupFk);
					SetDiscountNAbsoluteFromApiOrWf(prcItem, isCalculateOverGross, inputDiscountVal, inputDiscountAbVal, inputDiscountAbOcVal, inputDiscountAbGrossVal, inputDiscountAbGrossOcVal, rate, vatPercent);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="isCalculateOverGross"></param>
		/// <param name="inputDiscountVal"></param>
		/// <param name="inputDiscountAbVal"></param>
		/// <param name="inputDiscountAbOcVal"></param>
		/// <param name="inputDiscountAbGrossVal"></param>
		/// <param name="inputDiscountAbGrossOcVal"></param>
		/// <param name="rate"></param>
		/// <param name="vatPercent"></param>
		public void SetDiscountNAbsoluteFromApiOrWf(PrcItemEntity prcItem, bool? isCalculateOverGross, decimal? inputDiscountVal, decimal? inputDiscountAbVal, decimal? inputDiscountAbOcVal, decimal? inputDiscountAbGrossVal, decimal? inputDiscountAbGrossOcVal, decimal rate = 1m, decimal vatPercent = 0m)
		{
			bool isOverGross = this.CalculateOverGross(isCalculateOverGross);
			#region discountAbOc
			var discountAbOc = 0m;
			if (!inputDiscountAbOcVal.HasValue)
			{
				if ((isOverGross && inputDiscountAbGrossOcVal.HasValue) ||
					(!inputDiscountAbVal.HasValue && inputDiscountAbGrossOcVal.HasValue))
				{
					prcItem.DiscountAbsoluteGrossOc = inputDiscountAbGrossOcVal.Value;
					discountAbOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByGrossOc(prcItem, vatPercent);
				}
				else if (inputDiscountAbVal.HasValue)
				{
					prcItem.DiscountAbsolute = inputDiscountAbVal.Value;
					discountAbOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByDA(prcItem, rate);
				}
				else if (inputDiscountAbGrossVal.HasValue)
				{
					prcItem.DiscountAbsoluteGross = inputDiscountAbGrossVal.Value;
					discountAbOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByGrossOc((prcItem.DiscountAbsoluteGross * rate), vatPercent);
				}
				else if (inputDiscountVal.HasValue)
				{
					prcItem.Discount = inputDiscountVal.Value;
					discountAbOc = PrcCalculationHelper.CalculateDiscountAbsoluteOc(prcItem);
				}
			}
			else
			{
				discountAbOc = inputDiscountAbOcVal.Value;
			}
			prcItem.DiscountAbsoluteOc = discountAbOc;
			#endregion

			#region discountAb
			var discountAb = 0m;
			if (!inputDiscountAbVal.HasValue)
			{
				if ((isOverGross && inputDiscountAbGrossVal.HasValue) ||
					(!inputDiscountAbOcVal.HasValue && inputDiscountAbGrossVal.HasValue))
				{
					prcItem.DiscountAbsoluteGross = inputDiscountAbGrossVal.Value;
					discountAb = PrcCalculationHelper.CalculateDiscountAbsoluteByGross(prcItem, vatPercent);
				}
				else if (inputDiscountAbOcVal.HasValue)
				{
					prcItem.DiscountAbsoluteOc = inputDiscountAbOcVal.Value;
					discountAb = PrcCalculationHelper.CalculateDiscountAbsoluteByOc(prcItem, rate);
				}
				else if (inputDiscountAbGrossOcVal.HasValue)
				{
					prcItem.DiscountAbsoluteGrossOc = inputDiscountAbGrossOcVal.Value;
					discountAb = PrcCalculationHelper.CalculateDiscountAbsoluteByOc((prcItem.DiscountAbsoluteGrossOc / (100 + vatPercent) * 100), rate);
				}
				else if (inputDiscountVal.HasValue)
				{
					prcItem.Discount = inputDiscountVal.Value;
					discountAb = PrcCalculationHelper.CalculateDiscountAbsolute(prcItem);
				}
			}
			else
			{
				discountAb = inputDiscountAbVal.Value;
			}
			prcItem.DiscountAbsolute = discountAb;
			#endregion

			#region discountAbGrossOc
			var discountAbGrossOc = 0m;
			if (!inputDiscountAbGrossOcVal.HasValue)
			{
				if ((isOverGross && inputDiscountAbGrossVal.HasValue) ||
					(!inputDiscountAbOcVal.HasValue && inputDiscountAbGrossVal.HasValue))
				{
					prcItem.DiscountAbsoluteGross = inputDiscountAbGrossVal.Value;
					discountAbGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByGross(prcItem, rate);
				}
				else if (inputDiscountAbOcVal.HasValue)
				{
					prcItem.DiscountAbsoluteOc = inputDiscountAbOcVal.Value;
					discountAbGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByOc(prcItem, vatPercent);
				}
				else if (inputDiscountAbVal.HasValue)
				{
					prcItem.DiscountAbsolute = inputDiscountAbVal.Value;
					discountAbGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByOc((prcItem.DiscountAbsolute * rate), vatPercent);
				}
				else
				{
					discountAbGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByOc(discountAbOc, vatPercent);
				}
			}
			else
			{
				discountAbGrossOc = inputDiscountAbGrossOcVal.Value;
			}
			prcItem.DiscountAbsoluteGrossOc = discountAbGrossOc;
			#endregion

			#region discountAbGross
			var discountAbGross = 0m;
			if (!inputDiscountAbGrossVal.HasValue)
			{
				if ((isOverGross && inputDiscountAbGrossOcVal.HasValue) ||
					(!inputDiscountAbVal.HasValue && inputDiscountAbGrossOcVal.HasValue))
				{
					prcItem.DiscountAbsoluteGrossOc = inputDiscountAbGrossOcVal.Value;
					discountAbGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByGrossOc(prcItem, rate);
				}
				else if (inputDiscountAbVal.HasValue)
				{
					prcItem.DiscountAbsolute = inputDiscountAbVal.Value;
					discountAbGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByDA(prcItem, vatPercent);
				}
				else if (inputDiscountAbOcVal.HasValue)
				{
					prcItem.DiscountAbsoluteOc = inputDiscountAbOcVal.Value;
					discountAbGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByGrossOc((prcItem.DiscountAbsoluteOc * (100 + vatPercent) / 100), rate);
				}
				else
				{
					discountAbGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByDA(discountAb, vatPercent);
				}
			}
			else
			{
				discountAbGross = inputDiscountAbGrossVal.Value;
			}
			prcItem.DiscountAbsoluteGross = discountAbGross;
			#endregion

			#region discount
			var discount = 0m;
			if (!inputDiscountVal.HasValue)
			{
				if (inputDiscountAbOcVal.HasValue)
				{
					decimal totalPriceOcNoDiscount = PrcCalculationHelper.CalTotalPriceOcNoDiscount(prcItem);
					discount = totalPriceOcNoDiscount != 0m ?
						CalculationHelper.RoundAwayFromZero(inputDiscountAbOcVal.Value / totalPriceOcNoDiscount * 100) :
						0m;
				}
				else if (inputDiscountAbVal.HasValue)
				{
					decimal totalPriceNoDiscount = PrcCalculationHelper.CalTotalPriceNoDiscount(prcItem);
					discount = totalPriceNoDiscount != 0m ?
						CalculationHelper.RoundAwayFromZero(inputDiscountAbVal.Value / totalPriceNoDiscount * 100) :
						0m;
				}
				else
				{
					decimal totalPriceOcNoDiscount = PrcCalculationHelper.CalTotalPriceOcNoDiscount(prcItem);
					discount = totalPriceOcNoDiscount != 0m ?
						CalculationHelper.RoundAwayFromZero(discountAbOc / totalPriceOcNoDiscount * 100) :
						0m;
				}
			}
			else
			{
				discount = inputDiscountVal.Value;
			}
			prcItem.Discount = discount;
			#endregion
		}



		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="vatPercent"></param>
		/// <param name="isPriceUpdate"></param>
		public void UpdateDiscountNAbsoluteAfterPriceOrDiscountUpdate(PrcItemEntity prcItem, decimal vatPercent = 0m, bool isPriceUpdate = true)
		{
			if (isPriceUpdate)
			{
				prcItem.Discount = PrcCalculationHelper.CalculateDiscount(prcItem);
			}
			else
			{
				prcItem.DiscountAbsoluteOc = PrcCalculationHelper.CalculateDiscountAbsoluteOc(prcItem);
			}
			prcItem.DiscountAbsolute = PrcCalculationHelper.CalculateDiscountAbsolute(prcItem);
			prcItem.DiscountAbsoluteGross = PrcCalculationHelper.CalculateDiscountAbsoluteGross(prcItem);
			prcItem.DiscountAbsoluteGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOc(prcItem);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <param name="isCalculateOverGross"></param>
		/// <param name="inputPriceVal"></param>
		/// <param name="inputPriceOcVal"></param>
		/// <param name="inputPriceGrossVal"></param>
		/// <param name="inputPriceGrossOcVal"></param>
		/// <param name="rate"></param>
		/// <param name="vatPercent"></param>
		public void SetPriceFieldsFromApi(PrcItemEntity prcItem, bool? isCalculateOverGross, decimal? inputPriceVal, decimal? inputPriceOcVal, decimal? inputPriceGrossVal, decimal? inputPriceGrossOcVal, decimal rate = 1m, decimal vatPercent = 0m)
		{
			bool isOverGross = this.CalculateOverGross(isCalculateOverGross);
			if (inputPriceVal.HasValue)
			{
				prcItem.Price = inputPriceVal.Value;
				prcItem.PriceOc = inputPriceOcVal.HasValue ? inputPriceOcVal.Value : PrcCalculationHelper.CalculateUnitRateOcByNotOc(prcItem.Price, rate);
				prcItem.PriceGross = inputPriceGrossVal.HasValue ? inputPriceGrossVal.Value : PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent);
				prcItem.PriceGrossOc = inputPriceGrossOcVal ??
					(isOverGross ?
					PrcCalculationHelper.CalculateUnitRateOcByNotOc(prcItem.PriceGross, rate) :
					PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent));
			}
			else if (inputPriceOcVal.HasValue)
			{
				prcItem.PriceOc = inputPriceOcVal.Value;
				prcItem.Price = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceOc, rate);
				prcItem.PriceGrossOc = inputPriceGrossOcVal.HasValue ? inputPriceGrossOcVal.Value : PrcCalculationHelper.CalculatePriceOcGross(prcItem.PriceOc, vatPercent);
				prcItem.PriceGross = inputPriceGrossVal ??
					(isOverGross ?
					PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceGrossOc, rate) :
					PrcCalculationHelper.CalculatePriceGross(prcItem.Price, vatPercent));
			}
			else if (inputPriceGrossVal.HasValue)
			{
				prcItem.PriceGross = inputPriceGrossVal.Value;
				prcItem.Price = PrcCalculationHelper.CalculatePriceByPriceGross(prcItem.PriceGross, vatPercent);
				prcItem.PriceGrossOc = inputPriceGrossOcVal.HasValue ? inputPriceGrossOcVal.Value : PrcCalculationHelper.CalculateUnitRateOcByNotOc(prcItem.PriceGross, rate);
				prcItem.PriceOc = (isOverGross ?
					PrcCalculationHelper.CalculatePriceOcByPriceGrossOc(prcItem.PriceGrossOc, vatPercent) :
					PrcCalculationHelper.CalculateUnitRateOcByNotOc(prcItem.Price, rate));
			}
			else if (inputPriceGrossOcVal.HasValue)
			{
				prcItem.PriceGrossOc = inputPriceGrossOcVal.Value;
				prcItem.PriceGross = PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceGrossOc, rate);
				prcItem.PriceOc = PrcCalculationHelper.CalculatePriceOcByPriceGrossOc(prcItem.PriceGrossOc, vatPercent);
				prcItem.Price = (isOverGross ?
					PrcCalculationHelper.CalculatePriceByPriceGross(prcItem.PriceGross, vatPercent) :
					PrcCalculationHelper.CalculateUnitRateNotOcByOc(prcItem.PriceOc, rate));
			}

			var priceConditionLogic = new PrcItemPriceConditionCalcLogic();
			IEnumerable<PrcItemPriceConditionEntity> itemPriceConditions = priceConditionLogic.GetSearchList(x => prcItem.Id == x.PrcItemFk);
			if (itemPriceConditions != null && itemPriceConditions.Any())
			{
				RecalculatePriceCondition(prcItem, itemPriceConditions, rate);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		protected override void SavePreProcessing(IEnumerable<PrcItemEntity> entities)
		{
			new RoundingHelper("basics.material").SavePreRounding(entities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <returns></returns>
		public static Dictionary<int, bool> GetPrcHeaderIdsHasItems(IEnumerable<int> prcHeaderIds)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbContext.Entities<PrcItemEntity>()
					.Where(e => prcHeaderIds.Contains(e.PrcHeaderFk))
					.Select(e => e.PrcHeaderFk)
					.Distinct()
					.ToDictionary(e => e, _ => true);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <param name="chunkSize"></param>
		/// <returns></returns>
		public static Dictionary<int, bool> GetPrcHeaderIdsHasItemsParallel(IEnumerable<int> prcHeaderIds, int chunkSize = 500)
		{
			var result = new Dictionary<int, bool>();
			if (prcHeaderIds == null || !prcHeaderIds.Any())
			{
				return result;
			}

			// divide the prcHeaderIds into batches
			prcHeaderIds = prcHeaderIds.Distinct().ToList();
			var prcHeaderIdBatch = prcHeaderIds.Select((e, i) => new { Index = i, Value = e }).GroupBy(e => e.Index / chunkSize).Select(e => e.Select(x => x.Value).ToArray());
			var context = BusinessApplication.BusinessEnvironment.CurrentContext;

			ParallelProxy.ForEach(prcHeaderIdBatch, batch =>
			{
				BusinessApplication.BusinessEnvironment.CurrentContext = context;
				IEnumerable<int> prcHeaderFks;
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					prcHeaderFks = dbContext.Entities<PrcItemEntity>()
						.Where(e => prcHeaderIds.Contains(e.PrcHeaderFk))
						.Select(e => e.PrcHeaderFk).ToList();
				}

				lock (_lock)
				{
					if (prcHeaderFks != null && prcHeaderFks.Any())
					{
						foreach (var prcHeaderFk in prcHeaderFks)
						{
							if (!result.ContainsKey(prcHeaderFk))
							{
								result.Add(prcHeaderFk, true);
							}
						}
					}
				}
			});

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItem"></param>
		/// <returns></returns>
		public PrcItemEntity RecalculatePrcItemBudgetTotal(PrcItemEntity prcItem)
		{
			prcItem.BudgetTotal = PrcCalculationHelper.CalculateBudgetTotal(prcItem.BudgetPerUnit, prcItem.Quantity);
			return prcItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		/// <param name="vatPercent"></param>
		/// <param name="isOverGross"></param>
		public void RecalculatePrcItemsAfterVatPrecentChanged(List<PrcItemEntity> items, decimal vatPercent, bool? isOverGross = null)
		{
			bool isCalculateOverGross = this.CalculateOverGross(isOverGross);
			foreach (var item in items)
			{
				RecalculatePrcItemAfterVatPrecentChanged(item, vatPercent, isCalculateOverGross);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="vatPercent"></param>
		/// <param name="isOverGross"></param>
		public void RecalculatePrcItemAfterVatPrecentChanged(PrcItemEntity item, decimal vatPercent, bool? isOverGross = null)
		{
			bool isCalculateOverGross = this.CalculateOverGross(isOverGross);
			if (isCalculateOverGross)
			{
				item.Price = PrcCalculationHelper.CalculatePriceByPriceGross(item.PriceGross, vatPercent);
				item.PriceOc = PrcCalculationHelper.CalculatePriceOcByPriceGrossOc(item.PriceGrossOc, vatPercent);
				item.TotalPrice = PrcCalculationHelper.CalculateTotalPrice(item, vatPercent, isCalculateOverGross);
				item.TotalPriceOc = PrcCalculationHelper.CalculateTotalPriceOc(item, vatPercent, isCalculateOverGross);
				item.Total = PrcCalculationHelper.CalculateTotal(item, vatPercent, isCalculateOverGross);
				item.TotalOc = PrcCalculationHelper.CalculateTotalOc(item, vatPercent, isCalculateOverGross);
				item.TotalNoDiscount = PrcCalculationHelper.CalculateTotalNoDiscount(item, vatPercent, isCalculateOverGross);
				item.TotalCurrencyNoDiscount = PrcCalculationHelper.CalculateTotalOcNoDiscount(item, vatPercent, isCalculateOverGross);
				item.DiscountAbsolute = PrcCalculationHelper.CalculateDiscountAbsoluteByGross(item, vatPercent);
				item.DiscountAbsoluteOc = PrcCalculationHelper.CalculateDiscountAbsoluteOcByGrossOc(item, vatPercent);
			}
			else
			{
				item.PriceGross = PrcCalculationHelper.CalculatePriceGross(item.Price, vatPercent);
				item.PriceGrossOc = PrcCalculationHelper.CalculatePriceOcGross(item.PriceOc, vatPercent);
				item.TotalPriceGrossOc = PrcCalculationHelper.CalculateTotalPriceOCGross(item, vatPercent, isCalculateOverGross);
				item.TotalPriceGross = PrcCalculationHelper.CalculateTotalPriceGross(item, vatPercent, isCalculateOverGross);
				item.TotalGrossOc = PrcCalculationHelper.CalculateTotalOCGross(item, vatPercent, isCalculateOverGross);
				item.TotalGross = PrcCalculationHelper.CalculateTotalGross(item, vatPercent, isCalculateOverGross);
				item.DiscountAbsoluteGross = PrcCalculationHelper.CalculateDiscountAbsoluteGrossByDA(item, vatPercent);
				item.DiscountAbsoluteGrossOc = PrcCalculationHelper.CalculateDiscountAbsoluteGrossOcByOc(item, vatPercent);
			}
			if (item.BasItemTypeFk == 2 || item.BasItemType2Fk == 3 || item.BasItemType2Fk == 5)
			{
				item.TotalNoDiscount = 0;
				item.TotalCurrencyNoDiscount = 0;
				item.Total = 0;
				item.TotalOc = 0;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="isOverGross"></param>
		/// <returns></returns>
		private bool CalculateOverGross(bool? isOverGross)
		{
			return isOverGross.HasValue ? isOverGross.Value : new BasicsCompanyLogic().IsCalculateOverGross();
		}

		/// <summary>
		/// Get company's IsCalculateOverGross setting.
		/// </summary>
		/// <returns></returns>
		public bool GetCompanyIsCalculateOverGross()
		{
			return new BasicsCompanyLogic().IsCalculateOverGross();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="includeProvidedPrcItems"></param>
		/// <returns></returns>
		public List<PrcItemEntity> GetRelatedPrcItems(List<PrcItemEntity> prcItems, bool includeProvidedPrcItems = false)
		{
			if (prcItems?.Any() != true)
			{
				return new List<PrcItemEntity>();
			}

			var sourcePrcItemIds = prcItems.Select(prcItem => prcItem.PrcItemFk.HasValue ? prcItem.PrcItemFk.Value : prcItem.Id).Distinct().ToList();
			//TODO Solve this big data performance problem DEV-22100
			var relatedPrcItems = this.GetSearchList(e => sourcePrcItemIds.Contains(e.Id) || (e.PrcItemFk.HasValue && sourcePrcItemIds.Contains(e.PrcItemFk.Value))).ToList();

			if (!includeProvidedPrcItems)
			{
				var prcItemIds = prcItems.Select(e => e.Id).ToList();
				relatedPrcItems = relatedPrcItems.Where(e => !prcItemIds.Contains(e.Id)).ToList();
			}

			return relatedPrcItems;
		}


		/// <summary>
		/// Builds a mapping between each provided PrcItemEntity's ID and its related PrcItemEntity list,
		/// </summary>
		/// <param name="prcItemIds"></param>
		/// <param name="includeProvidedPrcItems"></param>
		/// <returns></returns>
		public Dictionary<long, List<PrcItemEntity>> GetRelatedPrcItemMapByIds(IEnumerable<long> prcItemIds, bool includeProvidedPrcItems = false)
		{
			if (prcItemIds?.Any() != true)
			{
				return [];
			}

			var prcItems = this.GetItemsByIds(prcItemIds.ToList());
			return this.GetRelatedPrcItemMap(prcItems, includeProvidedPrcItems);
		}
		/// <summary>
		/// Builds a mapping between each provided PrcItemEntity's ID and its related PrcItemEntity list,
		/// based on either direct linkage or reverse PrcItemFk references.
		/// </summary>
		/// <param name="prcItems">The list of PrcItemEntity objects to map from.</param>
		/// <param name="includeProvidedPrcItems">
		/// Whether to include the original PrcItemEntity itself in the result list for each ID.
		/// </param>
		/// <returns>
		/// A dictionary mapping each original PrcItemEntity's ID to its associated related PrcItemEntity list.
		/// </returns>
		public Dictionary<long, List<PrcItemEntity>> GetRelatedPrcItemMap(List<PrcItemEntity> prcItems, bool includeProvidedPrcItems = false)
		{
			if (prcItems?.Count == 0)
			{
				return [];
			}

			// Collect IDs of all provided PrcItems and their parent (PrcItemFk)
			var sourcePrcItemIds = prcItems
				 .Select(prcItem => prcItem.PrcItemFk ?? prcItem.Id)
				 .ToHashSet();

			// Get the list of all related PrcItems based on the IDs and their parent relationships
			var relatedPrcItems = this.GetSearchList(e =>
				 sourcePrcItemIds.Contains(e.Id) || (e.PrcItemFk.HasValue && sourcePrcItemIds.Contains(e.PrcItemFk.Value))
			).ToList();

			// Prepare the result dictionary to map each provided PrcItemId to its related items
			var result = new Dictionary<long, List<PrcItemEntity>>();

			foreach (var item in prcItems)
			{
				var keyId = item.Id;
				var itemNo = item.Itemno;
				List<PrcItemEntity> related;

				// If the current item has a parent (PrcItemFk), find the parent and all its children (siblings)
				if (item.PrcItemFk.HasValue)
				{
					// Get all items that are children of the parent (including the parent itself)
					related = relatedPrcItems
						 .Where(e => e.Itemno == itemNo &&
							  (e.PrcItemFk == item.PrcItemFk || e.Id == item.PrcItemFk || e.Id == keyId))
						 .ToList();
				}
				else
				{
					// If no parent (item is the root), find all its children
					related = relatedPrcItems
						 .Where(e => e.Itemno == itemNo &&
							  (e.PrcItemFk == keyId || e.Id == keyId))
						 .ToList();
				}

				// Add the related items for this item to the result dictionary
				if (!includeProvidedPrcItems)
				{
					related = related.Where(e => e.Id != keyId).ToList();  // Exclude the provided item itself if needed
				}

				result[keyId] = related;
			}
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemIds"></param>
		/// <param name="includeProvidedPrcItems"></param>
		/// <returns></returns>
		public List<PrcItemEntity> GetRelatedPrcItemsByIds(IEnumerable<long> prcItemIds, bool includeProvidedPrcItems = false)
		{
			if (prcItemIds?.Any() != true)
			{
				return new List<PrcItemEntity>();
			}

			var prcItems = this.GetItemsByIds(prcItemIds.ToList());
			return this.GetRelatedPrcItems(prcItems, includeProvidedPrcItems);
		}

		/// <summary>
		/// Update package (material) with additional items.
		/// Note: currently only support update package from requisition additional items.
		/// Similar function Refer to: [Route("SyncBaseBoQ")].
		/// </summary>
		/// <param name="updateParam">Parameter.</param>
		public UpdatePackageMaterialResponse UpdatePackageWithAdditionalItem(UpdatePackageMaterialRequest updateParam)
		{
			if (updateParam == null)
			{
				throw new ArgumentNullException(nameof(updateParam));
			}

			var response = new UpdatePackageMaterialResponse { ToUpdateCount = 0 };
			if (updateParam.SourceType != "requisition") // currently only support update package from requisition.
			{
				return response;
			}

			var reqHeaderId = updateParam.HeaderId;   // Req Header to process.
			var reqHeader = Injector.Get<IGetReqHeaderLogic>().GetReqHeaderByIds(new[] { reqHeaderId }).FirstOrDefault();
			if (reqHeader == null)
			{
				return response;
			}

			var pkg2HeaderEntity = reqHeader.Package2HeaderFk.HasValue  // Get Package2Header Entity from Req Header.
				? Injector.Get<IPrcPackage2HeaderLogic>().GetItemById(reqHeader.Package2HeaderFk.Value)
				: Injector.Get<IPrcPackage2HeaderLogic>().GetItemsByPrcPackageFKs(new[] { reqHeader.PackageFk.Value }).FirstOrDefault();
			if (pkg2HeaderEntity == null)
			{
				return response;
			}

			var prcHeader = new PrcHeaderLogic().GetItemById(pkg2HeaderEntity.PrcHeaderFk); // Package's PrcHeader.
			if (prcHeader == null)
			{
				return response;
			}

			// Get PrcItems of ReqHeader to update.
			var toUpdateReqPrcItems = GetPrcItemsByPrcHeader(reqHeader.PrcHeaderFk).Where(item => item.Id == item.PrcItemFk && !item.IsDisabled).ToList();
			response.ToUpdateCount = toUpdateReqPrcItems.Count;
			if (response.ToUpdateCount == 0)
			{
				return response;
			}

			CreatePackageItemsFromReqItems(reqHeader, pkg2HeaderEntity, prcHeader, toUpdateReqPrcItems);

			return response;
		}

		/// <summary>
		/// Create package items from Req items.
		/// </summary>
		/// <param name="reqHeader">ReqHeader.</param>
		/// <param name="pkg2HeaderEntity">Package2Header entity.</param>
		/// <param name="pkgPrcHeader">Package's PrcHeader</param>
		/// <param name="srcReqPrcItems">Source Req items to process.</param>
		private void CreatePackageItemsFromReqItems(IRequistionHeaderEntity reqHeader, IPrcPackage2HeaderEntity pkg2HeaderEntity, PrcHeaderEntity pkgPrcHeader, List<PrcItemEntity> srcReqPrcItems)
		{
			var step = GetPrcItemIncreaseStep();
			var totalNos = GetSearchList(prcItem => prcItem.PrcHeaderFk == pkg2HeaderEntity.PrcHeaderFk).Select(x => x.Itemno).ToHashSet();
			var projectId = reqHeader.ProjectFk.GetValueOrDefault();

			var prcItemPriceConditionLogic = new PrcItemPriceConditionLogic();
			var srcPrcItemIds = srcReqPrcItems.Select(x => x.Id).ToHashSet();
			var allQtnConPrcItems = GetSearchList(e => e.PrcItemFk.HasValue && !srcPrcItemIds.Contains(e.Id) && srcPrcItemIds.Contains(e.PrcItemFk.Value)).ToList(); // all reference QTN, CON subsequent prc items.
			var srcAllPCs = prcItemPriceConditionLogic.GetSearchList(pc => srcPrcItemIds.Contains(pc.PrcItemFk)).ToList(); // all pc items.
			var pcIds = srcAllPCs.Count > 0 ? prcItemPriceConditionLogic.GetNextIds(srcAllPCs.Count).ToList() : new List<int>();

			var prcPackageLogic = Injector.Get<IPrcPackageLogic>();
			var prcPackage = prcPackageLogic.GetPackageById(pkg2HeaderEntity.PrcPackageFk);
			var rateReq2Pkg = new ProcurementCommonExchangeRateLogic().GetExchangeRateByHomeCurrency(reqHeader.BasCurrencyFk, prcPackage.CurrencyFk, reqHeader.PackageFk, true);

			var pkgPrcItemsCreated = new List<PrcItemEntity>();
			var reqPrcItemsUpdated = new List<PrcItemEntity>();
			var qtnConPrcItemsUpdated = new List<PrcItemEntity>();
			var priceConditionCreated = new List<PrcItemPriceConditionEntity>();

			foreach (var srcPrcItem in srcReqPrcItems)
			{
				// Create and add new prcItem to package.
				var maxNo = GetMaxItemNoByStep(pkgPrcHeader.Id, totalNos, step);
				totalNos.Add(maxNo);

				var newTempEntity = CreateNew(pkgPrcHeader.Id, pkgPrcHeader.ConfigurationFk, projectId, maxNo, true);
				var entity = CopyValuesFromNewItem(srcPrcItem.Clone() as PrcItemEntity, newTempEntity);
				entity.PrcPackageFk = pkg2HeaderEntity.PrcPackageFk;
				pkgPrcItemsCreated.Add(entity);

				// Copy price conditions of prc item.
				var tempPCs = new List<PrcItemPriceConditionEntity>();
				if (srcAllPCs.Any())
				{
					var srcPCs = srcAllPCs.Where(e => e.PrcItemFk == srcPrcItem.Id);
					var takeCount = srcPCs.Count();
					var newIds = new List<int>(pcIds.Take(takeCount));
					pcIds.RemoveRange(0, newIds.Count);
					tempPCs = prcItemPriceConditionLogic.CreateFromItems(entity.Id, newIds, srcPCs);
					priceConditionCreated.AddRange(tempPCs);
				}

				// Recalculate PriceOc, then recalculate Price if req and package have different currency.
				if (rateReq2Pkg.HasValue && rateReq2Pkg.Value != 1m && rateReq2Pkg.Value != 0m)
				{
					entity.PriceOc = srcPrcItem.PriceOc / rateReq2Pkg.Value;    // calculate Package PrcItem’s PriceOc from Req PrcItem’s PriceOc
					entity.Price = PrcCalculationHelper.CalculateUnitRateNotOcByOc(entity.PriceOc, prcPackage.ExchangeRate);
					if (tempPCs.Any())
					{
						RecalculatePriceCondition(entity, tempPCs, prcPackage.ExchangeRate);
					}
					entity.PriceGross = 0;
					entity.PriceGrossOc = 0;
					RecalculatePrcItemTotalAndGross(entity, prcPackage.BpdVatGroupFk);
				}

				// Update source req prcItem.
				srcPrcItem.PrcItemFk = entity.Id;
				srcPrcItem.PrcPackageFk = entity.PrcPackageFk;
				reqPrcItemsUpdated.Add(srcPrcItem);

				// Update Req PrcItem's QTN, CON subsequent PrcItems' PrcItemFk.
				if (allQtnConPrcItems.Any())
				{
					var qtnConPrcItems = allQtnConPrcItems.Where(e => e.PrcItemFk == srcPrcItem.Id).ToList();
					foreach (var refItem in qtnConPrcItems)
					{
						refItem.PrcItemFk = entity.Id;
						refItem.PrcPackageFk = entity.PrcPackageFk;
					}
					qtnConPrcItemsUpdated.AddRange(qtnConPrcItems);
				}
			}

			// Save created and updated entities.
			using TransactionScope transaction = new(TransactionScopeOption.RequiresNew, new TransactionOptions() { IsolationLevel = IsolationLevel.ReadCommitted });

			Save(pkgPrcItemsCreated, false, true);    //true to Auto-generate InstanceId.
			prcItemPriceConditionLogic.Save(priceConditionCreated);

			reqPrcItemsUpdated.AddRange(qtnConPrcItemsUpdated);
			if (reqPrcItemsUpdated.Any())
			{
				Save(reqPrcItemsUpdated);
			}

			// Update CON PrcItem's Pes subsequent PesItems' PrcPackageFk.
			if (qtnConPrcItemsUpdated.Any())
			{
				var qtnConPrcItemIds = qtnConPrcItemsUpdated.Select(e => e.Id);
				Injector.Get<IPesItemLogic>().UpdatePesItemPrcPackageFk(qtnConPrcItemIds, pkg2HeaderEntity.PrcPackageFk);
			}

			prcPackageLogic.RecalucateTotals(pkg2HeaderEntity.PrcPackageFk); // Recalculate Package Totals.

			transaction.Complete();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="item"></param>
		/// <param name="companyCurrencyId"></param>
		/// <param name="headerCurrencyFk"></param>
		/// <param name="projectId"></param>
		/// <param name="materialCurrencyFk"></param>
		/// <param name="materialCharges"></param>
		/// <param name="exchangeRate"></param>
		public PrcItemEntity ResetChargeByMaterialCharge(PrcItemEntity item, int companyCurrencyId, int headerCurrencyFk, int? projectId, int? materialCurrencyFk, decimal materialCharges, decimal exchangeRate)
		{

			if (companyCurrencyId == materialCurrencyFk || !materialCurrencyFk.HasValue)
			{
				item.Charge = PrcCalculationHelper.CalculateChargeByMdcCharge(materialCharges);
				item.ChargeOc = PrcCalculationHelper.CalculateUnitRateOcByNotOc(item.Charge, exchangeRate);
			}
			else
			{
				decimal? rate = new ProcurementCommonExchangeRateLogic().GetExchangeRateOc(headerCurrencyFk, materialCurrencyFk.Value, projectId);
				item.ChargeOc = (!rate.HasValue || rate.Value == 0m) ? 0m : PrcCalculationHelper.CalculatePriceByMdcListPrice(materialCharges, rate.Value);
				item.Charge = exchangeRate == 0m ? 0m : PrcCalculationHelper.CalculateUnitRateNotOcByOc(item.ChargeOc, exchangeRate);
			}
			return item;
		}


		/// <summary>
		/// Populate the PrcItem entities with contextual extended data, including material information, units of measure (UOM),
		/// text descriptions, pricing conditions, delivered quantities, and other business-relevant fields.
		/// </summary>
		/// <param name="prcItems"></param>
		/// <param name="prcHeaderId"></param>
		/// <param name="moduleName"></param>
		public void PopulatePrcItemFields(IEnumerable<PrcItemEntity> prcItems, int prcHeaderId, string moduleName = null)
		{
			this.SetPrcItemsTotalNoDiscount(prcItems);
			// Step 1: Mark contracted items
			if (moduleName != null && moduleName is ProcurementCommonModuleDataConfiguration.PackageModuleIdentifier or
							 ProcurementCommonModuleDataConfiguration.RequisitionModuleIdentifier or
							 ProcurementCommonModuleDataConfiguration.QuoteModuleIdentifier)
			{
				this.SetPrcItemsIsContracted(prcItems, moduleName);
			}

			var itemIds = prcItems.CollectIds(e => e.Id).ToList();
			var mdcMaterialIds = prcItems.CollectIds(e => e.MdcMaterialFk).ToList();

			// Step 2: Prepare basic material-related data
			var mdcMaterials = new MaterialLogic().GetSearchList(e => mdcMaterialIds.Contains(e.Id), prcItems.Any()).ToList();
			var mdcGroupFks = mdcMaterials.CollectIds(e => e.MaterialGroupFk).ToList();
			var materialGroups = new MaterialGroupLogic().GetItemsByKey(mdcGroupFks).ToList();
			var mdcCatalogInfos = this.GetMaterialCatalogsByMaterialGroupIds(mdcGroupFks, materialGroups);

			var orderTextBlobs = new PrcItemblobLogic().GetSearchList(e => itemIds.Contains(e.PrcItemFk) && e.PrcTexttypeFk == TextTypeConstant.OrderText).ToList();
			var supplierTextBlobs = new PrcItemblobLogic().GetSearchList(e => itemIds.Contains(e.PrcItemFk) && e.PrcTexttypeFk == TextTypeConstant.SupplierText).ToList();
			var priceConditions = new PrcItemPriceConditionNewLogic().GetHaveLeadTimeFormulaPriceConditions(itemIds).ToList();

			var material2UomsDic = new Material2basUomLogic().GetMaterialIds2UomItems(mdcMaterialIds, mdcMaterials);
			var materialGroupsDic = materialGroups.GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.FirstOrDefault());
			var mdcCatalogInfosDic = mdcCatalogInfos.GroupBy(e => e.MaterialCatalogId).ToDictionary(e => e.Key, e => e.FirstOrDefault());
			var mdcMaterialsDic = mdcMaterials.GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.FirstOrDefault());

			// Step 3: Enrich each main item with material, text, pricing, and quantity info
			foreach (var item in prcItems)
			{
				if (item?.MdcMaterialFk is int materialId && mdcMaterialsDic.TryGetValue(materialId, out var material))
				{
					item.MaterialCode = material.Code;
					item.MaterialExternalCode = material.ExternalCode;
					item.MaterialDescription = material.Description1;
					item.MaterialStockFk = material.MdcMaterialStockFk;
					item.Material2Uoms = material2UomsDic.GetValueOrDefault(materialId, null);

					if (materialGroupsDic.TryGetValue(material.MaterialGroupFk, out var group) &&
						mdcCatalogInfosDic.TryGetValue(group.MaterialCatalogFk, out var catalog))
					{
						item.MaterialCatalogCode = catalog.MaterialCatalogCode;
						item.MaterialCatalogDescription = catalog.MaterialCatalogDescription;
						item.MaterialCatalogTypeFk = catalog.MaterialCatalogTypeFk;
						item.MaterialCatalogSupplier = catalog.MaterialCatalogSupplier;
					}
				}

				item.OrderText = orderTextBlobs.FirstOrDefault(e => e.PrcItemFk == item.Id)?.PlainText;
				item.SupplierText = supplierTextBlobs.FirstOrDefault(e => e.PrcItemFk == item.Id)?.PlainText;
				item.HasLeadTimeFormula = priceConditions.Any(e => e.PrcItemFk == item.Id);
			}

			// Step 4: Prepare contract delivery quantity data (only for package module)
			Dictionary<long, List<PrcItemEntity>> contractItemMap = [];
			Dictionary<long, decimal> deliveredQuantities = [];
			if (moduleName == ProcurementCommonModuleDataConfiguration.PackageModuleIdentifier)
			{
				var packageProvider = Injector.Get<IPackage2HeaderInfoProvider>();
				var contractProvider = Injector.Get<IContractHeaderInfoProvider>();
				var pesItemProvider = Injector.Get<IPesItemInfoProvider>();

				var subPackageIds = packageProvider.GetPackage2HeaderByPrcHeaderIds([prcHeaderId]).CollectIds(e => e.PrcPackageFk).ToArray();
				if (subPackageIds?.Any() == true)
				{
					var prcHeaderIds = contractProvider.GetContractByPackageIds(subPackageIds).Select(e => e.PrcHeaderFk).ToArray();
					if (prcHeaderIds?.Any() == true)
					{
						var contractPrcItems = this.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk))
										.Where(e => e.PrcItemFk.HasValue)
										.ToList();

						contractItemMap = contractPrcItems.GroupBy(e => e.PrcItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());
						var conPrcItemIds = contractPrcItems.CollectIds(e => e.Id).ToList();
						deliveredQuantities = pesItemProvider.CalculateDeliveredQuantitiesByPrcItemIds(conPrcItemIds) as Dictionary<long, decimal>;
					}
				}

				// Step 4-1: Calculate delivered and remaining quantities
				foreach (var item in prcItems)
				{
					item.QuantityDeliveredUi = 0;
					if (contractItemMap.TryGetValue(item.Id, out var relatedItems))
					{
						item.QuantityDeliveredUi = relatedItems.Sum(ci => deliveredQuantities.GetValueOrDefault(ci.Id, 0));
					}
					item.QuantityRemainingUi = item.Quantity - item.QuantityDeliveredUi;
				}
			}
		}

		/// <summary>
		/// Copy Address To PrcItem
		/// </summary>
		/// <param name="newItem"></param>
		public void CopyAddressToPrcItem(PrcItemEntity newItem)
		{
			if (newItem.BasAddressFk.HasValue)
			{
				var addressEntity = new AddressLogic().GetItemByKey(newItem.BasAddressFk.Value);
				if (addressEntity != null)
				{
					var addressCopy = addressEntity.Clone() as AddressEntity;
					addressCopy.Id = this.SequenceManager.GetNext("BAS_ADDRESS");
					addressCopy.Version = 0;
					addressCopy.InsertedBy = newItem.InsertedBy;
					addressCopy.InsertedAt = newItem.InsertedAt;
					addressCopy.UpdatedAt = null;
					addressCopy.UpdatedBy = null;
					newItem.AddressEntity = addressCopy;
					newItem.BasAddressFk = addressCopy.Id;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// <returns></returns>
		public List<PrcItemEntity> GetPrcItemByConHeaderId(int conHeaderId)
		{
			var conHeaderInfoLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IContractHeaderInfoProvider>();
			var conHeader = conHeaderInfoLogic.GetPureConHeadersByKeys(new List<int>() { conHeaderId }).FirstOrDefault();
			if (conHeader != null)
			{
				return GetPrcItemsByPrcHeader(conHeader.PrcHeaderFk).ToList();
			}

			return [];
		}

		/// <summary>
		/// Creates complete PRC items from materials including price conditions
		/// </summary>
		/// <param name="param">Creation parameters including header, config and package info</param>
		/// <param name="materialIds">IDs of materials to convert</param>
		/// <param name="priceListIds">Applicable price lists</param>
		/// <param name="maxNo">Starting item number</param>
		/// <param name="isSelfRef">Whether items reference themselves</param>
		/// <param name="providedHeaderInfo">Preloaded header info (optional)</param>
		/// <returns>List of created PRC items</returns>
		public List<PrcItemComplete> CreatePrcItemsCompleteFromMaterials(PrcItemCreateParameterEntity param, IEnumerable<int> materialIds, IEnumerable<int> priceListIds, int maxNo, bool isSelfRef = false, HeaderInfo providedHeaderInfo = null)
		{
			var result = new List<PrcItemComplete>();
			if (!materialIds.Any())
			{
				return result;
			}

			#region Prepare data
			var prcItemDefaultInfo = GetPrcItemDefaultInfo(param.ConfigurationFk);
			var headerInfo = providedHeaderInfo == null ? GetHeaderInfoByPrcHeader(param.PrcHeaderFk) : providedHeaderInfo;
			var mdcCommodities = new MdcCommoditySearchVLogic().GetItemsByKey(materialIds).ToList();
			var material2basUomsDic = new Material2basUomLogic().GetSearchVMdcIds2UomItems(materialIds, mdcCommodities);
			var prcStructureIds = mdcCommodities.CollectIds(e => e.PrcStructureFk).ToList();
			var prcStructureTaxCodes = prcStructureIds.Any() ? new PrcStructureTaxCodeLogic().GetSearchList(e => prcStructureIds.Contains(e.PrcStructureFk)).ToList() :[];
			var packageHeader = param.PrcPackageFk.HasValue ? Injector.Get<IPrcPackageLogic>().GetPackageById(param.PrcPackageFk.Value) : null;
			var priceListEntities = priceListIds.Any() ? new MaterialPriceListLogic().GetItemsByKey(priceListIds).ToList() : [];
			var basicPrcItems = param.ContractHeaderFk.HasValue ? GetPrcItemByConHeaderId(param.ContractHeaderFk.Value) : [];
			int? addressFk = (param.FrmStyle.HasValue && param.FrmHeaderFk.HasValue) ? GetHeaderAddressFk(param.FrmStyle.Value, param.FrmHeaderFk.Value) : null;
			var newAddressEntities = addressFk.HasValue ? new AddressLogic().BulkCloneAddress(addressFk.Value, mdcCommodities.Count) : null;

			int ledger = this.GetCompanyInfoProvider().GetLedgerContext(RVPARB.BusinessEnvironment.CurrentContext.ClientId);
			var companyCurrencyId = this.GetCompanyInfoProvider().GetCurrency();
			var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();

			var ids = GetNextIds(mdcCommodities.Count).GetEnumerator();
			var prcItemIncreaseStep = GetPrcItemIncreaseStep();

			bool isHasBasicContract = (param.FrmStyle == (int)FrmStyle.FromContract && param.ContractHeaderFk.HasValue);
			var priceConditionLogic = new PrcItemPriceConditionCalcLogic();
			var vatEvaluator = new VatEvaluator();
			#endregion

			foreach (var mdcCommodity in mdcCommodities)
			{
				#region Create new prc item
				ids.MoveNext();
				maxNo = maxNo + prcItemIncreaseStep;
				var entity = CreateNew(param.PrcHeaderFk, param.ConfigurationFk, param.ProjectFk, maxNo, isSelfRef, param.PrcPackageFk, ids.Current, prcItemDefaultInfo.PrcConfigurationEntity, prcItemDefaultInfo, packageHeader);
				UpdateAddressByAddressEntity(entity, newAddressEntities?.Pop());
				UpdatePrcItemAltFkByItemType2(entity);
				UpdateFieldsByCreateParam(entity, param);
				entity.PrcIncotermFk = param.PrcIncotermFk ?? entity.PrcIncotermFk;
				#endregion

				#region Set material fields to prc item
				// Apply material fields to prc item
				var prcStructureTaxCode = prcStructureTaxCodes.Find(e => e.MdcLedgerContextFk == ledger);
				var basicPrcItem = isHasBasicContract ? basicPrcItems.Find(e => e.MdcMaterialFk == mdcCommodity.Id) : null;
				var priceList = priceListEntities.Find(e => e.MaterialFk == mdcCommodity.Id);
				var material2basUoms = material2basUomsDic.ContainsKey(mdcCommodity.Id) ? material2basUomsDic[mdcCommodity.Id] : null;
				ApplyMaterialToPrcItem(new ApplyMaterialToPrcItemInfo() {
					entity = entity,
					material = mdcCommodity,
					companyCurrencyFk = companyCurrencyId,
					headerCurrencyFk = headerInfo.currencyFk,
					projectId = param.ProjectFk,
					exchangeRate = headerInfo.exchangeRate,
					vatGroupFk = headerInfo.vatGroupId,
					priceList = priceList,
					prcStructureTaxCode =prcStructureTaxCode,
					frmStyle = param.FrmStyle,
					basicPrcItem = basicPrcItem,
					material2basUoms = material2basUoms
				}, vatEvaluator);
				#endregion

				// Calculate price conditions and set priceExtra of PRC item
				var itemPriceConditions = priceConditionLogic.Reload(entity, headerInfo.exchangeRate, null, priceList?.Id, null, true);
				var currItemPriceConditions = itemPriceConditions.PriceConditions;
				RecalculatePriceCondition(entity, currItemPriceConditions, headerInfo.exchangeRate);

				// Calculate PRC item
				var vatPercent = vatEvaluator.EvaluateVatPercent(entity.MdcTaxCodeFk, headerInfo.vatGroupId);
				RecalculateOneItemTotalAndGross(entity, isCalculateOverGross, vatPercent, headerInfo.exchangeRate);

				result.Add(new PrcItemComplete()
				{
					PrcItem = entity,
					PriceConditionToSave = currItemPriceConditions.Select(x => x as PrcItemPriceConditionEntity)
				});
			}

			return result;
		}

		/// <summary>
		/// Gets default values for PRC item creation
		/// </summary>
		/// <param name="configurationFk">Configuration ID</param>
		/// <returns>Default values including status, types and incoterms</returns>
		/// <exception cref="InvalidOperationException">Thrown when default status not found</exception>
		private PrcItemDefualtInfo GetPrcItemDefaultInfo(int configurationFk)
		{
			var defaultPrcItemStatus = new PrcItemstatusLogic().GetDefault();
			if (defaultPrcItemStatus == null)
			{
				throw new InvalidOperationException(String.Format(COMMON_NLS.ERR_DefaultStatusNullMessage, "Prc Items"));
			}

			return new PrcItemDefualtInfo()
			{
				PrcIncotermFk = GetDefaultPrcIncotermId(),
				BasItemTypeFk = GetDefaultItemTypeId(),
				BasItemType2Fk = GetDefaultItemType2Id(),
				ItemType85Entity = new PrcItemType85Logic().GetDefault(),
				PrcItemstatusEntity = defaultPrcItemStatus,
				PrcConfigurationEntity = new PrcConfigurationLogic().GetItemByKey(configurationFk)
			};
		}

		/// <summary>
		/// Applies material data to PRC item including prices, descriptions and tax info, and ...
		/// </summary>
		/// <param name="info">Container with material and pricing data</param>
		/// <param name="vatEvaluator">VAT calculator</param>
		private void ApplyMaterialToPrcItem(ApplyMaterialToPrcItemInfo info, VatEvaluator vatEvaluator)
		{
			PrcItemEntity entity = info.entity;
			MdcCommoditySearchVEntity material = info.material;
			MaterialPriceListEntity priceList = info.priceList;
			PrcItemEntity basicPrcItem = info.basicPrcItem;
			bool hasPriceList = priceList != null;

			#region Set material-related properties
			entity.MdcMaterialFk = material.Id;
			entity.MaterialExternalCode = material.ExternalCode;
			entity.MaterialCatalogCode = material.CatalogCode;
			entity.MaterialCatalogDescription = material.CatalogDescriptionInfo?.Translated;
			entity.MaterialCatalogTypeFk = material.MaterialCatalogTypeFk;
			entity.MaterialCatalogSupplier = material.MaterialCatalogSupplier;
			entity.Description1 = !String.IsNullOrEmpty(material.DescriptionInfo?.Translated) ? material.DescriptionInfo.Translated : material.DescriptionInfo?.Description;
			entity.Description2 = !String.IsNullOrEmpty(material.DescriptionInfo2?.Translated) ? material.DescriptionInfo2.Translated : material.DescriptionInfo2?.Description;

			entity.BasUomFk = material.BasUomFk;
			entity.BasUomPriceUnitFk = material.BasUomPriceUnitFk;
			entity.PriceUnit =  material.PriceUnit;
			entity.FactorPriceUnit = (material.FactorPriceUnit.HasValue && material.FactorPriceUnit.Value != 0m) ? material.FactorPriceUnit.Value : 1;
			entity.PrcPriceConditionFk = material.PrcPriceconditionFk;

			entity.PrcStructureFk = material.PrcStructureFk;
			entity.MaterialStockFk = material.MaterialStockFk;

			entity.Userdefined1 = material.UserDefined1;
			entity.Userdefined2 = material.UserDefined2;
			entity.Userdefined3 = material.UserDefined3;
			entity.Userdefined4 = material.UserDefined4;
			entity.Userdefined5 = material.UserDefined5;

			bool hasPriceListCo2Project = (hasPriceList && priceList.Co2Project.HasValue);
			entity.Co2Project = hasPriceListCo2Project ? priceList.Co2Project.Value : material.Co2Project;
			entity.Co2Source = hasPriceListCo2Project ? priceList.Co2Source : material.Co2Source;
			entity.Co2ProjectTotal = CalculationHelper.RoundAwayFromZero((entity.Co2Project ?? 0m) * entity.Quantity, 3);
			entity.Co2SourceTotal = CalculationHelper.RoundAwayFromZero((entity.Co2Source ?? 0m) * entity.Quantity, 3);

			entity.SellUnit = hasPriceList ? priceList.SellUnit : material.SellUnit;
			entity.MinQuantity = hasPriceList ? priceList.MinQuantity : material.MinQuantity;
			entity.LeadTime = hasPriceList ? priceList.LeadTime : material.LeadTime;
			entity.LeadTimeExtra = 0;
			entity.TotalLeadTime = entity.LeadTime + entity.SafetyLeadTime + entity.LeadTimeExtra;

			entity.Material2Uoms = info.material2basUoms;
			entity.AlternativeUomFk = material.MaterialStock2UomFk ?? material.BasUomFk;
			entity.MdcSalesTaxGroupFk = info.prcStructureTaxCode != null ? info.prcStructureTaxCode.MdcSalesTaxGroupFk : entity.MdcSalesTaxGroupFk;
			UpdateAlternativeQuantity(entity, entity.Material2Uoms);

			var specification = !String.IsNullOrEmpty(material.SpecificationInfo?.Translated) ? material.SpecificationInfo.Translated : material.SpecificationInfo?.Description;
			if (!string.IsNullOrEmpty(specification))
			{
				entity.Specification = specification.Replace("<[^>]+>", "").Replace("(&nbsp;)", "");
			}

			if (entity.PrcStructureFk.HasValue)
			{
				entity.MdcTaxCodeFk = priceList != null ? priceList.TaxCodeFk : material.MdcTaxCodeFk;
			}
			#endregion

			#region Set price fields
			var vatPercent = vatEvaluator.EvaluateVatPercent(entity.MdcTaxCodeFk, info.vatGroupFk);
			if (priceList != null)
			{
				ResetPriceByMaterialPriceList(entity, info.companyCurrencyFk, info.headerCurrencyFk, info.projectId, priceList, info.exchangeRate, vatPercent);
			}
			else
			{
				ResetPriceByMaterial(entity, info.companyCurrencyFk, info.headerCurrencyFk, info.projectId, material.BasCurrencyFk, material.ListPrice, material.Charges, material.Discount, info.exchangeRate, vatPercent);
			}
			#endregion

			#region Update fields by different module
			if (info.frmStyle == (int)FrmStyle.FromContract && basicPrcItem != null)
			{
				UpdateFieldsBybasicPrcItemWithSameMaterial(entity, basicPrcItem);
			}
			else if (info.frmStyle == (int)FrmStyle.FromQuote)
			{
				entity.TotalLeadTime = entity.LeadTime + entity.SafetyLeadTime + entity.BufferLeadTime;
			}
			#endregion
		}

		/// <summary>
		/// Sets PrcItemAltFk to item's own ID when itemType2Fk is Base or BasePostponed
		/// </summary>
		/// <param name="entity">PRC item to update</param>
		public void UpdatePrcItemAltFkByItemType2(PrcItemEntity entity)
		{
			if (entity.BasItemType2Fk is ((int)BasItemType2Constants.Base) or ((int)BasItemType2Constants.BasePostponed))
			{
				entity.PrcItemAltFk = entity.Id;
			}
		}

		/// <summary>
		/// Updates PRC item fields from creation parameters
		/// </summary>
		/// <param name="entity">PRC item to update</param>
		/// <param name="param">Creation parameters containing new values</param>
		public void UpdateFieldsByCreateParam(PrcItemEntity entity, PrcItemCreateParameterEntity param)
		{
			entity.InstanceId = param.InstanceId ?? entity.InstanceId;
			entity.PrcPackageFk = param.PrcPackageFk ?? entity.PrcPackageFk;
			if (param.TaxCodeFk.HasValue && entity.PrcStructureFk == null)
			{
				entity.MdcTaxCodeFk = param.TaxCodeFk;
			}
			entity.BasPaymentTermFiFk = param.BasPaymentTermFiFk;
			entity.BasPaymentTermPaFk = param.BasPaymentTermPaFk;
		}

		/// <summary>
		/// Updates PRC item address reference if address is provided
		/// </summary>
		/// <param name="entity">PRC item to update</param>
		/// <param name="address">Address to assign (optional)</param>
		/// <remarks>
		/// Sets both AddressEntity and BasAddressFk when address is not null
		/// </remarks>
		private void UpdateAddressByAddressEntity(PrcItemEntity entity, AddressEntity address = null)
		{
			if (address == null)
			{
				return;
			}

			entity.AddressEntity = address;
			entity.BasAddressFk = entity.AddressEntity.Id;
		}

		/// <summary>
		/// Updates PRC item fields from matching basic contract item with same material
		/// </summary>
		/// <param name="entity">Target PRC item to update</param>
		/// <param name="basicPrcItem">Source contract item with same mateiral</param>
		private void UpdateFieldsBybasicPrcItemWithSameMaterial(PrcItemEntity entity, PrcItemEntity basicPrcItem)
		{
			if (entity == null || basicPrcItem == null)
			{
				return;
			}

			entity.SellUnit = basicPrcItem.SellUnit;
			entity.MinQuantity = basicPrcItem.MinQuantity;
			entity.LeadTime = basicPrcItem.LeadTime;
			entity.Charge = basicPrcItem.Charge;
			entity.ChargeOc = basicPrcItem.ChargeOc;
			entity.DiscountAbsolute = basicPrcItem.DiscountAbsolute;
			entity.DiscountAbsoluteOc = basicPrcItem.DiscountAbsoluteOc;
			entity.DiscountAbsoluteGross = basicPrcItem.DiscountAbsoluteGross;
			entity.DiscountAbsoluteGrossOc = basicPrcItem.DiscountAbsoluteGrossOc;
			entity.Discount = basicPrcItem.Discount;
			entity.TotalLeadTime = entity.LeadTime + entity.SafetyLeadTime + entity.LeadTimeExtra;

			entity.BasUomFk = basicPrcItem.BasUomFk > 0 ? basicPrcItem.BasUomFk : entity.BasUomFk;
			entity.MaterialStockFk = basicPrcItem.MaterialStockFk;
			entity.AlternativeUomFk = basicPrcItem.BasUomFk;
			entity.FactorPriceUnit = basicPrcItem.FactorPriceUnit != 0m ? basicPrcItem.FactorPriceUnit : 1;
			entity.PriceUnit = basicPrcItem.PriceUnit;
			entity.BasUomPriceUnitFk = basicPrcItem.BasUomPriceUnitFk;
			entity.PrcPriceConditionFk = basicPrcItem.PrcPriceConditionFk;

			entity.MdcTaxCodeFk = entity.PrcStructureFk.HasValue ? basicPrcItem.MdcTaxCodeFk : entity.MdcTaxCodeFk;
		}
	}

	/// <summary>
	///
	/// </summary>
	public class PrcItemGrpSetLogic : GrpSetDataBaseLogic<PrcItemEntity>
	{
		/// <summary>
		///
		/// </summary>
		public PrcItemGrpSetLogic()
		{
			PermissionGUID = "75F8704D0EEE480BA3DFD2528D99ADA1";
		}

		/// <summary>
		/// GetDbModel
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public Dictionary<string, object> Get(IEnumerable<PrcItemComplete> entities)
		{
			foreach (var entity in entities)
			{
				entity.Header = entity.PrcItem;
			}
			var _entities = base.BaseSave(entities, false);
			foreach (var entity in entities)
			{
				entity.PrcItem = _entities.Where(e => e.MainItemId == entity.MainItemId).Select(e => e.Header).FirstOrDefault(); ;
			}
			Dictionary<string, object> saveList = new Dictionary<string, object>();
			saveList.Add("Header", _entities.Where(e => e.Header != null).Select(e => e.Header).ToList());
			saveList.Add("AfterDeleteset", _entities.Where(e => e.AfterDeletesetFK != 0).Select(e => e.AfterDeletesetFK));
			return saveList;
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public void DeleteEntities(IEnumerable<PrcItemEntity> entities)
		{
			var grpSetLogic = Injector.Get<IControllingGrpSetLogic>();
			var ids = entities.Where(e => e.ControllinggrpsetFk != null).Select(e => e.ControllinggrpsetFk.Value);
			grpSetLogic.Delete(ids);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		public void DeleteSet(IEnumerable<int> ids)
		{
			var grpSetLogic = Injector.Get<IControllingGrpSetLogic>();
			grpSetLogic.Delete(ids, false);
		}
	}

	/// <summary>
	///
	/// </summary>
	public class HeaderInfo
	{
		/// <summary>
		///
		/// </summary>
		public int? vatGroupId { get; set; }

		/// <summary>
		///
		/// </summary>
		public decimal exchangeRate { get; set; }

		/// <summary>
		///
		/// </summary>
		public int? rubricFk { get; set; }

		/// <summary>
		///
		/// </summary>
		public int? headerId { get; set; }

		/// <summary>
		///
		/// </summary>
		public int companyId { get; set; }

		/// <summary>
		///
		/// </summary>
		public int currencyFk { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public class PrcItemDefualtInfo
	{
		/// <summary>
		///
		/// </summary>
		public int? PrcIncotermFk { get; set; }
		/// <summary>
		///
		/// </summary>
		public int BasItemTypeFk { get; set; }
		/// <summary>
		///
		/// </summary>
		public int BasItemType2Fk { get; set; }
		/// <summary>
		///
		/// </summary>
		public ItemType85Entity ItemType85Entity { get; set; }

		/// <summary>
		///
		/// </summary>
		public PrcItemstatusEntity PrcItemstatusEntity { get; set; }

		/// <summary>
		///
		/// </summary>
		public PrcConfigurationEntity PrcConfigurationEntity { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public class ApplyMaterialToPrcItemInfo {
		/// <summary>
		///
		/// </summary>
		public PrcItemEntity entity { get; set; }

		/// <summary>
		///
		/// </summary>
		public MdcCommoditySearchVEntity material { get; set; }

		/// <summary>
		///
		/// </summary>
		public MaterialPriceListEntity priceList { get; set; }

		/// <summary>
		///
		/// </summary>
		public int companyCurrencyFk { get; set; }

		/// <summary>
		///
		/// </summary>
		public int headerCurrencyFk { get; set; }

		/// <summary>
		///
		/// </summary>
		public int? projectId { get; set; }

		/// <summary>
		///
		/// </summary>
		public decimal exchangeRate { get; set; }

		/// <summary>
		///
		/// </summary>
		public int? vatGroupFk { get; set; }

		/// <summary>
		///
		/// </summary>
		public int? frmStyle{ get; set; }

		/// <summary>
		///
		/// </summary>
		public PrcStructureTaxEntity prcStructureTaxCode { get; set; }

		/// <summary>
		///
		/// </summary>
		public PrcItemEntity basicPrcItem { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<Material2UomItems> material2basUoms { get; set; }
	}
}
