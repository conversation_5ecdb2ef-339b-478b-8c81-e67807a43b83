using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.TaxCode.BusinessComponents;

namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class TransactionPostingScope : ITransactionPostingScope, IDisposable
	{
		/// <summary>
		///
		/// </summary>
		protected readonly int maxVatCodeLength = 16;

		/// <summary>
		///  #127916 - Treating the Controlling Units in Sales for transaction
		/// </summary>
		public bool IsTreatingCU { get; set; }

		/// <summary>
		///
		/// </summary>
		public int TransactionId { get; set; }

		/// <summary>
		/// #139560 - Build line reference for line type 3-6.
		/// </summary>
		public bool BuildLineReference { get; set; }

		/// <summary>
		///
		/// </summary>
		public int MaxLineReferenceNumber { get; set; }

		/// <summary>
		///
		/// </summary>
		public Dictionary<string, object> Globals { get; set; }

		/// <summary>
		///
		/// </summary>
		public Dictionary<string, object> Eaches { get; set; }

		/// <summary>
		///
		/// </summary>
		public string HeaderTable { get; set; }

		/// <summary>
		///
		/// </summary>
		public IIdentifyable CurrentHeader { get; set; }

		/// <summary>
		///
		/// </summary>
		public IIdentifyable ExcludeHeader { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<IIdentifyable> PartialHeaders { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<IIdentifyable> Headers { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<ITransactionEntity> TransactionsToSave { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<ITransactionEntity> SuccessTransactionsToSave { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<ITransactionEntity> TransactionsToDelete { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<IValidationEntity> ValidationsToSave { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<IValidationEntity> ValidationsToDelete { get; set; }

		/// <summary>
		///
		/// </summary>
		public List<IControllingGrpSetComplete> ControllingGrpSetToSave { get; set; }

		/// <summary>
		///
		/// </summary>
		public IValidationLogic ValidationLogic { get; set; }

		/// <summary>
		///
		/// </summary>
		public ITransactionLogic TransactionLogic { get; set; }

		/// <summary>
		///
		/// </summary>
		public ForeignKeyHandle FkHandle { get; set; }

		/// <summary>
		///
		/// </summary>
		public CompanyEntity Company { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<CompanyPeriodEntity> CompanyPeriods { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<PeriodStatusEntity> PeriodStatuses { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<IUnitEntity> Uoms { get; set; }

		/// <summary>
		///
		/// </summary>
		public VatEvaluator VatEvaluator  { get; set; }

		/// <summary>
		///
		/// </summary>
		public Dictionary<int, IEnumerable<IControllingGrpSetDTLEntity>> ControllingGrpSetDetails { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<BillingSchemaEntity> BillingSchemas { get; set; }

		/// <summary>
		///
		/// </summary>
		public BillingSchemaEntity CurrentBillingSchema
		{
			get
			{
				return this.BillingSchemas.FirstOrDefault(e => e.Id == ProvideHeaderBillingSchema(this.CurrentHeader));
			}
		}

		/// <summary>
		/// Procurement structure account resolver
		/// </summary>
		public PrcStructureAccountResolver AccountResolver { get; set; }

		/// <summary>
		/// The data cache object
		/// </summary>
		public Lazy<TransactionPostingCache> Cache { get; set; }

		/// <summary>
		/// DEV-27963 - A system option, the generation of transaction can be aborted completely if account is blocked
		/// </summary>
		public bool IsAbortTransactionIfAccountIsBlocked { get; set; }

		/// <summary>
		///
		/// </summary>
		public TransactionPostingScope()
		{
			this.TransactionId = 1;
			this.Headers = new List<IIdentifyable>();
			this.TransactionsToSave = new List<ITransactionEntity>();
			this.TransactionsToDelete = new List<ITransactionEntity>();
			this.ValidationsToSave = new List<IValidationEntity>();
			this.ValidationsToDelete = new List<IValidationEntity>();
			this.CompanyPeriods = new List<CompanyPeriodEntity>();
			this.PeriodStatuses = new List<PeriodStatusEntity>();
			this.VatEvaluator = new ();
			this.Globals = new Dictionary<string, object>();
			this.Eaches = new Dictionary<string, object>();
			this.FkHandle = new ForeignKeyHandle();
			this.ControllingGrpSetDetails = new Dictionary<int, IEnumerable<IControllingGrpSetDTLEntity>>();
			this.ControllingGrpSetToSave = new List<IControllingGrpSetComplete>();
			this.BillingSchemas = new List<BillingSchemaEntity>();
			this.SuccessTransactionsToSave = new List<ITransactionEntity>();
			this.Cache = new Lazy<TransactionPostingCache>(CreateCache);
			this.AccountResolver = new PrcStructureAccountResolver() { Cache = this.Cache, CreateValidation = CreateValidation };
			this.VatEvaluator.MatrixIssue += VatEvaluator_MatrixIssue;
		}

		private void VatEvaluator_MatrixIssue(Object sender, MatrixIssueEventArgs e)
		{
			if (!e.VatGroupFk.HasValue)
			{
				throw new ArgumentNullException("e.VatGroupFk");
			}

			var vatGroup = Cache.Value.GetVatGroup(e.VatGroupFk.Value);

			var messageId = e.Type switch
			{
				MatrixIssueType.NotFound => (int)BasMessageEnum.TaxCodeMatrixIsNotFound,
				MatrixIssueType.Invalid => (int)BasMessageEnum.InvalidTaxCodeMatrix,
				_ => throw new ArgumentOutOfRangeException("e.Type")
			};
			CreateValidation(messageId, e.TaxCode.Code, vatGroup.DescriptionInfo.Translated);
		}

		/// <summary>
		/// Create a cache object
		/// </summary>
		/// <returns></returns>
		protected virtual TransactionPostingCache CreateCache()
		{
			var instance = this.ProvideCacheInstance();

			instance.ControllingUnitValidator = e =>
			{
				var isValid = IsValidControllingUnit(e);

				if (!isValid)
				{
					CreateValidation((int)BasMessageEnum.InvalidControllingUnit, e.Code);
				}

				return isValid;
			};

			return instance;
		}

		/// <summary>
		/// Provide a real cache instance, it is allowed to extend TransactionPostingCache.
		/// </summary>
		/// <returns></returns>
		protected virtual TransactionPostingCache ProvideCacheInstance()
		{
			return new TransactionPostingCache();
		}

		/// <summary>
		///
		/// </summary>
		public void LoadBillingSchemas()
		{
			var billingSchemaFks = this.Headers.Select(e => ProvideHeaderBillingSchema(e)).Where(e => e.HasValue).Distinct();
			this.BillingSchemas = new BillingSchemaLogic().GetItemsByKey(billingSchemaFks);
		}

		/// <summary>
		///
		/// </summary>
		public void LoadSystemOptions()
		{
			this.IsAbortTransactionIfAccountIsBlocked = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.AbortTransactionIfAccountIsBlocked);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="messageId"></param>
		/// <param name="parameter1"></param>
		/// <param name="parameter2"></param>
		/// <param name="parameter3"></param>
		public virtual void CreateValidation(int messageId, string parameter1 = "", string parameter2 = "", string parameter3 = "")
		{
			if (this.ValidationLogic == null)
			{
				return;
			}

			var headerId = this.CurrentHeader.Id;
			var validation = this.ValidationLogic.Create(headerId);

			validation.HeaderFk = headerId;
			validation.MessageFk = messageId;
			validation.Parameter1 = this.Truncate(parameter1, 42);
			validation.Parameter2 = this.Truncate(parameter2, 42);
			validation.Parameter3 = this.Truncate(parameter3, 42);

			this.ValidationsToSave.Add(validation);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="text"></param>
		/// <param name="length"></param>
		/// <returns></returns>
		public string Truncate(string text, int length)
		{
			if (text == null)
			{
				return text;
			}

			text = text.Trim();

			if (text.Length > length)
			{
				text = text.Substring(0, length - 3) + "...";
			}

			return text;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="value"></param>
		/// <returns></returns>
		public decimal GetValueRound(decimal value)
		{
			var decimals = 2;
			return Math.Round(value, decimals, MidpointRounding.AwayFromZero);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public virtual bool IsValidControllingUnit(ControllingunitEntity entity)
		{
			return true;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="controllingUnitFk"></param>
		/// <returns></returns>
		public bool IsValidControllingUnitId(int controllingUnitFk)
		{
			return IsValidControllingUnit(GetControllingUnit(controllingUnitFk));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="controllingUnitFk"></param>
		/// <returns></returns>
		public ControllingunitEntity GetControllingUnit(int controllingUnitFk)
		{
			return this.Cache.Value.GetControllingUnit(controllingUnitFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcStructureFk"></param>
		/// <returns></returns>
		public PrcStructureEntity GetPrcStructure(int prcStructureFk)
		{
			return this.Cache.Value.GetPrcStructure(prcStructureFk);
		}

		/// <summary>
		/// Load uoms
		/// </summary>
		public void LoadUoms()
		{
			this.Uoms = Injector.Get<IGetUnitLogic>().IGetList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="uomFk"></param>
		/// <returns></returns>
		public IUnitEntity GetUom(int uomFk)
		{
			return this.Uoms.First(e => e.Id == uomFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="companyId"></param>
		/// <returns></returns>
		public IEnumerable<CompanyICPartnerEntity> GetCompanyICPartners(int companyId)
		{
			return this.Cache.Value.GetCompanyIcPartners(companyId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="companyId"></param>
		/// <param name="companyPartnerId"></param>
		/// <returns></returns>
		public CompanyICPartnerEntity GetCompanyICPartner(int companyId, int companyPartnerId)
		{
			return this.GetCompanyICPartners(companyId).FirstOrDefault(e => e.BasCompanyPartnerFk == companyPartnerId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="companyIcPartnerId"></param>
		/// <returns></returns>
		public IEnumerable<CompanyICPartnerAccEntity> GetCompanyICPartnerAccs(int companyIcPartnerId)
		{
			return this.Cache.Value.GetCompanyIcPartnerAccs(companyIcPartnerId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="companyIcPartnerId"></param>
		/// <param name="prcStructureId"></param>
		/// <returns></returns>
		public CompanyICPartnerAccEntity GetCompanyICPartnerAcc(int companyIcPartnerId, int prcStructureId)
		{
			return GetCompanyICPartnerAccs(companyIcPartnerId).FirstOrDefault(e => e.PrcStructureFk == prcStructureId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="companyICPartner"></param>
		/// <param name="prcStructureId"></param>
		/// <param name="isIcSurcharge"></param>
		/// <returns></returns>
		public string GetCompanyICPartnerAccount(CompanyICPartnerEntity companyICPartner, int? prcStructureId = null, bool isIcSurcharge = false)
		{
			var isIcRechargingBill = IsCurrentIcRecharging();
			var account = isIcRechargingBill ? companyICPartner.AccountRevenue : companyICPartner.AccountCost;

			if (!isIcSurcharge)
			{
				isIcSurcharge = IsCurrentIcSurcharge();
			}

			if (prcStructureId.HasValue)
			{
				var companyICPartnerAcc = this.SearchCompanyICPartnerAcc(companyICPartner.Id, prcStructureId.Value);
				if (companyICPartnerAcc != null)
				{
					account = isIcRechargingBill ? (isIcSurcharge ? companyICPartnerAcc.AccountRevenueSurcharge : companyICPartnerAcc.AccountRevenue) : companyICPartnerAcc.AccountCost;
				}
			}

			return account;
		}

		private CompanyICPartnerAccEntity SearchCompanyICPartnerAcc(int companyICPartnerId, int prcStructureId)
		{
			var companyICPartnerAcc = this.GetCompanyICPartnerAcc(companyICPartnerId, prcStructureId);

			if (companyICPartnerAcc == null)
			{
				var prcStructure = this.GetPrcStructure(prcStructureId);

				if (prcStructure.PrcStructureFk.HasValue)
				{
					return SearchCompanyICPartnerAcc(companyICPartnerId, prcStructure.PrcStructureFk.Value);
				}
			}

			return companyICPartnerAcc;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="controllingUnitFk"></param>
		/// <returns></returns>
		public bool IsIntercompanyCU(int controllingUnitFk)
		{
			var controllingUnitEntity = this.GetControllingUnit(controllingUnitFk);
			var headerCompanyId = ProvideHeaderCompany(this.CurrentHeader);
			return headerCompanyId.HasValue && controllingUnitEntity.CompanyFk != headerCompanyId.Value;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="controllingUnitFk"></param>
		/// <returns></returns>
		public CompanyICPartnerEntity GetCompanyICPartnerByCU(int controllingUnitFk)
		{
			var controllingUnitEntity = this.GetControllingUnit(controllingUnitFk);
			var headerCompanyId = ProvideHeaderCompany(this.CurrentHeader);
			CompanyICPartnerEntity companyICPartner = null;

			if (headerCompanyId.HasValue)
			{
				companyICPartner = GetCompanyICPartner(headerCompanyId.Value, controllingUnitEntity.CompanyFk);
			}

			if (companyICPartner == null)
			{
				throw new TransactionAbortedException((int)BasMessageEnum.IntercompanyPartnerIsNull, "Comapny IC partner is not existed!") { Parameter1 = controllingUnitEntity.Code };
			}

			return companyICPartner;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="controllingUnitFk"></param>
		/// <param name="disableIntercompany"></param>
		public virtual void FillControllingUnit(ITransactionEntity transaction, int controllingUnitFk, bool disableIntercompany = false)
		{
			var length = 32;
			var controllingUnitEntity = this.GetControllingUnit(controllingUnitFk);
			var lineType = 0;

			if (!string.IsNullOrWhiteSpace(transaction.LineType))
			{
				lineType = Convert.ToInt16(transaction.LineType);
			}

			transaction.ControllingUnitFk = controllingUnitFk;

			if (!DisableControllingUnitIcSwitch())
			{
				if (lineType > 2 && DoTreatIntercompany() && !disableIntercompany && IsIntercompanyCU(controllingUnitFk))
				{
					// #140192 - it is an intercompany transaction
					var companyICPartner = this.GetCompanyICPartnerByCU(controllingUnitFk);

					if (companyICPartner.MdcControllingunitIcFk == null)
					{
						throw new Exception("MdcControllingunitIcFk of Comapny IC partner is null!");
					}

					FillControllingUnitIcFk(transaction, companyICPartner.MdcControllingunitIcFk.Value);
					// override by ic
					controllingUnitEntity = this.GetControllingUnit(companyICPartner.MdcControllingunitIcFk.Value);
				}
			}

			transaction.ControllingUnitCode = controllingUnitEntity.Code;
			transaction.ControllingUnitAssign01 = this.Truncate(controllingUnitEntity.Assignment01, length);
			transaction.ControllingUnitAssign02 = this.Truncate(controllingUnitEntity.Assignment02, length);
			transaction.ControllingUnitAssign03 = this.Truncate(controllingUnitEntity.Assignment03, length);
			transaction.ControllingUnitAssign04 = this.Truncate(controllingUnitEntity.Assignment04, length);
			transaction.ControllingUnitAssign05 = this.Truncate(controllingUnitEntity.Assignment05, length);
			transaction.ControllingUnitAssign06 = this.Truncate(controllingUnitEntity.Assignment06, length);
			transaction.ControllingUnitAssign07 = this.Truncate(controllingUnitEntity.Assignment07, length);
			transaction.ControllingUnitAssign08 = this.Truncate(controllingUnitEntity.Assignment08, length);
			transaction.ControllingUnitAssign09 = this.Truncate(controllingUnitEntity.Assignment09, length);
			transaction.ControllingUnitAssign10 = this.Truncate(controllingUnitEntity.Assignment10, length);
			SetTransactionControllingGrpDetail(transaction, controllingUnitEntity);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="taxCodeFk"></param>
		public virtual void FillTaxCode(ITransactionEntity transaction, int taxCodeFk)
		{
			var vatEntity = this.GetVatEntity(taxCodeFk);
			transaction.TaxCodeFk = taxCodeFk;
			transaction.TaxCodeMatrixFk = vatEntity.UsedTaxCodeMatrix?.Id;
			transaction.VatCode = vatEntity.Code;
			this.EvalVatAmount(transaction, vatEntity.Percent);
			this.SetAdditionalVatInfo(transaction, vatEntity);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="vatEntity"></param>
		protected virtual void SetAdditionalVatInfo(ITransactionEntity transaction, VatEntity vatEntity)
		{

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="vatPercent"></param>
		public void EvalVatAmount(ITransactionEntity transaction, decimal vatPercent)
		{
			transaction.VatAmount = this.ComputeVatAmount(transaction.Amount, vatPercent);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="amount"></param>
		/// <param name="vatPercent"></param>
		/// <returns></returns>
		public decimal ComputeVatAmount(decimal amount, decimal vatPercent)
		{
			return this.GetValueRound(amount * vatPercent / 100);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <returns></returns>
		public decimal EvalVatPercent(ITransactionEntity transaction)
		{
			var vatGroupId = ProvideVatGroup();

			if (transaction.TaxCodeFk.HasValue)
			{
				return VatEvaluator.EvaluateVatPercentByTaxCodeMatrix(transaction.TaxCodeFk.Value, transaction.TaxCodeMatrixFk);
			}

			if (!string.IsNullOrWhiteSpace(transaction.VatCode))
			{
				return VatEvaluator.EvaluateVatPercentByVatCode(transaction.VatCode, vatGroupId, maxVatCodeLength);
			}

			return 0;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		public void EvalVatAmount(ITransactionEntity transaction)
		{
			decimal vatPercent = this.EvalVatPercent(transaction);

			if (vatPercent != 0)
			{
				this.EvalVatAmount(transaction, vatPercent);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="billingSchema"></param>
		/// <returns></returns>
		public decimal EvalAmount(IBillingSchemaPostingEntity billingSchema)
		{
			if (billingSchema.Factor.HasValue)
			{
				return billingSchema.ResultOc * billingSchema.Factor.Value;
			}

			return 0.0m;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual List<ITransactionEntity> GetCurrentSuccessTransactions()
		{
			return this.Eaches["CurrentSuccessTransactions"] as List<ITransactionEntity>;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual List<ITransactionEntity> GetCurrentSuccessTransactionsCopy()
		{
			return this.Eaches["CurrentSuccessTransactionsCopy"] as List<ITransactionEntity>;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="isTheSame"></param>
		public virtual void DeductCurrentAmount(ITransactionEntity transaction, Func<ITransactionEntity, ITransactionEntity, bool> isTheSame)
		{
			var successTransactions = this.GetCurrentSuccessTransactions();
			this.DeductAmount(transaction, isTheSame, successTransactions);
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual List<ITransactionEntity> GetChainSuccessTransactions()
		{
			return this.Eaches["ChainSuccessTransactions"] as List<ITransactionEntity>;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="isTheSame"></param>
		public virtual IEnumerable<ITransactionEntity> DeductChainAmount(ITransactionEntity transaction, Func<ITransactionEntity, ITransactionEntity, bool> isTheSame)
		{
			var successTransactions = this.GetChainSuccessTransactions();
			return this.DeductAmount(transaction, isTheSame, successTransactions);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="isTheSame"></param>
		/// <param name="successTransactions"></param>
		private IEnumerable<ITransactionEntity> DeductAmount(ITransactionEntity transaction, Func<ITransactionEntity, ITransactionEntity, bool> isTheSame, List<ITransactionEntity> successTransactions)
		{
			var sameTransactions = successTransactions.Where(e => isTheSame(e, transaction)).ToList();

			transaction.Amount -= sameTransactions.Sum(e => e.Amount);
			transaction.VatAmount -= sameTransactions.Sum(e => e.VatAmount);
			transaction.Quantity -= sameTransactions.Sum(e => e.Quantity);
			transaction.DiscountAmount -= sameTransactions.Sum(e => e.DiscountAmount);

			// Remove the processed items to create the missing group at last.
			foreach (var sameTransaction in sameTransactions)
			{
				successTransactions.Remove(sameTransaction);
			}

			return sameTransactions;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		public virtual void ClearVersion(IIdentifyable entity)
		{
			(entity as EntityBase).Version = 0;
		}

		/// <summary>
		/// Obsolete
		/// </summary>
		/// <param name="lineType"></param>
		public virtual void AddChainTransactionObsolete(string lineType)
		{
			var header = this.CurrentHeader;
			var chainSuccessTransactions = this.GetChainSuccessTransactions();
			var targetSuccessTransactions = chainSuccessTransactions.Where(e => e.LineType == lineType).ToList();

			// create the missing group
			if (targetSuccessTransactions.Count > 0)
			{
				foreach (var item in targetSuccessTransactions)
				{
					item.Id = this.TransactionLogic.GenerateNextId();
					item.HeaderFk = header.Id;
					item.TransactionId = this.TransactionId;
					item.IsSuccess = false;
					item.Amount *= -1;
					item.VatAmount *= -1;
					item.Quantity *= -1;
					this.FillChainTransaction(item);
					this.ClearVersion(item);
					this.TransactionsToSave.Add(item);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> CreateCurrrentTransaction()
		{
			var missedTransactions = this.GetCurrentSuccessTransactions();
			return this.CreateMissedTransaction(missedTransactions).Where(e => !this.IsInvalid(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineTypes"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> CreateCurrrentTransaction(IEnumerable<string> lineTypes)
		{
			var missedTransactions = this.GetCurrentSuccessTransactions().Where(e => lineTypes.Contains(e.LineType)).ToList();
			return this.CreateMissedTransaction(missedTransactions).Where(e => !this.IsInvalid(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineType"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> CreateChainTransaction(string lineType)
		{
			var chainSuccessTransactions = this.GetChainSuccessTransactions();
			var targetSuccessTransactions = chainSuccessTransactions.Where(e => e.LineType == lineType).ToList();
			return this.CreateMissedTransaction(targetSuccessTransactions);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="missedTransactions"></param>
		/// <returns></returns>
		protected virtual IEnumerable<ITransactionEntity> CreateMissedTransaction(IEnumerable<ITransactionEntity> missedTransactions)
		{
			var header = this.CurrentHeader;
			var result = new List<ITransactionEntity>();

			// create the missing group
			if (missedTransactions.Count() > 0)
			{
				var groups = missedTransactions.Select(e => new TransactionDecorator(e).DecorateControllingGroupSet(this)).GroupBy(e => new
				{
					e.Entity.DocumentType,
					e.Entity.ControllingUnitCode,
					e.Entity.VatCode,
					e.Entity.NominalAccount,
					e.Entity.NominalDimension,
					e.Entity.NominalDimension2,
					e.Entity.NominalDimension3,
					e.Entity.LineType,
					e.Entity.CompanyDeferalTypeFk,
					e.Entity.DateDeferalStart,
					e.Entity.MatchText,
					e.Entity.CodeRetention,
					e.ControllinggroupdetailText,
					e.ControllinggroupText
				});

				foreach (var group in groups)
				{
					var firstItem = group.First();
					var entity = firstItem.Entity;
					entity.Id = this.TransactionLogic.GenerateNextId();
					entity.HeaderFk = header.Id;
					entity.TransactionId = this.TransactionId;
					entity.IsSuccess = false;

					if (entity is IIncrementTransactionEntity)
					{
						var transaction = entity as IIncrementTransactionEntity;

						transaction.IncAmount = -1 * group.Sum(e => (e.Entity as IIncrementTransactionEntity).IncAmount);
						transaction.IncAmountOc = -1 * group.Sum(e => (e.Entity as IIncrementTransactionEntity).IncAmountOc);
						transaction.IncQuantity = -1 * group.Sum(e => (e.Entity as IIncrementTransactionEntity).IncQuantity);
						transaction.IncVatAmount = -1 * group.Sum(e => (e.Entity as IIncrementTransactionEntity).IncVatAmount);
						transaction.IncVatAmountOc = -1 * group.Sum(e => (e.Entity as IIncrementTransactionEntity).IncVatAmountOc);

						transaction.Amount = 0;
						transaction.AmountOc = 0;
						transaction.Quantity = 0;
						transaction.VatAmount = 0;
						transaction.VatAmountOc = 0;
					}
					else
					{
						entity.Amount = -1 * group.Sum(e => e.Entity.Amount);
						entity.VatAmount = -1 * group.Sum(e => e.Entity.VatAmount);
						entity.Quantity = -1 * group.Sum(e => e.Entity.Quantity);
					}

					if (entity.ControllinggrpsetFk.HasValue)
					{
						this.CreateControllingGrpSet(entity, firstItem.ControllingGrpSetDetails);

					}
					this.FillChainTransaction(entity);
					this.ClearVersion(entity);
					result.Add(entity);
				}
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		public virtual void FillChainTransaction(ITransactionEntity transaction)
		{

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transactions"></param>
		public void FillControllingUnitFk(IEnumerable<ITransactionEntity> transactions)
		{
			var companyId = Injector.GetCurrentContext().ClientId;
			var logic = new ControllingunitLogic();
			var mdcContextId = logic.CompanyInfo.GetMasterDataContext(companyId);
			var targetTransactions = transactions.Where(e => e.ControllingUnitFk == null).ToList();

			if (!targetTransactions.Any())
			{
				return;
			}

			var codes = targetTransactions.Select(e => e.ControllingUnitCode).Where(e => !string.IsNullOrWhiteSpace(e)).Distinct();
			var entities = logic.GetSearchList(e => e.ContextFk == mdcContextId && codes.Contains(e.Code));

			foreach (var transaction in targetTransactions)
			{
				var entity = entities.FirstOrDefault(e => e.Code == transaction.ControllingUnitCode);

				if (entity != null)
				{
					transaction.ControllingUnitFk = entity.Id;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual bool IsProgress()
		{
			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual bool IsCumulativeTransaction()
		{
			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual string GetDocumentType()
		{
			return "";
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineType"></param>
		/// <param name="isTheSame"></param>
		public void MergeSuccessedTransaction(string lineType, Func<ITransactionEntity, ITransactionEntity, bool> isTheSame)
		{
			var currentSuccessedTransactions = this.GetCurrentSuccessTransactions();
			var chainedSuccessedTransactions = this.GetChainSuccessTransactions();

			foreach (var item in currentSuccessedTransactions)
			{
				var theSameItems = chainedSuccessedTransactions.Where(e => e.LineType == lineType && isTheSame(e, item)).ToList();

				if (theSameItems.Count > 0)
				{
					foreach (var theSameItem in theSameItems)
					{
						item.Amount += theSameItem.Amount;
						item.VatAmount += theSameItem.VatAmount;
						item.Quantity += theSameItem.Quantity = 0;
						theSameItem.Amount = 0;
						theSameItem.VatAmount = 0;
						theSameItem.Quantity = 0;
						item.Amount = this.GetValueRound(item.Amount);
						item.VatAmount = this.GetValueRound(item.VatAmount);
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="source"></param>
		/// <param name="target"></param>
		/// <returns></returns>
		public virtual bool IsTheSame(ITransactionEntity source, ITransactionEntity target)
		{
			return source.LineType == target.LineType &&
				source.DocumentType == target.DocumentType &&
				source.MatchText == target.MatchText &&
				source.ControllingUnitCode == target.ControllingUnitCode &&
				source.NominalAccount == target.NominalAccount &&
				source.NominalDimension == target.NominalDimension &&
				source.NominalDimension2 == target.NominalDimension2 &&
				source.NominalDimension3 == target.NominalDimension3 &&
				//source.VatCode == target.VatCode &&
				source.TaxCodeFk == target.TaxCodeFk &&
				source.TaxCodeMatrixFk == target.TaxCodeMatrixFk &&
				source.CompanyDeferalTypeFk == target.CompanyDeferalTypeFk &&
				source.DateDeferalStart == target.DateDeferalStart &&
				source.CodeRetention == target.CodeRetention &&
				IsSameControllingGrpSet(source.ControllinggrpsetFk, target.ControllinggrpsetFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="setId1"></param>
		/// <param name="setId2"></param>
		/// <returns></returns>
		public bool IsSameControllingGrpSet(int? setId1, int? setId2)
		{
			var isSame = (setId1 == null && setId2 == null);

			if (!isSame)
			{
				if (setId1.HasValue && setId2.HasValue)
				{
					var detail1 = this.GetControllingGrpSetDetail(setId1.Value);
					var detail2 = this.GetControllingGrpSetDetail(setId2.Value);
					if (detail1 != null && detail2 != null)
					{
						isSame = IsSame(detail1.Select(e => e.ControllinggroupdetailFk), detail2.Select(e => e.ControllinggroupdetailFk)) &&
								IsSame(detail1.Select(e => e.ControllinggroupFk), detail2.Select(e => e.ControllinggroupFk));
					}
					else
					{
						return false;
					}
				}
				else
				{
					isSame = false;
				}
			}

			return isSame;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="collection1"></param>
		/// <param name="collection2"></param>
		/// <returns></returns>
		private bool IsSame(IEnumerable<int> collection1, IEnumerable<int> collection2)
		{
			var count = collection1.Count();

			if (count != collection2.Count())
			{
				return false;
			}

			var ordered1 = collection1.Distinct().OrderBy(e => e).ToArray();
			var ordered2 = collection2.Distinct().OrderBy(e => e).ToArray();

			for (var i = 0; i < count; i++)
			{
				if (ordered1[i] != ordered2[i])
				{
					return false;
				}
			}

			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <returns></returns>
		public virtual bool IsInvalid(ITransactionEntity transaction)
		{
			return transaction.Amount == 0 && transaction.Quantity == 0 && transaction.VatAmount == 0;
		}

		private void SetControllingUnitAssignDesc(Action<string> setter, int? controllingGrpDetailFk)
		{
			if (controllingGrpDetailFk == null)
			{
				return;
			}

			var entity = this.Cache.Value.GetControllingGrpDetail(controllingGrpDetailFk.Value);
			if (entity != null)
			{
				setter(string.IsNullOrWhiteSpace(entity.DescriptionInfo.Translated) ? entity.DescriptionInfo.Description : entity.DescriptionInfo.Translated);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="controllingUnitEntity"></param>
		public void SetTransactionControllingGrpDetail(ITransactionEntity transaction, ControllingunitEntity controllingUnitEntity)
		{
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign01desc = e, controllingUnitEntity.ControllingGrpDetail01Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign02desc = e, controllingUnitEntity.ControllingGrpDetail02Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign03desc = e, controllingUnitEntity.ControllingGrpDetail03Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign04desc = e, controllingUnitEntity.ControllingGrpDetail04Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign05desc = e, controllingUnitEntity.ControllingGrpDetail05Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign06desc = e, controllingUnitEntity.ControllingGrpDetail06Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign07desc = e, controllingUnitEntity.ControllingGrpDetail07Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign08desc = e, controllingUnitEntity.ControllingGrpDetail08Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign09desc = e, controllingUnitEntity.ControllingGrpDetail09Fk);
			SetControllingUnitAssignDesc(e => transaction.ControllingunitAssign10desc = e, controllingUnitEntity.ControllingGrpDetail10Fk);
		}

		/// <summary>
		///
		/// </summary>
		public virtual int AssetManagementAccountType { get { return 4; } }

		/// <summary>
		///
		/// </summary>
		public virtual int ProgressAccountType { get { return 2; } }

		/// <summary>
		///
		/// </summary>
		public virtual int DefaultAccountType { get { return 1; } }

		/// <summary>
		///
		/// </summary>
		public virtual int FullInventoryManagementAccountType { get { return 6; } }

		/// <summary>
		/// if ISPROGRESS = TRUE THEN PRC_ACCOUNTTYPE_FK has to be 2 ELSE 1,
		/// if PRJ_STOCK_FK.PRJ_STOCKACCOUNTINGTYPE_FK.ID = 1 we use ACCOUNTTYPE = 6 independent of the flag ISPROGRESS
		/// </summary>
		/// <param name="isAssetManagement"></param>
		/// <param name="isProgress"></param>
		/// <param name="isFullInventory"></param>
		/// <returns></returns>
		public int ProvidePrcAccountType(bool isAssetManagement, bool isProgress, bool isFullInventory)
		{
			return isFullInventory ? FullInventoryManagementAccountType :
			(isAssetManagement ? AssetManagementAccountType : (isProgress ? ProgressAccountType : DefaultAccountType));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="taxCodeFk"></param>
		/// <returns></returns>
		public virtual VatEntity GetVatEntity(int taxCodeFk)
		{
			var vatGroupId = this.ProvideVatGroup();
			return this.VatEvaluator.EvaluateVat(taxCodeFk, vatGroupId, maxVatCodeLength);
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual int? ProvideVatGroup()
		{
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="setIds"></param>
		public void LoadControllingGrpSetDetails(IEnumerable<int> setIds)
		{
			var loadedSetIds = this.ControllingGrpSetDetails.Select(e => e.Key).ToArray();
			var unloadedSetIds = setIds.Where(e => !loadedSetIds.Contains(e)).ToArray();

			if (unloadedSetIds.Any())
			{
				var unloadedEntities = Injector.Get<IControllingGrpSetDTLELogic>().GetListBySetIds(setIds).GroupBy(e => e.ControllinggrpsetFk);
				foreach (var group in unloadedEntities)
				{
					this.ControllingGrpSetDetails.Add(group.Key, group);
				}
			}

			// in case no details
			foreach (var setId in setIds)
			{
				if (!this.ControllingGrpSetDetails.ContainsKey(setId))
				{
					this.ControllingGrpSetDetails[setId] = new List<IControllingGrpSetDTLEntity>();
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="setId"></param>
		/// <returns></returns>
		public IEnumerable<IControllingGrpSetDTLEntity> GetControllingGrpSetDetail(int setId)
		{
			if (this.ControllingGrpSetDetails.ContainsKey(setId))
			{
				return this.ControllingGrpSetDetails[setId];
			}

			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="details"></param>
		public void CreateControllingGrpSet(ITransactionEntity transaction, IEnumerable<IControllingGrpSetDTLEntity> details)
		{
			var logic = Injector.Get<IControllingGrpSetLogic>();
			var complete = logic.CreateComplete(details);
			transaction.ControllinggrpsetFk = complete.SetId;
			this.ControllingGrpSetDetails[complete.SetId] = details;
			this.ControllingGrpSetToSave.Add(complete);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="isAssetManagement"></param>
		public void FillPostingType(ITransactionEntity transaction, bool? isAssetManagement = null)
		{
			if (isAssetManagement.HasValue)
			{
				transaction.PostingType = isAssetManagement.Value ? "Fixed Asset" : "G/L Account";
			}
			else
			{
				transaction.PostingType = "G/L Account";

				if (transaction.ControllingUnitFk.HasValue)
				{
					var controllingUnitEntity = this.GetControllingUnit(transaction.ControllingUnitFk.Value);

					if (controllingUnitEntity.Isassetmanagement)
					{
						transaction.PostingType = "Fixed Asset";
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="entity"></param>
		public virtual void FillAssetInfo(ITransactionEntity transaction, ISubTotalPostingEntity entity)
		{

		}

		/// <summary>
		///
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="key"></param>
		/// <param name="load"></param>
		/// <returns></returns>
		protected T GetFromEachs<T>(string key, Func<T> load = null)
		{
			T value;

			if (this.Eaches.ContainsKey(key))
			{
				value = (T)this.Eaches[key];
			}
			else if (load != null)
			{
				value = load();
				this.Eaches[key] = value;
			}
			else
			{
				value = default(T);
			}

			return value;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual bool IsConsolidatedTransaction()
		{
			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="value"></param>
		public virtual void FillNominalAccountFi(ITransactionEntity transaction, string value)
		{

		}

		/// <summary>
		/// Fill sub total information such as account description
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="decorator"></param>
		public virtual void FillSubtotalInfo(ITransactionEntity transaction, SubTotalPostingDecorator decorator)
		{

		}

		/// <summary>
		/// Clear sub total information such as account description
		/// </summary>
		/// <param name="transaction"></param>
		public virtual void ClearSubtotalInfo(ITransactionEntity transaction)
		{

		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual int? ProvideHeaderControllingUnit()
		{
			// should implement it in the concrete scope
			throw new NotImplementedException("ProvideHeaderControllingUnit");
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public virtual IEnumerable<ISubTotalPostingEntity> FilterSubTotalEntities(IEnumerable<ISubTotalPostingEntity> entities)
		{
			if (entities == null || !entities.Any())
			{
				return entities;
			}

			var scope = this;
			var headerCuFk = scope.ProvideHeaderControllingUnit();
			ControllingunitEntity headerCU = null;

			if (headerCuFk.HasValue)
			{
				headerCU = scope.GetControllingUnit(headerCuFk.Value);
			}

			var list = new List<ISubTotalPostingEntity>();

			foreach (var entity in entities)
			{
				if (entity.ControllingUnitFk.HasValue)
				{
					var cu = scope.GetControllingUnit(entity.ControllingUnitFk.Value);

					if (scope.IsValidControllingUnit(cu))
					{
						list.Add(entity);
					}
					else
					{
						if (headerCU != null && scope.IsValidControllingUnit(headerCU))
						{
							entity.ControllingUnitFk = headerCU.Id;
							list.Add(entity);
						}
						else
						{
							//scope.CreateValidation((int)BasMessageEnum.InvalidControllingUnit, cu.Code);
						}
					}
				}
				else
				{
					list.Add(entity);
				}
			}

			return list;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <returns></returns>
		protected virtual int? ProvideHeaderCompany(IIdentifyable header)
		{
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <returns></returns>
		protected virtual int? ProvideHeaderBillingSchema(IIdentifyable header)
		{
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="controllingunitIcFk"></param>
		public virtual void FillControllingUnitIcFk(ITransactionEntity transaction, int controllingunitIcFk)
		{

		}

		/// <summary>
		/// #140243 - Is current header intercompany recharging invoice/sales
		/// </summary>
		/// <returns></returns>
		public virtual bool IsCurrentIcRecharging()
		{
			return false;
		}

		/// <summary>
		/// #140246 - Is current header intercompany sales invoice/sales
		/// </summary>
		/// <returns></returns>
		public virtual bool IsCurrentIcSales()
		{
			return false;
		}

		/// <summary>
		/// #140243 - Is current header intercompany surcharge invoice/sales
		/// </summary>
		/// <returns></returns>
		public virtual bool IsCurrentIcSurcharge()
		{
			return false;
		}

		/// <summary>
		/// #140243 - do treat intercompany logic
		/// </summary>
		/// <returns></returns>
		public virtual bool DoTreatIntercompany()
		{
			return false;
		}

		/// <summary>
		/// #121091 - Intercompany Invoice II
		/// </summary>
		protected virtual bool DisableControllingUnitIcSwitch()
		{
			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual string NextLineReference()
		{
			this.MaxLineReferenceNumber += 10000;
			return this.MaxLineReferenceNumber.ToString();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="transactions"></param>
		public void InitMaxLineReferenceNumber(IEnumerable<ITransactionEntity> transactions)
		{
			foreach (var transaction in transactions)
			{
				var lineType = Convert.ToInt32(transaction.LineType);

				if (lineType == 10 || lineType == 11 || lineType == 12)
				{
					var lineReference = (transaction as ITransactionReferenceEntity).LineReference;

					if (lineReference != null && lineReference.Length > 0)
					{
						var referenceNumber = Convert.ToInt32(lineReference);

						if (referenceNumber > this.MaxLineReferenceNumber)
						{
							this.MaxLineReferenceNumber = referenceNumber;
						}
					}
				}
			}
		}

		/// <summary>
		/// Check controlling unit validity of billing schema.
		/// </summary>
		/// <param name="billingSchema"></param>
		/// <returns>validity</returns>
		public bool CheckBillingSchemaControllingUnit(IBillingSchemaPostingEntity billingSchema)
		{
			if (billingSchema.ControllingUnitFk == null && billingSchema.HasControllingUnit)
			{
				billingSchema.ControllingUnitFk = this.ProvideHeaderControllingUnit();
			}

			if (billingSchema.ControllingUnitFk.HasValue)
			{
				return IsValidControllingUnitId(billingSchema.ControllingUnitFk.Value);
			}

			return true;
		}

		/// <summary>
		///
		/// </summary>
		public void Dispose()
		{
			VatEvaluator.Dispose();
		}

		#region Depend by Logistic.Settlement, should be removed in future after they use common VatEvaluator

		private readonly Lazy<IEnumerable<TaxCodeEntity>> _lazyTaxCodes = new (() => new TaxCodeLogic().GetList());
		/// <summary>
		/// 
		/// </summary>
		[Obsolete("use VatEvaluator instead")]
		public IEnumerable<TaxCodeEntity> TaxCodes { get { return _lazyTaxCodes.Value; } }


		private IEnumerable<TaxCodeMatrixEntity> _taxCodeMatrixs = null;

		/// <summary>
		/// 
		/// </summary>
		[Obsolete("use VatEvaluator instead")]
		public IEnumerable<TaxCodeMatrixEntity> TaxCodeMatrixs
		{
			get
			{
				if (_taxCodeMatrixs == null)
				{
					var taxCodeIds = TaxCodes.Select(e => e.Id).ToArray();
					_taxCodeMatrixs = new TaxCodeMatrixLogic().GetSearchList(e => taxCodeIds.Contains(e.TaxCodeFk));
				}

				return _taxCodeMatrixs;
			}
		}

		#endregion
	}
}
