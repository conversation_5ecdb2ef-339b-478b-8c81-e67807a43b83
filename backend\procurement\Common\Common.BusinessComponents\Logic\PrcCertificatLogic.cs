using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Data.Entity;
using System.Threading.Tasks;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using System.ComponentModel.Composition;
using RIB.Visual.Basics.Core.Core;
using System.Linq.Expressions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RVPBizProConfig = RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;

namespace RIB.Visual.Procurement.Common.BusinessComponents
{
	/// <summary>
	/// prccertificatlogic
	/// </summary>
	[Export(typeof(IPrcCertificateInfoProvider))]
	public class PrcCertificateLogic : HeaderSubDataLogicBase<PrcCertificateEntity, int>, IPrcCertificateInfoProvider
	{
		/// <summary>
		/// create new item with default certificate type.
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="prcConfigHeaderFk"></param>
		/// <param name="structureFk"></param>
		/// <returns></returns>
		public PrcCertificateEntity CreateCertificate(int prcHeaderFk, int? prcConfigHeaderFk, int? structureFk)
		{
			var entity = new PrcCertificateEntity();
			entity.Id = SequenceManager.GetNext("PRC_CERTIFICATE");
			entity.RequiredBy = DateTime.Now;
			entity.RequiredAmount = 0;
			entity.PrcHeaderFk = prcHeaderFk;

			var certificateType = new RIB.Visual.BusinessPartner.Certificate.BusinessComponents.CertificateTypeLogic().GetDefault();
			if (certificateType != null)
			{
				entity.BpdCertificateTypeFk = certificateType.Id;
				entity.IsValued = certificateType.IsValued;
				entity.CommentText = null;
			}
			return entity;
		}

		/// <summary>
		/// create items from Configuration2Certificate table, all items will be loaded.
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="prcConfigurationFk"></param>
		/// <param name="structureFk"></param>
		/// <param name="netTotal"></param>
		/// <returns></returns>
		public IEnumerable<PrcCertificateEntity> CreateCertificatesFormConfig(int prcHeaderFk, int? prcConfigurationFk, int? structureFk, decimal? netTotal = null)
		{
			var items = new List<PrcCertificateEntity>();
			if (!structureFk.HasValue)
			{
				return items;
			}
			IEnumerable<int> structureIds;
			var certificate2ConfigTypes = GetValidConfig2CertsByConfigAndStructure(prcConfigurationFk, structureFk, out structureIds);
			if (!certificate2ConfigTypes.Any())
			{
				return items;
			}

			var certificatesDic = new Dictionary<int, PrcConfiguration2CertEntity>();
			var index = 0;
			var certificatedTypeIds = certificate2ConfigTypes.CollectIds(x => x.BpdCertificateTypeFk);
			foreach (var prcStructureId in structureIds)
			{
				var curCertificates = certificate2ConfigTypes.Where(e => e.PrcStructureFk == prcStructureId);
				foreach (var certificate in curCertificates)
				{
					if (!certificatesDic.ContainsKey(certificate.BpdCertificateTypeFk))
					{
						certificatesDic[certificate.BpdCertificateTypeFk] = certificate;
					}
				}
			}
			var certificateTypeLogic = new RIB.Visual.BusinessPartner.Certificate.BusinessComponents.CertificateTypeLogic();
			var certificateTypes = certificateTypeLogic.GetSearchList(x => certificatedTypeIds.Contains(x.Id), certificatedTypeIds.Any());
			var sequence = SequenceManager.GetNextList("PRC_CERTIFICATE", certificate2ConfigTypes.Count());

			foreach (var certificateType in certificateTypes)
			{
				var certificate2ConfigType = certificatesDic[certificateType.Id];
				var entity = new PrcCertificateEntity
				{
					Id = sequence[index++],
					RequiredBy = DateTime.Now,
					PrcHeaderFk = prcHeaderFk,
					BpdCertificateTypeFk = certificateType.Id,
					IsValued = certificateType.IsValued,
					Isrequired = certificate2ConfigType.IsRequired,
					Ismandatory = certificate2ConfigType.IsMandatory,
					Ismandatorysubsub = certificate2ConfigType.IsMandatorySubSub,
					Isrequiredsubsub = certificate2ConfigType.IsRequiredSubSub,
					CommentText = certificate2ConfigType.CommentText,
					GuaranteeCost = certificate2ConfigType.GuaranteeCost,
					GuaranteeCostPercent = certificate2ConfigType.GuaranteeCostPercent,
					ValidFrom = certificate2ConfigType.ValidFrom,
					ValidTo = certificate2ConfigType.ValidTo,
					RequiredAmount = certificate2ConfigType.Amount
				};

				if (entity.GuaranteeCostPercent.HasValue && netTotal.HasValue)
				{
					entity.RequiredAmount = Math.Round(entity.GuaranteeCostPercent.Value * netTotal.Value / 100, 6);
				}
				items.Add(entity);
			}
			return items;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcConfigurationFk"></param>
		/// <param name="structureFk"></param>
		/// <param name="structureIds"></param>
		/// <returns></returns>
		public IEnumerable<PrcConfiguration2CertEntity> GetValidConfig2CertsByConfigAndStructure(int? prcConfigurationFk, int? structureFk, out IEnumerable<int> structureIds)
		{
			return GetConfig2CertsByConfigAndStructure(prcConfigurationFk, structureFk, out structureIds, DateTime.Now);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcConfigurationFk"></param>
		/// <param name="structureFk"></param>
		/// <param name="structureIds"></param>
		/// <param name="validDate"></param>
		/// <returns></returns>
		#pragma warning disable S2325
		public IEnumerable<PrcConfiguration2CertEntity> GetConfig2CertsByConfigAndStructure(int? prcConfigurationFk, int? structureFk, out IEnumerable<int> structureIds, DateTime? validDate = null)
		{
			if (!prcConfigurationFk.HasValue || !structureFk.HasValue)
			{
				structureIds = [];
				return [];
			}
			var configuration = new RVPBizProConfig.PrcConfigurationLogic().GetItemByKey(prcConfigurationFk.Value);
			var prcStructureIds = structureIds = new PrcStructureLogic().GetParentIds(structureFk.Value).OrderByDescending(e => e);
			if (validDate == null)
			{
				var certificate2Configs = new PrcConfiguration2CertLogic().GetSearchList(x => x.PrcConfigHeaderFk == configuration.PrcConfigHeaderFk
														&& prcStructureIds.Contains(x.PrcStructureFk), prcStructureIds.Any());
				return certificate2Configs;
			}
			else
			{
				var certificate2Configs = new PrcConfiguration2CertLogic().GetSearchList(x => x.PrcConfigHeaderFk == configuration.PrcConfigHeaderFk
													&& (!x.ValidFrom.HasValue || validDate >= x.ValidFrom.Value)
													&& (!x.ValidTo.HasValue || validDate <= x.ValidTo.Value)
													&& prcStructureIds.Contains(x.PrcStructureFk), prcStructureIds.Any());
				return certificate2Configs;
			}
		}
		#pragma warning restore S2325
		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="oldPrcHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<PrcCertificateEntity> CopyCertificate(int prcHeaderFk, int oldPrcHeaderFk)
        {
            IEnumerable<PrcCertificateEntity> oldEntities = GetSearchListComplete(x => x.PrcHeaderFk == oldPrcHeaderFk, true);
            IEnumerable<PrcCertificateEntity> entities = GetSearchListComplete(x => x.PrcHeaderFk == prcHeaderFk, true);

            foreach (var entity in entities)
            {
                int certificateType = entity.BpdCertificateTypeFk;

                PrcCertificateEntity existingEntity = oldEntities.FirstOrDefault(oldEntity => oldEntity.BpdCertificateTypeFk == certificateType);

                if (existingEntity != null)
                {
                    entity.Id = existingEntity.Id;
                    entity.InsertedBy = existingEntity.InsertedBy;
                    entity.Version = existingEntity.Version;
                    entity.InsertedAt = existingEntity.InsertedAt;
                    entity.UpdatedAt = existingEntity.UpdatedAt;
                    entity.UpdatedBy = existingEntity.UpdatedBy;
                    entity.PrcHeaderFk = existingEntity.PrcHeaderFk;
                }
                else
                {
                    entity.Id = GetNextId();
                    entity.InsertedBy = 0;
                    entity.Version = 0;
                    entity.InsertedAt = DateTime.Now;
                    entity.UpdatedAt = null;
                    entity.UpdatedBy = null;
                    entity.PrcHeaderFk = oldPrcHeaderFk;
                }
            }
            return entities;
        }


		/// <summary>
		///
		/// </summary>
		/// <param name="sourcePrcHeaderId"></param>
		/// <param name="targetPrcHeaderId"></param>
		public void CopyAndSaveCertificates(int sourcePrcHeaderId, int targetPrcHeaderId)
		{
			var sourcePrcCertificates = this.GetListByParentId(sourcePrcHeaderId);
			List<PrcCertificateEntity> prcCertificatesToSave = new List<PrcCertificateEntity>();
			foreach (var certificates in sourcePrcCertificates)
			{
				PrcCertificateEntity newCertificates = certificates.Clone() as PrcCertificateEntity;
				newCertificates.Id = this.GetNextId();
				newCertificates.PrcHeaderFk = targetPrcHeaderId;
				newCertificates.Version = 0;
				newCertificates.InsertedAt = DateTime.Now;
				newCertificates.UpdatedAt = null;
				newCertificates.UpdatedBy = null;
				prcCertificatesToSave.Add(newCertificates);
			}
			if (prcCertificatesToSave.Any())
			{
				this.Save(prcCertificatesToSave);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <param name="mdcMaterialId"></param>
		/// <param name="save"></param>
		/// <param name="certTypeIds">if it is null, regard it as no UI or UI has not been showed, then will filter by source in DB</param>
		/// <returns></returns>
		public IDictionary<string, object> CopyCertificatesFromMaterial(int prcHeaderId, int? mdcMaterialId, bool save = true, IEnumerable<int> certTypeIds = null)
		{
			if (mdcMaterialId == null)
			{
				return new Dictionary<string, object>();
			}
			var materialCerts = new Material2CertificateLogic().GetSearchList(e => e.MaterialFk == mdcMaterialId.Value);
			var prcCerts = this.GetSearchList(e => e.PrcHeaderFk == prcHeaderId);
			if (certTypeIds == null)
			{
				certTypeIds = prcCerts.CollectIds(e => e.BpdCertificateTypeFk);
			}

			var copyResult = CopyCertificatesFromMaterialCerts(prcHeaderId, materialCerts, certTypeIds);
			var existedCerts = copyResult.ExistedMaterial2Certificates;
			var result = new List<PrcCertificateEntity>();
			var invCertificatesNew = copyResult.NewPrcCertificates;
			if (invCertificatesNew?.Any() == true)
			{
				result.AddRange(invCertificatesNew);
			}
			if (save)
			{
				if (existedCerts.Any())
				{
					foreach (var existedCert in existedCerts)
					{
						var mark = false;
						var prcCert = prcCerts.FirstOrDefault(e => e.BpdCertificateTypeFk == existedCert.BpdCertificateTypeFk);
						if (prcCert != null)
						{
							if (existedCert.IsRequired && !prcCert.Isrequired)
							{
								prcCert.Isrequired = true;
								mark = true;
							}
							if (existedCert.IsMandatory && !prcCert.Ismandatory)
							{
								prcCert.Ismandatory = true;
								mark = true;
							}
							if (existedCert.IsRequiredSub && !prcCert.Isrequiredsubsub)
							{
								prcCert.Isrequiredsubsub = true;
								mark = true;
							}
							if (existedCert.IsMandatorySub && !prcCert.Isrequiredsubsub)
							{
								prcCert.Ismandatorysubsub = true;
								mark = true;
							}
							if (!String.IsNullOrEmpty(existedCert.CommentText) && String.IsNullOrEmpty(prcCert.CommentText))
							{
								prcCert.CommentText = existedCert.CommentText;
								mark = true;
							}
							if (mark)
							{
								result.Add(prcCert);
							}
						}
					}
				}

				if (result.Any())
				{
					this.Save(result);
				}
			}

			var dictionaryValue = new Dictionary<string, object>();
			dictionaryValue.Add("invCertificatesNew", invCertificatesNew);
			dictionaryValue.Add("existedCertificates", existedCerts);

			return dictionaryValue;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <param name="materialCerts"></param>
		/// <param name="existedCertTypeIds"></param>
		/// <returns></returns>
		public CopyCertificatesFromMaterialResult CopyCertificatesFromMaterialCerts(int prcHeaderId, IEnumerable<Material2CertificateEntity> materialCerts, IEnumerable<int> existedCertTypeIds = null)
		{
			if (materialCerts?.Any() != true)
			{
				return new CopyCertificatesFromMaterialResult();
			}

			var existedCerts = existedCertTypeIds?.Any() == true ? materialCerts.Where(e => existedCertTypeIds.Contains(e.BpdCertificateTypeFk)).ToList() : [];
			var notExistedCerts = materialCerts.Except(existedCerts).ToList();
			var prcCertificatesNew = new List<PrcCertificateEntity>();
			if (notExistedCerts?.Any() == true)
			{
				var index = 0;
				var sequence = SequenceManager.GetNextList("Prc_Certificate", notExistedCerts.Count);
				foreach (var notExistedCert in notExistedCerts)
				{
					var entity = new PrcCertificateEntity();
					entity.Id = sequence[index++];
					entity.PrcHeaderFk = prcHeaderId;
					entity.BpdCertificateTypeFk = notExistedCert.BpdCertificateTypeFk;
					entity.Isrequired = notExistedCert.IsRequired;
					entity.Ismandatory = notExistedCert.IsMandatory;
					entity.Isrequiredsubsub = notExistedCert.IsRequiredSub;
					entity.Ismandatorysubsub = notExistedCert.IsMandatorySub;
					entity.CommentText = notExistedCert.CommentText;
					entity.RequiredBy = DateTime.Now;
					prcCertificatesNew.Add(entity);
				}
			}

			return new CopyCertificatesFromMaterialResult()
			{
				NewPrcCertificates = prcCertificatesNew,
				ExistedMaterial2Certificates = existedCerts
			};
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <param name="projectId"></param>
		/// <param name="save"></param>
		/// <param name="certTypeIds">if it is null, regard it as no UI or UI has not been showed, then will filter by source in DB</param>
		/// <returns></returns>
		public IDictionary<string, object> CopyCertificatesFromProject(int prcHeaderId, int? projectId, bool save = true, IEnumerable<int> certTypeIds = null)
		{
			if (!projectId.HasValue)
			{
				return new Dictionary<string, object>();
			}
			var project2CertLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IProject2CertificateLogic>();
			var projectIds = new List<int> { projectId.Value };
			var prjCerts = project2CertLogic.GetItemsByProjectIds(projectIds);
			var prcCerts = this.GetSearchList(e => e.PrcHeaderFk == prcHeaderId);
			if (certTypeIds == null)
			{
				certTypeIds = prcCerts.CollectIds(e => e.BpdCertificateTypeFk);
			}

			var existedCerts = prjCerts.Where(e => certTypeIds.Contains(e.CertificateTypeFk)).ToList();
			var notExistedCerts = prjCerts.Except(existedCerts).ToList();
			var result = new List<PrcCertificateEntity>();
			var invCertificatesNew = new List<PrcCertificateEntity>();
			if (notExistedCerts.Any())
			{
				var index = 0;
				var sequence = SequenceManager.GetNextList("Prc_Certificate", notExistedCerts.Count);
				foreach (var notExistedCert in notExistedCerts)
				{
					var entity = new PrcCertificateEntity();
					entity.Id = sequence[index++];
					entity.PrcHeaderFk = prcHeaderId;
					entity.BpdCertificateTypeFk = notExistedCert.CertificateTypeFk;
					entity.Isrequired = notExistedCert.IsRequired;
					entity.Ismandatory = notExistedCert.IsMandatory;
					entity.Isrequiredsubsub = notExistedCert.IsRequiredSub;
					entity.Ismandatorysubsub = notExistedCert.IsMandatorySub;
					entity.CommentText = notExistedCert.CommentText;
					entity.RequiredBy = DateTime.Now;
					invCertificatesNew.Add(entity);
				}
				result.AddRange(invCertificatesNew);
			}
			if (save)
			{
				if (existedCerts.Any())
				{
					foreach (var existedCert in existedCerts)
					{
						var mark = false;
						var prcCert = prcCerts.FirstOrDefault(e => e.BpdCertificateTypeFk == existedCert.CertificateTypeFk);
						if (prcCert != null)
						{
							if (existedCert.IsRequired && !prcCert.Isrequired)
							{
								prcCert.Isrequired = true;
								mark = true;
							}
							if (existedCert.IsMandatory && !prcCert.Ismandatory)
							{
								prcCert.Ismandatory = true;
								mark = true;
							}
							if (existedCert.IsRequiredSub && !prcCert.Isrequiredsubsub)
							{
								prcCert.Isrequiredsubsub = true;
								mark = true;
							}
							if (existedCert.IsMandatorySub && !prcCert.Ismandatorysubsub)
							{
								prcCert.Ismandatorysubsub = true;
								mark = true;
							}
							if (!String.IsNullOrEmpty(existedCert.CommentText) && String.IsNullOrEmpty(prcCert.CommentText))
							{
								prcCert.CommentText = existedCert.CommentText;
								mark = true;
							}
							if (mark)
							{
								result.Add(prcCert);
							}
						}
					}
				}

				if (result.Any())
				{
					this.Save(result);
				}
			}

			var dictionaryValue = new Dictionary<string, object>();
			dictionaryValue.Add("invCertificatesNew", invCertificatesNew);
			dictionaryValue.Add("existedCertificates", existedCerts);

			return dictionaryValue;
		}

		/// <summary>
		/// save and set isValue
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public override IEnumerable<PrcCertificateEntity> Save(IEnumerable<PrcCertificateEntity> entities)
		{
			base.Save(entities);
			if (entities.Any())
			{
				return FillRelated(entities);
			}
			return entities;
		}

		/// <summary>
		/// save and set isValue
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public override PrcCertificateEntity Save(PrcCertificateEntity entity)
		{
			base.Save(entity);
			FillRelated(new PrcCertificateEntity[] { entity });
			return entity;
		}

		/// <summary>
		/// FillRelated
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		protected override IEnumerable<PrcCertificateEntity> FillRelated(IEnumerable<PrcCertificateEntity> items)
		{
			base.FillRelated(items);
			var certificateTypeKeys = items.Select(x => (int?)x.BpdCertificateTypeFk).Distinct();
			var certificateTypes = new RIB.Visual.BusinessPartner.Certificate.BusinessComponents.CertificateTypeLogic().GetItemsByKey(certificateTypeKeys);
			foreach (var item in items)
			{
				var totalType = certificateTypes.FirstOrDefault(x => x.Id == item.BpdCertificateTypeFk);
				if (totalType != null)
				{
					item.IsValued = totalType.IsValued;
				}
				else
				{
					item.IsValued = false;
				}
			}
			return items;
		}

		/// <summary>
		/// GetSequenceName
		/// </summary>
		/// <returns></returns>
		public override string GetSequenceName()
		{
			return "PRC_CERTIFICATE";
		}

		/// <summary>
		/// get list from PrcHeaderFk list where Isrequired or Ismandatory or Isrequiredsubsub or Ismandatorysubsub
		/// </summary>
		/// <param name="prcHeaderFks"> PrcHeaderFk list</param>
		/// <returns></returns>
		public IEnumerable<IPrcCertificateData> GetRequireOrMandatoryList(IEnumerable<int> prcHeaderFks)
		{
			return this.GetSearchList(x => prcHeaderFks.Contains(x.PrcHeaderFk) && (x.Ismandatory || x.Isrequired || x.Ismandatorysubsub || x.Isrequiredsubsub));
		}

		/// <summary>
		/// Overload method, get procurement certificates by filter.
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		#pragma warning disable S2325
		public IEnumerable<PrcCertificateEntity> GetSearchList(System.Linq.Expressions.Expression<Func<PrcCertificateEntity, bool>> filter)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<PrcCertificateEntity> query = dbContext.Entities<PrcCertificateEntity>();

				if (filter != null)
				{
					query = query.Where(filter);
				}

				return query.ToList();
			}
		}
		#pragma warning restore S2325

		#region IPrcCertificateInfoProvider members

		/// <summary>
		///
		/// </summary>
		/// <param name="PrcHeaderFks"></param>
		/// <returns></returns>
		IEnumerable<IPrcCertificateData> IPrcCertificateInfoProvider.GetRequireOrMandatoryList(IEnumerable<int> PrcHeaderFks)
		{
			return GetRequireOrMandatoryList(PrcHeaderFks);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		IEnumerable<IPrcCertificateData> IPrcCertificateInfoProvider.GetSearchList(string filter)
		{
			List<IPrcCertificateData> resultSet = new List<IPrcCertificateData>();
			var filteredList = base.GetSearchList(filter);
			resultSet.AddRange(filteredList);
			return resultSet;
		}

		#endregion

        #region IEntityCopier

        /// <summary>
        ///
        /// </summary>
        /// <param name="responsible"></param>
        /// <param name="entity"></param>
        /// <param name="intermediate"></param>
        /// <returns></returns>
        protected override IEnumerable<IIdentifyable> ProvideAggregatedForDeepCopy(IEntityRelationInfo responsible, IIdentifyable entity, IEntityRelationInfo intermediate)
        {
            return this.GetSearchList(e => e.PrcHeaderFk == entity.Id).OrderByDescending(e => e.Id);
        }

        /// <summary>
        /// Should get children of TEntity, if there any. Base implementation does nothing
        /// </summary>
        /// <param name="parent">Possible parent of other entities</param>
        /// <returns>null - and the method may not be overwritten</returns>
        protected override IEnumerable<PrcCertificateEntity> GetChildren(PrcCertificateEntity parent)
        {
            // wui: because copy logic error if return null
            return new List<PrcCertificateEntity>();
        }

		/// <summary>
		/// Should initializes aggregating owner and if possbile the parent of the passed entity. Base implementation does nothing
		/// </summary>
		/// <param name="entity">Entity to which owner and parent must be set</param>
		/// <param name="newOwner">TEntiy (parent in hier. structures) or different type not known here</param>
		/// <param name="ownerMapping"></param>
		protected override void InitNewOwner(PrcCertificateEntity entity, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
        {
            entity.PrcHeaderFk = newOwner.Id;
        }

        #endregion

        /// <summary>
        /// extend LogicBase, it need override it, it is different from EntityLogic
        /// </summary>
        /// <returns></returns>
        protected override string GetDbTable()
        {
            return "PRC_CERTIFICATE";
        }
	}
}
