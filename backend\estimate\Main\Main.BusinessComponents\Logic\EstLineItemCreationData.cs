using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Estimate.Rule.BusinessComponents;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// the identifier of the line item new created locations belong to
	/// </summary>
	public class EstLineItemCreationData
	{
		/// <summary>
		/// Get / set the Estimate Header Id of the Line Item
		/// </summary>
		public int EstHeaderFk { get; set; }

		/// <summary>
		/// Get / set the assembly category
		/// </summary>
		public int? EstAssemblyCatFk { get; set; }

		/// <summary>
		/// Get / set the Activity of the Line Item
		/// </summary>
		public int? PsdActivityFk { get; set; }

		/// <summary>
		/// Get / set the BoQ of the Line Item
		/// </summary>
		public int? BoqItemFk { get; set; }

		/// <summary>
		/// Get / set the BoQ Header of the Line Item
		/// </summary>
		public int? BoqHeaderFk { get; set; }

		/// <summary>
		/// Get / set the Controlling Unit of the Line Item
		/// </summary>
		public int? MdcControllingUnitFk { get; set; }

		/// <summary>
		/// Get / set the Cost Group 1 of the Line Item
		/// </summary>
		public int? LicCostGroup1Fk { get; set; }

		/// <summary>
		/// Get / set the Cost Group 2 of the Line Item
		/// </summary>
		public int? LicCostGroup2Fk { get; set; }

		/// <summary>
		/// Get / set the Cost Group 3 of the Line Item
		/// </summary>
		public int? LicCostGroup3Fk { get; set; }

		/// <summary>
		/// Get / set the Cost Group 4 of the Line Item
		/// </summary>
		public int? LicCostGroup4Fk { get; set; }

		/// <summary>
		/// Get / set the Cost Group 5 of the Line Item
		/// </summary>
		public int? LicCostGroup5Fk { get; set; }

		/// <summary>
		/// Get / set the Project Location of the Line Item
		/// </summary>
		public int? PrjLocationFk { get; set; }

		/// <summary>
		/// Get / set the Procurement Structure of the Line Item
		/// </summary>
		public int? PrcStructureFk { get; set; }

		/// <summary>
		/// Get / set the Project Cost Group 1 of the Line Item
		/// </summary>
		public int? PrjCostGroup1Fk { get; set; }

		/// <summary>
		/// Get / set the Project Cost Group 2 of the Line Item
		/// </summary>
		public int? PrjCostGroup2Fk { get; set; }

		/// <summary>
		/// Get / set the Project Cost Group 3 of the Line Item
		/// </summary>
		public int? PrjCostGroup3Fk { get; set; }

		/// <summary>
		/// Get / set the Project Cost Group 4 of the Line Item
		/// </summary>
		public int? PrjCostGroup4Fk { get; set; }

		/// <summary>
		/// Get / set the Project Cost Group 5 of the Line Item
		/// </summary>
		public int? PrjCostGroup5Fk { get; set; }

		/// <summary>
		/// Get / set the Quantity Target of the Line Item
		/// </summary>
		public decimal Quantity { get; set; }

		/// <summary>
		/// Get / Set the Quantity Target Detail of the Line Item
		/// </summary>
		public string QuantityDetail { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string QuantityTargetDetail { get; set; }

		/// <summary>
		/// Get / set the bas uom fk of the Line Item
		/// </summary>
		public int BasUomFk { get; set; }

		/// <summary>
		/// Get / set the Bas Uom Target fk of the Line Item
		/// </summary>
		public int BasUomTargetFk { get; set; }

		/// <summary>
		/// Get / set the DescriptionInfo of the Line Item
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }

		/// <summary>
		/// Get/set the selected Line Item Entity
		/// </summary>
		public EstLineItemEntity SelectedItem { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<EstLineItemEntity> SelectedItems { get; set; }

		/// <summary>
		/// Get / set the QtyTakeOverStructFk of the Line Item
		/// </summary>
		public int? QtyTakeOverStructFk { get; set; }

		/// <summary>
		/// Get / set the QtyRelFk of the Line Item
		/// </summary>
		public int? QtyRelFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyTelAotFk { get; set; }

		/// <summary>
		///Get / set the ProjectId
		/// </summary>
		public int ProjectId { get; set; }

		/// <summary>
		/// Get/ Set ProjectName
		/// </summary>
		public string ProjectName { get; set; }

		/// <summary>
		/// Get/ Set ProjectNo
		/// </summary>
		public string ProjectNo { get; set; }

		/// <summary>
		/// Get/ Set EstimationCode
		/// </summary>
		public string EstimationCode { get; set; }

		/// <summary>
		/// Get/ Set EstimationDescription
		/// </summary>
		public DescriptionTranslateType EstimationDescription { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqItemFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqWicCatFk { get; set; }

		/// <summary>
		/// Get / set the WqQuantityTarget of the Line Item
		/// </summary>
		public decimal WqQuantityTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string WqQuantityTargetDetail { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsOptional { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsOptionalIT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsIncluded { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedPrice { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsDaywork { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string ChangedField { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? ActualFinlPricOc { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string ExternalCode { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int AssemblyType { get; set; }

	}
    /// <summary>
    /// 
    /// </summary>
    public class ConfDetailData
    {
        /// <summary>
        /// 
        /// </summary>
        public string key { get; set; }

	/// <summary>
	/// 
	/// </summary>
        public decimal value { get; set; }

    }
    /// <summary>
    /// 
    /// </summary>
	public class DynamicColumnItem
	{
		/// <summary>
		/// 
		/// </summary>
		public string Field { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Value { get; set; }

	}

	/// <summary>
	/// 
	/// </summary>
	public class EstMainPrcPackageDeleteData
	{
		/// <summary>
		/// Get / set the Estimate Header Id of the Line Item
		/// </summary>
		public int EstHeaderFk { get; set; }

		/// <summary>
		/// Get / set SelectedLevel
		/// </summary>
		public string SelectedLevel { get; set; }

		/// <summary>
		/// Get / set the Project Id
		/// </summary>
		public int PrjProjectFk { get; set; }

		/// <summary>
		/// Get / set the IsLineItems
		/// </summary>
		public bool IsLineItems { get; set; }

		/// <summary>
		/// Get / set the IsLineItems
		/// </summary>
		public bool IsResources { get; set; }

		/// <summary>
		///The estimate Line items
		/// </summary>
		public IEnumerable<EstLineItemEntity> EstLineItems { get; set; }

		/// <summary>
		///The not matching estimate Line items
		/// </summary>
		public IEnumerable<EstLineItemEntity> NotMatchingEstLineItems { get; set; }

		/// <summary>
		/// Resource entities
		/// </summary>
		public IEnumerable<EstResourceEntity> EstResources { get; set; }

		/// <summary>
		/// Package entities Ids
		/// </summary>
		public IEnumerable<int?> PrcPackages { get; set; }

		/// <summary>
		/// Resource entities with PrcPackage
		/// </summary>
		public IEnumerable<EstResourceEntity> PackageResources { get; set; }
		/// <summary>
		/// option IsGeneratedPrc
		/// </summary>
		public bool IsGeneratePrc { get; set; }
		/// <summary>
		/// option IsGeneratedPrc
		/// </summary>
		public bool IsDisablePrc { get; set; }
	}

	/// <summary>
	/// Get / Set the Estimate rules assignment delet data
	/// </summary>
	public class EstMainRuleAssignDeleteData
	{
		/// <summary>
		/// Get / set the Project Id
		/// </summary>
		public int ProjectId { get; set; }

		/// <summary>
		/// Get / set the Estimate Header Id of the Line Item
		/// </summary>
		public int EstHeaderFk { get; set; }

		/// <summary>
		/// Get / set SelectedLevel
		/// </summary>
		public string LineItemsSelectedLevel { get; set; }

		/// <summary>
		///Get/ Set the selected estimate line items--- 
		/// </summary>
		public IEnumerable<int> SelectedEstLineItemIds { get; set; }

		/// <summary>
		/// Get / Set the selected estimate rules
		/// </summary>
		public IEnumerable<PrjEstRuleEntity> PrjEstRules { get; set; }

		/// <summary>
		/// Get / set the Is Leading Structure
		/// </summary>
		public bool IsLeadingStructure { get; set; }

		/// <summary>
		/// Get / set the Is Root
		/// </summary>
		public bool IsRoot { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<string> SelectedParams { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsRemoveRuleParam { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int StructureId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<int> PrjEstRuleIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public EstLeadingStructureContext EstLeadingStructureContext { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<int> MdcRuleIds { get; set; }

	}

	/// <summary>
	/// The Estimate Main Revenue update data
	/// </summary>
	public class EstMainUpdateRevenueData
	{
		/// <summary>
		/// Get / set the Project Id
		/// </summary>
		public int ProjectId { get; set; }

		/// <summary>
		/// Get / set the Estimate Header Id of the Line Item
		/// </summary>
		public int EstHeaderId { get; set; }

		/// <summary>
		///Get/ Set the selected estimate line items--- 
		/// </summary>
		public IEnumerable<EstLineItemEntity> EstLineItems { get; set; }

		/// <summary>
		///Get/ Set the selected estimate line items--- 
		/// </summary>
		public int? SelectedItemId { get; set; }

		/// <summary>
		/// Get / set the IsDistributeByCost
		/// </summary>
		public bool IsDistributeByCost { get; set; }
	}

	/// <summary>
	/// Generate Estimate from project Boq wizard data.
	/// </summary>
	public class GenerateEstimateFromBoqData
	{
		/// <summary>
		/// Get / set the Project Id
		/// </summary>
		public int TargetProjectId { get; set; }

		/// <summary>
		/// Get / set the Estimate Header Id of the Line Item
		/// </summary>
		public int TargetEstHeaderId { get; set; }

		/// <summary>
		///Get/ Set the search criteria based on boq outline specification
		/// </summary>
		public bool OutlineSpecification { get; set; }

		/// <summary>
		///Get/ Set the search criteria based on boq uom
		/// </summary>
		public bool BasUomFk { get; set; }

		/// <summary>
		///Get/ Set the selected estimate line items--- 
		/// </summary>
		public IEnumerable<SourceBoQsData> SourceBoQsData { get; set; }

		/// <summary>
		/// Get / set the Target Boq Header Id 
		/// </summary>
		public int? TargetBoqHeaderFk { get; set; }

		/// <summary>
		///Get/ Set the search criteria, boq ref/wic
		/// </summary>
		public int SearchCriteria { get; set; }

		/// <summary>
		/// Get / set the Existing Estimate
		/// </summary>
		public int ExistingEstimate { get; set; }

		/// <summary>
		/// Get / set LineItem Context ID
		/// </summary>
		public int MdcLineItemContextId { get; set; }
	}

	/// <summary>
	/// Generate Estimate from project Boq grid data
	/// </summary>
	public class SourceBoQsData
	{
		/// <summary>
		/// Get / set the Id
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// Type source is Project BoQ/WiC BoQ
		/// </summary>
		public int Type { get; set; }

		/// <summary>
		/// Get / set the Source Project or WIC Id
		/// </summary>
		public int ProjectWicId { get; set; }

		/// <summary>
		/// Get / set the Boq Header Id 
		/// </summary>
		public int SourceBoqHeaderFk { get; set; }

		/// <summary>
		/// Get / set the Source Estimate Header Id
		/// </summary>
		public int? SourceEstHeaderId { get; set; }

		/// <summary>
		/// Get / set the Source BoQ from Reference range
		/// </summary>
		public int? FromBoqItemRefNo { get; set; }

		/// <summary>
		/// Get / set the Source Source BoQ to Reference range
		/// </summary>
		public int? ToBoqItemRefNo { get; set; }

		/// <summary>
		/// Get / set the Boq Header Id 
		/// </summary>
		public int? RootItemId { get; set; }

		/// <summary>
		/// Get / set the Boq Structure Id 
		/// </summary>
		public int? StructureId { get; set; }

		/// <summary>
		/// Get / set the Boq Structure Name 
		/// </summary>
		public string StructureName { get; set; }

		/// <summary>
		/// Get / set the Version
		/// </summary>
		public int Version { get; set; }

		/// <summary>
		/// Get / set the Estimate Header Id 
		/// </summary>
		public int? SourceWicId { get; set; }
	}
}
