﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DCC951FC-6F08-41D3-A1AC-06670C205CD9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Procurement.Common.ServiceFacade.WebApi</RootNamespace>
    <AssemblyName>RIB.Visual.Procurement.Common.ServiceFacade.WebApi</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Procurement.Common.ServiceFacade.WebApi.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Basics.Currency.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Currency.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.PriceCondition.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.PriceCondition.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.PriceCondition.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.PriceCondition.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Boq.Main.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Boq.Main.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Boq.Main.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Boq.Main.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MasterData.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MasterData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MasterData.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MasterData.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Material.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Material.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Material.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Material.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementStructure.ServiceFacade.WebApi">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementStructure.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceDomain">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceDomain.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Clerk.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Clerk.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementStructure.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementStructure.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Unit.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Unit.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Unit.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Unit.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.BusinessPartner.Main.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.BusinessPartner.Main.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.BusinessPartner.Main.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.BusinessPartner.Main.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Customize.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Customize.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostGroups.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostGroups.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostGroups.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostGroups.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.TextModules.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.TextModules.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.BillingSchema.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.BillingSchema.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.AspNetCore.Mvc.WebApiCompatShim.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Net.Http.Formatting.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Jint">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Jint.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Runtime.Caching.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Security.Cryptography.ProtectedData.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementConfiguration.ServiceFacade.WebApi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementConfiguration.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MaterialCatalog.BusinessComponents, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MaterialCatalog.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.IndexTable.BusinessComponents, Version=3.2.201.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.IndexTable.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.BillingSchema.ServiceFacade.WebApi, Version=24.2.351.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.BillingSchema.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\CreateAccrualTransactionBaseController.cs" />
    <Compile Include="Controllers\CreateInterCompanyBaseController.cs" />
    <Compile Include="Controllers\PrcItem2PlantController.cs" />
    <Compile Include="Controllers\PrcPackage2ExtBidderController.cs" />
    <Compile Include="Controllers\PrcPackageExtbidder2ContactController.cs" />
    <Compile Include="Controllers\ProcurementCommonBoqChangeOverviewController.cs" />
    <Compile Include="Controllers\ProcurementCommonConfigurationController.cs" />
    <Compile Include="Controllers\ProcurementCommonOverallDiscountController.cs" />
    <Compile Include="Controllers\PrcCommonOptionProfileController.cs" />
    <Compile Include="Convert\PrcItemAssignmentDtoConverter.cs" />
    <Compile Include="Convert\PrcItemAssignmentDtoEntityMapper.cs" />
    <Compile Include="Dtos\BasicsAddressDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\BasicsAddressDto.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>BasicsAddressDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ControllingTotalDto.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcPackage2ExtBidderCreateParameter.cs" />
    <Compile Include="Dtos\PrcItemsCreateFromMaterialsResult.cs" />
    <Compile Include="Dtos\Customize\CreateInsertItemDto.cs" />
    <Compile Include="Dtos\Customize\CreateItemAssignmentFromPackage2EstimateResultDto.cs" />
    <Compile Include="Dtos\Customize\UpdatePackageBoqDto.cs" />
    <Compile Include="Dtos\PrcDocumentStatusDto.cs" />
    <Compile Include="Dtos\PrcDocumentStatusDto.Generated.cs" />
    <Compile Include="Dtos\PrcItem2PlantDto.cs" />
    <Compile Include="Dtos\PrcItem2PlantDto.Generated.cs" />
    <Compile Include="Dtos\PrcOverviewDto.cs" />
    <Compile Include="Dtos\PrcPackage2ExtBidderCompleteDto.cs" />
    <Compile Include="Dtos\PrcPackage2ExtBpContactDto.cs" />
    <Compile Include="Dtos\PrcPackage2ExtBpContactDto.Generated.cs" />
    <Compile Include="Dtos\PrcPsStatusDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcPsStatusDto.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PrcPsStatusDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ProcurementDataformatDto.cs" />
    <Compile Include="Dtos\UpdateVersionBoqDto.cs" />
    <Compile Include="Dtos\PrcPackage2ExtBidderDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcPackage2ExtBidderDto.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PrcPackage2ExtBidderDto.cs</DependentUpon>
    </Compile>
    <Compile Include="LookupServices\Account\AccassignAccTypeService.cs" />
    <Compile Include="LookupServices\Account\AccassignFactoryService.cs" />
    <Compile Include="Dtos\AccassignAccountDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignAccountDto.Generated.cs">
      <DependentUpon>AccassignAccountDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignAccTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignAccTypeDto.Generated.cs">
      <DependentUpon>AccassignAccTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignBusinessDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignBusinessDto.Generated.cs">
      <DependentUpon>AccassignBusinessDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignControlDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignControlDto.Generated.cs">
      <DependentUpon>AccassignControlDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignConTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignConTypeDto.Generated.cs">
      <DependentUpon>AccassignConTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignFactoryDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignFactoryDto.Generated.cs">
      <DependentUpon>AccassignFactoryDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignItemTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignItemTypeDto.Generated.cs">
      <DependentUpon>AccassignItemTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\AccassignMatGroupDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AccassignMatGroupDto.Generated.cs">
      <DependentUpon>AccassignMatGroupDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="LookupServices\Account\AccassignAccountService.cs" />
    <Compile Include="LookupServices\Account\AccassignBusinessService.cs" />
    <Compile Include="LookupServices\Account\AccassignControlService.cs" />
    <Compile Include="LookupServices\Account\AccassignConTypeService.cs" />
    <Compile Include="LookupServices\Account\AccassignItemTypeService.cs" />
    <Compile Include="LookupServices\Account\AccassignMatGroupService.cs" />
    <Compile Include="Controllers\PrcPostconHistoryController.cs" />
    <Compile Include="Controllers\PrcRecalculateTotalsController.cs" />
    <Compile Include="Controllers\PrcItemInfoBlController.cs" />
    <Compile Include="Controllers\PrcItemScopeController.cs" />
    <Compile Include="Controllers\PrcItemScopeDetailController.cs" />
    <Compile Include="Controllers\PrcItemScopeDetailPcController.cs" />
    <Compile Include="Controllers\PrcItemScopeDtlBlobController.cs" />
    <Compile Include="Controllers\PrcStructure2ClerkController.cs" />
    <Compile Include="Controllers\ProcurementCommonBoqItemType2Controller.cs" />
    <Compile Include="Controllers\ProcurementCommonBoqItemTypeController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcItemAssignmentController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcMandatoryDeadlineController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcCallOffAgreementController.cs" />
    <Compile Include="Controllers\ProcurementCommonOverviewController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcPaymentScheduleController.cs" />
    <Compile Include="Controllers\PrcDependentDataVController.cs" />
    <Compile Include="Controllers\ProcurementBoqExtendedController.cs" />
    <Compile Include="Controllers\ProcurementCommonBoqController.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Controllers\ProcurementCommonCodeController.cs" />
    <Compile Include="Controllers\ProcurementCommonDeliveryScheduleController.cs" />
    <Compile Include="Controllers\ProcurementCommonExchangeRateController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcCertificateController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcContactController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcOverviewController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcDataController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcDocumentController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcGeneralsController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcHeaderTextController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcItemController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcSuggestedBidderController.cs" />
    <Compile Include="Controllers\ProcurementCommonPriceConditionController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcMilestoneController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcMilestoneTypeController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcRadiusController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcSubreferenceController.cs" />
    <Compile Include="Controllers\ProcurementCommonPrcItemTextController.cs" />
    <Compile Include="Controllers\ProcurementCommonReplaceNeutralMaterialController.cs" />
    <Compile Include="Controllers\ProcurementCommonUpdateItemPriceController.cs" />
    <Compile Include="Controllers\ProcurementCommonWarrantyController.cs" />
    <Compile Include="Controllers\ProcurementCommonWizardController.cs" />
    <Compile Include="Dtos\AIReplaceMaterialCompleteDto.cs" />
    <Compile Include="Dtos\AIReplaceMaterialDto.cs" />
    <Compile Include="Dtos\AIUpdateMaterialCompleteDto.cs" />
    <Compile Include="Dtos\AIUpdateMaterialDto.cs" />
    <Compile Include="Dtos\BpdBp2prcStructureVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\BpdBp2prcStructureVDto.Generated.cs">
      <DependentUpon>BpdBp2prcStructureVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\CreateParameterDto\PrcCertificateFromMaterialParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcPaymentScheduleCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\GeneralCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcSuggestedBidderCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcTotalCopyParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcTotalCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcCertificateCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcContactCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcDocumentCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcHeaderblobCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcItemblobCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcItemCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcItemdeliveryCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcMilestoneCreateParameter.cs" />
    <Compile Include="Dtos\CreateParameterDto\PrcSubreferenceCreateParameter.cs" />
    <Compile Include="Dtos\Customize\ConditionContainerDto.cs" />
    <Compile Include="Dtos\Customize\DataContainerDto.cs" />
    <Compile Include="Dtos\Customize\DataContainerTreeDto.cs" />
    <Compile Include="Dtos\Customize\InsertMaterialDto.cs" />
    <Compile Include="Dtos\Customize\ReplaceNeutralSimulationDto.cs" />
    <Compile Include="Dtos\Customize\UpdateItemPriceDto.cs" />
    <Compile Include="Dtos\Customize\UpdateMaterialDto.cs" />
    <Compile Include="Dtos\GetItemTextListParamererDto\PrcGetItemTextParameter.cs" />
    <Compile Include="Dtos\ItemType2Dto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ItemType2Dto.Generated.cs">
      <DependentUpon>ItemType2Dto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ItemType85Dto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ItemType85Dto.Generated.cs">
      <DependentUpon>ItemType85Dto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ItemTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ItemTypeDto.Generated.cs">
      <DependentUpon>ItemTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcBoqCompleteDto.cs" />
    <Compile Include="Dtos\PrcBoqDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcBoqDto.Generated.cs">
      <DependentUpon>PrcBoqDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcBoqExtendedDto.cs" />
    <Compile Include="Dtos\PrcBoqLookupVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcBoqLookupVDto.Generated.cs">
      <DependentUpon>PrcBoqLookupVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcBoqUpdateDto.cs" />
    <Compile Include="Dtos\PrcCallOffAgreementDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcCallOffAgreementDto.Generated.cs">
      <DependentUpon>PrcCallOffAgreementDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcCertificateDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcCertificateDto.Generated.cs">
      <DependentUpon>PrcCertificateDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcClerkDatasDto.cs" />
    <Compile Include="Dtos\PrcConfiguration2strategyVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcConfiguration2strategyVDto.Generated.cs">
      <DependentUpon>PrcConfiguration2strategyVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcConfiguration2texttypeVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcConfiguration2texttypeVDto.Generated.cs">
      <DependentUpon>PrcConfiguration2texttypeVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcContactDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcContactDto.Generated.cs">
      <DependentUpon>PrcContactDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcDataViewDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcDataViewDto.Generated.cs">
      <DependentUpon>PrcDataViewDto.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcDependentDataVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcDependentDataVDto.Generated.cs">
      <DependentUpon>PrcDependentDataVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcDocumentDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcDocumentDto.Generated.cs">
      <DependentUpon>PrcDocumentDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcDocumentTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcDocumentTypeDto.Generated.cs">
      <DependentUpon>PrcDocumentTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcGeneralsDto.cs">
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcGeneralsDto.Generated.cs">
      <DependentUpon>PrcGeneralsDto.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcHeader2prcStructureVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcHeader2prcStructureVDto.Generated.cs">
      <DependentUpon>PrcHeader2prcStructureVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcHeaderblobDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcHeaderblobDto.Generated.cs">
      <DependentUpon>PrcHeaderblobDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcHeaderDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcHeaderDto.Generated.cs">
      <DependentUpon>PrcHeaderDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItem2MdlObjectVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItem2MdlObjectVDto.Generated.cs">
      <DependentUpon>PrcItem2MdlObjectVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemAIMappingDto.cs" />
    <Compile Include="Dtos\PrcItemAssignmentDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemAssignmentDto.Generated.cs">
      <DependentUpon>PrcItemAssignmentDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemblobDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemblobDto.Generated.cs">
      <DependentUpon>PrcItemblobDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemCompleteDto.cs" />
    <Compile Include="Dtos\PrcItemdeliveryDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemdeliveryDto.Generated.cs">
      <DependentUpon>PrcItemdeliveryDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemDto.Generated.cs">
      <DependentUpon>PrcItemDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemEvaluationDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemEvaluationDto.Generated.cs">
      <DependentUpon>PrcItemEvaluationDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemInfoBLDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemInfoBLDto.Generated.cs">
      <DependentUpon>PrcItemInfoBLDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemLookupVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemLookupVDto.Generated.cs">
      <DependentUpon>PrcItemLookupVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItempriceconditionDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItempriceconditionDto.Generated.cs">
      <DependentUpon>PrcItempriceconditionDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeCompleteDto.cs" />
    <Compile Include="Dtos\PrcItemScopeDetailCompleteDto.cs" />
    <Compile Include="Dtos\PrcItemScopeDetailDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDetailDto.Generated.cs">
      <DependentUpon>PrcItemScopeDetailDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDetailPcDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDetailPcDto.Generated.cs">
      <DependentUpon>PrcItemScopeDetailPcDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDtlBlobDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDtlBlobDto.Generated.cs">
      <DependentUpon>PrcItemScopeDtlBlobDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemScopeDto.Generated.cs">
      <DependentUpon>PrcItemScopeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemstatus2externalDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemstatus2externalDto.Generated.cs">
      <DependentUpon>PrcItemstatus2externalDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcItemstatusDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcItemstatusDto.Generated.cs">
      <DependentUpon>PrcItemstatusDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcMandatoryDeadlineDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcMandatoryDeadlineDto.Generated.cs">
      <DependentUpon>PrcMandatoryDeadlineDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcMilestoneDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcMilestoneDto.Generated.cs">
      <DependentUpon>PrcMilestoneDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcMilestonetypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcMilestonetypeDto.Generated.cs">
      <DependentUpon>PrcMilestonetypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcOverviewVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcOverviewVDto.Generated.cs">
      <DependentUpon>PrcOverviewVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcPaymentScheduleDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcPaymentScheduleDto.Generated.cs">
      <DependentUpon>PrcPaymentScheduleDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcPostconHistoryDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcPostconHistoryDto.Generated.cs">
      <DependentUpon>PrcPostconHistoryDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcRadiusDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcRadiusDto.Generated.cs">
      <DependentUpon>PrcRadiusDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcStckTranType2RubCatDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcStckTranType2RubCatDto.Generated.cs">
      <DependentUpon>PrcStckTranType2RubCatDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactionDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactionDto.Generated.cs">
      <DependentUpon>PrcStocktransactionDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactionLookupVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactionLookupVDto.Generated.cs">
      <DependentUpon>PrcStocktransactionLookupVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactiontypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcStocktransactiontypeDto.Generated.cs">
      <DependentUpon>PrcStocktransactiontypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcStructure2clerkDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcStructure2clerkDto.Generated.cs">
      <DependentUpon>PrcStructure2clerkDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcSubreferenceDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcSubreferenceDto.Generated.cs">
      <DependentUpon>PrcSubreferenceDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcSuggestedBidderDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcSuggestedBidderDto.Generated.cs">
      <DependentUpon>PrcSuggestedBidderDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PrcWarrantyDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrcWarrantyDto.Generated.cs">
      <DependentUpon>PrcWarrantyDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\PriceBoqItemDto.cs" />
    <Compile Include="Dtos\PrjGeneralsDto.cs">
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PrjGeneralsDto.Generated.cs">
      <DependentUpon>PrjGeneralsDto.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ProcurementCompleteDto.cs" />
    <Compile Include="Dtos\ProcurementIncotermDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ProcurementIncotermDto.Generated.cs">
      <DependentUpon>ProcurementIncotermDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\QtnRequisitionDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\QtnRequisitionDto.Generated.cs">
      <DependentUpon>QtnRequisitionDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ReqHeaderLookupDto.cs" />
    <Compile Include="Dtos\ReqHeaderLookupVDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ReqHeaderLookupVDto.Generated.cs">
      <DependentUpon>ReqHeaderLookupVDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ReqStatusDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ReqStatusDto.Generated.cs">
      <DependentUpon>ReqStatusDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\ReqTypeDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\ReqTypeDto.Generated.cs">
      <DependentUpon>ReqTypeDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\WarrantyObligationDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\WarrantyObligationDto.Generated.cs">
      <DependentUpon>WarrantyObligationDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Dtos\WarrantySecurityDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\WarrantySecurityDto.Generated.cs">
      <DependentUpon>WarrantySecurityDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="LookupServices\AddressLookupService.cs" />
    <Compile Include="LookupServices\PrcBoqExtendedService.cs" />
    <Compile Include="LookupServices\PrcCommonDocumentTypeService.cs" />
    <Compile Include="LookupServices\PrcDocumentStatusLookupService.cs" />
    <Compile Include="LookupServices\PrcItemEvalutionService.cs" />
    <Compile Include="LookupServices\PrcItemEntitiyLookupService.cs" />
    <Compile Include="LookupServices\PrcItemLookupService.cs" />
    <Compile Include="LookupServices\PrcItemMergedLookupService.cs" />
    <Compile Include="LookupServices\PrcItemService.cs" />
    <Compile Include="LookupServices\PrcBoqService.cs" />
    <Compile Include="LookupServices\MilestoneTypeService.cs" />
    <Compile Include="LookupServices\PrcConfiguration2StrategyViewService.cs" />
    <Compile Include="LookupServices\PrcConfiguration2TextTypeService.cs" />
    <Compile Include="LookupServices\PrcItemStatusSevice.cs" />
    <Compile Include="LookupServices\PrcItemType2Service.cs" />
    <Compile Include="LookupServices\PrcItemType85Service.cs" />
    <Compile Include="LookupServices\PrcItemTypeService.cs" />
    <Compile Include="LookupServices\PrcMergeBoqViewService.cs" />
    <Compile Include="LookupServices\PrcPsStatusService.cs" />
    <Compile Include="LookupServices\PrcStocktransactionLookupVService.cs" />
    <Compile Include="LookupServices\PrcStocktransactiontypeService.cs" />
    <Compile Include="LookupServices\PrcTexttypeService.cs" />
    <Compile Include="LookupServices\ReqHeaderLookupViewService.cs" />
    <Compile Include="LookupServices\ReqStatusService.cs" />
    <Compile Include="LookupServices\ReqTypeService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Requests\CopyFromPrcItemRequest.cs" />
    <Compile Include="Requests\GetBoqItemIdsIsContractedRequest.cs" />
    <Compile Include="Requests\GetMergedBoqRootItemRequest.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Requests\HasContractedDataRequest.cs" />
    <Compile Include="Requests\ModelObjectRequest.cs" />
    <Compile Include="LookupServices\WarrantyObligationLookupService.cs" />
    <Compile Include="LookupServices\WarrantySecurityLookupService.cs" />
    <Compile Include="Requests\PriceVersionRequest.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.BusinessComponents\RIB.Visual.Procurement.Common.BusinessComponents.csproj">
      <Project>{B725F817-02F4-4DE3-89A4-8C7999A9F6D9}</Project>
      <Name>RIB.Visual.Procurement.Common.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Common\RIB.Visual.Procurement.Common.Common.csproj">
      <Project>{00008849-3AB3-4636-86DE-C0430490CAD5}</Project>
      <Name>RIB.Visual.Procurement.Common.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Core\RIB.Visual.Procurement.Common.Core.csproj">
      <Project>{2BEA7191-C82A-4F53-99D9-6C756C089011}</Project>
      <Name>RIB.Visual.Procurement.Common.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Localization\RIB.Visual.Procurement.Common.Localization.csproj">
      <Project>{ABA42D58-77FF-4319-83CC-0E0668495AA5}</Project>
      <Name>RIB.Visual.Procurement.Common.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>