﻿<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2012" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="MDC_CONTROLLINGGRPSETAPI_Vs" EntityType="BusinessComponents.Store.MDC_CONTROLLINGGRPSETAPI_V" store:Type="Views" Table="MDC_CONTROLLINGGRPSETAPI_V" />
          <EntitySet Name="MDC_CONTROLLINGGRPSETDTLAPI_Vs" EntityType="BusinessComponents.Store.MDC_CONTROLLINGGRPSETDTLAPI_V" store:Type="Views" Table="MDC_CONTROLLINGGRPSETDTLAPI_V" />
          <EntitySet Name="MDC_CONTROLLINGUNITAPI_Vs" EntityType="BusinessComponents.Store.MDC_CONTROLLINGUNITAPI_V" store:Type="Views" Table="MDC_CONTROLLINGUNITAPI_V" />
          <EntitySet Name="MDC_CONTROLLINGUNITGROUPAPI_Vs" EntityType="BusinessComponents.Store.MDC_CONTROLLINGUNITGROUPAPI_V" store:Type="Views" Table="MDC_CONTROLLINGUNITGROUPAPI_V" />
          <EntitySet Name="BAS_COMPANY_COSTDATAAPI_Vs" EntityType="BusinessComponents.Store.BAS_COMPANY_COSTDATAAPI_V" store:Type="Views" Table="BAS_COMPANY_COSTDATAAPI_V" />
          <EntitySet Name="BAS_COMPANY_COSTHEADERAPI_Vs" EntityType="BusinessComponents.Store.BAS_COMPANY_COSTHEADERAPI_V" store:Type="Views" Table="BAS_COMPANY_COSTHEADERAPI_V" />
        </EntityContainer>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="KEY1" Type="int" />
          <Property Name="KEY2" Type="int" />
          <Property Name="KEY3" Type="int" />
        </EntityType>
        <EntityType Name="MDC_CONTROLLINGGRPSETAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="MDC_CONTROLLINGGRPSETDTLAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGRPSET_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGROUP_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGROUP_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="MDC_CONTROLLINGGROUP_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="MDC_CONTROLLINGUNITAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTRUNITSTATUS_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTRUNITSTATUS_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTROLLINGUNIT_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLTEMPLATE_FK" Type="int" />
          <Property Name="MDC_CONTROLTEMPLATE_UNIT_FK" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTEXT_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTEXT_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="32" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="PRJ_PROJECT_ID" Type="int" />
          <Property Name="PRJ_PROJECT_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="PRJ_PROJECT_DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="BAS_UOM_ID" Type="int" Nullable="false" />
          <Property Name="BAS_UOM_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ISBILLINGELEMENT" Type="bit" Nullable="false" />
          <Property Name="ISACCOUNTINGELEMENT" Type="bit" Nullable="false" />
          <Property Name="ISPLANNINGELEMENT" Type="bit" Nullable="false" />
          <Property Name="ISSTOCKMANAGEMENT" Type="bit" Nullable="false" />
          <Property Name="PRJ_STOCK_ID" Type="int" />
          <Property Name="PRJ_STOCK_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="PRJ_STOCK_DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="ISASSETMANAGEMENT" Type="bit" Nullable="false" />
          <Property Name="ISPLANTMANAGEMENT" Type="bit" Nullable="false" />
          <Property Name="ETM_PLANT_ID" Type="int" />
          <Property Name="ETM_PLANT_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="ETM_PLANT_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="PLANNED_START" Type="date" />
          <Property Name="PLANNED_END" Type="date" />
          <Property Name="PLANNED_DURATION" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="ASSIGNMENT01" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL01_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL01_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL01_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT02" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL02_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL02_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL02_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT03" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL03_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL03_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL03_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT04" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL04_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL04_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL04_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT05" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL05_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL05_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL05_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT06" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL06_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL06_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL06_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT07" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL07_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL07_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL07_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT08" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL08_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL08_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL08_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT09" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL09_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL09_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL09_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="ASSIGNMENT10" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL10_ID" Type="int" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL10_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL10_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED4" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED5" Type="nvarchar" MaxLength="252" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="BUDGET" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="ISFIXED_BUDGET" Type="bit" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" />
          <Property Name="BUDGET_DIFFERENCE" Type="numeric" Precision="19" Scale="7" />
          <Property Name="ESTIMATE_COST" Type="numeric" Precision="19" Scale="7" />
          <Property Name="BUDGET_COST_DIFF" Type="numeric" Precision="19" Scale="7" />
          <Property Name="ISINTERCOMPANY" Type="bit" Nullable="false" />
          <Property Name="BAS_CONTROLLINGCAT_ID" Type="int" Nullable="false" DefaultValue="1" />
          <Property Name="BAS_CONTROLLINGCAT_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="BAS_CLERK_ID" Type="int" />
          <Property Name="BAS_CLERK_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="BAS_CLERK_DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_COMPANY_RESPONSIBLE_ID" Type="int" />
          <Property Name="BAS_COMPANY_RESPONSIBLE_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="BAS_COMPANY_ID" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="ISTIMEKEEPINGELEMENT" Type="bit" Nullable="false" DefaultValue="true" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="MDC_CONTROLLINGUNITGROUPAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGUNIT_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGUNIT_CODE" Type="nvarchar" Nullable="false" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGUNIT_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTROLLINGGROUP_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGROUP_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="MDC_CONTROLLINGGROUP_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGGRPDETAIL_DESC" Type="nvarchar" MaxLength="2000" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="BAS_COMPANY_COSTDATAAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_COSTHEADER_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_COSTHEADER_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="MDC_CONTROLLINGUNIT_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_COST_CODE_FK" Type="int" />
          <Property Name="MDC_COST_CODE_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="COST_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="MDC_CONTR_COST_CODE_FK" Type="int" />
          <Property Name="MDC_CONTR_COST_CODE_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="CONTR_COST_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="BAS_ACCOUNT_FK" Type="int" />
          <Property Name="BAS_ACCOUNT_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="BAS_ACCOUNT_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="BAS_CURRENCY_FK" Type="int" />
          <Property Name="BAS_CURRENCY_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="BAS_UOM_FK" Type="int" Nullable="false" />
          <Property Name="BAS_UOM_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="UOM" Type="nvarchar" MaxLength="16" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="NOMINAL_DIMENSION1" Type="nvarchar" MaxLength="64" />
          <Property Name="NOMINAL_DIMENSION2" Type="nvarchar" MaxLength="64" />
          <Property Name="NOMINAL_DIMENSION3" Type="nvarchar" MaxLength="64" />
          <Property Name="AMOUNT_PROJECT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="BAS_COMPANY_COSTHEADERAPI_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="BAS_COMPANY_YEAR_FK" Type="int" Nullable="false" />
          <Property Name="TRADING_YEAR" Type="int" />
          <Property Name="YEAR_START_DATE" Type="date" />
          <Property Name="YEAR_END_DATE" Type="date" />
          <Property Name="BAS_COMPANY_PERIOD_FK" Type="int" Nullable="false" />
          <Property Name="TRADING_PERIOD" Type="int" />
          <Property Name="PERIOD_START_DATE" Type="date" />
          <Property Name="PERIOD_END_DATE" Type="date" />
          <Property Name="BAS_VALUETYPE_FK" Type="int" Nullable="false" />
          <Property Name="BAS_VALUETYPE_DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="PRJ_PROJECT_FK" Type="int" />
          <Property Name="PROJECTNO" Type="nvarchar" MaxLength="16" />
          <Property Name="PROJECT_NAME" Type="nvarchar" MaxLength="252" />
          <Property Name="HASCOSTCODE" Type="bit" Nullable="false" />
          <Property Name="HASACCOUNT" Type="bit" Nullable="false" />
          <Property Name="HASCONTCOSTCODE" Type="bit" Nullable="false" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="TOTAL" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="TOTAL_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="ISFINAL" Type="bit" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="LANGUAGE_ID" Type="int" Nullable="false" />
        </EntityType>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Controlling.PublicApi.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Controlling.PublicApi.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="ControllingGrpSetApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingGrpSetApiEntity" />
          <EntitySet Name="ControllingGrpSetDtlApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingGrpSetDtlApiEntity" />
          <EntitySet Name="ControllingUnitApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingUnitApiEntity" />
          <EntitySet Name="ControllingUnitGroupApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingUnitGroupApiEntity" />
          <EntitySet Name="ActualsCostDataApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ActualsCostDataApiEntity" />
          <EntitySet Name="ActualsCostHeaderApiEntities" EntityType="RIB.Visual.Controlling.PublicApi.BusinessComponents.ActualsCostHeaderApiEntity" />
        </EntityContainer>
        <EntityType Name="DdTempIdsEntity" ed:Guid="6902c783-fe1a-4395-9200-d7ba04c3ee9a" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="fadd9d49-dad6-4f9d-aaea-b56f8bdf6269" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="06c83e50-aa8d-4e66-b740-2bac6cd8ce19" />
          <Property Name="Key1" Type="Int32" ed:ValidateRequired="false" ed:Guid="071b85bc-f540-40ee-9763-613b332d5dd2" />
          <Property Name="Key2" Type="Int32" ed:ValidateRequired="false" ed:Guid="ec2272c2-669f-444c-9c7b-c7166edd40ea" />
          <Property Name="Key3" Type="Int32" ed:ValidateRequired="false" ed:Guid="9b7493da-00d2-45bb-96d7-8f86e76b790a" />
        </EntityType>
        <EntityType Name="ControllingGrpSetApiEntity" ed:Guid="e2bd2d45-fdc3-4fa2-ba90-2dfaa4afc052" ed:PublicApiVersion="1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ee1960cb-0e25-4c0f-9511-f10afe7feb12" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="0368463a-8831-48b6-ba7c-35ad1cfc24d7" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="98d09939-eaf9-41a1-a9ba-f100de6dddba" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="6da5b7a8-eeab-486a-a90a-88b4d1f778df" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="050e2e96-d529-4db2-bdc7-802e5fd7e10e" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7527dd88-1804-436d-a61d-446fe3b9d304" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5cebd41a-a3cc-4d8a-9d6f-3f5ab880104e" />
        </EntityType>
        <EntityType Name="ControllingGrpSetDtlApiEntity" ed:Guid="3f2776f7-5b2d-4731-add0-07ff13f6e8f8" ed:PublicApiVersion="1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4faaf13f-e971-4aa9-97e5-66f079f97e92" />
          <Property Name="ControllingGrpSetId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b5a95639-0664-47f5-818b-37639450e57f" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllinggrpsetFk" />
          <Property Name="ControllingGroupId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e54fcfec-bb09-4fac-a3a7-106ddd68e64b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllinggroupFk" />
          <Property Name="ControllingGroupCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="475b86c7-5ae0-4c88-a45c-add867142546" />
          <Property Name="ControllingGroupDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="67434182-cedf-4ead-86b6-5072cd6c542e" />
          <Property Name="ControllingGrpDetailId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="81ed369f-45eb-4e70-8d04-1427f09f95ba" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllinggroupdetailFk" />
          <Property Name="ControllingGrpDetailCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="9570642a-c3e7-40fa-a7ad-a504322d051a" />
          <Property Name="ControllinggGrpDetailDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="8f9fc51b-4ce6-4692-84ec-d25aa27660f3" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="8d497103-21be-4396-b71e-98ae55e270f8" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b04b05a5-5639-4e1f-9efa-24b9aa8cb419" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="2a7539b3-863a-4777-8399-478de339c6d2" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="68109863-3cda-49da-b50c-55f9f63c1b81" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="944dcda6-4626-446d-8f89-2562c7d20896" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8a1e7930-295e-4feb-803c-84dcb5c74318" />
        </EntityType>
        <EntityType Name="ControllingUnitApiEntity" ed:Guid="e81ebdba-b77e-4eae-9979-3617d7da735b" ed:PublicApiVersion="2">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="d40150fe-16b5-498a-8f4b-7f1a8ffae40f" />
          <Property Name="ControllingUnitStatusId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bda8cd83-4367-4f50-8b01-cd1dd1911af0" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingunitstatusFk" />
          <Property Name="ControllingUnitStatusDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="4a7ce9cf-3e38-406b-91f1-d3cdc8cc6890" />
          <Property Name="ControllingUnitId" Type="Int32" ed:ValidateRequired="false" ed:Guid="6e62f38d-8ddd-427d-8200-1ac7d2566f9b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingunitFk" />
          <Property Name="ControllingUnitCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="9071576b-e281-48ef-acfd-fed9b0169cc0" />
          <Property Name="ControllingTemplateId" Type="Int32" ed:ValidateRequired="false" ed:Guid="244cada1-4cd0-4845-abc6-bbfc4addba90" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControltemplateFk">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingTemplateUnitId" Type="Int32" ed:ValidateRequired="false" ed:Guid="a80ace21-800b-4ffe-b088-399dfdb47b3b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControltemplateUnitFk">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="f746d7ee-a98c-4741-b60c-c9dcf8a2f24a" />
          <Property Name="ContextId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6765ba88-c7f3-4bbf-b485-546eb32985a7" ed:InternalPropertyName="ContextFk" />
          <Property Name="ContextDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="b1574dfc-f8ba-4b28-a0c5-6a3973a53957" />
          <Property Name="Code" Type="String" Nullable="false" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="0e9f110f-acdd-4e51-91f8-3fbab217bdbe" ed:CopyFromPublicApi="True" ed:IsIdentifyingCode="True" />
          <Property Name="Description" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="e10fb911-165f-4880-846e-7611d01b635b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="DescriptionInfo" />
          <Property Name="ProjectId" Type="Int32" ed:ValidateRequired="false" ed:Guid="4707d7a0-e9ad-44b8-925d-5e34f8e7caca" ed:InternalPropertyName="ProjectFk" />
          <Property Name="ProjectCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="d25b3b8f-2da3-4219-9f31-f95c0a5140fd" />
          <Property Name="ProjectDesc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="fc598227-6cf3-40ea-b6bd-132c4a22f7b3" />
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="535d93b4-cf2d-4869-9bee-2502ee15aa6e" ed:CopyFromPublicApi="True" />
          <Property Name="UomId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ce745918-**************-d90c6bb212df" ed:CopyFromPublicApi="True" ed:InternalPropertyName="UomFk" />
          <Property Name="UomDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="e857c9c8-a168-4c30-9a10-ce4a788288cc" />
          <Property Name="IsBillingElement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="a3df147c-86b1-4f8b-9c6d-0bb23f36d3d4" ed:CopyFromPublicApi="True" />
          <Property Name="IsAccountingElement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="f8ed33a2-0bc4-47d3-8d36-4f13369f79f5" ed:CopyFromPublicApi="True" />
          <Property Name="IsPlanningElement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="141c6007-8144-467b-bffc-b6bd874e0bd4" ed:CopyFromPublicApi="True" />
          <Property Name="IsStockManagement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="392f0069-894f-4e19-a958-08968a873329" ed:CopyFromPublicApi="True" ed:InternalPropertyName="IsStockmanagement" />
          <Property Name="StockId" Type="Int32" ed:ValidateRequired="false" ed:Guid="362e4312-f92e-4864-abb7-e8359345f810" ed:CopyFromPublicApi="True" ed:InternalPropertyName="StockFk" />
          <Property Name="StockCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="f6b6ce42-68f4-4b1d-a87c-05382bca01de" />
          <Property Name="StockDesc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e73db24a-63b3-4c08-a170-6243497947b6" />
          <Property Name="IsAssetManagement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="3aac7acd-4688-44f3-af6e-8ad84886d69d" ed:CopyFromPublicApi="True" ed:InternalPropertyName="IsAssetmanagement" />
          <Property Name="IsPlantManagement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="3502d186-e7a7-4c68-97d5-722789d61c61" ed:CopyFromPublicApi="True" ed:InternalPropertyName="IsPlantmanagement" />
          <Property Name="PlantId" Type="Int32" ed:ValidateRequired="false" ed:Guid="933aa232-faba-447e-abf0-0d287bd0d39d" ed:CopyFromPublicApi="True" ed:InternalPropertyName="EtmPlantFk" />
          <Property Name="PlantCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="574753c7-dde7-4df2-bc02-e3b2be2858f2" />
          <Property Name="PlantDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="e1e407ae-7ff1-4284-bf33-bdb231cb1a7a" />
          <Property Name="PlannedStart" Type="DateTime" ed:ValidateRequired="false" ed:Guid="9ea34200-9486-4d3b-839a-4f88f1294919" ed:CopyFromPublicApi="True" />
          <Property Name="PlannedEnd" Type="DateTime" ed:ValidateRequired="false" ed:Guid="d127c054-a950-4615-9c2d-09557a66c357" ed:CopyFromPublicApi="True" />
          <Property Name="PlannedDuration" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="52ffd380-3a1c-4f05-8e05-a4530ce6ae5b" ed:CopyFromPublicApi="True" />
          <Property Name="Assignment01" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="a4bf75c3-b7c1-4859-aaf7-3bf58d45b9a5" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail01Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="87d2c08a-43eb-4bdb-880b-4611320d6f15" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail01Fk" />
          <Property Name="ControllingGrpDetail01Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="6c8cb8bd-3837-4b93-8861-adc22f03a0ba" />
          <Property Name="ControllingGrpDetail01Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="b0c697c9-0356-49f0-be67-85eba015fc3a" />
          <Property Name="Assignment02" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="28320efd-5023-4adf-a942-35d6b85e05be" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail02Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="6737a17e-917e-4f8d-8bdf-99834a707a4d" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail02Fk" />
          <Property Name="ControllingGrpDetail02Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="716acc94-09c8-439a-9ae2-208251938d5d" />
          <Property Name="ControllingGrpDetail02Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="3439ebfa-39df-4c9f-9ec5-a19a252b8548" />
          <Property Name="Assignment03" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="0acb930e-5834-424e-8abd-fc71b65ccb20" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDdetail03Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="675c29a0-c3ec-4d60-80a2-288fc4050a76" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail03Fk" />
          <Property Name="ControllingGrpDetail03Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="149e455b-a1b4-490d-8522-246e79a5934d" />
          <Property Name="ControllingGrpDetail03Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="8baa2542-3a32-4532-8316-895f4fb9af42" />
          <Property Name="Assignment04" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="b2917888-f8ea-4a1e-8bd3-74fa24754a4a" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail04Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="839d8d14-73f6-4b1e-b542-9bf04b662340" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail04Fk" />
          <Property Name="ControllingGrpDetail04Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="f0686e84-**************-5b26435fe7e9" />
          <Property Name="ControllingGrpDetail04Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="493464af-d9d9-4db9-891c-3b4bfb72e60a" />
          <Property Name="Assignment05" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="81fb7ff8-a942-4099-8e4a-f491c75616a0" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail05Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="947ffd17-760e-44bf-8c52-7a1af7146b5c" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail05Fk" />
          <Property Name="ControllingGrpDetail05Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="87afa3c4-f173-41e2-8a3d-20d2ddf4bac8" />
          <Property Name="ControllingGrpDetail05Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="13902f29-fd56-4bf8-b57a-0c0a93f09084" />
          <Property Name="Assignment06" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="9b50cb62-cda7-49b2-a8d4-d950d4647b62" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail06Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="fc91a8d3-6734-4c44-963e-d98b766284db" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail056Fk" />
          <Property Name="ControllingGrpDetail06Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="43b18fb2-6a16-40e2-aff7-266788625b17" />
          <Property Name="ControllingGrpDetail06Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="21334f0f-628b-4761-9044-76e0ec5e9d06" />
          <Property Name="Assignment07" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="21d61323-c4e4-4b74-837d-5e1914ffca37" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail07Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="7ce1a467-991f-44fd-99f6-b367dff334da" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail07Fk" />
          <Property Name="ControllingGrpDetail07Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="226c823d-13f8-4b99-a139-37989cffcf38" />
          <Property Name="ControllingGrpDetail07Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="50acf073-e095-4367-8d13-67b3339628eb" />
          <Property Name="Assignment08" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="*************-498e-9c40-57d503e8d363" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail08Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="70be65d0-a9d7-4800-aeb2-0d309695e297" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail08Fk" />
          <Property Name="ControllingGrpDetail08Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="b23d87b6-bb58-44bd-86f4-74dec2cbb812" />
          <Property Name="ControllingGrpDetail08Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="c4350582-beea-48c3-b7c0-e2134d616d3f" />
          <Property Name="Assignment09" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="1af35fb9-ff65-42e8-80a0-3a4f31927c9e" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail09Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="e58d6f27-e9bb-4ea0-9e81-51b51191d231" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail09Fk" />
          <Property Name="ControllingGrpDetail09Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="be19a92b-6b85-4447-98a8-0070586df6f4" />
          <Property Name="ControllingGrpDetail09Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="8172f8d6-9453-4061-8f3f-fa01f882f75a" />
          <Property Name="Assignment10" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="b1f1d63b-cab1-4f22-b200-85e412def36e" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingGrpDetail10Id" Type="Int32" ed:ValidateRequired="false" ed:Guid="d4366034-95c8-4d20-a757-556ed2ec1394" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingGrpDetail10Fk" />
          <Property Name="ControllingGrpDetail10Code" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="06f3f0d2-aa71-45b2-93e1-4daf69903db2" />
          <Property Name="ControllingGrpDetail10Desc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="3ff6acb7-b591-4a40-8f4a-bbd0f7db0e82" />
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="b0097da3-6700-47ee-a5e4-fa88e1847d61" ed:CopyFromPublicApi="True" />
          <Property Name="UserDefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="bef512b0-7a6a-423b-b727-a2f22a526dd3" ed:CopyFromPublicApi="True" />
          <Property Name="UserDefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="55299472-a0ba-4f8b-b623-c7ac28870f11" ed:CopyFromPublicApi="True" />
          <Property Name="UserDefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="132f2f93-56d7-4729-bb78-fd06c6a70f80" ed:CopyFromPublicApi="True" />
          <Property Name="UserDefined4" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="04ba4014-05ed-4b62-97a4-45bf42e3757b" ed:CopyFromPublicApi="True" />
          <Property Name="UserDefined5" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="57e6fefc-47e3-4518-8057-c0c2c46284d8" ed:CopyFromPublicApi="True" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="080c1b0a-0f4b-4e1e-9155-276ab953843e" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ba4cdf7c-26b3-4beb-9d2a-e71d4547b6e4" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="500ac396-4055-4099-97f0-d0f88ee8a9c5" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="30b947ae-bfcb-4f8a-bf06-60091bfed78b" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0af92bfd-02fb-45c2-9cec-11c16e467b6e" />
          <Property Name="Budget" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="79315f3e-e7d8-4888-a853-4e0c5e7c2616" ed:CopyFromPublicApi="True" />
          <Property Name="IsFixedBudget" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="6d25c746-426e-419a-a7c1-5289bc57d6ce" ed:CopyFromPublicApi="True" />
          <Property Name="IsDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="e8834593-ed0e-43f9-a223-3013d9f9b1fa" ed:CopyFromPublicApi="True" />
          <Property Name="BudgetDifference" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="11cd51b8-d3d6-4478-805b-ac36e208872a" />
          <Property Name="EstimateCost" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="9b3e7f98-46e9-4200-80a7-7b3f2ea3f59b" ed:CopyFromPublicApi="True" />
          <Property Name="BudgetCostDiff" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="69a9a98b-ad2b-446f-a99a-98854edab85e" ed:CopyFromPublicApi="True" />
          <Property Name="IsIntercompany" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="520689ce-b7cc-4a4a-9645-8f491cef1369" ed:CopyFromPublicApi="True" />
          <Property Name="ControllingCatId" Type="Int32" Nullable="false" DefaultValue="1" ed:ValidateRequired="false" ed:Guid="275edf4a-6f33-447c-b7a1-7b7eea77b86c" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingCatFk" />
          <Property Name="ControllingCatDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="e012b54a-b841-4264-9b04-44e4870f15eb" />
          <Property Name="ClerkId" Type="Int32" ed:ValidateRequired="false" ed:Guid="dc76541a-7ee3-4c2f-9515-dd4026223925" ed:CopyFromPublicApi="True" />
          <Property Name="ClerkCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="7f9ab912-dc1d-479c-9fef-40df2893c3f9" />
          <Property Name="ClerkDesc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="3786fb69-b390-45c8-9231-83e843f0bc85" />
          <Property Name="ProfitCenterId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9dc97fee-613e-42ea-9c47-15157f79b25d" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CompanyResponsibleFk" />
          <Property Name="ProfitCenterCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="016cd460-9de9-4bbc-85f3-6f0c0b0cfebe" />
          <Property Name="CompanyId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="901a1b95-afbd-4ed9-8a03-f6196237d683" ed:InternalPropertyName="CompanyFk" />
          <Property Name="CompanyCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="08107b60-c85d-4624-a4eb-0f2224cd5f14" />
          <Property Name="ForTimekeeping" Type="Boolean" Nullable="false" DefaultValue="true" ed:ValidateRequired="false" ed:Guid="85b32161-01a8-4a09-9b4a-6e9ce7f306d9" ed:CopyFromPublicApi="True" ed:InternalPropertyName="IsTimekeepingElement" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e8fc5ea1-1b57-40bf-97d7-74195192eafb" />
        </EntityType>
        <EntityType Name="ControllingUnitGroupApiEntity" ed:Guid="07efe5d3-fde0-4e55-b9d0-451b9855a660" ed:PublicApiVersion="2">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="334152bc-3d64-47df-b610-40c166628bea" />
          <Property Name="ControllingUnitId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="956b0ba1-252f-4d22-aae5-6a7704a99856" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllingunitFk" />
          <Property Name="ControllingUnitCode" Type="String" Nullable="false" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="c84b6079-5fd5-4abf-a23e-b2b28bf430bc" />
          <Property Name="ControllingUnitDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="1b9f503e-0a14-413a-bc25-ad58eaa34c11" />
          <Property Name="ControllingGroupId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9bedb981-a20a-40b2-bc57-b5fab03844df" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllinggroupFk" />
          <Property Name="ControllingGroupCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="2e9bd9c1-fdf6-48a4-a5a9-13eb6f56e2ba" />
          <Property Name="ControllingGroupDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="9b854807-4fcf-4404-88b0-e93fe48732cc" />
          <Property Name="ControllingGrpDetailId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e49b44d3-ec30-4962-a1d9-a429d5576cb1" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ControllinggroupdetailFk" />
          <Property Name="ControllingGrpDetailCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="2060d653-acfd-41de-b1d6-82f7d680e9c9" />
          <Property Name="ControllingGrpDetailDesc" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="eb20927d-1442-47cc-a88e-96941092d180" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="833b98cc-078b-4d05-bc61-2308df047967" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0a7c5fb8-f99b-4809-a643-a4405db6c174" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="43841127-af35-4042-a119-a9b8dec55caa" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="d83ef6d5-1977-46d3-8767-bbb9cc4d148e" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4d7d7e85-727a-4635-bc51-8318a4b355d0" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f70c2eac-adad-4dde-9fac-944d26c1aa36" />
        </EntityType>
        <EntityType Name="ActualsCostDataApiEntity" ed:Guid="6daf83d7-d712-4b41-971a-c9e9461a0461">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9b22dc86-3abd-40c9-8366-7f6af0f75669" />
          <Property Name="CompanyCostHeaderId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2367c081-ad52-463e-84f6-e71ff78ff1e2" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CompanyCostHeaderFk" />
          <Property Name="CompanyCostHeaderCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="ccebf080-0cbc-4f06-9033-cf8324f889ea" />
          <Property Name="ControllingUnitId" Type="Int32" ed:ValidateRequired="false" ed:Guid="f8ba1f5c-3831-4c00-8a5e-519a0995ae39" ed:CopyFromPublicApi="True" ed:InternalPropertyName="MdcControllingUnitFk" />
          <Property Name="ControllingUnitDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="0da1707d-7ff8-4451-bc63-3531e2377a04" />
          <Property Name="ControllingUnitCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="a770dbfa-8345-47a7-8dbc-78cc6ecfccc9" />
          <Property Name="CostCodeId" Type="Int32" ed:ValidateRequired="false" ed:Guid="dfa280ef-b792-42dc-ad5f-e958a6ed7562" ed:CopyFromPublicApi="True" ed:InternalPropertyName="MdcCostCodeFk" />
          <Property Name="CostCodeDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="ec1d65d5-8931-474e-b493-c5f32a5a6749" />
          <Property Name="CostCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="56611780-a45d-40de-8b69-d973dee91900" />
          <Property Name="ControllingCostCodeId" Type="Int32" ed:ValidateRequired="false" ed:Guid="16969e76-9d02-4f5f-ac53-3db3cc6be51e" ed:CopyFromPublicApi="True" ed:InternalPropertyName="MdcContrCostCodeFk" />
          <Property Name="ControllingCostCodeDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="fb9f33cf-92c1-4916-9706-026542a8c54b" />
          <Property Name="ControllingCostCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="8110c98c-82bd-4d66-a595-b1f80253a317" />
          <Property Name="AccountId" Type="Int32" ed:ValidateRequired="false" ed:Guid="1e8ca262-1bea-4045-aa96-c7a3253ab690" ed:CopyFromPublicApi="True" ed:InternalPropertyName="AccountFk" />
          <Property Name="AccountDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="********-6c3e-4178-8f59-3c5caaf23645" />
          <Property Name="AccountCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="80e28a55-4e18-4684-b037-ea90419e0cb3" />
          <Property Name="CurrencyId" Type="Int32" ed:ValidateRequired="false" ed:Guid="1cda650d-b4f9-4120-a080-6597b6f411c0" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CurrencyFk" />
          <Property Name="CurrencyDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="********-3c91-4fe2-a6e6-7246061e88a3" />
          <Property Name="BasUomId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="215066ed-2500-4e07-ba42-726b3d0e84be" ed:CopyFromPublicApi="True" ed:InternalPropertyName="UomFk" />
          <Property Name="UomDescription" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="1add7571-9276-413e-8f93-f8cbfb491af6" />
          <Property Name="Uom" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="0eee6884-6cd9-45a1-8751-07b64421a0ac" />
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="931d1939-da8f-4d7a-8cb7-b1077f9597fa" ed:CopyFromPublicApi="True" />
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="6a729219-60c6-4604-b097-9d8df52ef2f6" ed:CopyFromPublicApi="True" />
          <Property Name="AmountOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="5cc0bd69-f856-4147-a7f5-e4c016a0d993" ed:CopyFromPublicApi="True" />
          <Property Name="Comment" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="0a690d71-653e-440d-9bda-aac2fda953ea" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CommentText" />
          <Property Name="NominalDimension1" Type="String" MaxLength="64" Unicode="true" ed:ValidateMaxLength="64" ed:ValidateRequired="false" ed:Guid="7e7fc2fd-a502-402d-af26-f4a3c93abb06" ed:CopyFromPublicApi="True" />
          <Property Name="NominalDimension2" Type="String" MaxLength="64" Unicode="true" ed:ValidateMaxLength="64" ed:ValidateRequired="false" ed:Guid="ae69c773-0900-4f9c-ba00-abe0326d96f6" ed:CopyFromPublicApi="True" />
          <Property Name="NominalDimension3" Type="String" MaxLength="64" Unicode="true" ed:ValidateMaxLength="64" ed:ValidateRequired="false" ed:Guid="75bb7858-d120-44f2-83a8-4156375f3869" ed:CopyFromPublicApi="True" />
          <Property Name="AmountProject" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="4ed4e6b8-7255-4a20-a338-14772e1721b4" ed:CopyFromPublicApi="True" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="false" ed:Guid="6cbddea7-d1eb-49c1-90f3-89f4cd2bdb23" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="c6ce3d13-f69e-47a3-95c8-ac853abde115" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="10b7359c-0664-4567-9b3b-bddd483934b7" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="5cfd1819-4d81-4d7c-b530-f1b5a115901c" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="9b293d04-383e-42cd-a814-a578a0a859c9" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="9c1e3fa6-3e24-43dc-8941-a82070f49460" />
        </EntityType>
        <EntityType Name="ActualsCostHeaderApiEntity" ed:Guid="8faef009-df4d-4591-a41d-76807cef6615">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4ea90eed-ea45-4661-b9b0-0c567b6d0944" />
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="db64fe93-09e5-47a7-8c4a-179a048a7d51" ed:CopyFromPublicApi="True" />
          <Property Name="CompanyId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="764a5447-039e-46a1-8c65-9965b5a33f4b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CompanyFk" />
          <Property Name="CompanyCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="92f68239-031d-456b-aacc-ae61688472d1" />
          <Property Name="CompanyYearId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="9f383839-29b3-4ec1-b410-9a52be071453" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CompanyYearFk" />
          <Property Name="TradingYear" Type="Int32" ed:ValidateRequired="false" ed:Guid="3da05f73-ecc9-4b71-b019-faa97925c51e" />
          <Property Name="CompanyYearStartDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="f9624a0d-5728-4d83-9c98-1580310a5d5c" />
          <Property Name="CompanyYearEndDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1d7a8d1c-05a8-4c35-8580-fa3ebdc0a954" />
          <Property Name="CompanyPeriodId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="23ed01c0-5745-428d-b4be-b8fea5706dec" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CompanyPeriodFk" />
          <Property Name="TradingPeriod" Type="Int32" ed:ValidateRequired="false" ed:Guid="c249e073-fecf-4c5d-88fc-11bcf959f15d" />
          <Property Name="TradingPeriodStartDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="2e42709d-2798-4901-948b-2a97e3e678cd" />
          <Property Name="TradingPeriodEndDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="3c84eda9-3e4a-4166-8629-156804e3b0a8" />
          <Property Name="ValueTypeId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="fac3bda1-ab72-438f-94b7-9b852555a704" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ValueTypeFk" />
          <Property Name="ValueType" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="74131f48-2830-42cd-8909-f4b302e81bdb" />
          <Property Name="ProjectId" Type="Int32" ed:ValidateRequired="false" ed:Guid="1a26db08-2282-4afe-a72e-b15b4a24e34a" ed:CopyFromPublicApi="True" ed:InternalPropertyName="ProjectFk" />
          <Property Name="Project" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="d8350b72-6359-429b-892f-9ab157a724b1" />
          <Property Name="ProjectName" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="214086d7-25fc-40d4-a88d-4ba51ff3d18b" />
          <Property Name="HasCostCode" Type="Boolean" Nullable="false" ed:ValidateRequired="false" ed:Guid="81106b29-5c25-483c-ac37-0eb43e4e8acf" ed:CopyFromPublicApi="True" />
          <Property Name="HasAccount" Type="Boolean" Nullable="false" ed:ValidateRequired="false" ed:Guid="eda96832-19d3-45dd-8bcb-e8b0f50dee0e" ed:CopyFromPublicApi="True" />
          <Property Name="HasControllingCostCode" Type="Boolean" Nullable="false" ed:ValidateRequired="false" ed:Guid="9ff1cd13-d814-4dde-8cdb-897fb3af88ec" ed:CopyFromPublicApi="True" ed:InternalPropertyName="HasContCostCode" />
          <Property Name="Comment" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="e19206e9-bef2-4221-b2dc-a7a81ea3914b" ed:CopyFromPublicApi="True" ed:InternalPropertyName="CommentText" />
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="fbfd8941-7843-4bde-8dc1-1d84519ad358" />
          <Property Name="TotalOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="9626fb57-da12-4f69-a6f9-a7d8a7f351ca" />
          <Property Name="IsFinal" Type="Boolean" Nullable="false" ed:ValidateRequired="false" ed:Guid="334bc058-756b-412d-b22e-351c47d93fef" ed:CopyFromPublicApi="True" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="false" ed:Guid="8a77e185-b50f-40ee-996c-dc2ea6579d95" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="6488ba0e-8411-4bd2-b4d4-eefc098be968" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="a2eab90b-407f-4e49-beda-9c55bdbd7152" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="3d7329eb-cc91-4d22-b677-a7a02cef9b54" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="1457326c-01f9-4d62-b762-4a04e36b888e" />
          <Property Name="LanguageId" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="96e7760c-b9fd-46ba-afda-64265b7b196c" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" ed:ValidateRequired="false" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="false" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Key1" ColumnName="KEY1" />
                <ScalarProperty Name="Key2" ColumnName="KEY2" />
                <ScalarProperty Name="Key3" ColumnName="KEY3" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ControllingGrpSetApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingGrpSetApiEntity">
              <MappingFragment StoreEntitySet="MDC_CONTROLLINGGRPSETAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ControllingGrpSetDtlApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingGrpSetDtlApiEntity">
              <MappingFragment StoreEntitySet="MDC_CONTROLLINGGRPSETDTLAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ControllingGrpSetId" ColumnName="MDC_CONTROLLINGGRPSET_ID" />
                <ScalarProperty Name="ControllingGroupId" ColumnName="MDC_CONTROLLINGGROUP_ID" />
                <ScalarProperty Name="ControllingGroupCode" ColumnName="MDC_CONTROLLINGGROUP_CODE" />
                <ScalarProperty Name="ControllingGroupDesc" ColumnName="MDC_CONTROLLINGGROUP_DESC" />
                <ScalarProperty Name="ControllingGrpDetailId" ColumnName="MDC_CONTROLLINGGRPDETAIL_ID" />
                <ScalarProperty Name="ControllingGrpDetailCode" ColumnName="MDC_CONTROLLINGGRPDETAIL_CODE" />
                <ScalarProperty Name="ControllinggGrpDetailDesc" ColumnName="MDC_CONTROLLINGGRPDETAIL_DESC" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ControllingUnitApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingUnitApiEntity">
              <MappingFragment StoreEntitySet="MDC_CONTROLLINGUNITAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ControllingUnitStatusId" ColumnName="MDC_CONTRUNITSTATUS_ID" />
                <ScalarProperty Name="ControllingUnitStatusDesc" ColumnName="MDC_CONTRUNITSTATUS_DESC" />
                <ScalarProperty Name="ControllingUnitId" ColumnName="MDC_CONTROLLINGUNIT_ID" />
                <ScalarProperty Name="ControllingUnitCode" ColumnName="MDC_CONTROLLINGUNIT_CODE" />
                <ScalarProperty Name="ControllingUnitDesc" ColumnName="MDC_CONTROLLINGUNIT_DESC" />
                <ScalarProperty Name="ContextId" ColumnName="MDC_CONTEXT_ID" />
                <ScalarProperty Name="ContextDesc" ColumnName="MDC_CONTEXT_DESC" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="ProjectId" ColumnName="PRJ_PROJECT_ID" />
                <ScalarProperty Name="ProjectCode" ColumnName="PRJ_PROJECT_CODE" />
                <ScalarProperty Name="ProjectDesc" ColumnName="PRJ_PROJECT_DESC" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="UomId" ColumnName="BAS_UOM_ID" />
                <ScalarProperty Name="UomDesc" ColumnName="BAS_UOM_DESC" />
                <ScalarProperty Name="IsBillingElement" ColumnName="ISBILLINGELEMENT" />
                <ScalarProperty Name="IsAccountingElement" ColumnName="ISACCOUNTINGELEMENT" />
                <ScalarProperty Name="IsPlanningElement" ColumnName="ISPLANNINGELEMENT" />
                <ScalarProperty Name="IsStockManagement" ColumnName="ISSTOCKMANAGEMENT" />
                <ScalarProperty Name="StockId" ColumnName="PRJ_STOCK_ID" />
                <ScalarProperty Name="StockCode" ColumnName="PRJ_STOCK_CODE" />
                <ScalarProperty Name="StockDesc" ColumnName="PRJ_STOCK_DESC" />
                <ScalarProperty Name="IsAssetManagement" ColumnName="ISASSETMANAGEMENT" />
                <ScalarProperty Name="IsPlantManagement" ColumnName="ISPLANTMANAGEMENT" />
                <ScalarProperty Name="PlantId" ColumnName="ETM_PLANT_ID" />
                <ScalarProperty Name="PlantCode" ColumnName="ETM_PLANT_CODE" />
                <ScalarProperty Name="PlantDesc" ColumnName="ETM_PLANT_DESC" />
                <ScalarProperty Name="PlannedStart" ColumnName="PLANNED_START" />
                <ScalarProperty Name="PlannedEnd" ColumnName="PLANNED_END" />
                <ScalarProperty Name="PlannedDuration" ColumnName="PLANNED_DURATION" />
                <ScalarProperty Name="Assignment01" ColumnName="ASSIGNMENT01" />
                <ScalarProperty Name="ControllingGrpDetail01Id" ColumnName="MDC_CONTROLLINGGRPDETAIL01_ID" />
                <ScalarProperty Name="ControllingGrpDetail01Code" ColumnName="MDC_CONTROLLINGGRPDETAIL01_CODE" />
                <ScalarProperty Name="ControllingGrpDetail01Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL01_DESC" />
                <ScalarProperty Name="Assignment02" ColumnName="ASSIGNMENT02" />
                <ScalarProperty Name="ControllingGrpDetail02Id" ColumnName="MDC_CONTROLLINGGRPDETAIL02_ID" />
                <ScalarProperty Name="ControllingGrpDetail02Code" ColumnName="MDC_CONTROLLINGGRPDETAIL02_CODE" />
                <ScalarProperty Name="ControllingGrpDetail02Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL02_DESC" />
                <ScalarProperty Name="Assignment03" ColumnName="ASSIGNMENT03" />
                <ScalarProperty Name="ControllingGrpDdetail03Id" ColumnName="MDC_CONTROLLINGGRPDETAIL03_ID" />
                <ScalarProperty Name="ControllingGrpDetail03Code" ColumnName="MDC_CONTROLLINGGRPDETAIL03_CODE" />
                <ScalarProperty Name="ControllingGrpDetail03Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL03_DESC" />
                <ScalarProperty Name="Assignment04" ColumnName="ASSIGNMENT04" />
                <ScalarProperty Name="ControllingGrpDetail04Id" ColumnName="MDC_CONTROLLINGGRPDETAIL04_ID" />
                <ScalarProperty Name="ControllingGrpDetail04Code" ColumnName="MDC_CONTROLLINGGRPDETAIL04_CODE" />
                <ScalarProperty Name="ControllingGrpDetail04Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL04_DESC" />
                <ScalarProperty Name="Assignment05" ColumnName="ASSIGNMENT05" />
                <ScalarProperty Name="ControllingGrpDetail05Id" ColumnName="MDC_CONTROLLINGGRPDETAIL05_ID" />
                <ScalarProperty Name="ControllingGrpDetail05Code" ColumnName="MDC_CONTROLLINGGRPDETAIL05_CODE" />
                <ScalarProperty Name="ControllingGrpDetail05Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL05_DESC" />
                <ScalarProperty Name="Assignment06" ColumnName="ASSIGNMENT06" />
                <ScalarProperty Name="ControllingGrpDetail06Id" ColumnName="MDC_CONTROLLINGGRPDETAIL06_ID" />
                <ScalarProperty Name="ControllingGrpDetail06Code" ColumnName="MDC_CONTROLLINGGRPDETAIL06_CODE" />
                <ScalarProperty Name="ControllingGrpDetail06Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL06_DESC" />
                <ScalarProperty Name="Assignment07" ColumnName="ASSIGNMENT07" />
                <ScalarProperty Name="ControllingGrpDetail07Id" ColumnName="MDC_CONTROLLINGGRPDETAIL07_ID" />
                <ScalarProperty Name="ControllingGrpDetail07Code" ColumnName="MDC_CONTROLLINGGRPDETAIL07_CODE" />
                <ScalarProperty Name="ControllingGrpDetail07Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL07_DESC" />
                <ScalarProperty Name="Assignment08" ColumnName="ASSIGNMENT08" />
                <ScalarProperty Name="ControllingGrpDetail08Id" ColumnName="MDC_CONTROLLINGGRPDETAIL08_ID" />
                <ScalarProperty Name="ControllingGrpDetail08Code" ColumnName="MDC_CONTROLLINGGRPDETAIL08_CODE" />
                <ScalarProperty Name="ControllingGrpDetail08Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL08_DESC" />
                <ScalarProperty Name="Assignment09" ColumnName="ASSIGNMENT09" />
                <ScalarProperty Name="ControllingGrpDetail09Id" ColumnName="MDC_CONTROLLINGGRPDETAIL09_ID" />
                <ScalarProperty Name="ControllingGrpDetail09Code" ColumnName="MDC_CONTROLLINGGRPDETAIL09_CODE" />
                <ScalarProperty Name="ControllingGrpDetail09Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL09_DESC" />
                <ScalarProperty Name="Assignment10" ColumnName="ASSIGNMENT10" />
                <ScalarProperty Name="ControllingGrpDetail10Id" ColumnName="MDC_CONTROLLINGGRPDETAIL10_ID" />
                <ScalarProperty Name="ControllingGrpDetail10Code" ColumnName="MDC_CONTROLLINGGRPDETAIL10_CODE" />
                <ScalarProperty Name="ControllingGrpDetail10Desc" ColumnName="MDC_CONTROLLINGGRPDETAIL10_DESC" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="UserDefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="UserDefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="UserDefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="UserDefined4" ColumnName="USERDEFINED4" />
                <ScalarProperty Name="UserDefined5" ColumnName="USERDEFINED5" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="Budget" ColumnName="BUDGET" />
                <ScalarProperty Name="IsFixedBudget" ColumnName="ISFIXED_BUDGET" />
                <ScalarProperty Name="IsDefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="BudgetDifference" ColumnName="BUDGET_DIFFERENCE" />
                <ScalarProperty Name="EstimateCost" ColumnName="ESTIMATE_COST" />
                <ScalarProperty Name="BudgetCostDiff" ColumnName="BUDGET_COST_DIFF" />
                <ScalarProperty Name="IsIntercompany" ColumnName="ISINTERCOMPANY" />
                <ScalarProperty Name="ControllingCatId" ColumnName="BAS_CONTROLLINGCAT_ID" />
                <ScalarProperty Name="ControllingCatDesc" ColumnName="BAS_CONTROLLINGCAT_DESC" />
                <ScalarProperty Name="CompanyId" ColumnName="BAS_COMPANY_ID" />
                <ScalarProperty Name="CompanyCode" ColumnName="BAS_COMPANY_CODE" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
                <ScalarProperty Name="ControllingTemplateId" ColumnName="MDC_CONTROLTEMPLATE_FK" />
                <ScalarProperty Name="ControllingTemplateUnitId" ColumnName="MDC_CONTROLTEMPLATE_UNIT_FK" />
                <ScalarProperty Name="ClerkId" ColumnName="BAS_CLERK_ID" />
                <ScalarProperty Name="ClerkCode" ColumnName="BAS_CLERK_CODE" />
                <ScalarProperty Name="ClerkDesc" ColumnName="BAS_CLERK_DESC" />
                <ScalarProperty Name="ProfitCenterId" ColumnName="BAS_COMPANY_RESPONSIBLE_ID" />
                <ScalarProperty Name="ProfitCenterCode" ColumnName="BAS_COMPANY_RESPONSIBLE_CODE" />
                <ScalarProperty Name="ForTimekeeping" ColumnName="ISTIMEKEEPINGELEMENT" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ControllingUnitGroupApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingUnitGroupApiEntity">
              <MappingFragment StoreEntitySet="MDC_CONTROLLINGUNITGROUPAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ControllingUnitId" ColumnName="MDC_CONTROLLINGUNIT_ID" />
                <ScalarProperty Name="ControllingUnitCode" ColumnName="MDC_CONTROLLINGUNIT_CODE" />
                <ScalarProperty Name="ControllingUnitDesc" ColumnName="MDC_CONTROLLINGUNIT_DESC" />
                <ScalarProperty Name="ControllingGroupId" ColumnName="MDC_CONTROLLINGGROUP_ID" />
                <ScalarProperty Name="ControllingGroupCode" ColumnName="MDC_CONTROLLINGGROUP_CODE" />
                <ScalarProperty Name="ControllingGroupDesc" ColumnName="MDC_CONTROLLINGGROUP_DESC" />
                <ScalarProperty Name="ControllingGrpDetailId" ColumnName="MDC_CONTROLLINGGRPDETAIL_ID" />
                <ScalarProperty Name="ControllingGrpDetailCode" ColumnName="MDC_CONTROLLINGGRPDETAIL_CODE" />
                <ScalarProperty Name="ControllingGrpDetailDesc" ColumnName="MDC_CONTROLLINGGRPDETAIL_DESC" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ActualsCostDataApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ActualsCostDataApiEntity">
              <MappingFragment StoreEntitySet="BAS_COMPANY_COSTDATAAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="CompanyCostHeaderId" ColumnName="BAS_COMPANY_COSTHEADER_FK" />
                <ScalarProperty Name="CompanyCostHeaderCode" ColumnName="BAS_COMPANY_COSTHEADER_CODE" />
                <ScalarProperty Name="ControllingUnitId" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="ControllingUnitDescription" ColumnName="MDC_CONTROLLINGUNIT_DESCRIPTION" />
                <ScalarProperty Name="ControllingUnitCode" ColumnName="MDC_CONTROLLINGUNIT_CODE" />
                <ScalarProperty Name="CostCodeId" ColumnName="MDC_COST_CODE_FK" />
                <ScalarProperty Name="CostCodeDescription" ColumnName="MDC_COST_CODE_DESCRIPTION" />
                <ScalarProperty Name="CostCode" ColumnName="COST_CODE" />
                <ScalarProperty Name="ControllingCostCodeId" ColumnName="MDC_CONTR_COST_CODE_FK" />
                <ScalarProperty Name="ControllingCostCodeDescription" ColumnName="MDC_CONTR_COST_CODE_DESCRIPTION" />
                <ScalarProperty Name="ControllingCostCode" ColumnName="CONTR_COST_CODE" />
                <ScalarProperty Name="AccountId" ColumnName="BAS_ACCOUNT_FK" />
                <ScalarProperty Name="AccountDescription" ColumnName="BAS_ACCOUNT_DESCRIPTION" />
                <ScalarProperty Name="AccountCode" ColumnName="BAS_ACCOUNT_CODE" />
                <ScalarProperty Name="CurrencyId" ColumnName="BAS_CURRENCY_FK" />
                <ScalarProperty Name="CurrencyDescription" ColumnName="BAS_CURRENCY_DESCRIPTION" />
                <ScalarProperty Name="BasUomId" ColumnName="BAS_UOM_FK" />
                <ScalarProperty Name="UomDescription" ColumnName="BAS_UOM_DESCRIPTION" />
                <ScalarProperty Name="Uom" ColumnName="UOM" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="Amount" ColumnName="AMOUNT" />
                <ScalarProperty Name="AmountOc" ColumnName="AMOUNT_OC" />
                <ScalarProperty Name="Comment" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="NominalDimension1" ColumnName="NOMINAL_DIMENSION1" />
                <ScalarProperty Name="NominalDimension2" ColumnName="NOMINAL_DIMENSION2" />
                <ScalarProperty Name="NominalDimension3" ColumnName="NOMINAL_DIMENSION3" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
                <ScalarProperty Name="AmountProject" ColumnName="AMOUNT_PROJECT" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ActualsCostHeaderApiEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Controlling.PublicApi.BusinessComponents.ActualsCostHeaderApiEntity">
              <MappingFragment StoreEntitySet="BAS_COMPANY_COSTHEADERAPI_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="CompanyId" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="CompanyCode" ColumnName="BAS_COMPANY_CODE" />
                <ScalarProperty Name="CompanyYearId" ColumnName="BAS_COMPANY_YEAR_FK" />
                <ScalarProperty Name="TradingYear" ColumnName="TRADING_YEAR" />
                <ScalarProperty Name="CompanyYearStartDate" ColumnName="YEAR_START_DATE" />
                <ScalarProperty Name="CompanyYearEndDate" ColumnName="YEAR_END_DATE" />
                <ScalarProperty Name="CompanyPeriodId" ColumnName="BAS_COMPANY_PERIOD_FK" />
                <ScalarProperty Name="TradingPeriod" ColumnName="TRADING_PERIOD" />
                <ScalarProperty Name="TradingPeriodStartDate" ColumnName="PERIOD_START_DATE" />
                <ScalarProperty Name="TradingPeriodEndDate" ColumnName="PERIOD_END_DATE" />
                <ScalarProperty Name="ValueTypeId" ColumnName="BAS_VALUETYPE_FK" />
                <ScalarProperty Name="ValueType" ColumnName="BAS_VALUETYPE_DESCRIPTION" />
                <ScalarProperty Name="ProjectId" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="Project" ColumnName="PROJECTNO" />
                <ScalarProperty Name="ProjectName" ColumnName="PROJECT_NAME" />
                <ScalarProperty Name="HasCostCode" ColumnName="HASCOSTCODE" />
                <ScalarProperty Name="HasAccount" ColumnName="HASACCOUNT" />
                <ScalarProperty Name="HasControllingCostCode" ColumnName="HASCONTCOSTCODE" />
                <ScalarProperty Name="Comment" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="Total" ColumnName="TOTAL" />
                <ScalarProperty Name="TotalOc" ColumnName="TOTAL_OC" />
                <ScalarProperty Name="IsFinal" ColumnName="ISFINAL" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="LanguageId" ColumnName="LANGUAGE_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>