//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Controlling.PublicApi.BusinessComponents;

namespace RIB.Visual.Controlling.PublicApi.ServiceFacade.WebApi.V2
{


    /// <summary>
    /// There are no comments for ControllingUnitApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("MDC_CONTROLLINGUNITAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(2)]
    public partial class ControllingUnitApiDto : RIB.Visual.Platform.Core.ITypedDto<ControllingUnitApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class ControllingUnitApiDto.
        /// </summary>
        public ControllingUnitApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class ControllingUnitApiDto.
        /// </summary>
        /// <param name="entity">the instance of class ControllingUnitApiEntity</param>
        public ControllingUnitApiDto(ControllingUnitApiEntity entity)
        {
            Id = entity.Id;
            ControllingUnitStatusId = entity.ControllingUnitStatusId;
            ControllingUnitStatusDesc = entity.ControllingUnitStatusDesc;
            ControllingUnitId = entity.ControllingUnitId;
            ControllingUnitCode = entity.ControllingUnitCode;
            ControllingTemplateId = entity.ControllingTemplateId;
            ControllingTemplateUnitId = entity.ControllingTemplateUnitId;
            ControllingUnitDesc = entity.ControllingUnitDesc;
            ContextId = entity.ContextId;
            ContextDesc = entity.ContextDesc;
            Code = entity.Code;
            Description = entity.Description;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDesc = entity.ProjectDesc;
            Quantity = entity.Quantity;
            UomId = entity.UomId;
            UomDesc = entity.UomDesc;
            IsBillingElement = entity.IsBillingElement;
            IsAccountingElement = entity.IsAccountingElement;
            IsPlanningElement = entity.IsPlanningElement;
            IsStockManagement = entity.IsStockManagement;
            StockId = entity.StockId;
            StockCode = entity.StockCode;
            StockDesc = entity.StockDesc;
            IsAssetManagement = entity.IsAssetManagement;
            IsPlantManagement = entity.IsPlantManagement;
            PlantId = entity.PlantId;
            PlantCode = entity.PlantCode;
            PlantDesc = entity.PlantDesc;
            PlannedStart = entity.PlannedStart;
            PlannedEnd = entity.PlannedEnd;
            PlannedDuration = entity.PlannedDuration;
            Assignment01 = entity.Assignment01;
            ControllingGrpDetail01Id = entity.ControllingGrpDetail01Id;
            ControllingGrpDetail01Code = entity.ControllingGrpDetail01Code;
            ControllingGrpDetail01Desc = entity.ControllingGrpDetail01Desc;
            Assignment02 = entity.Assignment02;
            ControllingGrpDetail02Id = entity.ControllingGrpDetail02Id;
            ControllingGrpDetail02Code = entity.ControllingGrpDetail02Code;
            ControllingGrpDetail02Desc = entity.ControllingGrpDetail02Desc;
            Assignment03 = entity.Assignment03;
            ControllingGrpDdetail03Id = entity.ControllingGrpDdetail03Id;
            ControllingGrpDetail03Code = entity.ControllingGrpDetail03Code;
            ControllingGrpDetail03Desc = entity.ControllingGrpDetail03Desc;
            Assignment04 = entity.Assignment04;
            ControllingGrpDetail04Id = entity.ControllingGrpDetail04Id;
            ControllingGrpDetail04Code = entity.ControllingGrpDetail04Code;
            ControllingGrpDetail04Desc = entity.ControllingGrpDetail04Desc;
            Assignment05 = entity.Assignment05;
            ControllingGrpDetail05Id = entity.ControllingGrpDetail05Id;
            ControllingGrpDetail05Code = entity.ControllingGrpDetail05Code;
            ControllingGrpDetail05Desc = entity.ControllingGrpDetail05Desc;
            Assignment06 = entity.Assignment06;
            ControllingGrpDetail06Id = entity.ControllingGrpDetail06Id;
            ControllingGrpDetail06Code = entity.ControllingGrpDetail06Code;
            ControllingGrpDetail06Desc = entity.ControllingGrpDetail06Desc;
            Assignment07 = entity.Assignment07;
            ControllingGrpDetail07Id = entity.ControllingGrpDetail07Id;
            ControllingGrpDetail07Code = entity.ControllingGrpDetail07Code;
            ControllingGrpDetail07Desc = entity.ControllingGrpDetail07Desc;
            Assignment08 = entity.Assignment08;
            ControllingGrpDetail08Id = entity.ControllingGrpDetail08Id;
            ControllingGrpDetail08Code = entity.ControllingGrpDetail08Code;
            ControllingGrpDetail08Desc = entity.ControllingGrpDetail08Desc;
            Assignment09 = entity.Assignment09;
            ControllingGrpDetail09Id = entity.ControllingGrpDetail09Id;
            ControllingGrpDetail09Code = entity.ControllingGrpDetail09Code;
            ControllingGrpDetail09Desc = entity.ControllingGrpDetail09Desc;
            Assignment10 = entity.Assignment10;
            ControllingGrpDetail10Id = entity.ControllingGrpDetail10Id;
            ControllingGrpDetail10Code = entity.ControllingGrpDetail10Code;
            ControllingGrpDetail10Desc = entity.ControllingGrpDetail10Desc;
            CommentText = entity.CommentText;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            Budget = entity.Budget;
            IsFixedBudget = entity.IsFixedBudget;
            IsDefault = entity.IsDefault;
            BudgetDifference = entity.BudgetDifference;
            EstimateCost = entity.EstimateCost;
            BudgetCostDiff = entity.BudgetCostDiff;
            IsIntercompany = entity.IsIntercompany;
            ControllingCatId = entity.ControllingCatId;
            ControllingCatDesc = entity.ControllingCatDesc;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
			ClerkId = entity.ClerkId;
			ClerkCode = entity.ClerkCode;
			ClerkDesc = entity.ClerkDesc;
			ProfitCenterId = entity.ProfitCenterId;
			ProfitCenterCode = entity.ProfitCenterCode;
            LanguageId = entity.LanguageId;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTRUNITSTATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingunitstatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ControllingUnitStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitStatusDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTRUNITSTATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingUnitStatusDesc { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingunitFk")]
        public int? ControllingUnitId { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingUnitCode { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingTemplateId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLTEMPLATE_FK", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControltemplateFk")]
        public int? ControllingTemplateId { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingTemplateUnitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLTEMPLATE_UNIT_FK", TypeName = "int", Order = 6)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControltemplateUnitFk")]
        public int? ControllingTemplateUnitId { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingUnitDesc { get; set; }
    
        /// <summary>
        /// There are no comments for ContextId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.InternalApiField("ContextFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ContextId { get; set; }
    
        /// <summary>
        /// There are no comments for ContextDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_DESC", TypeName = "nvarchar(2000)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ContextDesc { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(32)", Order = 10)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.IdentifyingCode]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(2000)", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DescriptionInfo")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 12)]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 14)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDesc { get; set; }
    
        /// <summary>
        /// There are no comments for Quantity in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QUANTITY", TypeName = "numeric(19,6)", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Quantity { get; set; }
    
        /// <summary>
        /// There are no comments for UomId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_ID", TypeName = "int", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UomFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int UomId { get; set; }
    
        /// <summary>
        /// There are no comments for UomDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_DESC", TypeName = "nvarchar(2000)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string UomDesc { get; set; }
    
        /// <summary>
        /// There are no comments for IsBillingElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISBILLINGELEMENT", TypeName = "bit", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsBillingElement { get; set; }
    
        /// <summary>
        /// There are no comments for IsAccountingElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISACCOUNTINGELEMENT", TypeName = "bit", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsAccountingElement { get; set; }
    
        /// <summary>
        /// There are no comments for IsPlanningElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANNINGELEMENT", TypeName = "bit", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsPlanningElement { get; set; }
    
        /// <summary>
        /// There are no comments for IsStockManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSTOCKMANAGEMENT", TypeName = "bit", Order = 21)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsStockmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsStockManagement { get; set; }
    
        /// <summary>
        /// There are no comments for StockId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_ID", TypeName = "int", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StockFk")]
        public int? StockId { get; set; }
    
        /// <summary>
        /// There are no comments for StockCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_CODE", TypeName = "nvarchar(16)", Order = 23)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string StockCode { get; set; }
    
        /// <summary>
        /// There are no comments for StockDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_DESC", TypeName = "nvarchar(252)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string StockDesc { get; set; }
    
        /// <summary>
        /// There are no comments for IsAssetManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISASSETMANAGEMENT", TypeName = "bit", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsAssetmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsAssetManagement { get; set; }
    
        /// <summary>
        /// There are no comments for IsPlantManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANTMANAGEMENT", TypeName = "bit", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsPlantmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsPlantManagement { get; set; }
    
        /// <summary>
        /// There are no comments for PlantId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_ID", TypeName = "int", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EtmPlantFk")]
        public int? PlantId { get; set; }
    
        /// <summary>
        /// There are no comments for PlantCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_CODE", TypeName = "nvarchar(16)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PlantCode { get; set; }
    
        /// <summary>
        /// There are no comments for PlantDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_DESC", TypeName = "nvarchar(2000)", Order = 29)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PlantDesc { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? PlannedStart { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? PlannedEnd { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedDuration in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_DURATION", TypeName = "numeric(19,6)", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PlannedDuration { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment01 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT01", TypeName = "nvarchar(32)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment01 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail01Fk")]
        public int? ControllingGrpDetail01Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_CODE", TypeName = "nvarchar(32)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail01Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_DESC", TypeName = "nvarchar(2000)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail01Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment02 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT02", TypeName = "nvarchar(32)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment02 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail02Fk")]
        public int? ControllingGrpDetail02Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_CODE", TypeName = "nvarchar(32)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail02Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail02Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment03 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT03", TypeName = "nvarchar(32)", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment03 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDdetail03Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_ID", TypeName = "int", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail03Fk")]
        public int? ControllingGrpDdetail03Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail03Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_CODE", TypeName = "nvarchar(32)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail03Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail03Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_DESC", TypeName = "nvarchar(2000)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail03Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment04 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT04", TypeName = "nvarchar(32)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment04 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_ID", TypeName = "int", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail04Fk")]
        public int? ControllingGrpDetail04Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_CODE", TypeName = "nvarchar(32)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail04Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_DESC", TypeName = "nvarchar(2000)", Order = 48)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail04Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment05 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT05", TypeName = "nvarchar(32)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment05 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_ID", TypeName = "int", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail05Fk")]
        public int? ControllingGrpDetail05Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_CODE", TypeName = "nvarchar(32)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail05Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_DESC", TypeName = "nvarchar(2000)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail05Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment06 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT06", TypeName = "nvarchar(32)", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment06 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail056Fk")]
        public int? ControllingGrpDetail06Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_CODE", TypeName = "nvarchar(32)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail06Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail06Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment07 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT07", TypeName = "nvarchar(32)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment07 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail07Fk")]
        public int? ControllingGrpDetail07Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_CODE", TypeName = "nvarchar(32)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail07Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_DESC", TypeName = "nvarchar(2000)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail07Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment08 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT08", TypeName = "nvarchar(32)", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment08 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail08Fk")]
        public int? ControllingGrpDetail08Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_CODE", TypeName = "nvarchar(32)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail08Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_DESC", TypeName = "nvarchar(2000)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail08Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment09 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT09", TypeName = "nvarchar(32)", Order = 65)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment09 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail09Fk")]
        public int? ControllingGrpDetail09Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_CODE", TypeName = "nvarchar(32)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail09Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_DESC", TypeName = "nvarchar(2000)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail09Desc { get; set; }
    
        /// <summary>
        /// There are no comments for Assignment10 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT10", TypeName = "nvarchar(32)", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string Assignment10 { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_ID", TypeName = "int", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail10Fk")]
        public int? ControllingGrpDetail10Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_CODE", TypeName = "nvarchar(32)", Order = 71)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string ControllingGrpDetail10Code { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_DESC", TypeName = "nvarchar(2000)", Order = 72)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingGrpDetail10Desc { get; set; }
    
        /// <summary>
        /// There are no comments for CommentText in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMMENT_TEXT", TypeName = "nvarchar(255)", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string CommentText { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 79)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 80)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 81)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 82)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 83)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for Budget in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET", TypeName = "numeric(19,7)", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Budget { get; set; }
    
        /// <summary>
        /// There are no comments for IsFixedBudget in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFIXED_BUDGET", TypeName = "bit", Order = 85)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsFixedBudget { get; set; }
    
        /// <summary>
        /// There are no comments for IsDefault in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDEFAULT", TypeName = "bit", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsDefault { get; set; }
    
        /// <summary>
        /// There are no comments for BudgetDifference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET_DIFFERENCE", TypeName = "numeric(19,7)", Order = 87)]
        public decimal? BudgetDifference { get; set; }
    
        /// <summary>
        /// There are no comments for EstimateCost in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ESTIMATE_COST", TypeName = "numeric(19,7)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public decimal? EstimateCost { get; set; }
    
        /// <summary>
        /// There are no comments for BudgetCostDiff in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET_COST_DIFF", TypeName = "numeric(19,7)", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public decimal? BudgetCostDiff { get; set; }
    
        /// <summary>
        /// There are no comments for IsIntercompany in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISINTERCOMPANY", TypeName = "bit", Order = 90)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsIntercompany { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CONTROLLINGCAT_ID", TypeName = "int", Order = 91)]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingCatFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ControllingCatId { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingCatDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CONTROLLINGCAT_DESC", TypeName = "nvarchar(2000)", Order = 92)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ControllingCatDesc { get; set; }

		/// <summary>
		/// Clerk
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_ID", TypeName = "int", Order = 93)]
		[RIB.Visual.Platform.Common.InternalApiField("ClerkIdFk")]
		[RIB.Visual.Platform.Common.CopyFromPublicApi]
		public int? ClerkId { get; set; }

		/// <summary>
		/// Clerk Code
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_CODE", TypeName = "nvarchar(16)", Order = 94)]
		[System.ComponentModel.DataAnnotations.StringLength(16)]
		public string ClerkCode { get; set; }

		/// <summary>
		///Clerk Description
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_DESC", TypeName = "nvarchar(252)", Order = 95)]
		[System.ComponentModel.DataAnnotations.StringLength(252)]
		public string ClerkDesc { get; set; }

		/// <summary>
		/// Profit Center  (Use the system default value if no json input)
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_RESPONSIBLE_ID", TypeName = "int", Order = 96)]
		[RIB.Visual.Platform.Common.CopyFromPublicApi]
		[RIB.Visual.Platform.Common.InternalApiField("CompanyResponsibleFk")]
		[System.ComponentModel.DataAnnotations.Required()]
		public int ProfitCenterId { get; set; }

		/// <summary>
		/// Profit Center Code
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_RESPONSIBLE_CODE", TypeName = "nvarchar(16)", Order = 97)]
		[System.ComponentModel.DataAnnotations.StringLength(16)]
		public string ProfitCenterCode { get; set; }

		/// <summary>
		/// There are no comments for CompanyId in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 93)]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }

		/// <summary>
		/// There are no comments for ForTimekeeping in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("ISTIMEKEEPINGELEMENT", TypeName = "bit", Order = 100)]
		[RIB.Visual.Platform.Common.InternalApiField("IsTimekeepingElement")]
		public  bool ForTimekeeping{ get; set; }

		/// <summary>
		/// There are no comments for LanguageId in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 95)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(ControllingUnitApiEntity); }
        }

        /// <summary>
        /// Copy the current ControllingUnitApiDto instance to a new ControllingUnitApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class ControllingUnitApiEntity</returns>
        public ControllingUnitApiEntity Copy()
        {
          var entity = new ControllingUnitApiEntity();

          entity.Id = this.Id;
          entity.ControllingUnitStatusId = this.ControllingUnitStatusId;
          entity.ControllingUnitStatusDesc = this.ControllingUnitStatusDesc;
          entity.ControllingUnitId = this.ControllingUnitId;
          entity.ControllingUnitCode = this.ControllingUnitCode;
          entity.ControllingTemplateId = this.ControllingTemplateId;
          entity.ControllingTemplateUnitId = this.ControllingTemplateUnitId;
          entity.ControllingUnitDesc = this.ControllingUnitDesc;
          entity.ContextId = this.ContextId;
          entity.ContextDesc = this.ContextDesc;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDesc = this.ProjectDesc;
          entity.Quantity = this.Quantity;
          entity.UomId = this.UomId;
          entity.UomDesc = this.UomDesc;
          entity.IsBillingElement = this.IsBillingElement;
          entity.IsAccountingElement = this.IsAccountingElement;
          entity.IsPlanningElement = this.IsPlanningElement;
          entity.IsStockManagement = this.IsStockManagement;
          entity.StockId = this.StockId;
          entity.StockCode = this.StockCode;
          entity.StockDesc = this.StockDesc;
          entity.IsAssetManagement = this.IsAssetManagement;
          entity.IsPlantManagement = this.IsPlantManagement;
          entity.PlantId = this.PlantId;
          entity.PlantCode = this.PlantCode;
          entity.PlantDesc = this.PlantDesc;
          entity.PlannedStart = this.PlannedStart;
          entity.PlannedEnd = this.PlannedEnd;
          entity.PlannedDuration = this.PlannedDuration;
          entity.Assignment01 = this.Assignment01;
          entity.ControllingGrpDetail01Id = this.ControllingGrpDetail01Id;
          entity.ControllingGrpDetail01Code = this.ControllingGrpDetail01Code;
          entity.ControllingGrpDetail01Desc = this.ControllingGrpDetail01Desc;
          entity.Assignment02 = this.Assignment02;
          entity.ControllingGrpDetail02Id = this.ControllingGrpDetail02Id;
          entity.ControllingGrpDetail02Code = this.ControllingGrpDetail02Code;
          entity.ControllingGrpDetail02Desc = this.ControllingGrpDetail02Desc;
          entity.Assignment03 = this.Assignment03;
          entity.ControllingGrpDdetail03Id = this.ControllingGrpDdetail03Id;
          entity.ControllingGrpDetail03Code = this.ControllingGrpDetail03Code;
          entity.ControllingGrpDetail03Desc = this.ControllingGrpDetail03Desc;
          entity.Assignment04 = this.Assignment04;
          entity.ControllingGrpDetail04Id = this.ControllingGrpDetail04Id;
          entity.ControllingGrpDetail04Code = this.ControllingGrpDetail04Code;
          entity.ControllingGrpDetail04Desc = this.ControllingGrpDetail04Desc;
          entity.Assignment05 = this.Assignment05;
          entity.ControllingGrpDetail05Id = this.ControllingGrpDetail05Id;
          entity.ControllingGrpDetail05Code = this.ControllingGrpDetail05Code;
          entity.ControllingGrpDetail05Desc = this.ControllingGrpDetail05Desc;
          entity.Assignment06 = this.Assignment06;
          entity.ControllingGrpDetail06Id = this.ControllingGrpDetail06Id;
          entity.ControllingGrpDetail06Code = this.ControllingGrpDetail06Code;
          entity.ControllingGrpDetail06Desc = this.ControllingGrpDetail06Desc;
          entity.Assignment07 = this.Assignment07;
          entity.ControllingGrpDetail07Id = this.ControllingGrpDetail07Id;
          entity.ControllingGrpDetail07Code = this.ControllingGrpDetail07Code;
          entity.ControllingGrpDetail07Desc = this.ControllingGrpDetail07Desc;
          entity.Assignment08 = this.Assignment08;
          entity.ControllingGrpDetail08Id = this.ControllingGrpDetail08Id;
          entity.ControllingGrpDetail08Code = this.ControllingGrpDetail08Code;
          entity.ControllingGrpDetail08Desc = this.ControllingGrpDetail08Desc;
          entity.Assignment09 = this.Assignment09;
          entity.ControllingGrpDetail09Id = this.ControllingGrpDetail09Id;
          entity.ControllingGrpDetail09Code = this.ControllingGrpDetail09Code;
          entity.ControllingGrpDetail09Desc = this.ControllingGrpDetail09Desc;
          entity.Assignment10 = this.Assignment10;
          entity.ControllingGrpDetail10Id = this.ControllingGrpDetail10Id;
          entity.ControllingGrpDetail10Code = this.ControllingGrpDetail10Code;
          entity.ControllingGrpDetail10Desc = this.ControllingGrpDetail10Desc;
          entity.CommentText = this.CommentText;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.Budget = this.Budget;
          entity.IsFixedBudget = this.IsFixedBudget;
          entity.IsDefault = this.IsDefault;
          entity.BudgetDifference = this.BudgetDifference;
          entity.EstimateCost = this.EstimateCost;
          entity.BudgetCostDiff = this.BudgetCostDiff;
          entity.IsIntercompany = this.IsIntercompany;
          entity.ControllingCatId = this.ControllingCatId;
          entity.ControllingCatDesc = this.ControllingCatDesc;
		  entity.ClerkId = this.ClerkId;
		  entity.ClerkCode = this.ClerkCode;
		  entity.ClerkDesc = this.ClerkDesc;
		  entity.ProfitCenterId = this.ProfitCenterId;
		  entity.ProfitCenterCode = this.ProfitCenterCode;
		  entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.LanguageId = this.LanguageId;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(ControllingUnitApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(ControllingUnitApiEntity entity);
    }

}
