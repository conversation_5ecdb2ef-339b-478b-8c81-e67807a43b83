using RIB.Visual.Procurement.Common.BusinessComponents;
using System.Collections.Generic;

namespace RIB.Visual.Procurement.Common.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class PrcItemsCreateFromMaterialsResult
	{
		/// <summary>
		/// The created PrcItems with all material-related properties populated
		/// </summary>
		public IEnumerable<PrcItemDto> PrcItems { get; set; }

		/// <summary>
		/// Price conditions created for the PrcItems
		/// </summary>
		public IEnumerable<PrcItemPriceConditionDto> PriceConditions { get; set; }

		/// <summary>
		/// Certificates copied from materials
		/// </summary>
		public CopyCertificatesFromMaterialResult CopyCertificatesResult { get; set; }
	}
}
