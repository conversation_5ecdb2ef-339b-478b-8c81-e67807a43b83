/*
 * Copyright(c) RIB Software GmbH
 */

import { moduleMetadata } from '@storybook/angular';
import { Story, Meta } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ModalBodyInputComponent, INPUT_DLG_OPTIONS_TOKEN } from './modal-body-input.component';
import { IInputDialogOptions } from '../../model/input-dialog-options.interface';
import { ICustomDialog, getCustomDialogDataToken } from '../../../base';

export default {
  title: 'UI Common/Dialogs/Input/ClickInputDialog',
  component: ModalBodyInputComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FormsModule],
      declarations: [ModalBodyInputComponent],
      providers: [
        {
          provide: INPUT_DLG_OPTIONS_TOKEN,
          useValue: {
            headerText: 'Input Context ID',
            bodyText: 'Please Enter A value',
            pattern: '[a-zA-Z ]*',
            width: '30%',
            maxLength: 20,
            type: 'text',
          } as IInputDialogOptions
        },
        {
          provide: getCustomDialogDataToken<string, ModalBodyInputComponent>(),
          useValue: {
            value: '',
            get body(): ModalBodyInputComponent {
              return {} as ModalBodyInputComponent;
            },
            close() {},
            setAlarmConfig() {}
          } as ICustomDialog<string, ModalBodyInputComponent>
        }
      ]
    })
  ],
} as Meta<ModalBodyInputComponent>;

const Template: Story<ModalBodyInputComponent> = (args) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {};

export const WithValue = Template.bind({});
WithValue.args = {
  value: 'Sample input value'
};