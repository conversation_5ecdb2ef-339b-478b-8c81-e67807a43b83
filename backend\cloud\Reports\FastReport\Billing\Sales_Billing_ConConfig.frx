﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" DoublePass="true" UseFileCache="false" StartReportEvent="_StartReport" FinishReportEvent="_FinishReport" ReportInfo.Name="Kunde: RIB Software GmbH" ReportInfo.Author="Copyrigth 2024 MSS Medien-Service-Systeme UG" ReportInfo.Created="01/13/2022 09:46:54" ReportInfo.Modified="06/11/2025 15:21:35" ReportInfo.CreatorVersion="2025.1.14.0">
  <ScriptText>/* *****************************************************
Dieser Bericht wurde erstellt für: RIB Software GmbH.
Copyrigth 2024 MSS Medien-Service-Systeme UG
***************************************************** */
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  // 1014195 - SR
  // 1014188 - 2. AR
  // 1014187 - 1. AR
  public class ReportScript
  {
    bool Test = false;
    bool HatZahlungen = false;
    bool LangtextDrucken = false;

    // Einstellungen Zuwachs
    bool ZuwachsLvDrucken = false;
    bool ZuwachsLvSummenDrucken = false;
    bool ZuwachsAufmassDrucken = false;
        
    // Einstellungen Kumuliert
    bool KumuliertLvDrucken = false;
    bool KumuliertLvFirstPrint = true;
    bool KumuliertLvSummenDrucken = false;
    bool KumuliertAufmassDrucken = false;
    
    decimal Betrag_Zeile_10 =  0;
    decimal Betrag_Zeile_30 =  0;
    decimal Betrag_Zeile_50 =  0;
    decimal Betrag_Zeile_70 =  0;
    decimal Betrag_Zeile_140 =  0;

    decimal SBPercent =  0;
    long BOQ_HEADER_FK = -1;
    
    // Funktionen ##############################################################################
    private bool GetRtfText(ref RichObject rich, string DataField) 
    {
      if(((Byte[])Report.GetColumnValue(DataField)) == null)
      {
        rich.Text = string.Empty;   
      }
      else
      {
        if(Report.GetColumnValue(DataField) != null)
        {
          Byte[] text = ((Byte[])Report.GetColumnValue(DataField));
          if(text != null)
          {
            try
            {
              string t =  ToRtf(text);
              if(string.IsNullOrWhiteSpace(t))
              {
                rich.Text = string.Empty;
              }
              else
              {
                rich.Text = t;
                
              }
            }
            catch(Exception ex)
            {
              rich.Text = ex.Message;
            }
          }
        }
      } 
      return rich.Text != string.Empty;
    }
    private int GetDataCount(string TableName) {
      DataSourceBase ds = Report.GetDataSource(TableName); 
      ds.Init();
      return ds.RowCount;
    }
    
    private string CreatePaymentText() {
      string h = &quot;&quot;;
      if (((String)Report.GetColumnValue(&quot;Rechnung.ZAHLBAR_SKONTO&quot;)) != &quot;&quot; &amp;&amp; ((Decimal)Report.GetColumnValue(&quot;Rechnung.SKONTPROZENT&quot;)) &gt; 0 &amp;&amp; ((Int32)Report.GetColumnValue(&quot;Rechnung.TAGE_SKONTO&quot;)) &gt; 0)
        h = &quot;Zahlbar innerhalb von &quot; + FormatNumber(((Int32)Report.GetColumnValue(&quot;Rechnung.TAGE_SKONTO&quot;)),0) + 
        &quot; Tagen mit &quot; + FormatNumber(((Decimal)Report.GetColumnValue(&quot;Rechnung.SKONTPROZENT&quot;)),2) + 
        &quot; % Skonto bis zum &quot; + ((String)Report.GetColumnValue(&quot;Rechnung.ZAHLBAR_SKONTO&quot;));  
      if (h != &quot;&quot;)
        h += &quot; oder innerhalb &quot; + FormatNumber(((Int32)Report.GetColumnValue(&quot;Rechnung.TAGE_NETTO&quot;)),0) + &quot; Tagen netto bis zum &quot; + ((String)Report.GetColumnValue(&quot;Rechnung.ZAHLBAR_NETTO&quot;));
      else
        h = &quot;Zahlbar innerhalb &quot; + FormatNumber(((Int32)Report.GetColumnValue(&quot;Rechnung.TAGE_NETTO&quot;)),0) + &quot; Tagen netto bis zum &quot; + ((String)Report.GetColumnValue(&quot;Rechnung.ZAHLBAR_NETTO&quot;));
      return h + &quot;.&quot;;
    }
    
    private string GetUstText() {
      string tc = ((String)Report.GetColumnValue(&quot;Rechnung.TAX_CODE&quot;));
      
      DataUstText.Visible = false;
      DataUstText2.Visible = false;
      switch (tc) {
        case &quot;00&quot;: 
        {
          DataUstText.Visible = true;
          DataUstText2.Visible = true;
          return &quot;&quot;;
        }
        case &quot;00_&quot;: 
        case &quot;07&quot;: 
        case &quot;16&quot;: 
        case &quot;16T&quot;: 
        case &quot;19&quot;: 
        case &quot;19T&quot;: 
        {
          return &quot;&quot;;
        }
        case &quot;13B16&quot;: 
        case &quot;13B19&quot;: 
        {
          return &quot;Steuerschuldnerschaft des Leistungsempfängers gemäß § 13b Abs. 2 Nr. 4 USt.&quot;;
        }
        case &quot;NSTB&quot;: 
        {
          return &quot;Nicht steuerbarer Umsatz.&quot;;
        }
        case &quot;ST_ORG&quot;: 
        {
          return &quot;Nicht steuerbarer Innenumsatz innerhalb der umsatzsteuerlichen Organschaft.&quot;;
        }
        default:
          return &quot;&quot;;
      }
    }
    private void AdDebugText(string lt, string rt) {
      WriteMessage(lt + &quot;: &quot; + rt);
    }
    private void WriteMessage(string message)
    {
      if(Report.Designer != null)
      {
        Report.Designer.ShowMessage(message);
      }
    }
    private void GetBillingSchemaSettings() {
      long Sorting = 0;
      decimal Betrag = 0;
      DataSourceBase ds = Report.GetDataSource(&quot;Abrechnungsschema&quot;);
      ds.Init();
      while (ds.HasMoreRows) {
        Sorting = ((Int32)Report.GetColumnValue(&quot;Abrechnungsschema.SORTING&quot;));
        Betrag = ((Decimal)Report.GetColumnValue(&quot;Abrechnungsschema.BETRAG&quot;));
        switch (Sorting) {
          case 10: {
            Betrag_Zeile_10 = Betrag;
            break;
          } 
          case 30: {
            Betrag_Zeile_30 = Betrag;
            break;
          } 
          case 50: {
            Betrag_Zeile_50 = Betrag;
            break;
          } 
          case 70: {
            Betrag_Zeile_70 = Betrag;
            break;
          } 
          case 140: {
            Betrag_Zeile_140 = Betrag;
            break;
          } 
          default: { break; }
        }
        ds.Next();
      }
    }
    private string CreateReceiverAddress() {
      string reg = &quot;&quot;;
      if (((Boolean)Report.GetParameterValue(&quot;GPReguliererDrucken&quot;)))
        reg = &quot;_REG&quot;;
      string txt = &quot;&quot;;
      string contact = &quot;&quot;;
      string BriefAnrede = Translate(((String)Report.GetColumnValue(&quot;Geschaeftspartner.LETTER_SALUTATION&quot; + reg)), ((Int32)Report.GetColumnValue(&quot;Geschaeftspartner.LETTER_SALUTATION_TR&quot; + reg)));
      string VolleBriefAnrede = Translate(((String)Report.GetColumnValue(&quot;Geschaeftspartner.FULL_LETTER_SALUTATION&quot; + reg)), ((Int32)Report.GetColumnValue(&quot;Geschaeftspartner.FULL_LETTER_SALUTATION_TR&quot; + reg)));
      string Land = Translate(((String)Report.GetColumnValue(&quot;Geschaeftspartner.COUNTRY&quot; + reg)), ((Int32)Report.GetColumnValue(&quot;Geschaeftspartner.COUNTRY_TR&quot; + reg)));
      
      txt += ((String)Report.GetColumnValue(&quot;Geschaeftspartner.BP_NAME1&quot; + reg));
      if (((String)Report.GetColumnValue(&quot;Geschaeftspartner.BP_NAME2&quot; + reg)).Trim() != &quot;&quot;)
        txt += &quot;&lt;br&gt;&quot; + ((String)Report.GetColumnValue(&quot;Geschaeftspartner.BP_NAME2&quot; + reg));    
      
      // Kontakt aufbauen
      if (((Boolean)Report.GetParameterValue(&quot;ContactSalutation&quot;)) &amp;&amp; ((String)Report.GetColumnValue(&quot;Geschaeftspartner.FAMILY_NAME&quot; + reg)).Trim() != &quot;&quot;) {
        if (BriefAnrede.Trim() != &quot;&quot;)
          contact += &quot;&lt;br&gt;&quot; + BriefAnrede + &quot; &quot;;
        if (((String)Report.GetColumnValue(&quot;Geschaeftspartner.FIRST_NAME&quot; + reg)).Trim() != &quot;&quot;) {
          if (contact.Trim() == &quot;&quot;)
            contact += &quot;&lt;br&gt;&quot;;
          contact += ((String)Report.GetColumnValue(&quot;Geschaeftspartner.FIRST_NAME&quot; + reg)) + &quot; &quot;;
        }
        if (contact.Trim() == &quot;&quot;)
          contact += &quot;&lt;br&gt;&quot;;
        contact += ((String)Report.GetColumnValue(&quot;Geschaeftspartner.FAMILY_NAME&quot; + reg));
      }
      txt += contact;
      txt += &quot;&lt;br&gt;&quot; + ((String)Report.GetColumnValue(&quot;Geschaeftspartner.STREET&quot; + reg));
      txt += &quot;&lt;br&gt;&quot; + ((String)Report.GetColumnValue(&quot;Geschaeftspartner.ZIPCODE&quot; + reg)) + &quot; &quot; + ((String)Report.GetColumnValue(&quot;Geschaeftspartner.CITY&quot; + reg));
      txt += &quot;&lt;br&gt;&quot; + Land;
      return txt;
    }
    
    // Funktionen Ende ##########################################################################

    private void _StartReport(object sender, EventArgs e)
    {
      // Druckbereiche und Elemente den Parametern entsprechend ausblenden
      OLayLetterHeader.Visible = ((Boolean)Report.GetParameterValue(&quot;PrintLetterHeader&quot;));
      ChildFooterCompanyText.Visible = !((Boolean)Report.GetParameterValue(&quot;PrintLetterHeader&quot;));
      PicCompanyLogo.Visible = !((Boolean)Report.GetParameterValue(&quot;PrintLetterHeader&quot;));
      

      
      // Ansprechpartner aufbauen
      string ContactHeader = &quot;Ihr Ansprechpartner:&lt;br&gt;&quot;; //Translate(&quot;report.clerk_prc&quot;,&quot;Your contact:&quot;) + &quot;&lt;br&gt;&quot;;
      TxtAnsprechpartner.Visible = ((String)Report.GetColumnValue(&quot;Clerk.FULL_NAME&quot;)).Trim() != &quot;&quot;;
      TxtAnsprechpartner.Text = &quot;&quot;;
      if (((String)Report.GetColumnValue(&quot;Clerk.FULL_NAME&quot;)).Trim() != &quot;&quot;){
        TxtAnsprechpartner.Text += ContactHeader;
        TxtAnsprechpartner.Text += ((String)Report.GetColumnValue(&quot;Clerk.FULL_NAME&quot;));
        if (((String)Report.GetColumnValue(&quot;Clerk.TELEPHONE&quot;)).Trim() != &quot;&quot;) 
          TxtAnsprechpartner.Text += &quot;&lt;br&gt;&quot; + Translate(&quot;report.tel&quot;,&quot;Telefon:&quot;) + &quot; &quot; + ((String)Report.GetColumnValue(&quot;Clerk.TELEPHONE&quot;));
        if (((String)Report.GetColumnValue(&quot;Clerk.MOBILE&quot;)).Trim() != &quot;&quot;) 
          TxtAnsprechpartner.Text += &quot;&lt;br&gt;&quot; + Translate(&quot;report.mobile&quot;,&quot;Mobile:&quot;) + &quot; &quot; + ((String)Report.GetColumnValue(&quot;Clerk.MOBILE&quot;));
        if (((String)Report.GetColumnValue(&quot;Clerk.EMAIL&quot;)).Trim() != &quot;&quot;) 
          TxtAnsprechpartner.Text += &quot;&lt;br&gt;&quot; + Translate(&quot;report.email&quot;,&quot;E-Mail:&quot;) + &quot; &quot; + ((String)Report.GetColumnValue(&quot;Clerk.EMAIL&quot;));
      }

      // Rechnungs Details ein/ausblenden
      ChildReData_Stadt.Visible = ((String)Report.GetColumnValue(&quot;Rechnung.PRJ_CITY&quot;)).Trim() != &quot;&quot;;
      ChildReData_Strasse.Visible = ((String)Report.GetColumnValue(&quot;Rechnung.PRJ_STREET&quot;)).Trim() != &quot;&quot;;
      ChildReData_Leistung.Visible = ((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGSVON&quot;)).Trim() != &quot;&quot; || ((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGBIS&quot;)).Trim() != &quot;&quot;;
      ChildReData_Auftrag.Visible = ((String)Report.GetColumnValue(&quot;Rechnung.AUFTRAGS_NR&quot;)) != &quot;&quot;;

      TxtLeistungZeitraum.Text = &quot;&quot;;
      if (((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGSVON&quot;)).Trim() != &quot;&quot;)
        TxtLeistungZeitraum.Text = ((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGSVON&quot;));
      if (((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGBIS&quot;)).Trim() != &quot;&quot;) {
        if (TxtLeistungZeitraum.Text != &quot;&quot;)
          TxtLeistungZeitraum.Text += &quot; bis &quot;;
        TxtLeistungZeitraum.Text += ((String)Report.GetColumnValue(&quot;Rechnung.LEISTUNGBIS&quot;));
      }
      
      string txt = &quot;&quot;;
      LangtextDrucken = ((Boolean)Report.GetParameterValue(&quot;LangtextDrucken&quot;));
      KumuliertLvFirstPrint = true;
      HatZahlungen = GetDataCount(&quot;Zahlungen&quot;) &gt; 0;

      // Einstellungen für Kumulierte Daten 
      KumuliertLvDrucken = ((Boolean)Report.GetParameterValue(&quot;KumuliertLvDrucken&quot;));
      KumuliertLvSummenDrucken = ((Boolean)Report.GetParameterValue(&quot;KumuliertLvSummenDrucken&quot;));
      KumuliertAufmassDrucken = ((Boolean)Report.GetParameterValue(&quot;KumuliertAufmassDrucken&quot;));
      DataKumuliertHeader.Visible = false;
      PageKumuliertHeader.Visible = false;
      DataKumuliertLv.Visible = false;
      DataKumuliertLvSummen.Visible = false;
      DataKumuliertAufmass.Visible = false;
      DataGeneralien.Visible = GetDataCount(&quot;Generalien&quot;) &gt; 0 &amp;&amp; ((Boolean)Report.GetParameterValue(&quot;GeneralienDrucken&quot;));
      
      //Inhaltsverzeichnis nur Anzeigen, wenn es mehr als eins gibt.
      if (Test)
        DataPageLvKumuliertInhalt.Visible = /*GetDataCount(&quot;LvKumuliertInhaltsverzeichnis&quot;) &gt; 1 &amp;&amp; */ KumuliertLvDrucken &amp;&amp; ((Boolean)Report.GetParameterValue(&quot;DruckeInhaltsverzeichnis&quot;));
      else
        DataPageLvKumuliertInhalt.Visible = GetDataCount(&quot;LvKumuliertInhaltsverzeichnis&quot;) &gt; 1 &amp;&amp; KumuliertLvDrucken &amp;&amp; ((Boolean)Report.GetParameterValue(&quot;DruckeInhaltsverzeichnis&quot;));

      
      if (KumuliertLvSummenDrucken &amp;&amp; GetDataCount(&quot;BoqKumuliertHeader&quot;) &gt; 0 &amp;&amp; GetDataCount(&quot;LvKumuliertSummen&quot;) &gt; 0) {
        DataKumuliertHeader.Visible = true;
        PageKumuliertHeader.Visible = true;
        DataKumuliertLvSummen.Visible = true;
      }

      if (KumuliertLvDrucken &amp;&amp; GetDataCount(&quot;BoqKumuliertHeader&quot;) &gt; 0 &amp;&amp; GetDataCount(&quot;LvKumuliert&quot;) &gt; 0) {
        DataKumuliertHeader.Visible = true;
        PageKumuliertHeader.Visible = true;
        DataKumuliertLv.Visible = true;
      }
      
      if (KumuliertAufmassDrucken &amp;&amp; GetDataCount(&quot;BoqKumuliertHeader&quot;) &gt; 0 &amp;&amp; GetDataCount(&quot;LvKumuliertMitAufmass&quot;) &gt; 0) {
        DataKumuliertHeader.Visible = true;
        PageKumuliertHeader.Visible = true;
        //AdDebugText(&quot;GetDataCount(LvKumuliertMitAufmass)&quot;, GetDataCount(&quot;LvKumuliertMitAufmass&quot;).ToString());
        DataKumuliertAufmass.Visible = true;
      }
      
      //AdDebugText(&quot;DataKumuliertLvSummen&quot;, DataKumuliertLvSummen.Visible.ToString());

      // Einstellungen für Zuwachs Daten
      ZuwachsLvDrucken = ((Boolean)Report.GetParameterValue(&quot;ZuwachsLvDrucken&quot;));
      ZuwachsLvSummenDrucken = ((Boolean)Report.GetParameterValue(&quot;ZuwachsLvSummenDrucken&quot;));
      ZuwachsAufmassDrucken = ((Boolean)Report.GetParameterValue(&quot;ZuwachsAufmassDrucken&quot;));

      DataZuwachsHeader.Visible = false;
      PageZuwachsHeader.Visible = false;
      DataLVHeaderLV.Visible = false;
      DataLVHeaderZusammenfassung.Visible = false;
      DataLVHeaderAufmass.Visible = false;
      
      if ((ZuwachsLvDrucken || ZuwachsLvSummenDrucken || ZuwachsAufmassDrucken) &amp;&amp; GetDataCount(&quot;BoqHeader&quot;) &gt; 0) {
        DataZuwachsHeader.Visible = true;
        PageZuwachsHeader.Visible = true;
        DataLVHeaderLV.Visible = ZuwachsLvDrucken;
        DataLVHeaderZusammenfassung.Visible = ZuwachsLvSummenDrucken;
        DataLVHeaderAufmass.Visible = ZuwachsAufmassDrucken;
      }      
      
      // Positionen anzeigen ?
      PagePositionHeader.Visible = GetDataCount(&quot;Positionen&quot;) &gt; 0;
      
      // ***********************************************************************

      // Bereiche drucken
      DataSpecification.Visible = LangtextDrucken;
      DataLvKumLangtext.Visible = LangtextDrucken;
      ChildMatPositionRichText.Visible = LangtextDrucken;
      ChildMatPositionPlainText.Visible = LangtextDrucken;

      // Rechnungs Titel anhand des Typs erzeugen
      int ar_count = GetDataCount(&quot;RechnungsHistorie&quot;) + 1;
      if (((Int32)Report.GetColumnValue(&quot;Rechnung.RECHNUNGSTYP&quot;)) == ((Int32)Report.GetParameterValue(&quot;BILL_TYPE_AR&quot;)))
        TxtRechnungsTyp.Text = ar_count.ToString() + &quot;. Abschlagsrechnung&quot;;
      else if (((Int32)Report.GetColumnValue(&quot;Rechnung.RECHNUNGSTYP&quot;)) == ((Int32)Report.GetParameterValue(&quot;BILL_TYPE_SR&quot;)) ||
        ((Int32)Report.GetColumnValue(&quot;Rechnung.RECHNUNGSTYP&quot;)) == ((Int32)Report.GetParameterValue(&quot;BILL_TYPE_SR2&quot;)))
        TxtRechnungsTyp.Text = &quot;Schlussrechnung&quot;;
      else if (((Int32)Report.GetColumnValue(&quot;Rechnung.RECHNUNGSTYP&quot;)) == ((Int32)Report.GetParameterValue(&quot;BILL_TYPE_TR&quot;))) {
        //TxtRechnungsTyp.Text = &quot;Teilrechnung&quot;; // Aus bei Teilrechnung soll stehen, die wievielte es ist
        TxtRechnungsTyp.Text = ar_count.ToString() + &quot;. Teilrechnung&quot;;
        if (((String)Report.GetColumnValue(&quot;Rechnung.RECHUNGSTITEL&quot;)) == &quot;Schlussrechung&quot;)
          TxtRechnungsTyp.Text = &quot;Schlussrechnung&quot;;
      }
      else if (((Int32)Report.GetColumnValue(&quot;Rechnung.RECHNUNGSTYP&quot;)) == ((Int32)Report.GetParameterValue(&quot;BILL_TYPE_EZ&quot;)))
        TxtRechnungsTyp.Text = &quot;Rechnung&quot;;
      else 
        TxtRechnungsTyp.Text = &quot;Rechnung&quot;;
      
      
      txt = ((String)Report.GetColumnValue(&quot;Company.MANDANT_LETTER_FOOTER&quot;));
      
      // Abrechnungsschema laden
      GetBillingSchemaSettings();
      
      // Texte setzen
      TxtZK.Text = CreatePaymentText();
      TxtZK2.Text = TxtZK.Text;
      
      TxtUst.Text = GetUstText();
      TxtUst2.Text = TxtUst.Text;
     
      AdDebugText(&quot;_StartReport&quot;, &quot;Ende&quot;);
    }
    // Begin PageUebersicht 
    // ##################################################################################################################################
    private void Data6_BeforePrint(object sender, EventArgs e)
    {
      long Sorting = ((Int32)Report.GetColumnValue(&quot;Abrechnungsschema.SORTING&quot;));
      decimal Betrag = ((Decimal)Report.GetColumnValue(&quot;Abrechnungsschema.BETRAG&quot;));
      decimal BruttoBetrag = 0;
      decimal Percent =  ((Decimal)Report.GetColumnValue(&quot;Abrechnungsschema.PROZ_WERT&quot;));
      bool IsBold = ((Boolean)Report.GetColumnValue(&quot;Abrechnungsschema.ISBOLD&quot;));
      bool IsUnderline = ((Boolean)Report.GetColumnValue(&quot;Abrechnungsschema.ISUNDERLINE&quot;));
      bool IsPercent = ((Boolean)Report.GetColumnValue(&quot;Abrechnungsschema.ISPERCENT&quot;));
      bool IsPrintZero = ((Boolean)Report.GetColumnValue(&quot;Abrechnungsschema.ISPRINTEDZERO&quot;));
      long BillingType = ((Int32)Report.GetColumnValue(&quot;Abrechnungsschema.MDC_BILLING_LINE_TYPE_FK&quot;));
      
      DataBold.Visible = false;
      DataNormal.Visible = false;
      DataBoldUnderline.Visible = false;
      
      DataEndsumme.Visible = false;
      DataUSt.Visible = false;
      DataArRechnungen.Visible = false;
      DataEinbehaltProzent.Visible = false;
      DataZahlungsuebersicht.Visible = false;
      
      if (!IsPrintZero &amp;&amp; Betrag == 0)
        return;
      
      TxtARSProzent.Text = &quot;&quot;;
      if (IsPercent)
        TxtARSProzent.Text = FormatNumber(Percent,2) + &quot; %&quot;; 
      
      if (BillingType == 25) {
        //AdDebugText(&quot;BillingType:&quot;, BillingType.ToString());
        if (Betrag &gt; 0)
          Betrag = Betrag * -1;
        PageUebersichtTxtZahlungGesamt.Text = FormatNumber(Betrag, 2);
        DataZahlungsuebersicht.Visible = true;
        return;
        
      } else if (BillingType == 20) {
        TxtARSProzent.Text = FormatNumber(((Decimal)Report.GetColumnValue(&quot;Rechnung.MWST_PROZENT&quot;)),2) + &quot; %&quot;;
      }
      
      if (IsBold &amp;&amp; !IsUnderline)
        DataBold.Visible = true;
      else if (IsBold &amp;&amp; IsUnderline)
        DataBoldUnderline.Visible = true;
      else if (!IsBold &amp;&amp; !IsUnderline)
        DataNormal.Visible = true;
    }
    private void RichUst_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichUst, &quot;Text.USTTEXT&quot;);
    }

    // ##################################################################################################################################
    // Ende PageUebersicht 
    
    private void DataTopLevel_BeforePrint(object sender, EventArgs e)
    {
      DataTopLevel.Visible = false;
      DataBottumLevel.Visible = false;
      DataLVPosition.Visible = false;
      DataSpace.Visible = false;
      if (((Int32)Report.GetColumnValue(&quot;LV.BOQ_LINE_TYPE_FK&quot;)) &gt;= 1 &amp;&amp; 
          ((Int32)Report.GetColumnValue(&quot;LV.BOQ_LINE_TYPE_FK&quot;)) &lt;= 8 &amp;&amp; ((String)Report.GetColumnValue(&quot;LV.PRINT_MARK&quot;)) == &quot;LEVEL&quot;) {
        DataTopLevel.Visible = true;                                                                                              
//        if (!((Boolean)Report.GetParameterValue(&quot;NullTitelDrucken&quot;)) &amp;&amp; ((Int32)Report.GetColumnValue(&quot;LV.HAS_QUANTITY&quot;)) == 0)
//          DataTopLevel.Visible = false;
      }
      else if (((Int32)Report.GetColumnValue(&quot;LV.BOQ_LINE_TYPE_FK&quot;)) &gt;= 1 &amp;&amp; 
      ((Int32)Report.GetColumnValue(&quot;LV.BOQ_LINE_TYPE_FK&quot;)) &lt;= 8 &amp;&amp; ((String)Report.GetColumnValue(&quot;LV.PRINT_MARK&quot;)) == &quot;ESUM&quot;) {
        DataBottumLevel.Visible = true;                                                                                          
//        if (!((Boolean)Report.GetParameterValue(&quot;NullTitelDrucken&quot;)) &amp;&amp; ((Int32)Report.GetColumnValue(&quot;LV.HAS_QUANTITY&quot;)) == 0)
//          DataBottumLevel.Visible = false;
      }
      else if (((String)Report.GetColumnValue(&quot;LV.PRINT_MARK&quot;)) == &quot;LVSUM&quot;) {
        DataBottumLevel.Visible = true;                                      
//        if (!((Boolean)Report.GetParameterValue(&quot;NullTitelDrucken&quot;)) &amp;&amp; ((Int32)Report.GetColumnValue(&quot;LV.HAS_QUANTITY&quot;)) == 0)
//          DataBottumLevel.Visible = false;
      }
      else if (((String)Report.GetColumnValue(&quot;LV.PRINT_MARK&quot;)) == &quot;POSITION&quot;) {
        DataSpace.Visible = ((Boolean)Report.GetParameterValue(&quot;NullMengenZeilenDrucken&quot;)) || ((Decimal)Report.GetColumnValue(&quot;LV.QUANTITY&quot;)) &gt; 0;
        DataLVPosition.Visible = DataSpace.Visible;                                          
      }
    }

    private void RichSPECIFICATION_FORMATTED_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichSPECIFICATION_FORMATTED, &quot;LV.SPECIFICATION_FORMATTED&quot;);
    }

    private void RichTextOben_AfterData(object sender, EventArgs e)
    {
      DataTextOben.Visible = GetRtfText(ref RichTextOben, &quot;Text.PRE_TEXT&quot;);
    }


    private void DataAufmass_BeforePrint(object sender, EventArgs e)
    {
      DataAufmassInfoZeile.Visible = false; 
      if (((Int32)Report.GetColumnValue(&quot;Aufmass.ZEILENTYP&quot;)) == 2)
          DataAufmassInfoZeile.Visible = true;                                                                
    }

    private void RichMatPosText_AfterData(object sender, EventArgs e)
    {
      ChildMatPositionRichText.Visible = GetRtfText(ref RichMatPosText, &quot;Positionen.BLOB_SPECIFICATION&quot;);
    }

    private void DataMatPosition_BeforePrint(object sender, EventArgs e)
    {
    }

    private void RichUst2_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichUst2, &quot;Text.USTTEXT&quot;);
    }

    private void ChildPageUebersicht_BeforePrint(object sender, EventArgs e)
    {
      Report.Engine.StartNewPage();      
    }

    private void DataAufmassZeile_BeforePrint(object sender, EventArgs e)
    {
//      DataAufmassZeile.Visible = false;
//      if (((Decimal)Report.GetColumnValue(&quot;LvMitAufmass.RESULT&quot;)) != 0)
//        DataAufmassZeile.Visible = true;
    }

    private void GrphGesamtLV_BeforePrint(object sender, EventArgs e)
    {
      //GrphGesamtLV.Visible = false;
      //GrphGesamtLV2.Visible = false;
      //DataGesamtLvAufmassZeile.Visible = false;
      //GrphGesamtLVSumme.Visible = false;
      
      if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertMitAufmass.MMEMGE&quot;)) == 0)
        return;
      if (((Int32)Report.GetColumnValue(&quot;LvKumuliertMitAufmass.ANZAHL&quot;)) == 0)
        return;

//      if (((Int32)Report.GetColumnValue(&quot;LvKumuliertMitAufmass.BOQ_LINE_TYPE_FK&quot;)) == 0) {
//        GrphGesamtLV2.Visible = true;                                                               
//        DataGesamtLvAufmassZeile.Visible = true;
//        GrphGesamtLVSumme.Visible = true;
//      }
    }

    private void DataLvKumLevel_BeforePrint(object sender, EventArgs e)
    {
      DataLvKumLevel.Visible = false;
      DataLvKumPosition.Visible = false;
      DataLvKumSumme.Visible = false;
      DataLvKumSummeSpace.Visible = false;
      
      DataZuschlagNachlassProzentLVPos.Visible = false;
      DataZuschlagNachlassProzentLV.Visible = false;
      DataZuschlagNachlassAbsolutLV.Visible = false;

      if (((Int32)Report.GetColumnValue(&quot;LvKumuliert.BOQ_LINE_TYPE_FK&quot;)) &gt;= 1 &amp;&amp; 
          ((Int32)Report.GetColumnValue(&quot;LvKumuliert.BOQ_LINE_TYPE_FK&quot;)) &lt;= 8 &amp;&amp; 
          ((String)Report.GetColumnValue(&quot;LvKumuliert.PRINT_MARK&quot;)) == &quot;LEVEL&quot;) {
        DataLvKumLevel.Visible = true;
        
      } else if (((Int32)Report.GetColumnValue(&quot;LvKumuliert.BOQ_LINE_TYPE_FK&quot;)) == 0) {
        DataLvKumPosition.Visible = ((Boolean)Report.GetParameterValue(&quot;NullMengenZeilenDrucken&quot;)) || ((Decimal)Report.GetColumnValue(&quot;LvKumuliert.G_QUANTITY&quot;)) &gt; 0;   
        if (DataLvKumPosition.Visible) {
          if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_POS&quot;)) &gt; 0) {
            DataZuschlagNachlassProzentLVPos.Visible = true;
            TxtProzentTextLV.Text = &quot;inkl. Zuschlag EP&quot;;
          } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_POS&quot;)) &lt; 0) {
            DataZuschlagNachlassProzentLVPos.Visible = true;
            TxtProzentTextLV.Text = &quot;inkl. Nachlass EP&quot;;
          }
        }
        
      } else if (((String)Report.GetColumnValue(&quot;LvKumuliert.PRINT_MARK&quot;)) == &quot;ESUM&quot;) {
        DataLvKumSumme.Visible = true;
        DataLvKumSummeSpace.Visible = true;
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_EBENE&quot;)) &gt; 0) {
          DataZuschlagNachlassProzentLV.Visible = true;
          TxtProzentTextLVEbene.Text = &quot;inkl. Zuschlag&quot;;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_EBENE&quot;)) &lt; 0) {
          DataZuschlagNachlassProzentLV.Visible = true;
          TxtProzentTextLVEbene.Text = &quot;inkl. Nachlass&quot;;
        }
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_ABS_EBENE&quot;)) &gt; 0) {
          DataZuschlagNachlassAbsolutLV.Visible = true;
          TxtAbsolutTextLVEbene.Text = &quot;inkl. Zuschlag&quot;;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_ABS_EBENE&quot;)) &lt; 0) {
          DataZuschlagNachlassAbsolutLV.Visible = true;
          TxtAbsolutTextLVEbene.Text = &quot;inkl. Nachlass&quot;;
        }
        
      } else if (((String)Report.GetColumnValue(&quot;LvKumuliert.PRINT_MARK&quot;)) == &quot;LVSUM&quot;) {
        DataLvKumSumme.Visible = true;        // LVSUMMARY
        DataLvKumSummeSpace.Visible = true;
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_EBENE&quot;)) &gt; 0) {
          DataZuschlagNachlassProzentLV.Visible = true;
          TxtProzentTextLVEbene.Text = &quot;inkl. Zuschlag&quot;;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliert.DISCOUNT_PER_EBENE&quot;)) &lt; 0) {
          DataZuschlagNachlassProzentLV.Visible = true;
          TxtProzentTextLVEbene.Text = &quot;inkl. Nachlass&quot;;
        }
      }
    }

    private void RichSPECIFICATION_FORMATTED2_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichSPECIFICATION_FORMATTED2, &quot;LvKumuliert.SPECIFICATION_FORMATTED&quot;);
    }

    private void GhLvKum_BeforePrint(object sender, EventArgs e)
    {
      GhLvKum.Visible = true;
      
//      if (BOQ_HEADER_FK != ((Int64)Report.GetColumnValue(&quot;LvKumuliert.PRJ_BOQ_HEADER_FK&quot;))) {
//        AdDebugText(&quot;GhLvKum_BeforePrint&quot;, &quot;if (BOQ_HEADER_FK != ...&quot;);
//        TxtLvKumLvNo.Text = ((String)Report.GetColumnValue(&quot;LvKumuliert.REFERENCE&quot;));
//        TxtLvKumLvBez.Text = ((String)Report.GetColumnValue(&quot;LvKumuliert.BRIEF&quot;));    
//        TxtLvKumLvNo2.Text = ((String)Report.GetColumnValue(&quot;LvKumuliert.REFERENCE&quot;));
//        TxtLvKumLvBez2.Text = ((String)Report.GetColumnValue(&quot;LvKumuliert.BRIEF&quot;));    
//        BOQ_HEADER_FK = ((Int64)Report.GetColumnValue(&quot;LvKumuliert.PRJ_BOQ_HEADER_FK&quot;));
//      }
    }

    private void DataHeaderKumSumLevelTop_BeforePrint(object sender, EventArgs e)
    {
      //AdDebugText(&quot;DataHeaderKumSumLevelTop&quot;, &quot;&quot;);
      DataHeaderKumSumLevelTop.Visible = false;
      DataHeaderKumSumLevel.Visible = false;
      DataHeaderKumSumSumme.Visible = false;
      DataZuschlagNachlassAbsolut.Visible = false;
      DataZuschlagNachlassProzent.Visible = false;
      
      DataZuschlagNachlassProzentLvSum.Visible = false;
      
      if (((Int32)Report.GetColumnValue(&quot;LvKumuliertSummen.BOQ_LINE_TYPE_FK&quot;)) == 1 &amp;&amp; 
          ((String)Report.GetColumnValue(&quot;LvKumuliertSummen.PRINT_MARK&quot;)) == &quot;LEVEL&quot;) {
        //AdDebugText(&quot;DataHeaderKumSumLevelTop_BeforePrint&quot;, &quot;LEVEL == 1&quot;);
        DataHeaderKumSumLevelTop.Visible = true;
        
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) &gt; 0) {
          TxtProzentText.Text = &quot;inkl. Zuschlag&quot;;
          DataZuschlagNachlassProzent.Visible = true;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) &lt; 0) {
          TxtProzentText.Text = &quot;inkl. Nachlass&quot;;
          DataZuschlagNachlassProzent.Visible = true;
        }
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_ABS_EBENE&quot;)) &gt; 0) {
          TxtAbsolutText.Text = &quot;inkl. Zuschlag&quot;;
          DataZuschlagNachlassAbsolut.Visible = true;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_ABS_EBENE&quot;)) &lt; 0) {
          TxtAbsolutText.Text = &quot;inkl. Nachlass&quot;;
          DataZuschlagNachlassAbsolut.Visible = true;
        }
      } else if (((Int32)Report.GetColumnValue(&quot;LvKumuliertSummen.BOQ_LINE_TYPE_FK&quot;)) &gt; 1 &amp;&amp; 
               ((Int32)Report.GetColumnValue(&quot;LvKumuliertSummen.BOQ_LINE_TYPE_FK&quot;)) != 103 &amp;&amp; 
               ((String)Report.GetColumnValue(&quot;LvKumuliertSummen.PRINT_MARK&quot;)) == &quot;LEVEL&quot;) {
        
        DataHeaderKumSumLevel.Visible = true;
        
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) &gt; 0) {
          TxtProzentText.Text = &quot;inkl. Zuschlag&quot;;
          DataZuschlagNachlassProzent.Visible = true;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) &lt; 0) {
          TxtProzentText.Text = &quot;inkl. Nachlass&quot;;
          DataZuschlagNachlassProzent.Visible = true;
        }
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_ABS_EBENE&quot;)) &gt; 0) {
          TxtAbsolutText.Text = &quot;inkl. Zuschlag&quot;;
          DataZuschlagNachlassAbsolut.Visible = true;
        } else if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_ABS_EBENE&quot;)) &lt; 0) {
          
          AdDebugText(&quot;(LvKumuliertSummen.DISCOUNT_ABS_EBENE)) &lt; 0&quot;, ((String)Report.GetColumnValue(&quot;LvKumuliertSummen.REFERENCE&quot;)));
          
          TxtAbsolutText.Text = &quot;inkl. Nachlass&quot;;
          DataZuschlagNachlassAbsolut.Visible = true;
        }
        
      } else if (((String)Report.GetColumnValue(&quot;LvKumuliertSummen.PRINT_MARK&quot;)) == &quot;LVSUM&quot;) {
        // LVSUM Ebene
        DataHeaderKumSumSumme.Visible = true;
          // Wenn es einen Prozentwert gibt
        if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) != 0) {
          DataZuschlagNachlassProzentLvSum.Visible = true;
          if (((Decimal)Report.GetColumnValue(&quot;LvKumuliertSummen.DISCOUNT_PER_EBENE&quot;)) &gt; 0) {
            TxtProzentTextLvSum.Text = &quot;inkl. Zuschlag&quot;;
          } else {
            TxtProzentTextLvSum.Text = &quot;inkl. Nachlass&quot;;
          }
        }
      }
    }

    private void DataKumuliertAufmass_BeforePrint(object sender, EventArgs e)
    {
//      AdDebugText(&quot;LvKumuliert&quot;, GetDataCount(&quot;LvKumuliert&quot;).ToString());
//      AdDebugText(&quot;LvKumuliertSummen&quot;, GetDataCount(&quot;LvKumuliertSummen&quot;).ToString());
//      AdDebugText(&quot;LvKumuliertMitAufmass&quot;, GetDataCount(&quot;LvKumuliertMitAufmass&quot;).ToString());
      
      if (KumuliertAufmassDrucken &amp;&amp; GetDataCount(&quot;LvKumuliertMitAufmass&quot;) &gt; 0 &amp;&amp; GetDataCount(&quot;LvKumuliert&quot;) &gt; 0 &amp;&amp; (KumuliertLvDrucken || KumuliertLvSummenDrucken))
          Report.Engine.StartNewPage();      
    }


    private void DataLVHeaderZusammenfassung_BeforePrint(object sender, EventArgs e)
    {
      if (ZuwachsLvSummenDrucken &amp;&amp; GetDataCount(&quot;LvZusammenfassung&quot;) &gt; 0 &amp;&amp; ZuwachsLvDrucken)
        Report.Engine.StartNewPage();      
    }

    private void DataLVHeaderAufmass_BeforePrint(object sender, EventArgs e)
    {
      if (ZuwachsAufmassDrucken &amp;&amp; GetDataCount(&quot;Aufmass&quot;) &gt; 0 &amp;&amp; (ZuwachsLvDrucken || ZuwachsLvSummenDrucken))
        Report.Engine.StartNewPage();      
    }

    private void DataGesamtLvAufmassZeileTyp1_BeforePrint(object sender, EventArgs e)
    {
      DataGesamtLvAufmassZeileTyp1.Visible = ((Int32)Report.GetColumnValue(&quot;LvKumuliertMitAufmass.QTO_LINETYPE_FK&quot;)) != 2;
      DataGesamtLvAufmassZeileTyp2.Visible = ((Int32)Report.GetColumnValue(&quot;LvKumuliertMitAufmass.QTO_LINETYPE_FK&quot;)) == 2;
    }

    private void DataSummaryBold_BeforePrint(object sender, EventArgs e)
    {
      DataSummaryBold.Visible = ((Int32)Report.GetColumnValue(&quot;LvZusammenfassung.BOQ_LINE_TYPE_FK&quot;)) == 1 &amp;&amp; ((String)Report.GetColumnValue(&quot;LvZusammenfassung.PRINT_MARK&quot;)) == &quot;LEVEL&quot;; 
      DataSummaryNormal.Visible = ((Int32)Report.GetColumnValue(&quot;LvZusammenfassung.BOQ_LINE_TYPE_FK&quot;)) &gt; 1 &amp;&amp; 
                                   ((Int32)Report.GetColumnValue(&quot;LvZusammenfassung.BOQ_LINE_TYPE_FK&quot;)) &lt;= 8 &amp;&amp; 
                                   ((String)Report.GetColumnValue(&quot;LvZusammenfassung.PRINT_MARK&quot;)) == &quot;LEVEL&quot;;
//      if (!((Boolean)Report.GetParameterValue(&quot;NullTitelDrucken&quot;)) &amp;&amp; ((Int32)Report.GetColumnValue(&quot;LvZusammenfassung.HAS_QUANTITY&quot;)) == 0) {
//        DataSummaryBold.Visible = false;
//        DataSummaryNormal.Visible = false;
//      }
    }


    private void DataKumuliertLv_BeforePrint(object sender, EventArgs e)
    {
      //gibt eine Seite aus, warum, wenn kumuliertlvdrucken eingeschaltet ist.
      
      if (KumuliertLvSummenDrucken &amp;&amp; GetDataCount(&quot;LvKumuliertSummen&quot;) &gt; 0 &amp;&amp; KumuliertLvDrucken) {
        //AdDebugText(&quot;DataKumuliertLv_BeforePrint&quot;, &quot;Report.Engine.StartNewPage()&quot;);
        Report.Engine.StartNewPage();                                                               
      }
    }

    private void DataGeneralienBand_BeforePrint(object sender, EventArgs e)
    {
      if (((Boolean)Report.GetColumnValue(&quot;Generalien.ISPERCENT&quot;)))
        TxtGeneralienWert.Text = FormatNumber(((Decimal)Report.GetColumnValue(&quot;Generalien.WERT&quot;)), 2) + &quot; %&quot;;
      else
        TxtGeneralienWert.Text = FormatNumber(((Decimal)Report.GetColumnValue(&quot;Generalien.WERT&quot;)), 2);
    }

    private void DataFooter2_AfterPrint(object sender, EventArgs e)
    {
      if (Test) {
        if (((Boolean)Report.GetParameterValue(&quot;DruckeInhaltsverzeichnis&quot;)) &amp;&amp; KumuliertLvDrucken)
          Report.Engine.StartNewPage();                                                               
      } else {
        if (((Boolean)Report.GetParameterValue(&quot;DruckeInhaltsverzeichnis&quot;)) &amp;&amp; GetDataCount(&quot;LvKumuliertInhaltsverzeichnis&quot;) &gt; 1 &amp;&amp; KumuliertLvDrucken)
          Report.Engine.StartNewPage();                                                               
      }
    }

    private void _FinishReport(object sender, EventArgs e)
    {
      KumuliertLvFirstPrint = true;
    }

    private void DataKumuliertLvSummen_BeforePrint(object sender, EventArgs e)
    {
      if (KumuliertLvFirstPrint) {
        KumuliertLvFirstPrint = false;
        return;
      }
      if (((Boolean)Report.GetParameterValue(&quot;KumuliertLvSummenDrucken&quot;)) &amp;&amp; GetDataCount(&quot;LvKumuliertSummen&quot;) &gt; 1 &amp;&amp; KumuliertLvDrucken) 
        Report.Engine.StartNewPage();                                                                                                      
    }

    private void PageRechnung_StartPage(object sender, EventArgs e)
    {
      KumuliertLvFirstPrint = true;
      
      // Anschrift erstellen
      TxtReceiver.Text = CreateReceiverAddress();
    }

    private void RichLetterFooterLeft_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichLetterFooterLeft, &quot;Company.LETTERFOOTERLEFT&quot;);
    }

    private void RichLetterFooterCenter_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichLetterFooterCenter, &quot;Company.LETTERFOOTERCENTER&quot;);
    }

    private void RichLetterFooterRight_AfterData(object sender, EventArgs e)
    {
      GetRtfText(ref RichLetterFooterRight, &quot;Company.LETTERFOOTERRIGHT&quot;);
    }

    private void RichTextUnten_AfterData(object sender, EventArgs e)
    {
      DataTextUnten.Visible = GetRtfText(ref RichTextUnten, &quot;Text.POST_TEXT&quot;);
    }

    private void DataUebersicht_BeforePrint(object sender, EventArgs e)
    {
      Report.Engine.StartNewPage();                                                                                                      
    }

    private void DataKumuliertHeader_BeforePrint(object sender, EventArgs e)
    {
      if (DataKumuliertHeader.Visible)
        Report.Engine.StartNewPage();                                                                                                      
    }

    private void DataZuwachsHeader_BeforePrint(object sender, EventArgs e)
    {
      if (DataZuwachsHeader.Visible)
        Report.Engine.StartNewPage();                                                                                                      
    }

    private void DataPositionHeader_BeforePrint(object sender, EventArgs e)
    {
      if (GetDataCount(&quot;Positionen&quot;) &gt; 1)
        Report.Engine.StartNewPage();                                                                                                      
    }

    private void ChildMatPositionPlainText_BeforePrint(object sender, EventArgs e)
    {
      ChildMatPositionPlainText.Visible = ((String)Report.GetColumnValue(&quot;Positionen.SPECIFICATION&quot;)).Trim() != &quot;&quot;;
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqDu5C5bj2Eklj4gsb6cv3Qi5QTptfbvlJ/3KgTHR+UP15nw+AMz7KgDkoCSBYah287b9fP/DcwOZ/JMJn1vmkdWpMBFO5pEdoLig+5/dKv/jMcZSvTB76BJ3hOKjzS6f5jAtZ/6NWHPaGONrvmdZcHe+hkpnQiAcU0IFmu1Bd6ZOAzxjp4NnqJvkagfhFmiksedmNHX5iaUymf5rzK6Xx5irMdOmIKYdFgwkN6f9JbfCHtI7X4LjhlEuG/8Gbrk48T8BsRRVr9Fqp31XzEmGoszKVWUKpJXSKIdI3mchwYV5LODPCW3GZjQHPire5DBSH">
      <TableDataSource Name="Table" Alias="Company" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @CompanyID BIGINT = 1000005;&#13;&#10;--DECLARE @TextTypeIdLetterFooterLeft BIGINT = 36; &#13;&#10;--DECLARE @TextTypeIdLetterFooterCenter BIGINT = 37; &#13;&#10;--DECLARE @TextTypeIdLetterFooterRight BIGINT = 38; &#13;&#10;&#13;&#10;IF (SELECT BAS_COMPANY_TYPE_FK FROM BAS_COMPANY WHERE ID = @CompanyID) = 3&#13;&#10;BEGIN&#13;&#10;	SELECT @CompanyID = BAS_COMPANY_FK FROM BAS_COMPANY WHERE ID = @CompanyID;&#13;&#10;END&#13;&#10;&#13;&#10;SELECT &#13;&#10;C.ID ID&#13;&#10;,C.CODE SCHLUESSEL&#13;&#10;,C.COMPANY_NAME NAME&#13;&#10;,C.COMPANY_NAME2 NAME2&#13;&#10;,C.COMPANY_NAME3 NAME3&#13;&#10;,C.INTERNET INTERNET&#13;&#10;,C.EMAIL EMAIL&#13;&#10;,C.TAXNO&#13;&#10;,B.CONTENT LOGO&#13;&#10;,A.STREET STRASSE&#13;&#10;,A.CITY ORT&#13;&#10;,A.ZIPCODE PLZ &#13;&#10;,COU.DESCRIPTION COUNTRY&#13;&#10;,COU.DESCRIPTION_TR COUNTRY_TR&#13;&#10;,TEL.TELEPHONE TELEFON&#13;&#10;,FAX.TELEPHONE FAX&#13;&#10;,BP.TRADE_REGISTER HANDELSREGISTER_GERICHT&#13;&#10;,BP.TRADE_REGISTERNO HANDELSREGISTER_NUMMER&#13;&#10;,BP.TAXNO STEUERNUMMER&#13;&#10;,BP.VATNO UMSATZSTEUERID&#13;&#10;,(SELECT (SELECT TB.CONTENT &#13;&#10;	FROM BAS_COMPANY2TEXT_MODULE CT&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE TM ON TM.ID = CT.BAS_TEXT_MODULE_FK AND CT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE_TEXT TMT ON TM.ID = TMT.BAS_TEXT_MODULE_FK AND TMT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_BLOBS TB ON TB.ID = TMT.BAS_BLOBS_FK&#13;&#10;	WHERE CT.BAS_COMPANY_FK = C.ID AND CT.BAS_LANGUAGE_FK = 2 AND CT.BAS_TEXT_MODULE_TYPE_FK = @TextTypeIdLetterFooterLeft)) LETTERFOOTERLEFT&#13;&#10;,(SELECT (SELECT TB.CONTENT &#13;&#10;	FROM BAS_COMPANY2TEXT_MODULE CT&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE TM ON TM.ID = CT.BAS_TEXT_MODULE_FK AND CT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE_TEXT TMT ON TM.ID = TMT.BAS_TEXT_MODULE_FK AND TMT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_BLOBS TB ON TB.ID = TMT.BAS_BLOBS_FK&#13;&#10;	WHERE CT.BAS_COMPANY_FK = C.ID AND CT.BAS_LANGUAGE_FK = 2 AND CT.BAS_TEXT_MODULE_TYPE_FK = @TextTypeIdLetterFooterCenter)) LETTERFOOTERCENTER&#13;&#10;,(SELECT (SELECT TB.CONTENT &#13;&#10;	FROM BAS_COMPANY2TEXT_MODULE CT&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE TM ON TM.ID = CT.BAS_TEXT_MODULE_FK AND CT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_TEXT_MODULE_TEXT TMT ON TM.ID = TMT.BAS_TEXT_MODULE_FK AND TMT.BAS_LANGUAGE_FK = TM.BAS_LANGUAGE_FK&#13;&#10;	LEFT JOIN BAS_BLOBS TB ON TB.ID = TMT.BAS_BLOBS_FK&#13;&#10;	WHERE CT.BAS_COMPANY_FK = C.ID AND CT.BAS_LANGUAGE_FK = 2 AND CT.BAS_TEXT_MODULE_TYPE_FK = @TextTypeIdLetterFooterRight)) LETTERFOOTERRIGHT&#13;&#10;,CONCAT(C.COMPANY_NAME, ' - ', A.STREET, ' - ', A.ZIPCODE, ' ', A.CITY) ADRESSZEILE&#13;&#10;,LETTER_H.CONTENT LETTERHEADER&#13;&#10;FROM BAS_COMPANY C&#13;&#10;LEFT JOIN BAS_ADDRESS A ON C.BAS_ADDRESS_FK = A.ID &#13;&#10;LEFT JOIN BAS_COUNTRY COU ON COU.ID = A.BAS_COUNTRY_FK&#13;&#10;LEFT JOIN BAS_BLOBS B  ON B.ID = C.BAS_BLOBS_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER TEL ON TEL.ID = C.BAS_TELEPHONE_NUMBER_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER FAX ON FAX.ID = C.BAS_TELEPHONE_TELEFAX_FK&#13;&#10;LEFT JOIN BPD_BUSINESSPARTNER BP ON BP.ID = C.BPD_BUSINESSPARTNER_FK&#13;&#10;LEFT JOIN BAS_BLOBS LETTER_H ON LETTER_H.ID = C.BAS_BLOBSLETTERHEADER_FK&#13;&#10;WHERE C.ID = @CompanyID&#13;&#10;">
        <Column Name="TAXNO" DataType="System.String"/>
        <Column Name="HANDELSREGISTER_GERICHT" DataType="System.String"/>
        <Column Name="HANDELSREGISTER_NUMMER" DataType="System.String"/>
        <Column Name="STEUERNUMMER" DataType="System.String"/>
        <Column Name="UMSATZSTEUERID" DataType="System.String"/>
        <Column Name="SCHLUESSEL" DataType="System.String"/>
        <Column Name="NAME2" DataType="System.String"/>
        <Column Name="NAME3" DataType="System.String"/>
        <Column Name="INTERNET" DataType="System.String"/>
        <Column Name="EMAIL" DataType="System.String"/>
        <Column Name="LOGO" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="STRASSE" DataType="System.String"/>
        <Column Name="ORT" DataType="System.String"/>
        <Column Name="PLZ" DataType="System.String"/>
        <Column Name="COUNTRY" DataType="System.String"/>
        <Column Name="COUNTRY_TR" DataType="System.Int32"/>
        <Column Name="TELEFON" DataType="System.String"/>
        <Column Name="FAX" DataType="System.String"/>
        <Column Name="LETTERFOOTERLEFT" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="LETTERFOOTERCENTER" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="LETTERFOOTERRIGHT" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="ADRESSZEILE" DataType="System.String"/>
        <Column Name="LETTERHEADER" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="ID" DataType="System.Int32"/>
        <Column Name="NAME" DataType="System.String"/>
        <CommandParameter Name="CompanyID" DataType="8" Expression="[CompanyID]" DefaultValue="1000005"/>
        <CommandParameter Name="TextTypeIdLetterFooterLeft" Expression="[TextTypeIdLetterFooterLeft]" DefaultValue="36"/>
        <CommandParameter Name="TextTypeIdLetterFooterCenter" Expression="[TextTypeIdLetterFooterCenter]" DefaultValue="37"/>
        <CommandParameter Name="TextTypeIdLetterFooterRight" Expression="[TextTypeIdLetterFooterRight]" DefaultValue="38"/>
      </TableDataSource>
      <TableDataSource Name="Table1" Alias="UserClerk" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="SELECT &#13;&#10;-- STANDARDFELDER&#13;&#10;C.ID			USERCLERK_ID&#13;&#10;,C.DESCRIPTION		USERCLERK_BEZEICHNUNG&#13;&#10;,C.FIRST_NAME		USERCLERK_VORNAME&#13;&#10;,C.FAMILY_NAME		USERCLERK_NACHNAME&#13;&#10;,C.SIGNATURE		USERCLERK_SIGNATUR&#13;&#10;,C.TITLE			USERCLERK_TITEL&#13;&#10;,C.DEPARTMENT		USERCLERK_ABTEILUNG&#13;&#10;,C.EMAIL			USERCLERK_EMAIL&#13;&#10;,C.USERDEFINED1		USERCLERK_TEXT1&#13;&#10;,C.USERDEFINED2		USERCLERK_TEXT2&#13;&#10;,C.USERDEFINED3		USERCLERK_TEXT3&#13;&#10;,C.USERDEFINED4		USERCLERK_TEXT4&#13;&#10;,C.USERDEFINED5		USERCLERK_TEXT5&#13;&#10;,COALESCE(ANREDET.DESCRIPTION, ANREDE.DESCRIPTION) USERCLERK_ANREDE&#13;&#10;,COALESCE(BRIEFANREDET.DESCRIPTION, BRIEFANREDE.DESCRIPTION) USERCLERK_BRIEFANREDE&#13;&#10;,COALESCE(GRUSSFORMT.DESCRIPTION, GRUSSFORM.DESCRIPTION) USERCLERK_GRUSSFORM&#13;&#10;-- PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) USERCLERK_VOLLERNAME&#13;&#10;-- i. A. PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN C.SIGNATURE IS NOT NULL AND LTRIM(C.SIGNATURE) &lt;&gt; '' THEN CONCAT(C.SIGNATURE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) USERCLERK_UNTERZEICHNUNG&#13;&#10;-- HERR PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(ANREDET.DESCRIPTION,ANREDE.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(ANREDET.DESCRIPTION,ANREDE.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) USERCLERK_VOLLERNAMEMITANREDE&#13;&#10;-- HERRN PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(BRIEFANREDET.DESCRIPTION,BRIEFANREDE.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(BRIEFANREDET.DESCRIPTION,BRIEFANREDE.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) USERCLERK_VOLLERNAMEMITBRIEFANREDE&#13;&#10;-- SEHR GEEHRTER HERR PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(GRUSSFORMT.DESCRIPTION,GRUSSFORM.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(GRUSSFORMT.DESCRIPTION,GRUSSFORM.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) USERCLERK_VOLLEGRUSSANREDE&#13;&#10;-- Telefon&#13;&#10;,TEL.TELEPHONE USERCLERK_TELEFON&#13;&#10;-- Mobil&#13;&#10;,MOB.TELEPHONE USERCLERK_MOBILE&#13;&#10;-- Fax&#13;&#10;,FAX.TELEPHONE USERCLERK_FAX&#13;&#10;FROM BAS_CLERK C&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER TEL ON TEL.ID = C.BAS_TELEPHONE_NUMBER_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER MOB ON MOB.ID = C.BAS_TELEPHONE_MOBILE_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER FAX ON FAX.ID = C.BAS_TELEPHONE_TELEFAX_FK&#13;&#10;-- ANREDE HERR&#13;&#10;LEFT JOIN BAS_TITLE ANREDE ON ANREDE.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION ANREDET ON ANREDET.ID = ANREDE.DESCRIPTION_TR AND ANREDET.BAS_LANGUAGE_FK = 2&#13;&#10;-- BRIEFANREDE HERRN&#13;&#10;LEFT JOIN BAS_TITLE BRIEFANREDE ON BRIEFANREDE.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION BRIEFANREDET ON BRIEFANREDET.ID = BRIEFANREDE.ADDRESS_TITLE_TR AND BRIEFANREDET.BAS_LANGUAGE_FK = 2&#13;&#10;-- GRUßFORMEL SEHR GEEHRTER HERR&#13;&#10;LEFT JOIN BAS_TITLE GRUSSFORM ON GRUSSFORM.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION GRUSSFORMT ON GRUSSFORMT.ID = GRUSSFORM.SALUTATION_TR AND GRUSSFORMT.BAS_LANGUAGE_FK = 2&#13;&#10;WHERE C.FRM_USER_FK = @UserID&#13;&#10;&#13;&#10;">
        <Column Name="USERCLERK_EMAIL" DataType="System.String"/>
        <Column Name="USERCLERK_MOBILE" DataType="System.String"/>
        <Column Name="USERCLERK_BEZEICHNUNG" DataType="System.String"/>
        <Column Name="USERCLERK_NACHNAME" DataType="System.String"/>
        <Column Name="USERCLERK_VORNAME" DataType="System.String"/>
        <Column Name="USERCLERK_SIGNATUR" DataType="System.String"/>
        <Column Name="USERCLERK_ABTEILUNG" DataType="System.String"/>
        <Column Name="USERCLERK_TEXT1" DataType="System.String"/>
        <Column Name="USERCLERK_TEXT2" DataType="System.String"/>
        <Column Name="USERCLERK_TEXT3" DataType="System.String"/>
        <Column Name="USERCLERK_TEXT4" DataType="System.String"/>
        <Column Name="USERCLERK_TEXT5" DataType="System.String"/>
        <Column Name="USERCLERK_ANREDE" DataType="System.String"/>
        <Column Name="USERCLERK_BRIEFANREDE" DataType="System.String"/>
        <Column Name="USERCLERK_GRUSSFORM" DataType="System.String"/>
        <Column Name="USERCLERK_VOLLERNAME" DataType="System.String"/>
        <Column Name="USERCLERK_VOLLERNAMEMITANREDE" DataType="System.String"/>
        <Column Name="USERCLERK_VOLLERNAMEMITBRIEFANREDE" DataType="System.String"/>
        <Column Name="USERCLERK_VOLLEGRUSSANREDE" DataType="System.String"/>
        <Column Name="USERCLERK_FAX" DataType="System.String"/>
        <Column Name="USERCLERK_TITEL" DataType="System.String"/>
        <Column Name="USERCLERK_UNTERZEICHNUNG" DataType="System.String"/>
        <Column Name="USERCLERK_ID" DataType="System.Int32"/>
        <Column Name="USERCLERK_TELEFON" DataType="System.String"/>
        <CommandParameter Name="UserID" Expression="[UserID]" DefaultValue="153"/>
      </TableDataSource>
      <TableDataSource Name="Table3" Alias="Text" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @BILID BIGINT = 1000015&#13;&#10;--DECLARE @TextModuleTypeIdPre BIGINT = 1000003;&#13;&#10;--DECLARE @TextModuleTypeIdPost BIGINT = 1000003;&#13;&#10;DECLARE @PRE_TEXT VARBINARY(MAX) = NULL;&#13;&#10;DECLARE @POST_TEXT VARBINARY(MAX) = NULL;&#13;&#10;&#13;&#10;SELECT &#13;&#10;@PRE_TEXT = H.CONTENT&#13;&#10;FROM SLS_HEADERBLOB H &#13;&#10;WHERE H.BIL_HEADER_FK = @BILID AND H.BAS_TEXT_MODULE_TYPE_FK = @TextModuleTypeIdPre&#13;&#10;&#13;&#10;SELECT &#13;&#10;@POST_TEXT = H.CONTENT&#13;&#10;FROM SLS_HEADERBLOB H &#13;&#10;WHERE H.BIL_HEADER_FK = @BILID AND H.BAS_TEXT_MODULE_TYPE_FK = @TextModuleTypeIdPost&#13;&#10;&#13;&#10;SELECT @PRE_TEXT PRE_TEXT, @POST_TEXT POST_TEXT&#13;&#10;">
        <Column Name="PRE_TEXT" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="POST_TEXT" DataType="System.Byte[]" BindableControl="Picture"/>
        <CommandParameter Name="BILID" Expression="[BIL_HEADER_FK]" DefaultValue="1000023"/>
        <CommandParameter Name="TextModuleTypeIdPre" Expression="[TextModuleTypeIdPretext]" DefaultValue="1000003"/>
        <CommandParameter Name="TextModuleTypeIdPost" Expression="[TextModuleTypeIdPosttext]" DefaultValue="1000005"/>
      </TableDataSource>
      <TableDataSource Name="Table2" Alias="Rechnung" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @MAINID BIGINT = 1016325 ;&#13;&#10;&#13;&#10;SELECT &#13;&#10;-- BILL&#13;&#10;M.BILL_NO RE_NR&#13;&#10;,M.BILL_DATE DATUM&#13;&#10;,M.DATE_POSTED STICHTAG&#13;&#10;,M.PERFORMEDFROM AUSF_VON&#13;&#10;,M.PERFORMEDTO AUSF_BIS&#13;&#10;,M.AMOUNT_NET NETTO&#13;&#10;,M.AMOUNT_GROSS BRUTTO&#13;&#10;,M.AMOUNT_GROSS - M.AMOUNT_NET MWST&#13;&#10;,M.USERDEFINED2 IHRZEICHEN&#13;&#10;,COALESCE(FORMAT(M.PERFORMEDFROM,'dd.MM.yyyy'), '') LEISTUNGSVON&#13;&#10;,COALESCE(FORMAT(M.PERFORMEDTO,'dd.MM.yyyy'), '') LEISTUNGBIS&#13;&#10;,M.BIL_TYPE_FK RECHNUNGSTYP&#13;&#10;,M.DESCRIPTION BEZEICHNUNG&#13;&#10;,M.COMMENT_TEXT RECHUNGSTITEL&#13;&#10;,COALESCE(FORMAT(M.DATE_DISCOUNT,'dd.MM.yyyy'), '') ZAHLBAR_SKONTO&#13;&#10;,COALESCE(FORMAT(M.DATE_NETPAYABLE,'dd.MM.yyyy'), '') ZAHLBAR_NETTO&#13;&#10;,TC.CODE TAX_CODE&#13;&#10;,TC.VATPERCENT MWST_PROZENT&#13;&#10;,CUR.CURRENCY WAEHRUNG&#13;&#10;&#13;&#10;-- Geschäftspartner&#13;&#10;,COALESCE(BPD.BP_NAME1, '') BP_NAME1&#13;&#10;,COALESCE(BPD.BP_NAME2, '') BP_NAME2&#13;&#10;,COALESCE(SUB.DESCRIPTION, '') BP_NL&#13;&#10;,COALESCE(BPD.EMAIL, '') BP_EMAIL&#13;&#10;,COALESCE(ADR.STREET, '') BP_STREET&#13;&#10;,COALESCE(ADR.ZIPCODE, '') BP_ZIPCODE&#13;&#10;,COALESCE(ADR.CITY, '') BP_CITY&#13;&#10;&#13;&#10;-- Regulierer&#13;&#10;,COALESCE(BPD_BIL.BP_NAME1, '') REG_NAME1&#13;&#10;,COALESCE(BPD_BIL.BP_NAME2, '') REG_NAME2&#13;&#10;,COALESCE(SUB_BIL.DESCRIPTION, '') REG_NL&#13;&#10;,COALESCE(BPD_BIL.EMAIL, '') REG_EMAIL&#13;&#10;,COALESCE(ADR_BIL.STREET, '') REG_STREET&#13;&#10;,COALESCE(ADR_BIL.ZIPCODE, '') REG_ZIPCODE&#13;&#10;,COALESCE(ADR_BIL.CITY, '') REG_CITY&#13;&#10;&#13;&#10;-- AUFTRAG&#13;&#10;,OH.CODE AUFTRAGS_NR&#13;&#10;,OH.ORDER_DATE&#13;&#10;-- PROJEKT&#13;&#10;,PRJ.PROJECTNO&#13;&#10;,PRJ.PROJECT_NAME&#13;&#10;,PRJ_ADR.STREET PRJ_STREET&#13;&#10;,PRJ_ADR.ZIPCODE PRJ_ZIPCODE&#13;&#10;,PRJ_ADR.CITY PRJ_CITY&#13;&#10;,PRJ_ADR.SUPPLEMENT PRJ_SUPPLEMENT&#13;&#10;,PRJ.EMAIL PRJ_EMAIL&#13;&#10;-- CLERK FROM BILL&#13;&#10;,C.FIRST_NAME CLERK_FIRSTNAME&#13;&#10;,C.FAMILY_NAME CLERK_FAMILYNAME&#13;&#10;,C.SIGNATURE CLERK_SIGNATURE&#13;&#10;,C.TITLE CLERK_TITLE&#13;&#10;,C.DEPARTMENT CLERK_DEPARTMENT&#13;&#10;,C.EMAIL CLERK_EMAIL&#13;&#10;,TEL.TELEPHONE CLERK_TELEPHONE&#13;&#10;,CONCAT(CASE WHEN C.TITLE IS NOT NULL AND TRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		 CASE WHEN C.FIRST_NAME IS NOT NULL AND TRIM(C.FIRST_NAME) &lt;&gt; '' THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		 CASE WHEN C.FAMILY_NAME IS NOT NULL AND TRIM(C.FAMILY_NAME) &lt;&gt; '' THEN C.FAMILY_NAME ELSE '' END) CLERK_FULLNAME&#13;&#10;&#13;&#10;,CASE WHEN M.COMMENT_TEXT IS NULL THEN COALESCE(PAYT.DESCRIPTION, PAY.PRINT_TEXT) ELSE M.COMMENT_TEXT END ZAHLUNGSTEXT&#13;&#10;,PAY.DISCOUNT_PERCENT SKONTPROZENT&#13;&#10;,PAY.NET_DAYS TAGE_NETTO&#13;&#10;,PAY.DISCOUNT_DAYS TAGE_SKONTO&#13;&#10;,CUST.CODE DEBITOR&#13;&#10;&#13;&#10;FROM BIL_HEADER M&#13;&#10;LEFT JOIN BAS_CURRENCY CUR ON CUR.ID = M.BAS_CURRENCY_FK&#13;&#10;&#13;&#10;LEFT JOIN ORD_HEADER OH ON OH.ID = M.ORD_HEADER_FK&#13;&#10;LEFT JOIN MDC_TAX_CODE TC ON TC.ID = M.MDC_TAXCODE_FK&#13;&#10;&#13;&#10;LEFT JOIN BPD_BUSINESSPARTNER BPD ON BPD.ID = M.BPD_BUSINESSPARTNER_FK&#13;&#10;LEFT JOIN BPD_SUBSIDIARY SUB ON SUB.ID =  M.BPD_SUBSIDIARY_FK &#13;&#10;LEFT JOIN BAS_ADDRESS ADR ON ADR.ID = SUB.BAS_ADDRESS_FK&#13;&#10;-- Rechnungsempfänger&#13;&#10;LEFT JOIN BPD_BUSINESSPARTNER BPD_BIL ON BPD_BIL.ID = M.BPD_BUSINESSPARTNER_BILLTO_FK&#13;&#10;LEFT JOIN BPD_SUBSIDIARY SUB_BIL ON SUB_BIL.ID =  M.BPD_SUBSIDIARY_BILLTO_FK&#13;&#10;LEFT JOIN BAS_ADDRESS ADR_BIL ON ADR_BIL.ID = SUB_BIL.BAS_ADDRESS_FK&#13;&#10;&#13;&#10;LEFT JOIN BAS_PAYMENT_TERM PAY ON PAY.ID = M.BAS_PAYMENT_TERM_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION PAYT ON PAYT.ID = PAY.PRINT_TEXT_TR AND PAYT.BAS_LANGUAGE_FK = 2&#13;&#10;&#13;&#10;LEFT JOIN BAS_CLERK C ON C.ID = M.BAS_CLERK_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER TEL ON TEL.ID = C.BAS_TELEPHONE_NUMBER_FK&#13;&#10;&#13;&#10;LEFT JOIN PRJ_PROJECT PRJ ON PRJ.ID = M.PRJ_PROJECT_FK&#13;&#10;LEFT JOIN BAS_ADDRESS PRJ_ADR ON PRJ_ADR.ID = PRJ.BAS_ADDRESS_FK&#13;&#10;&#13;&#10;LEFT JOIN BPD_CUSTOMER CUST ON CUST.ID = M.BPD_CUSTOMER_FK&#13;&#10;LEFT JOIN ORD_HEADER ORD ON ORD.ID = M.ORD_HEADER_FK&#13;&#10;&#13;&#10;WHERE M.ID = @MAINID">
        <Column Name="RE_NR" DataType="System.String"/>
        <Column Name="DATUM" DataType="System.DateTime"/>
        <Column Name="STICHTAG" DataType="System.DateTime"/>
        <Column Name="AUSF_VON" DataType="System.DateTime"/>
        <Column Name="AUSF_BIS" DataType="System.DateTime"/>
        <Column Name="NETTO" DataType="System.Decimal"/>
        <Column Name="BRUTTO" DataType="System.Decimal"/>
        <Column Name="MWST" DataType="System.Decimal"/>
        <Column Name="IHRZEICHEN" DataType="System.String"/>
        <Column Name="TAX_CODE" DataType="System.String"/>
        <Column Name="MWST_PROZENT" DataType="System.Decimal"/>
        <Column Name="BP_NAME1" DataType="System.String"/>
        <Column Name="BP_NAME2" DataType="System.String"/>
        <Column Name="BP_EMAIL" DataType="System.String"/>
        <Column Name="AUFTRAGS_NR" DataType="System.String"/>
        <Column Name="PROJECTNO" DataType="System.String"/>
        <Column Name="PROJECT_NAME" DataType="System.String"/>
        <Column Name="PRJ_STREET" DataType="System.String"/>
        <Column Name="PRJ_ZIPCODE" DataType="System.String"/>
        <Column Name="PRJ_CITY" DataType="System.String"/>
        <Column Name="PRJ_EMAIL" DataType="System.String"/>
        <Column Name="CLERK_FIRSTNAME" DataType="System.String"/>
        <Column Name="CLERK_FAMILYNAME" DataType="System.String"/>
        <Column Name="CLERK_SIGNATURE" DataType="System.String"/>
        <Column Name="CLERK_TITLE" DataType="System.String"/>
        <Column Name="CLERK_DEPARTMENT" DataType="System.String"/>
        <Column Name="CLERK_EMAIL" DataType="System.String"/>
        <Column Name="CLERK_TELEPHONE" DataType="System.String"/>
        <Column Name="CLERK_FULLNAME" DataType="System.String"/>
        <Column Name="ZAHLUNGSTEXT" DataType="System.String"/>
        <Column Name="LEISTUNGSVON" DataType="System.String"/>
        <Column Name="LEISTUNGBIS" DataType="System.String"/>
        <Column Name="RECHNUNGSTYP" DataType="System.Int32"/>
        <Column Name="BEZEICHNUNG" DataType="System.String"/>
        <Column Name="DEBITOR" DataType="System.String"/>
        <Column Name="RECHUNGSTITEL" DataType="System.String"/>
        <Column Name="ZAHLBAR_SKONTO" DataType="System.String"/>
        <Column Name="ZAHLBAR_NETTO" DataType="System.String"/>
        <Column Name="SKONTPROZENT" DataType="System.Decimal"/>
        <Column Name="TAGE_NETTO" DataType="System.Int32"/>
        <Column Name="TAGE_SKONTO" DataType="System.Int32"/>
        <Column Name="PRJ_SUPPLEMENT" DataType="System.String"/>
        <Column Name="BP_NL" DataType="System.String"/>
        <Column Name="BP_STREET" DataType="System.String"/>
        <Column Name="BP_ZIPCODE" DataType="System.String"/>
        <Column Name="BP_CITY" DataType="System.String"/>
        <Column Name="REG_NAME1" DataType="System.String"/>
        <Column Name="REG_NAME2" DataType="System.String"/>
        <Column Name="REG_NL" DataType="System.String"/>
        <Column Name="REG_EMAIL" DataType="System.String"/>
        <Column Name="REG_STREET" DataType="System.String"/>
        <Column Name="REG_ZIPCODE" DataType="System.String"/>
        <Column Name="REG_CITY" DataType="System.String"/>
        <Column Name="WAEHRUNG" DataType="System.String"/>
        <Column Name="ORDER_DATE" DataType="System.DateTime"/>
        <CommandParameter Name="MAINID" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table5" Alias="RechnungsHistorie" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="WITH CTE AS (&#13;&#10;	SELECT BIL_HEADER_FK, BIL_BEFORE_HEADER_FK, ISSELECTED, CANLOAD, ISSTORNO&#13;&#10;		   FROM BIL_BIL2BIL AS BASE&#13;&#10;				  WHERE BIL_HEADER_FK = @BIL_HEADER_FK&#13;&#10;				  UNION ALL&#13;&#10;	SELECT PARENT.BIL_HEADER_FK, PARENT.BIL_BEFORE_HEADER_FK, PARENT.ISSELECTED, PARENT.CANLOAD, PARENT.ISSTORNO&#13;&#10;		   FROM BIL_BIL2BIL AS PARENT&#13;&#10;		   INNER JOIN CTE&#13;&#10;		   ON PARENT.BIL_HEADER_FK = CTE.BIL_BEFORE_HEADER_FK&#13;&#10;		   WHERE PARENT.BIL_BEFORE_HEADER_FK IS NOT NULL&#13;&#10;)&#13;&#10;SELECT CTE.*&#13;&#10;	,BIL_HEADER.BILL_NO, BIL_HEADER.DESCRIPTION AS BIL_DESCR, BIL_HEADER.DESCRIPTION_TR AS BIL_DESCR_TR, BIL_HEADER.DATE_EFFECTIVE , BIL_HEADER.BILL_DATE, BIL_HEADER.PERFORMEDFROM, BIL_HEADER.PERFORMEDTO&#13;&#10;    ,BIL_STATUS.DESCRIPTION AS BIL_STATUS_DESCR, BIL_STATUS.DESCRIPTION_TR AS BIL_STATUS_DESCR_TR&#13;&#10;	,(SELECT SUM(RESULT) FROM BIL_BILLINGSCHEMA WHERE BIL_HEADER_FK = CTE.BIL_BEFORE_HEADER_FK AND SORTING = 330) FINALPRICE&#13;&#10;	,(SELECT SUM(RESULT) FROM BIL_BILLINGSCHEMA WHERE BIL_HEADER_FK = CTE.BIL_BEFORE_HEADER_FK AND SORTING = 320) FINALTAX&#13;&#10;	,(SELECT SUM(RESULT) FROM BIL_BILLINGSCHEMA WHERE BIL_HEADER_FK = CTE.BIL_BEFORE_HEADER_FK AND SORTING = 140) FINALGROSS&#13;&#10;FROM CTE&#13;&#10;LEFT JOIN BIL_HEADER ON BIL_HEADER.ID = CTE.BIL_BEFORE_HEADER_FK&#13;&#10;LEFT JOIN BIL_BOQ ON BIL_BOQ.BIL_HEADER_FK = CTE.BIL_BEFORE_HEADER_FK&#13;&#10;LEFT JOIN BIL_STATUS ON BIL_STATUS.ID = BIL_HEADER.BIL_STATUS_FK&#13;&#10;&#13;&#10;GROUP BY CTE.BIL_BEFORE_HEADER_FK, CTE.BIL_HEADER_FK, CTE.CANLOAD, CTE.ISSELECTED, CTE.ISSTORNO&#13;&#10;       , BIL_HEADER.BILL_NO, BIL_HEADER.DESCRIPTION, BIL_HEADER.DESCRIPTION_TR, BIL_HEADER.DATE_EFFECTIVE , BIL_HEADER.BILL_DATE, BIL_HEADER.PERFORMEDFROM, BIL_HEADER.PERFORMEDTO&#13;&#10;       , BIL_STATUS.DESCRIPTION, BIL_STATUS.DESCRIPTION_TR&#13;&#10;&#13;&#10;">
        <Column Name="BIL_HEADER_FK" DataType="System.Int32"/>
        <Column Name="BIL_BEFORE_HEADER_FK" DataType="System.Int32"/>
        <Column Name="ISSELECTED" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="CANLOAD" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="ISSTORNO" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="BILL_NO" DataType="System.String"/>
        <Column Name="BIL_DESCR" DataType="System.String"/>
        <Column Name="BIL_DESCR_TR" DataType="System.Int32"/>
        <Column Name="DATE_EFFECTIVE" DataType="System.DateTime"/>
        <Column Name="BILL_DATE" DataType="System.DateTime"/>
        <Column Name="PERFORMEDFROM" DataType="System.DateTime"/>
        <Column Name="PERFORMEDTO" DataType="System.DateTime"/>
        <Column Name="BIL_STATUS_DESCR" DataType="System.String"/>
        <Column Name="BIL_STATUS_DESCR_TR" DataType="System.Int32"/>
        <Column Name="FINALPRICE" DataType="System.Decimal"/>
        <Column Name="FINALGROSS" DataType="System.Decimal"/>
        <Column Name="FINALTAX" DataType="System.Decimal"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table6" Alias="Zahlungen" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="WITH VORHER(ID, BIL_HEADER_FK, BIL_BEFORE_HEADER_FK) AS (&#13;&#10;  SELECT ID, BIL_HEADER_FK, BIL_BEFORE_HEADER_FK&#13;&#10;  FROM   BIL_BIL2BIL WHERE  BIL_HEADER_FK = @BIL_HEADER_FK&#13;&#10;  UNION ALL&#13;&#10;  SELECT CUR.ID, CUR.BIL_HEADER_FK, CUR.BIL_BEFORE_HEADER_FK&#13;&#10;  FROM   BIL_BIL2BIL CUR, VORHER&#13;&#10;  WHERE  CUR.BIL_HEADER_FK = VORHER.BIL_BEFORE_HEADER_FK&#13;&#10;)&#13;&#10;SELECT &#13;&#10;P.PAYMENT_DATE ZAHLDATUM,&#13;&#10;P.POSTING_NARRITIVE BUCHUNGSTEXT,&#13;&#10;P.AMOUNT - P.AMOUNT_VAT NETTO, &#13;&#10;P.AMOUNT_VAT USTEUER, &#13;&#10;P.AMOUNT BRUTTO&#13;&#10;,BH.BILL_NO&#13;&#10;FROM VORHER V&#13;&#10;JOIN BIL_PAYMENT P ON P.BIL_HEADER_FK = V.BIL_BEFORE_HEADER_FK&#13;&#10;JOIN BIL_HEADER BH ON BH.ID = P.BIL_HEADER_FK&#13;&#10;ORDER BY P.PAYMENT_DATE, P.INSERTED&#13;&#10;">
        <Column Name="ZAHLDATUM" DataType="System.DateTime"/>
        <Column Name="BUCHUNGSTEXT" DataType="System.String"/>
        <Column Name="NETTO" DataType="System.Decimal"/>
        <Column Name="USTEUER" DataType="System.Decimal"/>
        <Column Name="BRUTTO" DataType="System.Decimal"/>
        <Column Name="BILL_NO" DataType="System.String"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table9" Alias="Positionen" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="SELECT &#13;&#10;BILI.ID BILI_ID&#13;&#10;,BILI.ITEMNO&#13;&#10;,BILI.DESCRIPTION1&#13;&#10;,BILI.DESCRIPTION2&#13;&#10;,BILI.SPECIFICATION&#13;&#10;,BILI.QUANTITY&#13;&#10;,BILI.PRICE&#13;&#10;,BILI.TOTAL&#13;&#10;,BILI.AMOUNT_NET&#13;&#10;,BILI.AMOUNT_GROSS&#13;&#10;,BILI.AMOUNT_GROSS - BILI.AMOUNT_NET TAX&#13;&#10;,TAX.VATPERCENT&#13;&#10;,COALESCE(UOMT.DESCRIPTION, UOM.UOM) COLLATE SQL_LATIN1_GENERAL_CP1_CI_AS UOM&#13;&#10;,SPEC.CONTENT BLOB_SPECIFICATION&#13;&#10;FROM BIL_ITEM BILI&#13;&#10;LEFT JOIN BAS_UOM UOM ON UOM.ID = BILI.BAS_UOM_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;LEFT JOIN BAS_BLOBS SPEC ON SPEC.ID = BILI.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;LEFT JOIN MDC_TAX_CODE TAX ON TAX.ID = BILI.MDC_TAX_CODE_FK&#13;&#10;WHERE BILI.BIL_HEADER_FK = @BIL_HEADER_FK&#13;&#10;ORDER BY BILI.ITEMNO">
        <Column Name="BILI_ID" DataType="System.Int32"/>
        <Column Name="ITEMNO" DataType="System.Int32"/>
        <Column Name="DESCRIPTION1" DataType="System.String"/>
        <Column Name="DESCRIPTION2" DataType="System.String"/>
        <Column Name="SPECIFICATION" DataType="System.String"/>
        <Column Name="QUANTITY" DataType="System.Decimal"/>
        <Column Name="PRICE" DataType="System.Decimal"/>
        <Column Name="TOTAL" DataType="System.Decimal"/>
        <Column Name="AMOUNT_NET" DataType="System.Decimal"/>
        <Column Name="AMOUNT_GROSS" DataType="System.Decimal"/>
        <Column Name="VATPERCENT" DataType="System.Decimal"/>
        <Column Name="UOM" DataType="System.String"/>
        <Column Name="BLOB_SPECIFICATION" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="TAX" DataType="System.Decimal"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1010645"/>
      </TableDataSource>
      <TableDataSource Name="Table10" Alias="RechnungClerk" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="DECLARE @CLERKID BIGINT = (SELECT BAS_CLERK_FK FROM BIL_HEADER WHERE ID = @BIL_HEADER_FK)&#13;&#10;&#13;&#10;SELECT &#13;&#10;-- STANDARDFELDER&#13;&#10;C.ID			RECHNUNGCLERK_ID&#13;&#10;,C.DESCRIPTION		RECHNUNGCLERK_BEZEICHNUNG&#13;&#10;,C.FIRST_NAME		RECHNUNGCLERK_VORNAME&#13;&#10;,C.FAMILY_NAME		RECHNUNGCLERK_NACHNAME&#13;&#10;,C.SIGNATURE		RECHNUNGCLERK_SIGNATUR&#13;&#10;,C.TITLE			RECHNUNGCLERK_TITEL&#13;&#10;,C.DEPARTMENT		RECHNUNGCLERK_ABTEILUNG&#13;&#10;,C.EMAIL			RECHNUNGCLERK_EMAIL&#13;&#10;,C.USERDEFINED1		RECHNUNGCLERK_TEXT1&#13;&#10;,C.USERDEFINED2		RECHNUNGCLERK_TEXT2&#13;&#10;,C.USERDEFINED3		RECHNUNGCLERK_TEXT3&#13;&#10;,C.USERDEFINED4		RECHNUNGCLERK_TEXT4&#13;&#10;,C.USERDEFINED5		RECHNUNGCLERK_TEXT5&#13;&#10;,COALESCE(ANREDET.DESCRIPTION, ANREDE.DESCRIPTION) RECHNUNGCLERK_ANREDE&#13;&#10;,COALESCE(BRIEFANREDET.DESCRIPTION, BRIEFANREDE.DESCRIPTION) RECHNUNGCLERK_BRIEFANREDE&#13;&#10;,COALESCE(GRUSSFORMT.DESCRIPTION, GRUSSFORM.DESCRIPTION) RECHNUNGCLERK_GRUSSFORM&#13;&#10;-- PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) RECHNUNGCLERK_VOLLERNAME&#13;&#10;-- i. A. PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN C.SIGNATURE IS NOT NULL AND LTRIM(C.SIGNATURE) &lt;&gt; '' THEN CONCAT(C.SIGNATURE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) RECHNUNGCLERK_UNTERZEICHNUNG&#13;&#10;-- HERR PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(ANREDET.DESCRIPTION,ANREDE.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(ANREDET.DESCRIPTION,ANREDE.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) RECHNUNGCLERK_VOLLERNAMEMITANREDE&#13;&#10;-- HERRN PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(BRIEFANREDET.DESCRIPTION,BRIEFANREDE.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(BRIEFANREDET.DESCRIPTION,BRIEFANREDE.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) RECHNUNGCLERK_VOLLERNAMEMITBRIEFANREDE&#13;&#10;-- SEHR GEEHRTER HERR PROF. DR. HARTWIG BURG&#13;&#10;,CONCAT(CASE WHEN COALESCE(GRUSSFORMT.DESCRIPTION,GRUSSFORM.DESCRIPTION) IS NOT NULL THEN CONCAT(COALESCE(GRUSSFORMT.DESCRIPTION,GRUSSFORM.DESCRIPTION), ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;		CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; ''  THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;		CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) RECHNUNGCLERK_VOLLEGRUSSANREDE&#13;&#10;-- Telefon&#13;&#10;,TEL.TELEPHONE RECHNUNGCLERK_TELEFON&#13;&#10;-- Mobil&#13;&#10;,MOB.TELEPHONE RECHNUNGCLERK_MOBILE&#13;&#10;-- Fax&#13;&#10;,FAX.TELEPHONE RECHNUNGCLERK_FAX&#13;&#10;FROM BAS_CLERK C&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER TEL ON TEL.ID = C.BAS_TELEPHONE_NUMBER_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER MOB ON MOB.ID = C.BAS_TELEPHONE_MOBILE_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER FAX ON FAX.ID = C.BAS_TELEPHONE_TELEFAX_FK&#13;&#10;-- ANREDE HERR&#13;&#10;LEFT JOIN BAS_TITLE ANREDE ON ANREDE.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION ANREDET ON ANREDET.ID = ANREDE.DESCRIPTION_TR AND ANREDET.BAS_LANGUAGE_FK = 2&#13;&#10;-- BRIEFANREDE HERRN&#13;&#10;LEFT JOIN BAS_TITLE BRIEFANREDE ON BRIEFANREDE.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION BRIEFANREDET ON BRIEFANREDET.ID = BRIEFANREDE.ADDRESS_TITLE_TR AND BRIEFANREDET.BAS_LANGUAGE_FK = 2&#13;&#10;-- GRUßFORMEL SEHR GEEHRTER HERR&#13;&#10;LEFT JOIN BAS_TITLE GRUSSFORM ON GRUSSFORM.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION GRUSSFORMT ON GRUSSFORMT.ID = GRUSSFORM.SALUTATION_TR AND GRUSSFORMT.BAS_LANGUAGE_FK = 2&#13;&#10;WHERE C.ID = @CLERKID&#13;&#10;&#13;&#10;">
        <Column Name="RECHNUNGCLERK_ID" DataType="System.Int32"/>
        <Column Name="RECHNUNGCLERK_BEZEICHNUNG" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_VORNAME" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_NACHNAME" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_SIGNATUR" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TITEL" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_ABTEILUNG" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_EMAIL" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TEXT1" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TEXT2" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TEXT3" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TEXT4" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TEXT5" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_ANREDE" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_GRUSSFORM" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_VOLLERNAME" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_UNTERZEICHNUNG" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_VOLLERNAMEMITANREDE" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_VOLLERNAMEMITBRIEFANREDE" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_VOLLEGRUSSANREDE" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_TELEFON" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_MOBILE" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_FAX" DataType="System.String"/>
        <Column Name="RECHNUNGCLERK_BRIEFANREDE" DataType="System.String"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table11" Alias="BoqHeader" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @BIL_HEADER_FK BIGINT = 1011525;&#13;&#10;SELECT &#13;&#10;	BOQ.BOQ_HEADER_FK&#13;&#10;	,BOQH.BOQ_HEADER_FK BOQ_HEADER_QTO_DETAIL_FK&#13;&#10;	,BH.ID BIL_ID&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.BRIEF FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ.BOQ_HEADER_FK) BOQ_TITEL&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.REFERENCE FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ.BOQ_HEADER_FK) BOQ_NUMMER&#13;&#10;	,BH.BILL_NO RECHNUNGSNUMMER&#13;&#10;	,BH.DESCRIPTION RECHNUNGBEZEICHNUNG&#13;&#10;FROM BIL_BOQ BOQ&#13;&#10;JOIN BIL_HEADER BH ON BH.ID = BOQ.BIL_HEADER_FK&#13;&#10;LEFT JOIN BOQ_HEADER BOQH ON BOQH.ID = BOQ.BOQ_HEADER_FK&#13;&#10;WHERE BOQ.BIL_HEADER_FK = @BIL_HEADER_FK &#13;&#10;ORDER BY BOQ.ID&#13;&#10;">
        <Column Name="BOQ_HEADER_FK" DataType="System.Int32"/>
        <Column Name="BOQ_TITEL" DataType="System.String"/>
        <Column Name="BOQ_NUMMER" DataType="System.String"/>
        <Column Name="RECHNUNGSNUMMER" DataType="System.String"/>
        <Column Name="RECHNUNGBEZEICHNUNG" DataType="System.String"/>
        <Column Name="BOQ_HEADER_QTO_DETAIL_FK" DataType="System.Int32"/>
        <Column Name="BIL_ID" DataType="System.Int32"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1013594"/>
        <CommandParameter Name="GesamtLVsDrucken" DataType="8" DefaultValue="0"/>
      </TableDataSource>
      <TableDataSource Name="Table12" Alias="LV" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @NULLMENGENZEILENDRUCKEN INT = 1;&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1033143;---1;&#13;&#10;&#13;&#10;DECLARE @FLATLV TABLE (&#13;&#10;	BOQ_HEADER_FK BIGINT,&#13;&#10;	ID BIGINT, &#13;&#10;	REFERENCE VARCHAR(100),&#13;&#10;	BOQ_ITEM_LEVEL1_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL2_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL3_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL4_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL5_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL6_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL7_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL8_FK BIGINT,&#13;&#10;	BRIEF VARCHAR(255),&#13;&#10;	BRIEF_TR BIGINT,&#13;&#10;	PRINT_MARK VARCHAR(10),&#13;&#10;	SORTING INT,&#13;&#10;	BOQ_LINE_TYPE_FK INT,&#13;&#10;	BAS_ITEM_TYPE_FK INT,&#13;&#10;	QUANTITY NUMERIC(19, 6),&#13;&#10;	PRICE NUMERIC(19, 7),&#13;&#10;	FINALPRICE NUMERIC(19, 7),&#13;&#10;	BAS_UOM_FK bigint,&#13;&#10;	UOM VARCHAR(20),&#13;&#10;	SPECIFICATION_FORMATTED VARBINARY(MAX),&#13;&#10;	SPECIFICATION NVARCHAR(MAX),&#13;&#10;	HAS_QUANTITY INT,&#13;&#10;	DISCOUNT_ABS_EBENE NUMERIC(19, 7),&#13;&#10;	DISCOUNT_PER_EBENE NUMERIC(19, 7),&#13;&#10;	DISCOUNT_PER_POS NUMERIC(19, 7)&#13;&#10;)&#13;&#10;&#13;&#10;DECLARE @SORTING INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_1 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_2 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_3 INT = 0; &#13;&#10;&#13;&#10;DECLARE @ID_L1 BIGINT = 0;&#13;&#10;DECLARE @REFERENCE_L1 VARCHAR(100) = '';&#13;&#10;DECLARE @BOQ_ITEM_LEVEL1_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L1 BIGINT = -1;&#13;&#10;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L2 BIGINT = Null;&#13;&#10;&#13;&#10;DECLARE @BOQ_LINE_TYPE_LEVEL2_L1 INT = 0;&#13;&#10;&#13;&#10;DECLARE @BRIEF_L1 VARCHAR(255) = '';&#13;&#10;DECLARE @BRIEF_TR_L1 BIGINT = 0;&#13;&#10;DECLARE @BAS_UOM_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @BOQ_LINE_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @BAS_ITEM_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @QUANTITY_L1 NUMERIC(19, 6) = 0.0;&#13;&#10;DECLARE @PRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @FINALPRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @UOM_L1 VARCHAR(20) = ''; &#13;&#10;DECLARE @SPECIFICATION_FORMATTED_L1 VARBINARY(MAX); &#13;&#10;DECLARE @SPECIFICATION_L1 NVARCHAR(MAX);&#13;&#10;DECLARE @DISCOUNT_ABS_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_PER_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_PER_POS NUMERIC(19, 7) = 0;&#13;&#10;&#13;&#10;&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM.ID, REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, COALESCE(UOMT.DESCRIPTION, UOM.DESCRIPTION) UOM,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL1_FK, -1) BOQ_ITEM_LEVEL1_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL2_FK, -1) BOQ_ITEM_LEVEL2_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL3_FK, -1) BOQ_ITEM_LEVEL3_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL4_FK, -1) BOQ_ITEM_LEVEL4_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL5_FK, -1) BOQ_ITEM_LEVEL5_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL6_FK, -1) BOQ_ITEM_LEVEL6_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL7_FK, -1) BOQ_ITEM_LEVEL7_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL8_FK, -1) BOQ_ITEM_LEVEL8_FK&#13;&#10;							 ,BLOB.CONTENT SPECIFICATION_FORMATTED, PRICE, FINALPRICE,&#13;&#10;							 DISCOUNT DISCOUNT_ABS_EBENE, -- Nachlass absolut &#13;&#10;							 DISCOUNT_PERCENT_IT DISCOUNT_PER_EBENE, -- Nachlass % &#13;&#10;							 -- Position&#13;&#10;							 DISCOUNT_PERCENT DISCOUNT_PER_POS -- % Position&#13;&#10;							FROM BOQ_ITEM &#13;&#10;								LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQ_ITEM.BAS_UOM_FK&#13;&#10;								LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;								LEFT JOIN BAS_BLOBS BLOB ON BLOB.ID = BOQ_ITEM.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;							WHERE BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK &#13;&#10;							ORDER BY DBO.GETSORTABLEREFERENCESTRING(REFERENCE) -- BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;						@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;						@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS&#13;&#10;&#13;&#10;&#13;&#10;IF @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LVHEADER', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, (SELECT COUNT(*) + 10000 FROM BOQ_ITEM WHERE BOQ_HEADER_FK = @BOQ_HEADER_FK), &#13;&#10;								'LVSUM', SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, FINALPRICE, @BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS&#13;&#10;	END &#13;&#10;END&#13;&#10;&#13;&#10;&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 in (1,2,3,4,5,6,7,8)&#13;&#10;	BEGIN&#13;&#10;		SET @BOQ_ITEM_LEVEL2_FK_L2 = @BOQ_ITEM_LEVEL2_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL3_FK_L2 = @BOQ_ITEM_LEVEL3_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL4_FK_L2 = @BOQ_ITEM_LEVEL4_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL5_FK_L2 = @BOQ_ITEM_LEVEL5_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL6_FK_L2 = @BOQ_ITEM_LEVEL6_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL7_FK_L2 = @BOQ_ITEM_LEVEL7_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL8_FK_L2 = @BOQ_ITEM_LEVEL8_FK_L1;&#13;&#10;	END&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 1&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 2&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 3&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 4&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 5&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 6&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 7&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END ELSE &#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'POSITION', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS);&#13;&#10;	END&#13;&#10;&#13;&#10;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1,  @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS&#13;&#10;&#13;&#10;	IF @@FETCH_STATUS = 0&#13;&#10;	BEGIN&#13;&#10;		IF @BOQ_ITEM_LEVEL8_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL8_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL7_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL7_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL6_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL6_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL5_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL5_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL4_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL4_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL3_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL3_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL2_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL2_FK_L2;&#13;&#10;		END&#13;&#10;	END&#13;&#10;&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;IF @BOQ_ITEM_LEVEL8_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL8_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL7_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL7_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL6_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL6_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL5_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL5_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL4_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL4_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL3_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL3_FK_L2;&#13;&#10;END&#13;&#10;&#13;&#10;IF @BOQ_ITEM_LEVEL2_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL2_FK_L2;&#13;&#10;END&#13;&#10;INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;						REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;						PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;				SELECT ID, &#13;&#10;						BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;						REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, (SELECT MAX(SORTING) + 1 FROM @FLATLV),&#13;&#10;						'SUMMARY', SPECIFICATION_FORMATTED,&#13;&#10;						PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;						FROM @FLATLV WHERE  BOQ_LINE_TYPE_FK IN (103, 1,2,3,4,5,6,7,8) AND (PRINT_MARK LIKE 'LEVEL%')&#13;&#10;						ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;		&#13;&#10;INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;						REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;						PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS) &#13;&#10;				SELECT ID, &#13;&#10;						BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;						REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, (SELECT MAX(SORTING) + 1 FROM @FLATLV),&#13;&#10;						'LVSUMMARY', SPECIFICATION_FORMATTED,&#13;&#10;						PRICE, FINALPRICE, BOQ_HEADER_FK, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 and BOQ_LINE_TYPE_FK = 103 and PRINT_MARK = 'LVHEADER';&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK&#13;&#10;							FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;							GROUP BY BOQ_ITEM_LEVEL1_FK&#13;&#10;							ORDER BY BOQ_ITEM_LEVEL1_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 1 WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 2 WHERE BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND BOQ_ITEM_LEVEL3_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 3 WHERE BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 4 WHERE BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 5 WHERE BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 6 WHERE BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 7 WHERE BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 8 WHERE BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;IF @NULLMENGENZEILENDRUCKEN = 1&#13;&#10;BEGIN&#13;&#10;	SELECT * FROM @FLATLV ORDER BY BOQ_HEADER_FK, SORTING &#13;&#10;END ELSE BEGIN&#13;&#10;	SELECT * FROM @FLATLV WHERE HAS_QUANTITY &gt; 0 ORDER BY BOQ_HEADER_FK, SORTING &#13;&#10;END&#13;&#10;">
        <Column Name="BOQ_HEADER_FK" DataType="System.Int64"/>
        <Column Name="ID" DataType="System.Int64"/>
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="BOQ_ITEM_LEVEL1_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL2_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL3_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL4_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL5_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL6_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL7_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL8_FK" DataType="System.Int64"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="BRIEF_TR" DataType="System.Int64"/>
        <Column Name="PRINT_MARK" DataType="System.String"/>
        <Column Name="SORTING" DataType="System.Int32"/>
        <Column Name="BOQ_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="BAS_ITEM_TYPE_FK" DataType="System.Int32"/>
        <Column Name="QUANTITY" DataType="System.Decimal"/>
        <Column Name="PRICE" DataType="System.Decimal"/>
        <Column Name="FINALPRICE" DataType="System.Decimal"/>
        <Column Name="BAS_UOM_FK" DataType="System.Int64"/>
        <Column Name="UOM" DataType="System.String"/>
        <Column Name="SPECIFICATION_FORMATTED" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="SPECIFICATION" DataType="System.String"/>
        <Column Name="HAS_QUANTITY" DataType="System.Int32"/>
        <Column Name="DISCOUNT_ABS_EBENE" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_PER_EBENE" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_PER_POS" DataType="System.Decimal"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqHeader.BOQ_HEADER_FK]" DefaultValue="1024130"/>
        <CommandParameter Name="NULLMENGENZEILENDRUCKEN" DataType="8" Expression="[NullMengenZeilenDrucken]" DefaultValue="0"/>
      </TableDataSource>
      <TableDataSource Name="Table4" Alias="LvZusammenfassung" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @NULLMENGENZEILENDRUCKEN INT = 0;&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1024130;---1;&#13;&#10;&#13;&#10;DECLARE @FLATLV TABLE (&#13;&#10;	BOQ_HEADER_FK BIGINT,&#13;&#10;	ID BIGINT, &#13;&#10;	REFERENCE VARCHAR(100),&#13;&#10;	BOQ_ITEM_LEVEL1_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL2_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL3_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL4_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL5_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL6_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL7_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL8_FK BIGINT,&#13;&#10;	BRIEF VARCHAR(255),&#13;&#10;	BRIEF_TR BIGINT,&#13;&#10;	PRINT_MARK VARCHAR(10),&#13;&#10;	SORTING INT,&#13;&#10;	BOQ_LINE_TYPE_FK INT,&#13;&#10;	BAS_ITEM_TYPE_FK INT,&#13;&#10;	QUANTITY NUMERIC(19, 6),&#13;&#10;	PRICE NUMERIC(19, 7),&#13;&#10;	FINALPRICE NUMERIC(19, 7),&#13;&#10;	BAS_UOM_FK bigint,&#13;&#10;	UOM VARCHAR(20),&#13;&#10;	SPECIFICATION_FORMATTED VARBINARY(MAX),&#13;&#10;	SPECIFICATION NVARCHAR(MAX),&#13;&#10;	HAS_QUANTITY INT&#13;&#10;)&#13;&#10;&#13;&#10;DECLARE @SORTING INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_1 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_2 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_3 INT = 0; &#13;&#10;&#13;&#10;DECLARE @ID_L1 BIGINT = 0;&#13;&#10;DECLARE @REFERENCE_L1 VARCHAR(100) = '';&#13;&#10;DECLARE @BOQ_ITEM_LEVEL1_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L1 BIGINT = -1;&#13;&#10;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L2 BIGINT = Null;&#13;&#10;&#13;&#10;DECLARE @BOQ_LINE_TYPE_LEVEL2_L1 INT = 0;&#13;&#10;&#13;&#10;DECLARE @BRIEF_L1 VARCHAR(255) = '';&#13;&#10;DECLARE @BRIEF_TR_L1 BIGINT = 0;&#13;&#10;DECLARE @BAS_UOM_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @BOQ_LINE_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @BAS_ITEM_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @QUANTITY_L1 NUMERIC(19, 6) = 0.0;&#13;&#10;DECLARE @PRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @FINALPRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @UOM_L1 VARCHAR(20) = ''; &#13;&#10;DECLARE @SPECIFICATION_FORMATTED_L1 VARBINARY(MAX); &#13;&#10;DECLARE @SPECIFICATION_L1 NVARCHAR(MAX);&#13;&#10;&#13;&#10;&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM.ID, REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, COALESCE(UOMT.DESCRIPTION, UOM.DESCRIPTION) UOM,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL1_FK, -1) BOQ_ITEM_LEVEL1_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL2_FK, -1) BOQ_ITEM_LEVEL2_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL3_FK, -1) BOQ_ITEM_LEVEL3_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL4_FK, -1) BOQ_ITEM_LEVEL4_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL5_FK, -1) BOQ_ITEM_LEVEL5_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL6_FK, -1) BOQ_ITEM_LEVEL6_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL7_FK, -1) BOQ_ITEM_LEVEL7_FK,&#13;&#10;							 COALESCE(BOQ_ITEM_LEVEL8_FK, -1) BOQ_ITEM_LEVEL8_FK&#13;&#10;							 ,BLOB.CONTENT SPECIFICATION_FORMATTED, PRICE, FINALPRICE&#13;&#10;							FROM BOQ_ITEM &#13;&#10;								LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQ_ITEM.BAS_UOM_FK&#13;&#10;								LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;								LEFT JOIN BAS_BLOBS BLOB ON BLOB.ID = BOQ_ITEM.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;							WHERE BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK and BOQ_ITEM.BOQ_LINE_TYPE_FK in (103,1,2,3,4,5,6,7,8)&#13;&#10;							ORDER BY DBO.GETSORTABLEREFERENCESTRING(REFERENCE) -- BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;						@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;						@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1&#13;&#10;&#13;&#10;IF @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LVHEADER', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, (SELECT COUNT(*) + 10000 FROM BOQ_ITEM WHERE BOQ_HEADER_FK = @BOQ_HEADER_FK),&#13;&#10;								'LVSUM', SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, FINALPRICE, @BOQ_HEADER_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1&#13;&#10;	END &#13;&#10;END&#13;&#10;&#13;&#10;&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 in (1,2,3,4,5,6,7,8)&#13;&#10;	BEGIN&#13;&#10;		SET @BOQ_ITEM_LEVEL2_FK_L2 = @BOQ_ITEM_LEVEL2_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL3_FK_L2 = @BOQ_ITEM_LEVEL3_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL4_FK_L2 = @BOQ_ITEM_LEVEL4_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL5_FK_L2 = @BOQ_ITEM_LEVEL5_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL6_FK_L2 = @BOQ_ITEM_LEVEL6_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL7_FK_L2 = @BOQ_ITEM_LEVEL7_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL8_FK_L2 = @BOQ_ITEM_LEVEL8_FK_L1;&#13;&#10;	END&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 1&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 2&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 3&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 4&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 5&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 6&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 7&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END ELSE &#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'POSITION', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @FINALPRICE_L1, @BOQ_HEADER_FK);&#13;&#10;	END&#13;&#10;&#13;&#10;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @QUANTITY_L1, @BAS_UOM_FK_L1,  @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @FINALPRICE_L1&#13;&#10;&#13;&#10;	IF @@FETCH_STATUS = 0&#13;&#10;	BEGIN&#13;&#10;		IF @BOQ_ITEM_LEVEL8_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL8_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL7_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL7_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL6_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL6_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL5_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL5_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL4_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL4_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL3_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL3_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL2_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, QUANTITY, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, FINALPRICE, BOQ_HEADER_FK&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL2_FK_L2;&#13;&#10;		END&#13;&#10;	END&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK&#13;&#10;							FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;							GROUP BY BOQ_ITEM_LEVEL1_FK&#13;&#10;							ORDER BY BOQ_ITEM_LEVEL1_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 1 WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 2 WHERE BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND BOQ_ITEM_LEVEL3_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 3 WHERE BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 4 WHERE BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 5 WHERE BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 6 WHERE BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 7 WHERE BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 8 WHERE BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;IF @NULLMENGENZEILENDRUCKEN = 1&#13;&#10;BEGIN&#13;&#10;	SELECT * FROM @FLATLV ORDER BY BOQ_HEADER_FK, SORTING &#13;&#10;END ELSE BEGIN&#13;&#10;	SELECT * FROM @FLATLV WHERE HAS_QUANTITY &gt; 0 ORDER BY BOQ_HEADER_FK, SORTING &#13;&#10;END&#13;&#10;">
        <Column Name="BOQ_HEADER_FK" DataType="System.Int64"/>
        <Column Name="ID" DataType="System.Int64"/>
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="BOQ_ITEM_LEVEL1_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL2_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL3_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL4_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL5_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL6_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL7_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL8_FK" DataType="System.Int64"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="BRIEF_TR" DataType="System.Int64"/>
        <Column Name="PRINT_MARK" DataType="System.String"/>
        <Column Name="SORTING" DataType="System.Int32"/>
        <Column Name="BOQ_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="BAS_ITEM_TYPE_FK" DataType="System.Int32"/>
        <Column Name="QUANTITY" DataType="System.Decimal"/>
        <Column Name="PRICE" DataType="System.Decimal"/>
        <Column Name="FINALPRICE" DataType="System.Decimal"/>
        <Column Name="BAS_UOM_FK" DataType="System.Int64"/>
        <Column Name="UOM" DataType="System.String"/>
        <Column Name="SPECIFICATION_FORMATTED" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="SPECIFICATION" DataType="System.String"/>
        <Column Name="HAS_QUANTITY" DataType="System.Int32"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqHeader.BOQ_HEADER_FK]" DefaultValue="1"/>
        <CommandParameter Name="NULLMENGENZEILENDRUCKEN" DataType="8" Expression="[NullMengenZeilenDrucken]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table13" Alias="Aufmass" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--Dieser BOQ_HEADER_FK kommt aus dem Feld BOQ_HEADER_FK des Headers des LVs und ist nicht die ID der Tabelle BOQ_HEADER&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1024116;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1011116;&#13;&#10;--DECLARE @BOQ_ITEM_FK BIGINT = 1011116;&#13;&#10;&#13;&#10;SELECT &#13;&#10;QTOH.CODE AUFMASSNUMMER&#13;&#10;,QTOH.DESCRIPTION AUFMASSBEZEICHNUNG&#13;&#10;,BOQI.REFERENCE OZ&#13;&#10;,BOQI.BRIEF BEZEICHNUNG&#13;&#10;,QTOD.BIL_HEADER_FK&#13;&#10;,QTOD.FACTOR&#13;&#10;,QTOD.LINE_REFERENCE ZEILE&#13;&#10;,QTOD.LINE_TEXT RECHENANSATZ&#13;&#10;,QTOD.RESULT ERGEBNIS&#13;&#10;,QTOD.PAGE_NUMBER ADRESSE&#13;&#10;,QTOF.CODE FORMEL&#13;&#10;,COALESCE(UOMT.DESCRIPTION, UOM.UOM COLLATE SQL_LATIN1_GENERAL_CP1_CI_AS ) ME&#13;&#10;,QTO_LINETYPE_FK ZEILENTYP&#13;&#10;,QTOD.BOQ_HEADER_FK&#13;&#10;FROM QTO_DETAIL QTOD &#13;&#10;JOIN QTO_HEADER QTOH ON QTOH.ID = QTOD.QTO_HEADER_FK&#13;&#10;JOIN QTO_SHEET QTOs ON QTOs.ID = qtod.QTO_SHEET_FK&#13;&#10;LEFT JOIN QTO_FORMULA QTOF ON QTOF.ID = QTOD.QTO_FORMULA_FK&#13;&#10;left JOIN BOQ_ITEM BOQI ON BOQI.ID = QTOD.BOQ_ITEM_FK&#13;&#10;LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;WHERE QTOD.BOQ_HEADER_FK = @BOQ_HEADER_FK and QTOD.BIL_HEADER_FK = @BIL_HEADER_FK&#13;&#10;ORDER BY BOQI.REFERENCE, QTOD.LINE_REFERENCE&#13;&#10;&#13;&#10;">
        <Column Name="AUFMASSNUMMER" DataType="System.String"/>
        <Column Name="AUFMASSBEZEICHNUNG" DataType="System.String"/>
        <Column Name="OZ" DataType="System.String"/>
        <Column Name="BEZEICHNUNG" DataType="System.String"/>
        <Column Name="FACTOR" DataType="System.Decimal"/>
        <Column Name="ZEILE" DataType="System.String"/>
        <Column Name="RECHENANSATZ" DataType="System.String"/>
        <Column Name="ERGEBNIS" DataType="System.Decimal"/>
        <Column Name="ADRESSE" DataType="System.Int32"/>
        <Column Name="FORMEL" DataType="System.String"/>
        <Column Name="ME" DataType="System.String"/>
        <Column Name="ZEILENTYP" DataType="System.Int32"/>
        <Column Name="BIL_HEADER_FK" DataType="System.Int32"/>
        <Column Name="BOQ_HEADER_FK" DataType="System.Int32"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqHeader.BOQ_HEADER_QTO_DETAIL_FK]" DefaultValue="1024118"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BoqHeader.BIL_ID]" DefaultValue="1011116"/>
      </TableDataSource>
      <TableDataSource Name="Table14" Alias="LvKumuliertMitAufmass" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @NULLMENGENZEILENDRUCKEN INT = 1;&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1020928;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1015178;&#13;&#10;&#13;&#10;DECLARE @BILLS TABLE (&#13;&#10;	BIL_HEADER_FK BIGINT&#13;&#10;	,BOQ_HEADER_FK BIGINT&#13;&#10;);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK) VALUES(@BIL_HEADER_FK, -1);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK) SELECT BIL_BEFORE_HEADER_FK, -1 FROM BIL_BIL2BIL_F() WHERE BIL_HEADER_FK = @BIL_HEADER_FK;&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK) SELECT -1, BIL_BOQ.BOQ_HEADER_FK FROM BIL_BOQ, @BILLS X WHERE BIL_boq.BIL_HEADER_FK = X.BIL_HEADER_FK;&#13;&#10;&#13;&#10;IF @NULLMENGENZEILENDRUCKEN = 1&#13;&#10;BEGIN&#13;&#10;	WITH ITEMS AS (&#13;&#10;		SELECT &#13;&#10;		@BOQ_HEADER_FK PRJ_BOQ_HEADER_FK &#13;&#10;		,BOQH.ID BIL_BOQ_HEADER_FK &#13;&#10;		,BOQI.ID BOQ_ITEM_FK&#13;&#10;		,BOQI.BAS_ITEM_TYPE_FK&#13;&#10;		,BOQI.BOQ_LINE_TYPE_FK&#13;&#10;		,BOQI.REFERENCE&#13;&#10;		,BOQI.BRIEF&#13;&#10;		,BOQI.BOQ_ITEM_LEVEL1_FK, BOQI.BOQ_ITEM_LEVEL2_FK, BOQI.BOQ_ITEM_LEVEL3_FK, BOQI.BOQ_ITEM_LEVEL4_FK, BOQI.BOQ_ITEM_LEVEL5_FK, BOQI.BOQ_ITEM_LEVEL6_FK, BOQI.BOQ_ITEM_LEVEL7_FK, BOQI.BOQ_ITEM_LEVEL8_FK&#13;&#10;		,COALESCE(UOMT.DESCRIPTION, UOM.DESCRIPTION) UOM&#13;&#10;		,(SELECT TOP 1 BOQ_ITEM.BRIEF FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK) BOQ_TITEL&#13;&#10;		,(SELECT TOP 1 BOQ_ITEM.REFERENCE FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK) BOQ_NUMMER&#13;&#10;		FROM BOQ_HEADER BOQH &#13;&#10;		JOIN BOQ_ITEM BOQI ON BOQI.BOQ_HEADER_FK = BOQH.ID&#13;&#10;		LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;		LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;		WHERE BOQH.ID = @BOQ_HEADER_FK&#13;&#10;	)&#13;&#10;&#13;&#10;	SELECT &#13;&#10;	ITEMS.PRJ_BOQ_HEADER_FK, ITEMS.BIL_BOQ_HEADER_FK, ITEMS.BOQ_ITEM_FK, ITEMS.BAS_ITEM_TYPE_FK,ITEMS.BOQ_LINE_TYPE_FK, ITEMS.REFERENCE, ITEMS.BRIEF, ITEMS.UOM&#13;&#10;	,(SELECT SUM(QUANTITY)    FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK)) MENGE&#13;&#10;	,(SELECT SUM(FINALPRICE)  FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK)) FINALPRICE&#13;&#10;	,(SELECT SUM(RESULT) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND QTOD.BOQ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) MMEMGE&#13;&#10;	,(SELECT COUNT(*) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) ANZAHL&#13;&#10;	,QTOD.FACTOR&#13;&#10;	,QTOD.LINE_TEXT&#13;&#10;	,QTOD.RESULT&#13;&#10;	,QTOD.QTO_LINETYPE_FK&#13;&#10;	,QTOD.REMARK_TEXT&#13;&#10;	,BIL.BILL_NO&#13;&#10;	,BIL.DESCRIPTION &#13;&#10;	,ITEMS.BOQ_TITEL&#13;&#10;	,ITEMS.BOQ_NUMMER&#13;&#10;	FROM ITEMS&#13;&#10;	JOIN QTO_DETAIL QTOD ON QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND QTOD.BOQ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK) --QTOD.BIL_HEADER_FK IS NOT NULL&#13;&#10;	JOIN BIL_HEADER BIL ON BIL.ID = QTOD.BIL_HEADER_FK&#13;&#10;	WHERE ((SELECT COUNT(*) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) &gt; 0)&#13;&#10;	ORDER BY ITEMS.BOQ_ITEM_LEVEL1_FK, ITEMS.BOQ_ITEM_LEVEL2_FK, ITEMS.BOQ_ITEM_LEVEL3_FK, ITEMS.BOQ_ITEM_LEVEL4_FK, ITEMS.BOQ_ITEM_LEVEL5_FK, ITEMS.BOQ_ITEM_LEVEL6_FK, ITEMS.BOQ_ITEM_LEVEL7_FK, ITEMS.BOQ_ITEM_LEVEL8_FK, BIL.BILL_NO&#13;&#10;&#13;&#10;END ELSE BEGIN&#13;&#10;	WITH ITEMS AS (&#13;&#10;		SELECT &#13;&#10;		@BOQ_HEADER_FK PRJ_BOQ_HEADER_FK &#13;&#10;		,BOQH.ID BIL_BOQ_HEADER_FK &#13;&#10;		,BOQI.ID BOQ_ITEM_FK&#13;&#10;		,BOQI.BAS_ITEM_TYPE_FK&#13;&#10;		,BOQI.BOQ_LINE_TYPE_FK&#13;&#10;		,BOQI.REFERENCE&#13;&#10;		,BOQI.BRIEF&#13;&#10;		,BOQI.BOQ_ITEM_LEVEL1_FK, BOQI.BOQ_ITEM_LEVEL2_FK, BOQI.BOQ_ITEM_LEVEL3_FK, BOQI.BOQ_ITEM_LEVEL4_FK, BOQI.BOQ_ITEM_LEVEL5_FK, BOQI.BOQ_ITEM_LEVEL6_FK, BOQI.BOQ_ITEM_LEVEL7_FK, BOQI.BOQ_ITEM_LEVEL8_FK&#13;&#10;		,COALESCE(UOMT.DESCRIPTION, UOM.DESCRIPTION) UOM&#13;&#10;		,(SELECT TOP 1 BOQ_ITEM.BRIEF FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK) BOQ_TITEL&#13;&#10;		,(SELECT TOP 1 BOQ_ITEM.REFERENCE FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = @BOQ_HEADER_FK) BOQ_NUMMER&#13;&#10;		FROM BOQ_HEADER BOQH &#13;&#10;		JOIN BOQ_ITEM BOQI ON BOQI.BOQ_HEADER_FK = BOQH.ID&#13;&#10;		LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;		LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;		WHERE BOQH.ID = @BOQ_HEADER_FK&#13;&#10;	)&#13;&#10;&#13;&#10;	SELECT &#13;&#10;	ITEMS.PRJ_BOQ_HEADER_FK, ITEMS.BIL_BOQ_HEADER_FK, ITEMS.BOQ_ITEM_FK, ITEMS.BAS_ITEM_TYPE_FK,ITEMS.BOQ_LINE_TYPE_FK, ITEMS.REFERENCE, ITEMS.BRIEF, ITEMS.UOM&#13;&#10;	,(SELECT SUM(QUANTITY)    FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK)) MENGE&#13;&#10;	,(SELECT SUM(FINALPRICE)  FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK)) FINALPRICE&#13;&#10;	,(SELECT SUM(RESULT) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND QTOD.BOQ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) MMEMGE&#13;&#10;	,(SELECT COUNT(*) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) ANZAHL&#13;&#10;	,QTOD.FACTOR&#13;&#10;	,QTOD.LINE_TEXT&#13;&#10;	,QTOD.RESULT&#13;&#10;	,QTOD.QTO_LINETYPE_FK&#13;&#10;	,QTOD.REMARK_TEXT&#13;&#10;	,BIL.BILL_NO&#13;&#10;	,BIL.DESCRIPTION &#13;&#10;	,ITEMS.BOQ_TITEL&#13;&#10;	,ITEMS.BOQ_NUMMER&#13;&#10;	FROM ITEMS&#13;&#10;	JOIN QTO_DETAIL QTOD ON QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND QTOD.BOQ_ITEM_FK = ITEMS.BOQ_ITEM_FK AND (QTOD.RESULT &lt;&gt; 0 OR QTOD.QTO_LINETYPE_FK = 2) AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK) --QTOD.BIL_HEADER_FK IS NOT NULL&#13;&#10;	JOIN BIL_HEADER BIL ON BIL.ID = QTOD.BIL_HEADER_FK&#13;&#10;	WHERE ((SELECT COUNT(*) FROM QTO_DETAIL QTOD WHERE QTOD.BOQ_HEADER_FK = ITEMS.PRJ_BOQ_HEADER_FK AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS WHERE BIL_HEADER_FK = QTOD.BIL_HEADER_FK)) &gt; 0)&#13;&#10;	ORDER BY ITEMS.BOQ_ITEM_LEVEL1_FK, ITEMS.BOQ_ITEM_LEVEL2_FK, ITEMS.BOQ_ITEM_LEVEL3_FK, ITEMS.BOQ_ITEM_LEVEL4_FK, ITEMS.BOQ_ITEM_LEVEL5_FK, ITEMS.BOQ_ITEM_LEVEL6_FK, ITEMS.BOQ_ITEM_LEVEL7_FK, ITEMS.BOQ_ITEM_LEVEL8_FK, QTOD.PAGE_NUMBER, QTOD.LINE_REFERENCE, QTOD.LINE_INDEX&#13;&#10;END&#13;&#10;">
        <Column Name="PRJ_BOQ_HEADER_FK" DataType="System.Int32"/>
        <Column Name="BIL_BOQ_HEADER_FK" DataType="System.Int32"/>
        <Column Name="BOQ_ITEM_FK" DataType="System.Int32"/>
        <Column Name="BAS_ITEM_TYPE_FK" DataType="System.Int32"/>
        <Column Name="BOQ_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="UOM" DataType="System.String"/>
        <Column Name="MENGE" DataType="System.Decimal"/>
        <Column Name="FINALPRICE" DataType="System.Decimal"/>
        <Column Name="MMEMGE" DataType="System.Decimal"/>
        <Column Name="FACTOR" DataType="System.Decimal"/>
        <Column Name="LINE_TEXT" DataType="System.String"/>
        <Column Name="RESULT" DataType="System.Decimal"/>
        <Column Name="BILL_NO" DataType="System.String"/>
        <Column Name="DESCRIPTION" DataType="System.String"/>
        <Column Name="BOQ_TITEL" DataType="System.String"/>
        <Column Name="BOQ_NUMMER" DataType="System.String"/>
        <Column Name="ANZAHL" DataType="System.Int32"/>
        <Column Name="QTO_LINETYPE_FK" DataType="System.Int32"/>
        <Column Name="REMARK_TEXT" DataType="System.String"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1013594"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV]" DefaultValue="1030140"/>
        <CommandParameter Name="NULLMENGENZEILENDRUCKEN" DataType="8" Expression="[NullMengenZeilenDrucken]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table17" Alias="BoqKumuliertHeader" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--2. AR&#13;&#10;-- Alle BOQs&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1014188;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1001895;&#13;&#10;--DECLARE @PRINTALLLVSTATUS INT =1&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1011087;&#13;&#10;--DECLARE @ORD_HEADER_FK BIGINT = (SELECT ORD_HEADER_FK FROM BIL_HEADER WHERE ID = @BIL_HEADER_FK);&#13;&#10;--DECLARE @PRJ_PROJECT_FK BIGINT = (SELECT PRJ_PROJECT_FK FROM BIL_HEADER WHERE ID = @BIL_HEADER_FK);&#13;&#10;--DECLARE @BOQ_STATUS_FK_NOT BIGINT = 1000001;&#13;&#10;&#13;&#10;DECLARE @BILLS TABLE (&#13;&#10;	BIL_HEADER_FK BIGINT&#13;&#10;	,BOQ_HEADER_FK_RELV BIGINT&#13;&#10;	,BOQ_HEADER_FK_PRLV BIGINT&#13;&#10;);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) VALUES(@BIL_HEADER_FK, -1, -1);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT BIL_BEFORE_HEADER_FK, -1, -1 FROM BIL_BIL2BIL_F() WHERE BIL_HEADER_FK = @BIL_HEADER_FK;&#13;&#10;&#13;&#10;-- BOQ_HEADER_FK VOM PROJEKT LV&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT -1, BIL_BOQ.BOQ_HEADER_FK, BH.BOQ_HEADER_FK FROM BIL_BOQ, @BILLS X, BOQ_HEADER BH WHERE BIL_BOQ.BIL_HEADER_FK = X.BIL_HEADER_FK AND BH.ID = BIL_BOQ.BOQ_HEADER_FK&#13;&#10;&#13;&#10;&#13;&#10;IF @PRINTALLLVSTATUS = 1&#13;&#10;BEGIN&#13;&#10;	SELECT &#13;&#10;	BOQ_HEADER_FK_PRLV &#13;&#10;	,CAST(BOQ_HEADER_FK_PRLV AS VARCHAR) BOQ_HEADER_FK_PRLV_STRING&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.REFERENCE FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ_HEADER_FK_PRLV) BOQ_NUMMER&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.BRIEF FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ_HEADER_FK_PRLV) BOQ_TITEL&#13;&#10;	FROM @BILLS WHERE BOQ_HEADER_FK_PRLV &lt;&gt; -1 &#13;&#10;	GROUP BY BOQ_HEADER_FK_PRLV&#13;&#10;	ORDER BY BOQ_NUMMER&#13;&#10;END ELSE BEGIN&#13;&#10;	SELECT &#13;&#10;	BOQ_HEADER_FK_PRLV &#13;&#10;	,CAST(BOQ_HEADER_FK_PRLV AS VARCHAR) BOQ_HEADER_FK_PRLV_STRING&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.REFERENCE FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ_HEADER_FK_PRLV) BOQ_NUMMER&#13;&#10;	,(SELECT TOP 1 BOQ_ITEM.BRIEF FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_LINE_TYPE_FK = 103 AND BOQ_ITEM.BOQ_HEADER_FK = BOQ_HEADER_FK_PRLV) BOQ_TITEL&#13;&#10;	FROM @BILLS WHERE BOQ_HEADER_FK_PRLV &lt;&gt; -1 AND NOT EXISTS(SELECT * FROM BOQ_HEADER WHERE BOQ_HEADER.ID = BOQ_HEADER_FK_PRLV AND BOQ_HEADER.BOQ_STATUS_FK = @BOQ_STATUS_FK_NOT)&#13;&#10;	GROUP BY BOQ_HEADER_FK_PRLV&#13;&#10;	ORDER BY BOQ_NUMMER&#13;&#10;END&#13;&#10;&#13;&#10;">
        <Column Name="BOQ_HEADER_FK_PRLV" DataType="System.Int64"/>
        <Column Name="BOQ_NUMMER" DataType="System.String"/>
        <Column Name="BOQ_TITEL" DataType="System.String"/>
        <Column Name="BOQ_HEADER_FK_PRLV_STRING" DataType="System.String"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1014811"/>
        <CommandParameter Name="BOQ_STATUS_FK_NOT" Expression="[BOQ_STATUS_FK_NOT]" DefaultValue="1000005"/>
        <CommandParameter Name="PRINTALLLVSTATUS" DataType="8" Expression="[PrintAllLvStatus]" DefaultValue="1"/>
        <CommandParameter Name="ORD_HEADER_FK" DataType="22"/>
        <CommandParameter Name="PRJ_PROJECT_FK" DataType="22"/>
      </TableDataSource>
      <TableDataSource Name="Table15" Alias="LvKumuliert" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @NULLMENGENZEILENDRUCKEN INT = 0;&#13;&#10;-- LV über alle Rechnungen&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1027470;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1011087;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1011553;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1001895;&#13;&#10;--DECLARE @ORD_HEADER_FK BIGINT = (SELECT ORD_HEADER_FK FROM BIL_HEADER WHERE ID = @BIL_HEADER_FK);&#13;&#10;--DECLARE @PRJ_PROJECT_FK BIGINT = (SELECT PRJ_PROJECT_FK FROM BIL_HEADER WHERE ID = @BIL_HEADER_FK);&#13;&#10;&#13;&#10;DECLARE @BILLS TABLE (&#13;&#10;	BIL_HEADER_FK BIGINT&#13;&#10;	,BOQ_HEADER_FK_RELV BIGINT&#13;&#10;	,BOQ_HEADER_FK_PRLV BIGINT&#13;&#10;);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) VALUES(@BIL_HEADER_FK, -1, -1);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT BIL_BEFORE_HEADER_FK, -1, -1 FROM BIL_BIL2BIL_F() WHERE BIL_HEADER_FK = @BIL_HEADER_FK;&#13;&#10;&#13;&#10;-- BOQ_HEADER_FK vom Projekt LV&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT -1, BIL_BOQ.BOQ_HEADER_FK, bh.BOQ_HEADER_FK FROM BIL_BOQ, @BILLS X, BOQ_HEADER bh WHERE BIL_boq.BIL_HEADER_FK = X.BIL_HEADER_FK and bh.ID = BIL_BOQ.BOQ_HEADER_FK;&#13;&#10;&#13;&#10;DECLARE @FLATLV TABLE (&#13;&#10;	ID BIGINT, &#13;&#10;	REFERENCE VARCHAR(100),&#13;&#10;	BOQ_ITEM_LEVEL1_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL2_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL3_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL4_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL5_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL6_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL7_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL8_FK BIGINT,&#13;&#10;	BRIEF VARCHAR(255),&#13;&#10;	BRIEF_TR BIGINT,&#13;&#10;	PRINT_MARK VARCHAR(10),&#13;&#10;	SORTING INT,&#13;&#10;	BOQ_LINE_TYPE_FK INT,&#13;&#10;	BAS_ITEM_TYPE_FK INT,&#13;&#10;	PRICE NUMERIC(19, 7),&#13;&#10;	BAS_UOM_FK BIGINT,&#13;&#10;	UOM VARCHAR(20),&#13;&#10;	SPECIFICATION_FORMATTED VARBINARY(MAX),&#13;&#10;	SPECIFICATION NVARCHAR(MAX),&#13;&#10;	PRJ_BOQ_HEADER_FK BIGINT,&#13;&#10;	G_FINALPRICE NUMERIC(19, 7),&#13;&#10;	G_QUANTITY NUMERIC(19, 6),&#13;&#10;	HAS_QUANTITY INT,&#13;&#10;	DISCOUNT_ABS_EBENE NUMERIC(19, 7),&#13;&#10;	DISCOUNT_PER_EBENE NUMERIC(19, 7),&#13;&#10;	DISCOUNT_PER_POS NUMERIC(19, 7),&#13;&#10;	FINALDISCOUNT NUMERIC(19, 7)&#13;&#10;)&#13;&#10;&#13;&#10;DECLARE @SORTING INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_1 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_2 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_3 INT = 0; &#13;&#10;DECLARE @CO INT = 0; &#13;&#10;&#13;&#10;DECLARE @ID_L1 BIGINT = 0;&#13;&#10;DECLARE @REFERENCE_L1 VARCHAR(100) = '';&#13;&#10;DECLARE @BOQ_ITEM_LEVEL1_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L1 BIGINT = -1;&#13;&#10;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L2 BIGINT = Null;&#13;&#10;&#13;&#10;DECLARE @BOQ_LINE_TYPE_LEVEL2_L1 INT = 0;&#13;&#10;&#13;&#10;DECLARE @BRIEF_L1 VARCHAR(255) = '';&#13;&#10;DECLARE @BRIEF_TR_L1 BIGINT = 0;&#13;&#10;DECLARE @BAS_UOM_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @BOQ_LINE_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @BAS_ITEM_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @PRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @G_QUANTITY_L1 NUMERIC(19, 6) = 0.0;&#13;&#10;DECLARE @G_FINALPRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @UOM_L1 VARCHAR(20) = ''; &#13;&#10;DECLARE @SPECIFICATION_FORMATTED_L1 VARBINARY(MAX); &#13;&#10;DECLARE @SPECIFICATION_L1 NVARCHAR(MAX);&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK BIGINT = 0;&#13;&#10;DECLARE @DISCOUNT_ABS_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_PER_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_PER_POS NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @FINALDISCOUNT NUMERIC(19, 7) = 0;&#13;&#10;&#13;&#10;DECLARE L1 CURSOR SCROLL FOR &#13;&#10;							SELECT 								&#13;&#10;								BOQI.ID&#13;&#10;								,BOQI.REFERENCE&#13;&#10;								,BOQI.BRIEF&#13;&#10;								,BOQI.BRIEF_TR&#13;&#10;								,BOQI.BOQ_LINE_TYPE_FK&#13;&#10;								,BOQI.BAS_ITEM_TYPE_FK&#13;&#10;								,BOQI.BAS_UOM_FK&#13;&#10;								,COALESCE(UOMT.DESCRIPTION, UOM.DESCRIPTION) UOM&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL1_FK, -1) BOQ_ITEM_LEVEL1_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL2_FK, -1) BOQ_ITEM_LEVEL2_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL3_FK, -1) BOQ_ITEM_LEVEL3_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL4_FK, -1) BOQ_ITEM_LEVEL4_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL5_FK, -1) BOQ_ITEM_LEVEL5_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL6_FK, -1) BOQ_ITEM_LEVEL6_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL7_FK, -1) BOQ_ITEM_LEVEL7_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL8_FK, -1) BOQ_ITEM_LEVEL8_FK&#13;&#10;								,BLOB.CONTENT SPECIFICATION_FORMATTED &#13;&#10;								,PRICE &#13;&#10;								,@BOQ_HEADER_FK PRJ_BOQ_HEADER_FK &#13;&#10;								,(SELECT SUM(QUANTITY)   FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV)) G_QUANTITY&#13;&#10;								,(SELECT SUM(FINALPRICE) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV)) G_FINALPRICE,&#13;&#10;								(SELECT SUM(DISCOUNT) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 DISCOUNT_ABS_EBENE, -- Nachlass absolut &#13;&#10;								(SELECT TOP 1 DISCOUNT_PERCENT_IT FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 DISCOUNT_PER_EBENE, -- Nachlass % &#13;&#10;								(SELECT TOP 1 DISCOUNT_PERCENT FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 DISCOUNT_PER_POS, -- % Position&#13;&#10;								(SELECT SUM(FINALDISCOUNT) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 FINALDISCOUNT&#13;&#10;								FROM BOQ_HEADER BOQH &#13;&#10;								JOIN BOQ_ITEM BOQI ON BOQI.BOQ_HEADER_FK = BOQH.ID&#13;&#10;								LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;								LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;								LEFT JOIN BAS_BLOBS BLOB ON BLOB.ID = BOQI.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;								WHERE BOQH.ID = @BOQ_HEADER_FK&#13;&#10;								ORDER BY DBO.GETSORTABLEREFERENCESTRING(REFERENCE) --  BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;						@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;						@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT&#13;&#10;IF @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LVHEADER', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING + 100000,--  (SELECT COUNT(*) + 10000 FROM BOQ_ITEM WHERE PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1),&#13;&#10;								'LVSUM', SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT&#13;&#10;	END &#13;&#10;END&#13;&#10;&#13;&#10;&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 in (1,2,3,4,5,6,7,8)&#13;&#10;	BEGIN&#13;&#10;		SET @BOQ_ITEM_LEVEL2_FK_L2 = @BOQ_ITEM_LEVEL2_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL3_FK_L2 = @BOQ_ITEM_LEVEL3_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL4_FK_L2 = @BOQ_ITEM_LEVEL4_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL5_FK_L2 = @BOQ_ITEM_LEVEL5_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL6_FK_L2 = @BOQ_ITEM_LEVEL6_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL7_FK_L2 = @BOQ_ITEM_LEVEL7_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL8_FK_L2 = @BOQ_ITEM_LEVEL8_FK_L1;&#13;&#10;	END&#13;&#10;	SET @PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1;&#13;&#10;&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 1&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 2&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 3&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 4&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 5&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 6&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 7&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LEVEL', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'LVHEADER', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;								PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING + 1000000,--  (SELECT COUNT(*) + 10000 FROM BOQ_ITEM WHERE PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1),&#13;&#10;								'LVSUM', SPECIFICATION_FORMATTED, PRICE, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	END ELSE&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							 PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1, @UOM_L1, @SORTING, 'POSITION', @SPECIFICATION_FORMATTED_L1,&#13;&#10;							 @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT);&#13;&#10;	END&#13;&#10;&#13;&#10;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BRIEF_TR_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @BAS_UOM_FK_L1,  @UOM_L1,&#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@SPECIFICATION_FORMATTED_L1, @PRICE_L1, @PRJ_BOQ_HEADER_FK_L1, @G_QUANTITY_L1, @G_FINALPRICE_L1, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE, @DISCOUNT_PER_POS, @FINALDISCOUNT&#13;&#10;&#13;&#10;	IF @@FETCH_STATUS = 0 and @PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1&#13;&#10;	BEGIN&#13;&#10;		IF @BOQ_ITEM_LEVEL8_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL8_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL7_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL7_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL6_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL6_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL5_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL5_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL4_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;		BEGIN&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL4_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL3_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;		BEGIN&#13;&#10;			&#13;&#10;			SET @SORTING = @SORTING + 1;&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL3_FK_L2;&#13;&#10;		END&#13;&#10;		IF @BOQ_ITEM_LEVEL2_FK_L2 &lt;&gt; @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;		BEGIN&#13;&#10;			INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;									PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;							SELECT ID, &#13;&#10;									BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;									REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;									'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;									FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL2_FK_L2;&#13;&#10;		END&#13;&#10;	END&#13;&#10;&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;IF @BOQ_ITEM_LEVEL8_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL8_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL7_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL7_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL6_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL6_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL5_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL5_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL4_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL4_FK_L2;&#13;&#10;END&#13;&#10;IF @BOQ_ITEM_LEVEL3_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL3_FK_L2;&#13;&#10;END&#13;&#10;&#13;&#10;IF @BOQ_ITEM_LEVEL2_FK_L1 &lt;&gt; -1&#13;&#10;BEGIN&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;	INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, SORTING, PRINT_MARK, SPECIFICATION_FORMATTED,&#13;&#10;							PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT) &#13;&#10;					SELECT ID, &#13;&#10;							BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							REFERENCE, BRIEF, BRIEF_TR, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, BAS_UOM_FK, UOM, @SORTING,&#13;&#10;							'ESUM', SPECIFICATION_FORMATTED, PRICE, PRJ_BOQ_HEADER_FK, G_QUANTITY, G_FINALPRICE, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE, DISCOUNT_PER_POS, FINALDISCOUNT&#13;&#10;							FROM @FLATLV WHERE ID = @BOQ_ITEM_LEVEL2_FK_L2;&#13;&#10;END&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK&#13;&#10;							FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;							GROUP BY BOQ_ITEM_LEVEL1_FK&#13;&#10;							ORDER BY BOQ_ITEM_LEVEL1_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 1 WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 2 WHERE BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND BOQ_ITEM_LEVEL3_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 3 WHERE BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 4 WHERE BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 5 WHERE BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 6 WHERE BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 7 WHERE BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_QUANTITY) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 8 WHERE BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;IF @NULLMENGENZEILENDRUCKEN = 1&#13;&#10;BEGIN&#13;&#10;	SELECT * FROM @FLATLV ORDER BY PRJ_BOQ_HEADER_FK, SORTING &#13;&#10;END ELSE BEGIN&#13;&#10;	SELECT * FROM @FLATLV WHERE HAS_QUANTITY &gt; 0 ORDER BY PRJ_BOQ_HEADER_FK, SORTING &#13;&#10;END&#13;&#10;&#13;&#10;">
        <Column Name="ID" DataType="System.Int64"/>
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="BOQ_ITEM_LEVEL1_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL2_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL3_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL4_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL5_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL6_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL7_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL8_FK" DataType="System.Int64"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="BRIEF_TR" DataType="System.Int64"/>
        <Column Name="PRINT_MARK" DataType="System.String"/>
        <Column Name="SORTING" DataType="System.Int32"/>
        <Column Name="BOQ_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="BAS_ITEM_TYPE_FK" DataType="System.Int32"/>
        <Column Name="PRICE" DataType="System.Decimal"/>
        <Column Name="BAS_UOM_FK" DataType="System.Int64"/>
        <Column Name="UOM" DataType="System.String"/>
        <Column Name="SPECIFICATION_FORMATTED" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="SPECIFICATION" DataType="System.String"/>
        <Column Name="PRJ_BOQ_HEADER_FK" DataType="System.Int64"/>
        <Column Name="G_FINALPRICE" DataType="System.Decimal"/>
        <Column Name="G_QUANTITY" DataType="System.Decimal"/>
        <Column Name="HAS_QUANTITY" DataType="System.Int32"/>
        <Column Name="DISCOUNT_ABS_EBENE" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_PER_EBENE" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_PER_POS" DataType="System.Decimal"/>
        <Column Name="FINALDISCOUNT" DataType="System.Decimal"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV]" DefaultValue="1024993"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1011652"/>
        <CommandParameter Name="NULLMENGENZEILENDRUCKEN" DataType="8" Expression="[NullMengenZeilenDrucken]" DefaultValue="1"/>
        <CommandParameter Name="ORD_HEADER_FK" DataType="22"/>
        <CommandParameter Name="PRJ_PROJECT_FK" DataType="22"/>
      </TableDataSource>
      <TableDataSource Name="Table16" Alias="LvKumuliertSummen" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="--DECLARE @NULLMENGENZEILENDRUCKEN INT = 0;&#13;&#10;-- Zusammenfassung&#13;&#10;--DECLARE @BOQ_HEADER_FK BIGINT = 1034429;&#13;&#10;--DECLARE @BIL_HEADER_FK BIGINT = 1015791;&#13;&#10;&#13;&#10;DECLARE @BILLS TABLE (&#13;&#10;	BIL_HEADER_FK BIGINT&#13;&#10;	,BOQ_HEADER_FK_RELV BIGINT&#13;&#10;	,BOQ_HEADER_FK_PRLV BIGINT&#13;&#10;);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) VALUES(@BIL_HEADER_FK, -1, -1);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT BIL_BEFORE_HEADER_FK, -1, -1 FROM BIL_BIL2BIL_F() WHERE BIL_HEADER_FK = @BIL_HEADER_FK;&#13;&#10;---- BOQ_HEADER_FK vom Projekt LV&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT -1, BIL_BOQ.BOQ_HEADER_FK, bh.BOQ_HEADER_FK FROM BIL_BOQ, @BILLS X, BOQ_HEADER bh WHERE BIL_boq.BIL_HEADER_FK = X.BIL_HEADER_FK and bh.ID = BIL_BOQ.BOQ_HEADER_FK;&#13;&#10;&#13;&#10;DECLARE @FLATLV TABLE (&#13;&#10;	ID BIGINT, &#13;&#10;	REFERENCE VARCHAR(100),&#13;&#10;	BOQ_ITEM_LEVEL1_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL2_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL3_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL4_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL5_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL6_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL7_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL8_FK BIGINT,&#13;&#10;	BRIEF VARCHAR(255),&#13;&#10;	PRINT_MARK VARCHAR(10),&#13;&#10;	SORTING INT,&#13;&#10;	BOQ_LINE_TYPE_FK INT,&#13;&#10;	BAS_ITEM_TYPE_FK INT,&#13;&#10;	PRJ_BOQ_HEADER_FK BIGINT,&#13;&#10;	G_FINALPRICE NUMERIC(19, 7),&#13;&#10;	HAS_QUANTITY INT,&#13;&#10;	FINALDISCOUNT NUMERIC(19, 7),&#13;&#10;	DISCOUNT_ABS_EBENE NUMERIC(19, 7),&#13;&#10;	DISCOUNT_PER_EBENE NUMERIC(19, 7)&#13;&#10;)&#13;&#10;&#13;&#10;DECLARE @SORTING INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_1 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_2 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_3 INT = 0; &#13;&#10;DECLARE @CO INT = 0; &#13;&#10;&#13;&#10;DECLARE @ID_L1 BIGINT = 0;&#13;&#10;DECLARE @REFERENCE_L1 VARCHAR(100) = '';&#13;&#10;DECLARE @BOQ_ITEM_LEVEL1_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L1 BIGINT = -1;&#13;&#10;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L2 BIGINT = Null;&#13;&#10;&#13;&#10;DECLARE @BOQ_LINE_TYPE_LEVEL2_L1 INT = 0;&#13;&#10;&#13;&#10;DECLARE @BRIEF_L1 VARCHAR(255) = '';&#13;&#10;DECLARE @BOQ_LINE_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @BAS_ITEM_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @G_FINALPRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @FINALDISCOUNT NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_ABS_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @DISCOUNT_PER_EBENE NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK BIGINT = 0;&#13;&#10;&#13;&#10;DECLARE L1 CURSOR SCROLL FOR &#13;&#10;							SELECT 								&#13;&#10;								BOQI.ID&#13;&#10;								,BOQI.REFERENCE&#13;&#10;								,BOQI.BRIEF&#13;&#10;								,BOQI.BOQ_LINE_TYPE_FK&#13;&#10;								,BOQI.BAS_ITEM_TYPE_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL1_FK, -1) BOQ_ITEM_LEVEL1_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL2_FK, -1) BOQ_ITEM_LEVEL2_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL3_FK, -1) BOQ_ITEM_LEVEL3_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL4_FK, -1) BOQ_ITEM_LEVEL4_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL5_FK, -1) BOQ_ITEM_LEVEL5_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL6_FK, -1) BOQ_ITEM_LEVEL6_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL7_FK, -1) BOQ_ITEM_LEVEL7_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL8_FK, -1) BOQ_ITEM_LEVEL8_FK&#13;&#10;								,PBOQ.BOQ_HEADER_FK PRJ_BOQ_HEADER_FK &#13;&#10;								,(SELECT SUM(FINALPRICE) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV)) G_FINALPRICE&#13;&#10;								,(SELECT SUM(FINALDISCOUNT) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 FINALDISCOUNT&#13;&#10;								,(SELECT SUM(DISCOUNT) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 DISCOUNT_ABS_EBENE&#13;&#10;								,(SELECT TOP 1 DISCOUNT_PERCENT_IT FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV))*-1 DISCOUNT_PER_EBENE&#13;&#10;								FROM PRJ_BOQ PBOQ&#13;&#10;								JOIN BOQ_HEADER BOQH ON BOQH.ID = PBOQ.BOQ_HEADER_FK&#13;&#10;								JOIN BOQ_ITEM BOQI ON BOQI.BOQ_HEADER_FK = BOQH.ID&#13;&#10;								LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;								LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;								LEFT JOIN BAS_BLOBS BLOB ON BLOB.ID = BOQI.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;								WHERE PBOQ.BOQ_HEADER_FK = @BOQ_HEADER_FK AND BOQI.BOQ_LINE_TYPE_FK IN (103,1,2,3,4,5,6,7,8)&#13;&#10;								ORDER BY DBO.GETSORTABLEREFERENCESTRING(REFERENCE) --  BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;						@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;						@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE&#13;&#10;&#13;&#10;&#13;&#10;IF @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK, PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LVHEADER', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK, PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, @SORTING + 100000, 'LVSUM', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE&#13;&#10;	END &#13;&#10;END&#13;&#10;&#13;&#10;&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 in (1,2,3,4,5,6,7,8)&#13;&#10;	BEGIN&#13;&#10;		SET @BOQ_ITEM_LEVEL2_FK_L2 = @BOQ_ITEM_LEVEL2_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL3_FK_L2 = @BOQ_ITEM_LEVEL3_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL4_FK_L2 = @BOQ_ITEM_LEVEL4_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL5_FK_L2 = @BOQ_ITEM_LEVEL5_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL6_FK_L2 = @BOQ_ITEM_LEVEL6_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL7_FK_L2 = @BOQ_ITEM_LEVEL7_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL8_FK_L2 = @BOQ_ITEM_LEVEL8_FK_L1;&#13;&#10;	END&#13;&#10;	SET @PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1;&#13;&#10;&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 1&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 2&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 3&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 4&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 5&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 6&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 7&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LVHEADER', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE, FINALDISCOUNT, DISCOUNT_ABS_EBENE, DISCOUNT_PER_EBENE) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, @SORTING + 1000000,&#13;&#10;								'LVSUM', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	END &#13;&#10;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1, @FINALDISCOUNT, @DISCOUNT_ABS_EBENE, @DISCOUNT_PER_EBENE&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK&#13;&#10;							FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;							GROUP BY BOQ_ITEM_LEVEL1_FK&#13;&#10;							ORDER BY BOQ_ITEM_LEVEL1_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 1 WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 2 WHERE BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND BOQ_ITEM_LEVEL3_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 3 WHERE BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 4 WHERE BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 5 WHERE BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 6 WHERE BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 7 WHERE BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 8 WHERE BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;&#13;&#10;IF @NULLMENGENZEILENDRUCKEN = 1&#13;&#10;BEGIN&#13;&#10;	SELECT * FROM @FLATLV ORDER BY PRJ_BOQ_HEADER_FK, SORTING &#13;&#10;END ELSE BEGIN&#13;&#10;	SELECT * FROM @FLATLV WHERE HAS_QUANTITY &gt; 0 ORDER BY PRJ_BOQ_HEADER_FK, SORTING &#13;&#10;END&#13;&#10;">
        <Column Name="ID" DataType="System.Int64"/>
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="BOQ_ITEM_LEVEL1_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL2_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL3_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL4_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL5_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL6_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL7_FK" DataType="System.Int64"/>
        <Column Name="BOQ_ITEM_LEVEL8_FK" DataType="System.Int64"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="PRINT_MARK" DataType="System.String"/>
        <Column Name="SORTING" DataType="System.Int32"/>
        <Column Name="BOQ_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="BAS_ITEM_TYPE_FK" DataType="System.Int32"/>
        <Column Name="PRJ_BOQ_HEADER_FK" DataType="System.Int64"/>
        <Column Name="G_FINALPRICE" DataType="System.Decimal"/>
        <Column Name="HAS_QUANTITY" DataType="System.Int32"/>
        <Column Name="FINALDISCOUNT" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_ABS_EBENE" DataType="System.Decimal"/>
        <Column Name="DISCOUNT_PER_EBENE" DataType="System.Decimal"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1011553"/>
        <CommandParameter Name="BOQ_HEADER_FK" Expression="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV]" DefaultValue="1024989"/>
        <CommandParameter Name="NULLMENGENZEILENDRUCKEN" DataType="8" Expression="[NullMengenZeilenDrucken]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table7" Alias="LvKumuliertInhaltsverzeichnis" DataType="System.Int32" Enabled="true" SelectCommand="--DECLARE @BIL_HEADER_FK BIGINT = 1016140;&#13;&#10;--DECLARE @BOQ_STATUS_FK_NOT BIGINT = 1000005;&#13;&#10;--DECLARE @PRINTALLLVSTATUS INT = 1;&#13;&#10;&#13;&#10;DECLARE @BILLS TABLE (&#13;&#10;	BIL_HEADER_FK BIGINT&#13;&#10;	,BOQ_HEADER_FK_RELV BIGINT&#13;&#10;	,BOQ_HEADER_FK_PRLV BIGINT&#13;&#10;);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) VALUES(@BIL_HEADER_FK, -1, -1);&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT BIL_BEFORE_HEADER_FK, -1, -1 FROM BIL_BIL2BIL_F() WHERE BIL_HEADER_FK = @BIL_HEADER_FK;&#13;&#10;&#13;&#10;-- BOQ_HEADER_FK VOM PROJEKT LV&#13;&#10;INSERT INTO @BILLS (BIL_HEADER_FK, BOQ_HEADER_FK_RELV, BOQ_HEADER_FK_PRLV) SELECT -12, BIL_BOQ.BOQ_HEADER_FK, BH.BOQ_HEADER_FK FROM BIL_BOQ, @BILLS X, BOQ_HEADER BH WHERE BIL_BOQ.BIL_HEADER_FK = X.BIL_HEADER_FK AND BH.ID = BIL_BOQ.BOQ_HEADER_FK;&#13;&#10;&#13;&#10;IF @PRINTALLLVSTATUS &lt;&gt; 1&#13;&#10;BEGIN&#13;&#10;	DELETE FROM @BILLS WHERE EXISTS(SELECT * FROM BOQ_HEADER WHERE BOQ_HEADER.ID = BOQ_HEADER_FK_PRLV AND BOQ_HEADER.BOQ_STATUS_FK = @BOQ_STATUS_FK_NOT)&#13;&#10;END&#13;&#10;&#13;&#10;DECLARE @FLATLV TABLE (&#13;&#10;	ID BIGINT, &#13;&#10;	REFERENCE VARCHAR(100),&#13;&#10;	BOQ_ITEM_LEVEL1_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL2_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL3_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL4_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL5_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL6_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL7_FK BIGINT,&#13;&#10;	BOQ_ITEM_LEVEL8_FK BIGINT,&#13;&#10;	BRIEF VARCHAR(255),&#13;&#10;	PRINT_MARK VARCHAR(10),&#13;&#10;	SORTING INT,&#13;&#10;	BOQ_LINE_TYPE_FK INT,&#13;&#10;	BAS_ITEM_TYPE_FK INT,&#13;&#10;	PRJ_BOQ_HEADER_FK BIGINT,&#13;&#10;	G_FINALPRICE NUMERIC(19, 7),&#13;&#10;	HAS_QUANTITY INT&#13;&#10;)&#13;&#10;&#13;&#10;DECLARE @SORTING INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_1 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_2 INT = 0; &#13;&#10;DECLARE @SORTING_LEVEL_3 INT = 0; &#13;&#10;DECLARE @CO INT = 0; &#13;&#10;&#13;&#10;DECLARE @ID_L1 BIGINT = 0;&#13;&#10;DECLARE @REFERENCE_L1 VARCHAR(100) = '';&#13;&#10;DECLARE @BOQ_ITEM_LEVEL1_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L1 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L1 BIGINT = -1;&#13;&#10;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL2_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL3_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL4_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL5_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL6_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL7_FK_L2 BIGINT = Null;&#13;&#10;DECLARE @BOQ_ITEM_LEVEL8_FK_L2 BIGINT = Null;&#13;&#10;&#13;&#10;DECLARE @BOQ_LINE_TYPE_LEVEL2_L1 INT = 0;&#13;&#10;&#13;&#10;DECLARE @BRIEF_L1 VARCHAR(255) = '';&#13;&#10;DECLARE @BOQ_LINE_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @BAS_ITEM_TYPE_FK_L1 INT = 0;&#13;&#10;DECLARE @G_FINALPRICE_L1 NUMERIC(19, 7) = 0;&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK_L1 BIGINT = 0;&#13;&#10;DECLARE @PRJ_BOQ_HEADER_FK BIGINT = 0;&#13;&#10;&#13;&#10;DECLARE L1 CURSOR SCROLL FOR &#13;&#10;							SELECT 								&#13;&#10;								BOQI.ID&#13;&#10;								,BOQI.REFERENCE&#13;&#10;								,BOQI.BRIEF&#13;&#10;								,BOQI.BOQ_LINE_TYPE_FK&#13;&#10;								,BOQI.BAS_ITEM_TYPE_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL1_FK, -1) BOQ_ITEM_LEVEL1_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL2_FK, -1) BOQ_ITEM_LEVEL2_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL3_FK, -1) BOQ_ITEM_LEVEL3_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL4_FK, -1) BOQ_ITEM_LEVEL4_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL5_FK, -1) BOQ_ITEM_LEVEL5_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL6_FK, -1) BOQ_ITEM_LEVEL6_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL7_FK, -1) BOQ_ITEM_LEVEL7_FK&#13;&#10;								,COALESCE(BOQ_ITEM_LEVEL8_FK, -1) BOQ_ITEM_LEVEL8_FK&#13;&#10;								,PBOQ.BOQ_HEADER_FK PRJ_BOQ_HEADER_FK &#13;&#10;								,(SELECT SUM(FINALPRICE) FROM BOQ_ITEM WHERE BOQ_ITEM.BOQ_ITEM_PRJ_ITEM_FK = BOQI.ID AND EXISTS(SELECT BIL_HEADER_FK FROM @BILLS X WHERE BOQ_ITEM.BOQ_HEADER_FK = X.BOQ_HEADER_FK_RELV)) G_FINALPRICE&#13;&#10;								FROM PRJ_BOQ PBOQ&#13;&#10;								JOIN BOQ_HEADER BOQH ON BOQH.ID = PBOQ.BOQ_HEADER_FK&#13;&#10;								JOIN BOQ_ITEM BOQI ON BOQI.BOQ_HEADER_FK = BOQH.ID&#13;&#10;								LEFT JOIN BAS_UOM UOM ON UOM.ID = BOQI.BAS_UOM_FK&#13;&#10;								LEFT JOIN BAS_TRANSLATION UOMT ON UOMT.ID = UOM.UOM_TR AND UOMT.BAS_LANGUAGE_FK = 2&#13;&#10;								LEFT JOIN BAS_BLOBS BLOB ON BLOB.ID = BOQI.BAS_BLOBS_SPECIFICATION_FK&#13;&#10;								WHERE PBOQ.BOQ_HEADER_FK in (SELECT BOQ_HEADER_FK_PRLV FROM @BILLS WHERE BOQ_HEADER_FK_PRLV &lt;&gt; -1 GROUP BY BOQ_HEADER_FK_PRLV) AND BOQI.BOQ_LINE_TYPE_FK IN (103,1,2,3,4,5,6,7,8)&#13;&#10;								ORDER BY DBO.GETSORTABLEREFERENCESTRING(REFERENCE) -- BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;						@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;						@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1&#13;&#10;&#13;&#10;IF @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK, PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LVHEADER', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK, PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, @SORTING + 100000, 'LVSUM', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1&#13;&#10;	END &#13;&#10;END&#13;&#10;&#13;&#10;&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 in (1,2,3,4,5,6,7,8)&#13;&#10;	BEGIN&#13;&#10;		SET @BOQ_ITEM_LEVEL2_FK_L2 = @BOQ_ITEM_LEVEL2_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL3_FK_L2 = @BOQ_ITEM_LEVEL3_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL4_FK_L2 = @BOQ_ITEM_LEVEL4_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL5_FK_L2 = @BOQ_ITEM_LEVEL5_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL6_FK_L2 = @BOQ_ITEM_LEVEL6_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL7_FK_L2 = @BOQ_ITEM_LEVEL7_FK_L1;&#13;&#10;		SET @BOQ_ITEM_LEVEL8_FK_L2 = @BOQ_ITEM_LEVEL8_FK_L1;&#13;&#10;	END&#13;&#10;	SET @PRJ_BOQ_HEADER_FK = @PRJ_BOQ_HEADER_FK_L1;&#13;&#10;&#13;&#10;	SET @SORTING = @SORTING + 1;&#13;&#10;&#13;&#10;	IF @BOQ_LINE_TYPE_FK_L1 = 1&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 2&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 3&#13;&#10;	BEGIN&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 4&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 5&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 6&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 7&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LEVEL', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;	END ELSE IF @BOQ_LINE_TYPE_FK_L1 = 103&#13;&#10;	BEGIN&#13;&#10;		SET @SORTING = @SORTING + 1;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;					 VALUES (@ID_L1, &#13;&#10;							 @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1, &#13;&#10;							 @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, @SORTING, 'LVHEADER', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1);&#13;&#10;&#13;&#10;		INSERT INTO @FLATLV (ID, &#13;&#10;							 BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;							 REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, SORTING, PRINT_MARK,&#13;&#10;							 PRJ_BOQ_HEADER_FK, G_FINALPRICE) &#13;&#10;						SELECT ID, &#13;&#10;								BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK, &#13;&#10;								REFERENCE, BRIEF, BOQ_LINE_TYPE_FK, BAS_ITEM_TYPE_FK, @SORTING + 1000000,&#13;&#10;								'LVSUM', @PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1;&#13;&#10;	END &#13;&#10;&#13;&#10;	FETCH NEXT FROM L1 INTO @ID_L1, @REFERENCE_L1, @BRIEF_L1, @BOQ_LINE_TYPE_FK_L1, @BAS_ITEM_TYPE_FK_L1, &#13;&#10;							@BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1,&#13;&#10;							@PRJ_BOQ_HEADER_FK_L1, @G_FINALPRICE_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK&#13;&#10;							FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1&#13;&#10;							GROUP BY BOQ_ITEM_LEVEL1_FK&#13;&#10;							ORDER BY BOQ_ITEM_LEVEL1_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 1 WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL2_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 2 WHERE BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL3_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND BOQ_ITEM_LEVEL3_FK &lt;&gt; -1 AND BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 3 WHERE BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL4_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 4 WHERE BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL5_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 5 WHERE BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL6_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 6 WHERE BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL7_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 7 WHERE BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;UPDATE @FLATLV SET HAS_QUANTITY = 0 WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;DECLARE L1 CURSOR SCROLL FOR SELECT BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								FROM @FLATLV WHERE BOQ_ITEM_LEVEL8_FK &lt;&gt; -1&#13;&#10;								GROUP BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;								ORDER BY BOQ_ITEM_LEVEL1_FK, BOQ_ITEM_LEVEL2_FK, BOQ_ITEM_LEVEL3_FK, BOQ_ITEM_LEVEL4_FK, BOQ_ITEM_LEVEL5_FK, BOQ_ITEM_LEVEL6_FK, BOQ_ITEM_LEVEL7_FK, BOQ_ITEM_LEVEL8_FK&#13;&#10;OPEN L1&#13;&#10;FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;WHILE @@FETCH_STATUS = 0&#13;&#10;BEGIN&#13;&#10;	IF (SELECT SUM(G_FINALPRICE) FROM @FLATLV WHERE BOQ_ITEM_LEVEL1_FK = @BOQ_ITEM_LEVEL1_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL2_FK = @BOQ_ITEM_LEVEL2_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL3_FK = @BOQ_ITEM_LEVEL3_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL4_FK = @BOQ_ITEM_LEVEL4_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL5_FK = @BOQ_ITEM_LEVEL5_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL6_FK = @BOQ_ITEM_LEVEL6_FK_L1 AND &#13;&#10;											    BOQ_ITEM_LEVEL7_FK = @BOQ_ITEM_LEVEL7_FK_L1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK &lt;&gt; -1 AND &#13;&#10;												BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1) &gt; 0&#13;&#10;	BEGIN&#13;&#10;		UPDATE @FLATLV SET HAS_QUANTITY = 8 WHERE BOQ_ITEM_LEVEL8_FK = @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;	END&#13;&#10;	FETCH NEXT FROM L1 INTO @BOQ_ITEM_LEVEL1_FK_L1, @BOQ_ITEM_LEVEL2_FK_L1, @BOQ_ITEM_LEVEL3_FK_L1, @BOQ_ITEM_LEVEL4_FK_L1, @BOQ_ITEM_LEVEL5_FK_L1, @BOQ_ITEM_LEVEL6_FK_L1, @BOQ_ITEM_LEVEL7_FK_L1, @BOQ_ITEM_LEVEL8_FK_L1&#13;&#10;END&#13;&#10;CLOSE L1;&#13;&#10;DEALLOCATE L1;&#13;&#10;&#13;&#10;&#13;&#10;SELECT REFERENCE, PRJ_BOQ_HEADER_FK,BRIEF, G_FINALPRICE FROM @FLATLV where PRINT_MARK = 'LVHEADER' ORDER BY PRJ_BOQ_HEADER_FK, SORTING &#13;&#10;">
        <Column Name="REFERENCE" DataType="System.String"/>
        <Column Name="PRJ_BOQ_HEADER_FK" DataType="System.Int64"/>
        <Column Name="BRIEF" DataType="System.String"/>
        <Column Name="G_FINALPRICE" DataType="System.Decimal"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1015976"/>
        <CommandParameter Name="BOQ_STATUS_FK_NOT" Expression="[BOQ_STATUS_FK_NOT]" DefaultValue="1000005"/>
        <CommandParameter Name="PrintAllLvStatus" DataType="8" Expression="[PrintAllLvStatus]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table18" Alias="Generalien" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="SELECT &#13;&#10;COALESCE(GENTT.DESCRIPTION, GENT.DESCRIPTION) TYPE&#13;&#10;,GENT.ISPERCENT&#13;&#10;,CON.COMMENT_TEXT KOMMENTAR&#13;&#10;,CON.VALUE WERT&#13;&#10;FROM BIL_GENERALS CON&#13;&#10;LEFT JOIN PRC_GENERALSTYPE GENT ON GENT.ID = CON.PRC_GENERALSTYPE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION GENTT ON GENTT.ID = GENT.DESCRIPTION_TR AND GENTT.BAS_LANGUAGE_FK = 2&#13;&#10;WHERE CON.BIL_HEADER_FK = @BIL_HEADER_FK&#13;&#10;ORDER BY GENT.SORTING&#13;&#10;">
        <Column Name="TYPE" DataType="System.String"/>
        <Column Name="KOMMENTAR" DataType="System.String"/>
        <Column Name="WERT" DataType="System.Decimal"/>
        <Column Name="ISPERCENT" DataType="System.Boolean" BindableControl="CheckBox"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table19" Alias="Geschaeftspartner" DataType="System.Int32" Enabled="true" SelectCommand="--DECLARE @BIL_HEADER_FK BIGINT = 1000015;&#13;&#10;&#13;&#10;SELECT&#13;&#10;B.BP_NAME1 BP_NAME1&#13;&#10;,B.BP_NAME2 BP_NAME2&#13;&#10;,S.DESCRIPTION SUBSIDIARY_NAME&#13;&#10;,A.STREET STREET&#13;&#10;,A.ZIPCODE ZIPCODE&#13;&#10;,A.CITY&#13;&#10;,C.TITLE&#13;&#10;,C.FIRST_NAME&#13;&#10;,C.FAMILY_NAME&#13;&#10;,BT.SALUTATION_TR BP_FULL_LETTER_SALUTATION_TR&#13;&#10;,BT.SALUTATION BP_FULL_LETTER_SALUTATION&#13;&#10;,TIT.DESCRIPTION_TR SALUTATION_TR&#13;&#10;,TIT.DESCRIPTION SALUTATION&#13;&#10;,TIT.ADDRESS_TITLE_TR LETTER_SALUTATION_TR&#13;&#10;,TIT.ADDRESS_TITLE LETTER_SALUTATION&#13;&#10;,TIT.SALUTATION_TR FULL_LETTER_SALUTATION_TR&#13;&#10;,TIT.SALUTATION FULL_LETTER_SALUTATION&#13;&#10;,COU.DESCRIPTION COUNTRY&#13;&#10;,COU.DESCRIPTION_TR COUNTRY_TR&#13;&#10;&#13;&#10;,BR.BP_NAME1 BP_NAME1_REG&#13;&#10;,BR.BP_NAME2 BP_NAME2_REG&#13;&#10;,SR.DESCRIPTION SUBSIDIARY_NAME_REG&#13;&#10;,AR.STREET STREET_REG&#13;&#10;,AR.ZIPCODE ZIPCODE_REG&#13;&#10;,AR.CITY CITY_REG&#13;&#10;,CR.TITLE TITLE_REG&#13;&#10;,CR.FIRST_NAME FIRST_NAME_REG&#13;&#10;,CR.FAMILY_NAME FAMILY_NAME_REG&#13;&#10;,BTR.SALUTATION_TR BP_FULL_LETTER_SALUTATION_TR_REG&#13;&#10;,BTR.SALUTATION BP_FULL_LETTER_SALUTATION_REG&#13;&#10;,TITR.DESCRIPTION_TR SALUTATION_TR_REG&#13;&#10;,TITR.DESCRIPTION SALUTATION_REG&#13;&#10;,TITR.ADDRESS_TITLE_TR LETTER_SALUTATION_TR_REG&#13;&#10;,TITR.ADDRESS_TITLE LETTER_SALUTATION_REG&#13;&#10;,TITR.SALUTATION_TR FULL_LETTER_SALUTATION_TR_REG&#13;&#10;,TITR.SALUTATION FULL_LETTER_SALUTATION_REG&#13;&#10;,COUR.DESCRIPTION COUNTRY_REG&#13;&#10;,COUR.DESCRIPTION_TR COUNTRY_TR_REG&#13;&#10;&#13;&#10;FROM BIL_HEADER BIL&#13;&#10;LEFT JOIN BPD_BUSINESSPARTNER B ON B.ID = BIL.BPD_BUSINESSPARTNER_FK&#13;&#10;LEFT JOIN BAS_TITLE BT ON BT.ID = B.BAS_TITLE_FK&#13;&#10;LEFT JOIN BPD_SUBSIDIARY S ON S.ID = BIL.BPD_SUBSIDIARY_FK&#13;&#10;LEFT JOIN BAS_ADDRESS A ON A.ID = S.BAS_ADDRESS_FK&#13;&#10;LEFT JOIN BPD_CONTACT C ON C.ID = BIL.BPD_CONTACT_FK&#13;&#10;LEFT JOIN BAS_TITLE TIT ON TIT.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_COUNTRY COU ON COU.ID = A.BAS_COUNTRY_FK&#13;&#10;LEFT JOIN BPD_BUSINESSPARTNER BR ON BR.ID = BIL.BPD_BUSINESSPARTNER_BILLTO_FK&#13;&#10;LEFT JOIN BAS_TITLE BTR ON BTR.ID = BR.BAS_TITLE_FK&#13;&#10;LEFT JOIN BPD_SUBSIDIARY SR ON SR.ID = BIL.BPD_SUBSIDIARY_BILLTO_FK&#13;&#10;LEFT JOIN BAS_ADDRESS AR ON AR.ID = SR.BAS_ADDRESS_FK&#13;&#10;LEFT JOIN BPD_CONTACT CR ON CR.ID = BIL.BPD_CONTACT_BILLTO_FK&#13;&#10;LEFT JOIN BAS_TITLE TITR ON TITR.ID = CR.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_COUNTRY COUR ON COUR.ID = AR.BAS_COUNTRY_FK&#13;&#10;WHERE Bil.ID = @BIL_HEADER_FK &#13;&#10;&#13;&#10;">
        <Column Name="BP_NAME1" DataType="System.String"/>
        <Column Name="BP_NAME2" DataType="System.String"/>
        <Column Name="SUBSIDIARY_NAME" DataType="System.String"/>
        <Column Name="STREET" DataType="System.String"/>
        <Column Name="ZIPCODE" DataType="System.String"/>
        <Column Name="CITY" DataType="System.String"/>
        <Column Name="TITLE" DataType="System.String"/>
        <Column Name="FIRST_NAME" DataType="System.String"/>
        <Column Name="FAMILY_NAME" DataType="System.String"/>
        <Column Name="BP_FULL_LETTER_SALUTATION_TR" DataType="System.Int32"/>
        <Column Name="BP_FULL_LETTER_SALUTATION" DataType="System.String"/>
        <Column Name="SALUTATION_TR" DataType="System.Int32"/>
        <Column Name="SALUTATION" DataType="System.String"/>
        <Column Name="LETTER_SALUTATION_TR" DataType="System.Int32"/>
        <Column Name="LETTER_SALUTATION" DataType="System.String"/>
        <Column Name="FULL_LETTER_SALUTATION_TR" DataType="System.Int32"/>
        <Column Name="FULL_LETTER_SALUTATION" DataType="System.String"/>
        <Column Name="COUNTRY" DataType="System.String"/>
        <Column Name="COUNTRY_TR" DataType="System.Int32"/>
        <Column Name="BP_NAME1_REG" DataType="System.String"/>
        <Column Name="BP_NAME2_REG" DataType="System.String"/>
        <Column Name="SUBSIDIARY_NAME_REG" DataType="System.String"/>
        <Column Name="STREET_REG" DataType="System.String"/>
        <Column Name="ZIPCODE_REG" DataType="System.String"/>
        <Column Name="CITY_REG" DataType="System.String"/>
        <Column Name="TITLE_REG" DataType="System.String"/>
        <Column Name="BP_FULL_LETTER_SALUTATION_TR_REG" DataType="System.Int32"/>
        <Column Name="BP_FULL_LETTER_SALUTATION_REG" DataType="System.String"/>
        <Column Name="SALUTATION_TR_REG" DataType="System.Int32"/>
        <Column Name="SALUTATION_REG" DataType="System.String"/>
        <Column Name="LETTER_SALUTATION_TR_REG" DataType="System.Int32"/>
        <Column Name="LETTER_SALUTATION_REG" DataType="System.String"/>
        <Column Name="FULL_LETTER_SALUTATION_TR_REG" DataType="System.Int32"/>
        <Column Name="FULL_LETTER_SALUTATION_REG" DataType="System.String"/>
        <Column Name="COUNTRY_REG" DataType="System.String"/>
        <Column Name="COUNTRY_TR_REG" DataType="System.Int32"/>
        <Column Name="FAMILY_NAME_REG" DataType="System.String"/>
        <Column Name="FIRST_NAME_REG" DataType="System.String"/>
        <CommandParameter Name="BIL_HEADER_FK" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table20" Alias="Clerk" DataType="System.Int32" Enabled="true" SelectCommand="--DECLARE @BILID INT&#13;&#10;DECLARE @CLERK_ID BIGINT = -1;&#13;&#10;&#13;&#10;SELECT TOP 1 @CLERK_ID = BAS_CLERK_FK FROM BIL_HEADER WHERE ID= @BILID&#13;&#10;&#13;&#10;SELECT &#13;&#10;TITT.DESCRIPTION SALUTATION&#13;&#10;,TATT.DESCRIPTION LETTER_SALUTATION&#13;&#10;,C.SIGNATURE&#13;&#10;,C.TITLE&#13;&#10;,C.FIRST_NAME&#13;&#10;,C.FAMILY_NAME&#13;&#10;,C.DEPARTMENT&#13;&#10;,C.EMAIL&#13;&#10;,TEL.TELEPHONE TELEPHONE&#13;&#10;,FAX.TELEPHONE FAX&#13;&#10;,MOB.TELEPHONE MOBILE&#13;&#10;,CONCAT(CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; '' THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_NAME&#13;&#10;,CONCAT(CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; '' THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_TITLE_NAME&#13;&#10;,CONCAT(CASE WHEN C.SIGNATURE IS NOT NULL AND LTRIM(C.SIGNATURE) &lt;&gt; '' THEN CONCAT(C.SIGNATURE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; '' THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_SIGNATURE&#13;&#10;,CONCAT(CASE WHEN C.SIGNATURE IS NOT NULL AND LTRIM(C.SIGNATURE) &lt;&gt; '' THEN CONCAT(C.SIGNATURE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL AND LTRIM(C.FIRST_NAME) &lt;&gt; '' THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_TITLE_SIGNATURE&#13;&#10;,CONCAT(CASE WHEN TITT.DESCRIPTION IS NOT NULL THEN CONCAT(TITT.DESCRIPTION, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_SALUTATION&#13;&#10;,CONCAT(CASE WHEN TITT.DESCRIPTION IS NOT NULL THEN CONCAT(TITT.DESCRIPTION, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_TITLE_SALUTATION&#13;&#10;,CONCAT(CASE WHEN TATT.DESCRIPTION IS NOT NULL THEN CONCAT(TATT.DESCRIPTION, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_LETTER_SALUTATION&#13;&#10;&#13;&#10;,CONCAT(CASE WHEN TATT.DESCRIPTION IS NOT NULL THEN CONCAT(TATT.DESCRIPTION, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.TITLE IS NOT NULL AND LTRIM(C.TITLE) &lt;&gt; '' THEN CONCAT(C.TITLE, ' ') ELSE '' END, &#13;&#10;	 CASE WHEN C.FIRST_NAME IS NOT NULL THEN CONCAT(C.FIRST_NAME, ' ') ELSE '' END,&#13;&#10;	 CASE WHEN C.FAMILY_NAME IS NOT NULL THEN C.FAMILY_NAME ELSE '' END) FULL_TITLE_LETTER_SALUTATION&#13;&#10;FROM BAS_CLERK C&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER TEL ON TEL.ID = C.BAS_TELEPHONE_NUMBER_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER FAX ON FAX.ID = C.BAS_TELEPHONE_TELEFAX_FK&#13;&#10;LEFT JOIN BAS_TELEPHONE_NUMBER MOB ON MOB.ID = C.BAS_TELEPHONE_MOBILE_FK&#13;&#10;LEFT JOIN BAS_TITLE TIT ON TIT.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION TITT ON TITT.ID = TIT.DESCRIPTION_TR AND TITT.BAS_LANGUAGE_FK = 2&#13;&#10;LEFT JOIN BAS_TITLE TAT ON TAT.ID = C.BAS_TITLE_FK&#13;&#10;LEFT JOIN BAS_TRANSLATION TATT ON TATT.ID = TAT.ADDRESS_TITLE_TR AND TATT.BAS_LANGUAGE_FK = 2&#13;&#10;WHERE C.ID = @CLERK_ID">
        <Column Name="SALUTATION" DataType="System.String"/>
        <Column Name="LETTER_SALUTATION" DataType="System.String"/>
        <Column Name="SIGNATURE" DataType="System.String"/>
        <Column Name="TITLE" DataType="System.String"/>
        <Column Name="FIRST_NAME" DataType="System.String"/>
        <Column Name="FAMILY_NAME" DataType="System.String"/>
        <Column Name="DEPARTMENT" DataType="System.String"/>
        <Column Name="EMAIL" DataType="System.String"/>
        <Column Name="TELEPHONE" DataType="System.String"/>
        <Column Name="FAX" DataType="System.String"/>
        <Column Name="MOBILE" DataType="System.String"/>
        <Column Name="FULL_NAME" DataType="System.String"/>
        <Column Name="FULL_TITLE_NAME" DataType="System.String"/>
        <Column Name="FULL_SIGNATURE" DataType="System.String"/>
        <Column Name="FULL_TITLE_SIGNATURE" DataType="System.String"/>
        <Column Name="FULL_SALUTATION" DataType="System.String"/>
        <Column Name="FULL_TITLE_SALUTATION" DataType="System.String"/>
        <Column Name="FULL_LETTER_SALUTATION" DataType="System.String"/>
        <Column Name="FULL_TITLE_LETTER_SALUTATION" DataType="System.String"/>
        <CommandParameter Name="BILID" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
      <TableDataSource Name="Table21" Alias="Abrechnungsschema" DataType="System.Int32" Enabled="true" SelectCommand="SELECT &#13;&#10;BS.SORTING&#13;&#10;,BS.DESCRIPTION BEZEICHNUNG&#13;&#10;,BS.MDC_BILLING_LINE_TYPE_FK&#13;&#10;,BS.VALUE PROZ_WERT&#13;&#10;,BS.RESULT_OC BETRAG&#13;&#10;,GENT.ISPERCENT&#13;&#10;,BS.ISBOLD&#13;&#10;,BS.ISITALIC&#13;&#10;,BS.ISUNDERLINE&#13;&#10;,BS.ISPRINTEDZERO&#13;&#10;,bs.ISPRINTED&#13;&#10;FROM BIL_BILLINGSCHEMA BS&#13;&#10;LEFT JOIN PRC_GENERALSTYPE GENT ON GENT.ID = BS.PRC_GENERALSTYPE_FK&#13;&#10;WHERE BIL_HEADER_FK = @BILID AND BS.ISPRINTED = 1&#13;&#10;ORDER BY BS.SORTING&#13;&#10;">
        <Column Name="SORTING" DataType="System.Int32"/>
        <Column Name="MDC_BILLING_LINE_TYPE_FK" DataType="System.Int32"/>
        <Column Name="PROZ_WERT" DataType="System.Decimal"/>
        <Column Name="BETRAG" DataType="System.Decimal"/>
        <Column Name="ISPERCENT" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="ISBOLD" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="ISITALIC" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="ISUNDERLINE" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="ISPRINTEDZERO" DataType="System.Boolean" BindableControl="CheckBox"/>
        <Column Name="BEZEICHNUNG" DataType="System.String"/>
        <Column Name="ISPRINTED" DataType="System.Boolean" BindableControl="CheckBox"/>
        <CommandParameter Name="BILID" Expression="[BIL_HEADER_FK]" DefaultValue="1000015"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CompanyID" DataType="System.Int32" Expression="1000005" Description="Company ID" UsedInOptions="true"/>
    <Parameter Name="UserID" DataType="System.Int32" Expression="153" Description="User ID" UsedInOptions="true"/>
    <Parameter Name="PreviewUICulture" DataType="System.String" Expression="&quot;de-de&quot;" UsedInOptions="false"/>
    <Parameter Name="TextTypeIdLetterFooter" DataType="System.Int32" Expression="1000010" Description="Text Typ ID Letter-Footer" UsedInOptions="true"/>
    <Parameter Name="TextTypeIdUStText" DataType="System.Int32" Expression="1028837" Description="Text Typ ID USt.-Text" UsedInOptions="true"/>
    <Parameter Name="BIL_HEADER_FK" DataType="System.Int32" Expression="1000015" Description="ID Rechnung" UsedInOptions="true"/>
    <Parameter Name="BILL_TYPE_AR" DataType="System.Int32" Expression="1" Description="Rechnungs-Typ ID Abschlagsrechnung" UsedInOptions="true"/>
    <Parameter Name="BILL_TYPE_SR" DataType="System.Int32" Expression="2" Description="Rechnungs-Typ ID Schlussrechnung" UsedInOptions="true"/>
    <Parameter Name="BILL_TYPE_SR2" DataType="System.Int32" Expression="1000003" Description="Rechnungs-Typ ID Schlussrechnung 2" UsedInOptions="true"/>
    <Parameter Name="BILL_TYPE_EZ" DataType="System.Int32" Expression="3" Description="Rechnungs-Typ ID Einzelrechnung" UsedInOptions="true"/>
    <Parameter Name="BILL_TYPE_TR" DataType="System.Int32" Expression="1000002" Description="Rechnungs-Typ ID Teilrechung" UsedInOptions="true"/>
    <Parameter Name="InternReBetrag_90" DataType="System.Decimal" Expression="0" Description="wird nur intern verwendet" UsedInOptions="false"/>
    <Parameter Name="LangtextDrucken" DataType="System.Boolean" Expression="true" Description="Langtext drucken" UsedInOptions="true"/>
    <Parameter Name="ZuwachsLvDrucken" DataType="System.Boolean" Expression="false" Description="LV drucken" UsedInOptions="true"/>
    <Parameter Name="ZuwachsLvSummenDrucken" DataType="System.Boolean" Expression="false" Description="Summen Zuwachs LV drucken" UsedInOptions="true"/>
    <Parameter Name="ZuwachsAufmassDrucken" DataType="System.Boolean" Expression="false" Description="Zuwachs LV Aufmass drucken" UsedInOptions="true"/>
    <Parameter Name="KumuliertLvDrucken" DataType="System.Boolean" Expression="true" Description="Kumuliertes LV drucken" UsedInOptions="true"/>
    <Parameter Name="KumuliertLvSummenDrucken" DataType="System.Boolean" Expression="false" Description="Kumulierte Summen drucken" UsedInOptions="true"/>
    <Parameter Name="KumuliertAufmassDrucken" DataType="System.Boolean" Expression="false" Description="Kumuliertes Aufmass drucken" UsedInOptions="true"/>
    <Parameter Name="NullMengenZeilenDrucken" DataType="System.Boolean" Expression="false" Description="Null Positionen drucken" UsedInOptions="true"/>
    <Parameter Name="GPNiederlassungDrucken" DataType="System.Boolean" Expression="true" Description="GP-Niederlassungsname drucken" UsedInOptions="false"/>
    <Parameter Name="GPReguliererDrucken" DataType="System.Boolean" Expression="false" Description="GP-Regulierer drucken" UsedInOptions="true"/>
    <Parameter Name="BOQ_STATUS_FK_NOT" DataType="System.Int32" Expression="-1" Description="LV mit diesem Status nicht drucken" UsedInOptions="false"/>
    <Parameter Name="PrintAllLvStatus" DataType="System.Boolean" Expression="true" Description="Alle LV Status drucken" UsedInOptions="false"/>
    <Parameter Name="GeneralienDrucken" DataType="System.Boolean" Expression="true" Description="Generalien drucken" UsedInOptions="true"/>
    <Parameter Name="DruckeInhaltsverzeichnis" DataType="System.Boolean" Expression="true" Description="Inhaltsverzeichnis drucken" UsedInOptions="true"/>
    <Parameter Name="DruckeSicherheitseinbehalt" DataType="System.Boolean" Expression="true" Description="Sicherheitseinbehalt drucken" UsedInOptions="true"/>
    <Parameter Name="TextTypeIdLetterFooterLeft" DataType="System.Int32" Expression="36" Description="Texttype ID Letter Footer left" UsedInOptions="true"/>
    <Parameter Name="TextTypeIdLetterFooterCenter" DataType="System.Int32" Expression="37" Description="Texttype ID Letter Footer Center" UsedInOptions="true"/>
    <Parameter Name="TextTypeIdLetterFooterRight" DataType="System.Int32" Expression="38" Description="Texttype ID Letter Footer right" UsedInOptions="true"/>
    <Parameter Name="PrintLetterHeader" DataType="System.Boolean" Expression="true" Description="Briefbogen drucken" UsedInOptions="true"/>
    <Parameter Name="ContactSalutation" DataType="System.Boolean" Expression="true" Description="Kontakt mit Anrede drucken" UsedInOptions="true"/>
    <Parameter Name="TextModuleTypeIdPretext" DataType="System.Int32" Expression="1000003" Description="Text ID RIB BauKonfig - Vortext" UsedInOptions="true"/>
    <Parameter Name="TextModuleTypeIdPosttext" DataType="System.Int32" Expression="1000005" Description="Text ID RIB BauKonfig - Nachtext" UsedInOptions="true"/>
    <Total Name="ZahlungenNetto" Expression="[Zahlungen.NETTO]"/>
    <Total Name="ZahlungenMwSt" Expression="[Zahlungen.USTEUER]"/>
    <Total Name="ZahlungenBrutto" Expression="[Zahlungen.BRUTTO]"/>
    <Total Name="OZSummeAufmass" Expression="[Aufmass.ERGEBNIS]" Evaluator="DataAufmass" PrintOn="GFAufmass" ResetOnReprint="true" IncludeInvisibleRows="true"/>
    <Total Name="PositionenNetto" Expression="[Positionen.AMOUNT_NET]" Evaluator="DataMatPosition" PrintOn="DataFooter1"/>
    <Total Name="PositionenMwSt" Expression="[Positionen.TAX]" Evaluator="DataMatPosition" PrintOn="DataFooter1"/>
    <Total Name="PositionenBrutto" Expression="[Positionen.AMOUNT_GROSS]" Evaluator="DataMatPosition" PrintOn="DataFooter1"/>
    <Total Name="OZSummeAufmassZusammen" Expression="[Aufmass.ERGEBNIS]" ResetOnReprint="true" IncludeInvisibleRows="true"/>
    <Total Name="SummeLvAufmassMenge" Expression="[Aufmass.ERGEBNIS]" Evaluator="DataTextOben"/>
    <Total Name="SummeLvKumMenge" Expression="[LvKumuliertMitAufmass.RESULT]" Evaluator="DataGesamtLvAufmassZeileTyp1" PrintOn="GrphGesamtLVSumme"/>
    <Total Name="KumuliertInhaltsSumme" Expression="[LvKumuliertInhaltsverzeichnis.G_FINALPRICE]" Evaluator="DataKumuliertInhaltGruppe" PrintOn="DataFooter2"/>
  </Dictionary>
  <ReportPage Name="PageRechnung" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" Watermark.Font="Arial, 60pt" StartPageEvent="PageRechnung_StartPage">
    <PageHeaderBand Name="PageHeader1" Width="793.8" Height="359.1" CanGrow="true" PrintOn="FirstPage">
      <PictureObject Name="PicCompanyLogo" Left="519.75" Top="37.8" Width="236.25" Height="102.06" DataColumn="Company.LOGO" ImageAlign="Top_Right"/>
      <TextObject Name="Text1654" Left="75.6" Top="170.1" Width="349.65" Height="29.11" Text="[Company.ADRESSZEILE]" Font="Tahoma, 7pt"/>
      <TextObject Name="TxtReceiver" Left="75.6" Top="198.45" Width="349.65" Height="160.65" CanGrow="true" Text="BP_NAME1&#13;&#10;BP_NAME2 (if empty = no line)&#13;&#10;STREET&#13;&#10;&#13;&#10;ZIPCODE CITY" Font="Tahoma, 9pt" TextRenderType="HtmlTags"/>
      <TextObject Name="TxtAnsprechpartner" Left="519.75" Top="170.1" Width="236.25" Height="75.6" CanGrow="true" CanShrink="true" CanBreak="false" Text="Ihr Ansprechpartner:&#13;&#10;[RechnungClerk.RECHNUNGCLERK_VORNAME] [RechnungClerk.RECHNUNGCLERK_NACHNAME]&#13;&#10;[RechnungClerk.RECHNUNGCLERK_TELEFON]&#13;&#10;[RechnungClerk.RECHNUNGCLERK_EMAIL]" Font="Tahoma, 9pt" LineHeight="15" TextRenderType="HtmlTags"/>
      <ChildBand Name="Child14" Top="363.1" Width="793.8" Height="94.61" PrintOn="LastPage, OddPages, EvenPages, RepeatedBand, SinglePage">
        <TextObject Name="Text2303" Left="75.6" Top="37.8" Width="113.4" Height="18.9" Text="Projekt-Nr.:" Font="Tahoma, 9pt"/>
        <TextObject Name="Text2304" Left="189" Top="37.8" Width="434.7" Height="18.9" Text="[Rechnung.PROJECTNO]" Font="Tahoma, 9pt">
          <Formats>
            <DateFormat/>
            <DateFormat/>
          </Formats>
        </TextObject>
        <TextObject Name="Text2305" Left="75.6" Top="56.7" Width="113.4" Height="18.9" Text="Rechnungs-Nr.:" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2306" Left="189" Top="56.7" Width="434.7" Height="18.9" Text="[Rechnung.RE_NR]" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2309" Left="75.6" Top="75.6" Width="113.4" Height="18.9" Text="Projektname:" Font="Tahoma, 9pt"/>
        <TextObject Name="Text2310" Left="189" Top="75.6" Width="434.7" Height="18.9" Text="[Rechnung.PROJECT_NAME]" Font="Tahoma, 9pt"/>
        <PictureObject Name="Picture3" Left="652.05" Top="37.8" Width="103.95" Height="56.81" DataColumn="Company.LOGO" ImageAlign="Top_Right"/>
        <LineObject Name="Line21" Left="75.6" Top="94.5" Width="680.4"/>
        <ChildBand Name="Child2" Top="461.71" Width="793.8" Height="9.45" PrintOn="LastPage, OddPages, EvenPages, RepeatedBand, SinglePage">
          <ChildBand Name="Child11" Top="475.16" Width="793.8" Height="37.8" PrintOn="FirstPage">
            <TextObject Name="TxtRechnungsTyp" Left="75.6" Top="5.45" Width="680.4" Height="30.8" Text="Schlussrechnung" Font="Arial, 20pt, style=Bold"/>
            <ChildBand Name="ChildReData_ReNr" Top="516.96" Width="793.8" Height="56.7" CanShrink="true" PrintOn="FirstPage">
              <TextObject Name="Text2575" Left="75.6" Top="28.35" Width="122.85" Height="18.9" Text="Rechnungsdatum:" Font="Tahoma, 9pt, style=Bold"/>
              <TextObject Name="Text2576" Left="207.9" Top="28.35" Width="548.1" Height="18.9" Text="[Rechnung.DATUM]" Format="Date" Format.Format="d" Font="Tahoma, 9pt, style=Bold"/>
              <TextObject Name="Text2577" Left="75.6" Top="9.45" Width="122.85" Height="18.9" Text="Rechnungs-Nr.:" Font="Tahoma, 9pt, style=Bold"/>
              <TextObject Name="Text2578" Left="207.9" Top="9.45" Width="548.1" Height="18.9" Text="[Rechnung.RE_NR]" Font="Tahoma, 9pt, style=Bold"/>
              <ChildBand Name="ChildReData_Projekt" Top="577.66" Width="793.8" Height="30.35" CanShrink="true" PrintOn="FirstPage">
                <TextObject Name="Text2581" Left="75.6" Top="3.55" Width="122.85" Height="15.9" Text="Projekt:" Font="Tahoma, 9pt"/>
                <TextObject Name="Text2582" Left="207.9" Top="3.55" Width="548.1" Height="15.9" Text="[Rechnung.PROJECTNO] - [Rechnung.PROJECT_NAME]" Font="Tahoma, 9pt">
                  <Formats>
                    <DateFormat/>
                    <DateFormat/>
                  </Formats>
                </TextObject>
                <ChildBand Name="ChildReData_Stadt" Top="612.01" Width="793.8" Height="39.01" CanShrink="true" PrintOn="FirstPage">
                  <TextObject Name="Text2585" Left="75.6" Top="3.4" Width="122.85" Height="15.9" CanGrow="true" Text="Stadt:" Font="Tahoma, 9pt"/>
                  <TextObject Name="Text2586" Left="207.9" Top="3.4" Width="548.1" Height="15.9" CanGrow="true" Text="[Rechnung.PRJ_CITY]" Font="Tahoma, 9pt"/>
                  <ChildBand Name="ChildReData_Strasse" Top="655.02" Width="793.8" Height="37.8" CanShrink="true" PrintOn="FirstPage">
                    <TextObject Name="Text2587" Left="75.6" Top="3.4" Width="122.85" Height="15.9" CanGrow="true" Text="Str./Haus-Nr.:" Font="Tahoma, 9pt"/>
                    <TextObject Name="Text2588" Left="207.9" Top="3.4" Width="548.1" Height="15.9" CanGrow="true" Text="[Rechnung.PRJ_STREET]" Font="Tahoma, 9pt"/>
                    <ChildBand Name="ChildReData_Leistung" Top="696.82" Width="793.8" Height="37.8" CanShrink="true" PrintOn="FirstPage">
                      <TextObject Name="Text2579" Left="75.6" Top="3.4" Width="122.85" Height="18.9" Text="Leistungszeitraum:" Font="Tahoma, 9pt"/>
                      <TextObject Name="TxtLeistungZeitraum" Left="207.9" Top="3.4" Width="548.1" Height="18.9" Text="[Rechnung.LEISTUNGSVON] bis [Rechnung.LEISTUNGBIS]" HideZeros="true" Font="Tahoma, 9pt">
                        <Formats>
                          <GeneralFormat/>
                          <GeneralFormat/>
                        </Formats>
                      </TextObject>
                      <ChildBand Name="ChildReData_Auftrag" Top="738.62" Width="793.8" Height="56.7" CanShrink="true" PrintOn="FirstPage">
                        <TextObject Name="Text2593" Left="75.6" Top="26.22" Width="122.85" Height="18.9" CanGrow="true" CanShrink="true" Text="Auftragsdatum:" Font="Tahoma, 9pt"/>
                        <TextObject Name="Text2594" Left="207.9" Top="26.22" Width="548.1" Height="18.9" CanGrow="true" CanShrink="true" Text="[Rechnung.ORDER_DATE]" Format="Date" Format.Format="d" Font="Tahoma, 9pt"/>
                        <TextObject Name="Text2591" Left="75.6" Top="3.4" Width="122.85" Height="18.9" CanGrow="true" CanShrink="true" Text="Auftrags-Nr.:" Font="Tahoma, 9pt"/>
                        <TextObject Name="Text2592" Left="207.9" Top="3.4" Width="548.1" Height="18.9" CanGrow="true" CanShrink="true" Text="[Rechnung.AUFTRAGS_NR]" Font="Tahoma, 9pt"/>
                        <ChildBand Name="ChildReData_Kundennr" Top="799.32" Width="793.8" Height="37.8" CanShrink="true" PrintOn="FirstPage">
                          <TextObject Name="Text2589" Left="75.6" Top="3.4" Width="122.85" Height="15.9" Text="Kunden-Nr.:" Font="Tahoma, 9pt"/>
                          <TextObject Name="Text2590" Left="207.9" Top="3.4" Width="548.1" Height="15.9" Text="[Rechnung.DEBITOR]" Font="Tahoma, 9pt"/>
                        </ChildBand>
                      </ChildBand>
                    </ChildBand>
                  </ChildBand>
                </ChildBand>
              </ChildBand>
            </ChildBand>
          </ChildBand>
        </ChildBand>
      </ChildBand>
    </PageHeaderBand>
    <DataBand Name="DataTextOben" Top="841.12" Width="793.8" Height="94.5" CanGrow="true" CanShrink="true" CanBreak="true" Columns.Layout="DownThenAcross">
      <RichObject Name="RichTextOben" Left="78.6" Top="66.15" Width="677.4" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichTextOben_AfterData"/>
      <TextObject Name="TxtSalutation" Left="75.6" Top="37.8" Width="680.4" Height="18.9" Text="Sehr geehrte Damen und Herren," Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="DataUebersicht" Top="939.62" Width="793.8" Height="37.8" CanGrow="true" CanShrink="true" BeforePrintEvent="DataUebersicht_BeforePrint" StartNewPage="true">
      <SubreportObject Name="Subreport23" Left="75.6" Width="680.4" Height="18.9" ReportPage="PageUebersicht"/>
    </DataBand>
    <DataBand Name="DataKumuliertHeader" Top="981.42" Width="793.8" Height="37.8" CanGrow="true" CanShrink="true" BeforePrintEvent="DataKumuliertHeader_BeforePrint">
      <SubreportObject Name="Subreport24" Left="75.6" Width="680.4" Height="18.9" ReportPage="PageKumuliertHeader"/>
    </DataBand>
    <DataBand Name="DataZuwachsHeader" Top="1023.22" Width="793.8" Height="37.8" CanGrow="true" CanShrink="true" BeforePrintEvent="DataZuwachsHeader_BeforePrint">
      <SubreportObject Name="Subreport25" Left="75.6" Width="680.4" Height="18.9" ReportPage="PageZuwachsHeader"/>
    </DataBand>
    <DataBand Name="DataPositionHeader" Top="1065.02" Width="793.8" Height="37.8" BeforePrintEvent="DataPositionHeader_BeforePrint">
      <SubreportObject Name="Subreport26" Left="75.6" Top="9.45" Width="680.4" Height="18.9" ReportPage="PagePositionHeader"/>
    </DataBand>
    <DataBand Name="DataTextUnten" Top="1106.82" Width="793.8" Height="66.15" CanGrow="true" CanShrink="true" CanBreak="true">
      <RichObject Name="RichTextUnten" Left="78.6" Top="37.8" Width="677.4" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichTextUnten_AfterData"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="1176.97" Width="793.8" Height="24.35" PrintOn="LastPage, OddPages, EvenPages, RepeatedBand, SinglePage">
      <TextObject Name="Text3" Left="359.1" Top="5.45" Width="396.9" Height="18.9" Border.Lines="Top" Text="Seite [Page] von [TotalPages#]" HorzAlign="Right" VertAlign="Bottom" Font="Tahoma, 9pt"/>
      <TextObject Name="Text2595" Left="75.6" Top="5.45" Width="396.9" Height="18.9" Border.Lines="Top" Text="[Date]" Format="Date" Format.Format="d" VertAlign="Bottom" Font="Tahoma, 9pt"/>
      <ChildBand Name="ChildFooterCompanyText" Top="1205.32" Width="793.8" Height="75.6" Border.Lines="Top" CanGrow="true" CanShrink="true" PrintOn="FirstPage">
        <RichObject Name="RichLetterFooterLeft" Left="75.6" Top="9.45" Width="207.9" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichLetterFooterLeft_AfterData"/>
        <RichObject Name="RichLetterFooterCenter" Left="307.12" Top="9.45" Width="217.35" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichLetterFooterCenter_AfterData"/>
        <RichObject Name="RichLetterFooterRight" Left="548.1" Top="9.45" Width="207.9" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichLetterFooterRight_AfterData"/>
        <ChildBand Name="Child17" Top="1284.92" Width="793.8" Height="18.9"/>
      </ChildBand>
    </PageFooterBand>
    <OverlayBand Name="OLayLetterHeader" Top="1307.82" Width="793.8" Height="1122.66" PrintOn="FirstPage">
      <PictureObject Name="PicLetterHeader" Width="793.8" Height="1122.66" DataColumn="Company.LETTERHEADER"/>
    </OverlayBand>
  </ReportPage>
  <ReportPage Name="PageUebersicht" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <DataBand Name="DataGeneralien" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" CanBreak="true">
      <SubreportObject Name="Subreport22" Width="680.4" Height="18.9" ReportPage="PageGeneralien"/>
    </DataBand>
    <DataBand Name="DataBold" Top="66.59" Width="680.4" Height="28.35" BeforePrintEvent="Data6_BeforePrint" DataSource="Table21">
      <TextObject Name="Text2312" Top="9.45" Width="302.4" Height="18.9" Text="[Abrechnungsschema.BEZEICHNUNG]" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text2313" Left="519.75" Top="9.45" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG] " Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
      <ChildBand Name="DataNormal" Top="98.94" Width="680.4" Height="28.35">
        <TextObject Name="Text2315" Left="519.75" Top="9.45" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt"/>
        <TextObject Name="Text2314" Top="9.45" Width="302.4" Height="18.9" Text="[Abrechnungsschema.BEZEICHNUNG]" Font="Tahoma, 10pt"/>
        <TextObject Name="TxtARSProzent" Left="311.85" Top="9.45" Width="94.5" Height="18.9" Text="[Abrechnungsschema.PROZ_WERT] %" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt"/>
        <ChildBand Name="DataBoldUnderline" Top="131.29" Width="680.4" Height="37.8">
          <TextObject Name="Text2317" Top="9.45" Width="491.4" Height="18.9" Text="[Abrechnungsschema.BEZEICHNUNG]" Font="Tahoma, 10pt, style=Bold"/>
          <TextObject Name="Text2318" Left="519.75" Top="9.45" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG] " Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
          <LineObject Name="Line17" Top="28.35" Width="680.4" Border.Width="0.5"/>
          <ChildBand Name="DataUSt" Top="173.09" Width="680.4" Height="18.9">
            <TextObject Name="Text2319" Left="519.75" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt"/>
            <TextObject Name="Text2320" Left="28.35" Width="283.5" Height="18.9" Text="[Abrechnungsschema.BEZEICHNUNG]" Font="Tahoma, 10pt"/>
            <TextObject Name="TxtARSProzent2" Left="311.85" Width="94.5" Height="18.9" Text="[Rechnung.MWST_PROZENT] %" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt"/>
            <ChildBand Name="DataEinbehaltProzent" Top="195.99" Width="680.4" Height="9.45">
              <TextObject Name="Text2325" Left="28.35" Width="283.5" Height="9.45" Text="[Abrechnungsschema.PROZ_WERT] % von [InternReBetrag_90]" Font="Tahoma, 6pt">
                <Formats>
                  <NumberFormat/>
                  <NumberFormat/>
                </Formats>
              </TextObject>
              <ChildBand Name="DataArRechnungen" Top="209.44" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" CanBreak="true">
                <SubreportObject Name="Subreport2" Left="28.35" Top="2.45" Width="661.5" Height="18.9" ReportPage="PageReHistorie"/>
                <ChildBand Name="DataZahlungsuebersicht" Top="251.24" Width="680.4" Height="66.15" CanGrow="true" CanShrink="true" CanBreak="true">
                  <TextObject Name="Text2326" Top="9.45" Width="302.4" Height="18.9" Text="Zahlungsübersicht" Font="Tahoma, 12pt, style=Bold"/>
                  <TextObject Name="PageUebersichtTxtZahlungGesamt" Left="519.75" Top="9.45" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG] " Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
                  <SubreportObject Name="Subreport3" Left="28.35" Top="37.8" Width="652.05" Height="18.9" ReportPage="PageZahlungen"/>
                  <LineObject Name="Line11" Top="31.35" Width="680.4" Border.Width="0.5"/>
                  <ChildBand Name="DataEndsumme" Top="321.39" Width="680.4" Height="56.7">
                    <TextObject Name="Text2417" Top="28.35" Width="491.4" Height="18.9" Text="[Abrechnungsschema.BEZEICHNUNG]" Font="Tahoma, 10pt, style=Bold"/>
                    <TextObject Name="Text2418" Left="519.75" Top="28.35" Width="160.65" Height="18.9" Text="[Abrechnungsschema.BETRAG] " Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
                    <LineObject Name="Line8" Top="47.25" Width="680.4" Border.Width="0.5"/>
                    <LineObject Name="Line9" Top="49.25" Width="680.4" Border.Width="0.5"/>
                    <LineObject Name="Line10" Top="23.35" Width="680.4" Border.Width="0.5"/>
                  </ChildBand>
                </ChildBand>
              </ChildBand>
            </ChildBand>
          </ChildBand>
        </ChildBand>
      </ChildBand>
      <DataHeaderBand Name="DataHeader2" Top="41.8" Width="680.4" Height="20.79" RepeatOnEveryPage="true">
        <TextObject Name="Text2311" Width="661.5" Height="20.79" CanGrow="true" Text="Rechnungsübersicht" HorzAlign="Center" Font="Tahoma, 12pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="Data11" Top="382.09" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true">
      <TextObject Name="TxtZK" Top="18.9" Width="680.4" Height="18.9" CanGrow="true" Text="Zahlungskondition:" Font="Arial, 10pt"/>
      <ChildBand Name="Child3" Top="423.89" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" CanBreak="true">
        <TextObject Name="TxtUst" Top="18.9" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" Text="USt. Text" Font="Arial, 10pt"/>
        <ChildBand Name="DataUstText" Top="465.69" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" CanBreak="true">
          <RichObject Name="RichUst" Top="18.9" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichUst_AfterData" Text="{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1031{\fonttbl{\f0\fnil\fcharset0 Arial;}{\f1\fnil Tahoma;}}&#13;&#10;{\*\generator Riched20 10.0.20348}\viewkind4\uc1 &#13;&#10;\pard\fs20 Ust. Text\f1\fs17\par&#13;&#10;}&#13;&#10;"/>
        </ChildBand>
      </ChildBand>
    </DataBand>
    <PageFooterBand Name="PageFooter2" Top="507.49" Width="680.4" Height="24.19" Visible="false" Border.Lines="Bottom">
      <TextObject Name="Text2448" Top="5.29" Width="680.4" Height="18.9" Text="[Page]/[TotalPages#]" HorzAlign="Right" Font="Tahoma, 9pt"/>
      <ChildBand Name="Child5" Top="535.68" Width="680.4" Height="67.28" Visible="false"/>
    </PageFooterBand>
  </ReportPage>
  <ReportPage Name="PageKumuliertHeader" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <DataBand Name="DataPageLvKumuliertInhalt" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" DataSource="Table">
      <SubreportObject Name="Subreport21" Width="680.4" Height="18.9" Dock="Top" ReportPage="PageLvKumuliertInhalt"/>
    </DataBand>
    <GroupHeaderBand Name="GroupHeader5" Top="41.8" Width="680.4" Height="18.9" Visible="false" Fill.Color="DarkGray" Condition="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV]" SortOrder="None">
      <DataBand Name="DataKumuliertLvSummen" Top="64.7" Width="680.4" Height="47.25" CanGrow="true" CanShrink="true" BeforePrintEvent="DataKumuliertLvSummen_BeforePrint" FirstRowStartsNewPage="true" DataSource="Table17" IdColumn="Keine">
        <SubreportObject Name="Subreport18" Width="680.4" Height="9.45" ReportPage="PageLvKumuliertSummen"/>
        <ChildBand Name="DataKumuliertLv" Top="115.95" Width="680.4" Height="18.9" CanShrink="true" BeforePrintEvent="DataKumuliertLv_BeforePrint">
          <SubreportObject Name="Subreport1" Width="680.4" Height="9.45" ReportPage="PageLvKumuliert"/>
          <ChildBand Name="DataKumuliertAufmass" Top="138.85" Width="680.4" Height="18.9" CanShrink="true" BeforePrintEvent="DataKumuliertAufmass_BeforePrint">
            <SubreportObject Name="Subreport19" Width="680.4" Height="9.45" ReportPage="PageLvKumuliertMitAufmass"/>
          </ChildBand>
        </ChildBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter5" Top="161.75" Width="680.4" Height="37.8" CanShrink="true"/>
    </GroupHeaderBand>
  </ReportPage>
  <ReportPage Name="PageLvKumuliertInhalt" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <DataBand Name="DataKumuliertInhaltGruppe" Top="70.15" Width="680.4" Height="93.9" CanGrow="true" CanShrink="true" DataSource="Table7" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text63" Top="14.9" Width="113.4" Height="18.9" CanGrow="true" Text="[LvKumuliertInhaltsverzeichnis.REFERENCE]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text66" Left="113.4" Top="14.9" Width="349.65" Height="18.9" CanGrow="true" Text="[LvKumuliertInhaltsverzeichnis.BRIEF]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text98" Left="576.45" Top="14.9" Width="103.95" Height="18.9" CanBreak="false" Text="[LvKumuliertInhaltsverzeichnis.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text2533" Left="481.95" Top="14.9" Width="66.15" Height="18.9" CanGrow="true" Text="[Engine.GetBookmarkPage(ToString([LvKumuliertInhaltsverzeichnis.PRJ_BOQ_HEADER_FK]))]" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <DataHeaderBand Name="DataHeader1" Width="680.4" Height="66.15" CanShrink="true">
        <TextObject Name="Text2545" Width="680.4" Height="17.79" CanGrow="true" Text="Inhaltsverzeichnis" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text2546" Top="28.35" Width="113.4" Height="18.9" Text="LV-Nr." Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2547" Left="113.4" Top="28.35" Width="349.65" Height="18.9" Text="Bezeichnung" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2548" Left="491.4" Top="28.35" Width="56.7" Height="18.9" Text="Seite" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <LineObject Name="Line18" Top="56.7" Width="680.4"/>
      </DataHeaderBand>
      <DataFooterBand Name="DataFooter2" Top="168.05" Width="680.4" Height="37.8" AfterPrintEvent="DataFooter2_AfterPrint">
        <TextObject Name="Text118" Left="113.4" Top="18.9" Width="406.35" Height="18.9" CanGrow="true" CanShrink="true" Text="Kumulierter Nettobetrag" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text119" Left="519.75" Top="18.9" Width="160.65" Height="18.9" CanBreak="false" Text="[KumuliertInhaltsSumme]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
        <LineObject Name="Line3" Left="113.4" Top="13.9" Width="567"/>
      </DataFooterBand>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageLvKumuliertSummen" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <DataBand Name="DataHeaderKumSumLevelTop" Top="90.94" Width="680.4" Height="27.75" CanGrow="true" BeforePrintEvent="DataHeaderKumSumLevelTop_BeforePrint" DataSource="Table16">
      <TextObject Name="Text114" Top="4.16" Width="113.4" Height="18.9" CanShrink="true" CanBreak="false" Text="[LvKumuliertSummen.REFERENCE]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text115" Left="113.4" Top="4.16" Width="311.85" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliertSummen.BRIEF]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text120" Left="557.55" Top="4.16" Width="122.85" Height="18.9" CanBreak="false" Text="[LvKumuliertSummen.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
      <ChildBand Name="DataHeaderKumSumLevel" Top="122.69" Width="680.4" Height="27.59" CanGrow="true">
        <TextObject Name="Text122" Top="4.16" Width="113.4" Height="18.9" CanBreak="false" Text="[LvKumuliertSummen.REFERENCE]" Font="Arial, 10pt"/>
        <TextObject Name="Text123" Left="113.4" Top="4.16" Width="311.85" Height="18.9" CanGrow="true" Text="[LvKumuliertSummen.BRIEF]" Font="Arial, 10pt"/>
        <TextObject Name="Text126" Left="425.25" Top="4.16" Width="113.4" Height="18.9" CanBreak="false" Text="[LvKumuliertSummen.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
        <ChildBand Name="DataZuschlagNachlassProzent" Top="154.28" Width="680.4" Height="18.9">
          <TextObject Name="TxtProzentText" Left="113.4" Width="198.45" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" Font="Arial, 10pt, style=Italic"/>
          <TextObject Name="Text2550" Left="425.25" Width="113.4" Height="18.9" CanBreak="false" Text="([LvKumuliertSummen.FINALDISCOUNT])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
          <TextObject Name="Text2551" Left="349.65" Width="75.6" Height="18.9" CanBreak="false" Text="([LvKumuliertSummen.DISCOUNT_PER_EBENE] %)" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
          <ChildBand Name="DataZuschlagNachlassAbsolut" Top="177.18" Width="680.4" Height="18.9">
            <TextObject Name="TxtAbsolutText" Left="113.4" Width="236.25" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" Font="Arial, 10pt, style=Italic"/>
            <TextObject Name="Text2553" Left="425.25" Width="113.4" Height="18.9" CanBreak="false" Text="([LvKumuliertSummen.DISCOUNT_ABS_EBENE])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
            <ChildBand Name="DataHeaderKumSumSumme" Top="200.08" Width="680.4" Height="37.8" CanGrow="true">
              <TextObject Name="Text128" Left="113.4" Top="9.45" Width="567" Height="18.9" Border.Lines="Bottom" Border.BottomLine.Style="Double" CanGrow="true" CanBreak="false" Text="Gesamt [LvKumuliertSummen.BRIEF]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
              <TextObject Name="Text129" Left="141.75" Top="9.45" Width="538.65" Height="18.9" CanGrow="true" CanBreak="false" Text="[LvKumuliertSummen.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
              <ChildBand Name="DataZuschlagNachlassProzentLvSum" Top="241.88" Width="680.4" Height="37.8">
                <TextObject Name="TxtProzentTextLvSum" Left="113.4" Top="5" Width="264.6" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" Font="Arial, 10pt, style=Italic"/>
                <LineObject Name="Line16" Left="113.4" Top="28.9" Width="567"/>
                <TextObject Name="Text2572" Left="538.65" Top="5" Width="141.75" Height="18.9" CanBreak="false" Text="([LvKumuliertSummen.FINALDISCOUNT])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
                <TextObject Name="Text2573" Left="396.9" Top="5" Width="141.75" Height="18.9" CanBreak="false" Text="([LvKumuliertSummen.DISCOUNT_PER_EBENE] %)" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
              </ChildBand>
            </ChildBand>
          </ChildBand>
        </ChildBand>
      </ChildBand>
      <DataHeaderBand Name="DataHeaderKumSum" Width="680.4" Height="86.94" Border.Lines="Bottom" OutlineExpression="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV_STRING]" RepeatOnEveryPage="true">
        <TextObject Name="Text108" Top="66.15" Width="113.4" Height="18.9" Text="OZ" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text109" Left="113.4" Top="66.15" Width="463.05" Height="18.9" Text="Kurztext/Langtext" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text113" Left="576.45" Top="66.15" Width="103.95" Height="18.9" Text="GP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text105" Top="37.8" Width="31.35" Height="18.9" CanGrow="true" Text="LV:" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="TxtLvKumLvNo2" Left="32.35" Top="37.8" Width="81.05" Height="18.9" Hyperlink.Kind="Bookmark" Bookmark="[BoqKumuliertHeader.BOQ_HEADER_FK_PRLV_STRING]" CanBreak="false" Text="[BoqKumuliertHeader.BOQ_NUMMER]" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="TxtLvKumLvBez2" Left="113.4" Top="37.8" Width="567" Height="18.9" CanBreak="false" Text="[BoqKumuliertHeader.BOQ_TITEL]" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text104" Top="9.45" Width="680.4" Height="17.79" CanGrow="true" Text="Kumulierte Zusammenfassung" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageLvKumuliert" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <GroupHeaderBand Name="GhLvKum" Width="680.4" Height="104.95" CanShrink="true" BeforePrintEvent="GhLvKum_BeforePrint" RepeatOnEveryPage="true" Condition="[LvKumuliert.PRJ_BOQ_HEADER_FK]" SortOrder="None">
      <TextObject Name="TxtKumLvTitelx" Top="9.45" Width="680.4" Height="17.79" CanGrow="true" Text="Kumuliertes Leistungsverzeichnis" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text87" Top="37.8" Width="31.35" Height="18.9" CanGrow="true" Text="LV:" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="TxtLvKumLvNo" Left="32.35" Top="37.8" Width="81.05" Height="18.9" CanBreak="false" Text="[BoqKumuliertHeader.BOQ_NUMMER]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="TxtLvKumLvBez" Left="113.4" Top="37.8" Width="567" Height="18.9" CanShrink="true" Text="[BoqKumuliertHeader.BOQ_TITEL]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text90" Top="66.15" Width="28.35" Height="18.9" Text="OZ" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text91" Left="113.4" Top="66.15" Width="245.7" Height="18.9" Text="Kurztext/Langtext" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text92" Left="368.55" Top="66.15" Width="75.6" Height="18.9" Text="Menge" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text93" Left="444.15" Top="66.15" Width="56.7" Height="18.9" Text="ME" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text94" Left="481.95" Top="66.15" Width="94.5" Height="18.9" Text="EP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text95" Left="576.45" Top="66.15" Width="103.95" Height="18.9" Text="GP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <LineObject Name="Line20" Top="85.05" Width="680.4"/>
      <ChildBand Name="Child20" Top="108.95" Width="680.4" Height="5.29"/>
      <DataBand Name="DataLvKumLevel" Top="118.24" Width="680.4" Height="27.75" CanGrow="true" BeforePrintEvent="DataLvKumLevel_BeforePrint" CanBreak="true" DataSource="Table15" KeepTogether="true" KeepDetail="true">
        <TextObject Name="Text76" Top="4" Width="113.4" Height="18.9" CanShrink="true" CanBreak="false" Text="[LvKumuliert.REFERENCE]" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text77" Left="113.4" Top="4" Width="567" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliert.BRIEF]" Font="Arial, 10pt, style=Bold"/>
        <ChildBand Name="DataLvKumPosition" Top="149.99" Width="680.4" Height="27.59" CanGrow="true" CanShrink="true" CanBreak="true">
          <TextObject Name="Text78" Top="5.45" Width="113.4" Height="18.9" CanBreak="false" Text="[LvKumuliert.REFERENCE]" Font="Arial, 10pt"/>
          <TextObject Name="Text79" Left="113.4" Top="5.45" Width="236.25" Height="18.9" CanGrow="true" Text="[LvKumuliert.BRIEF]" Font="Arial, 10pt"/>
          <TextObject Name="Text80" Left="444.15" Top="5.45" Width="56.7" Height="18.9" CanGrow="true" BeforePrintEvent="Text1806_BeforePrint" Text="[LvKumuliert.UOM]" WordWrap="false" Font="Arial, 10pt" Trimming="EllipsisCharacter">
            <Formats>
              <NumberFormat UseLocale="false" DecimalDigits="3" NegativePattern="1"/>
              <GeneralFormat/>
            </Formats>
          </TextObject>
          <TextObject Name="Text81" Left="500.85" Top="5.45" Width="75.6" Height="18.9" CanBreak="false" Text="[LvKumuliert.PRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
          <TextObject Name="Text82" Left="576.45" Top="5.45" Width="103.95" Height="18.9" CanBreak="false" Text="[LvKumuliert.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
          <TextObject Name="Text83" Left="321.3" Top="5.45" Width="122.85" Height="18.9" CanGrow="true" BeforePrintEvent="Text1806_BeforePrint" Text="[LvKumuliert.G_QUANTITY]" HideZeros="true" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt" Trimming="EllipsisCharacter">
            <Formats>
              <NumberFormat UseLocale="false" DecimalDigits="3" NegativePattern="1"/>
              <GeneralFormat/>
            </Formats>
          </TextObject>
          <ChildBand Name="DataZuschlagNachlassProzentLVPos" Top="181.58" Width="680.4" Height="18.9">
            <TextObject Name="TxtProzentTextLV" Left="283.5" Width="160.65" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" HorzAlign="Right" Font="Arial, 10pt"/>
            <TextObject Name="Text2561" Left="576.45" Width="103.95" Height="18.9" CanBreak="false" Text="([LvKumuliert.FINALDISCOUNT])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
            <TextObject Name="Text2562" Left="444.15" Width="132.3" Height="18.9" CanBreak="false" Text="([LvKumuliert.DISCOUNT_PER_POS] %)" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
            <ChildBand Name="DataLvKumLangtext" Top="204.48" Width="680.4" Height="28.35" CanGrow="true" CanShrink="true" CanBreak="true">
              <RichObject Name="RichSPECIFICATION_FORMATTED2" Left="116.4" Top="4.45" Width="557.55" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichSPECIFICATION_FORMATTED2_AfterData" Text="{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1031{\fonttbl{\f0\fnil Tahoma;}}&#13;&#10;{\*\generator Riched20 10.0.20348}\viewkind4\uc1 &#13;&#10;\pard\f0\fs17\par&#13;&#10;}&#13;&#10;"/>
              <ChildBand Name="Child15" Top="236.83" Width="680.4" Height="52.7" Visible="false" CanBreak="true">
                <ChildBand Name="DataLvKumSumme" Top="293.53" Width="680.4" Height="28.35" CanGrow="true" CanBreak="true">
                  <TextObject Name="Text84" Left="113.4" Top="9.45" Width="406.35" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliert.REFERENCE] [LvKumuliert.BRIEF]" Font="Arial, 10pt, style=Bold"/>
                  <TextObject Name="Text85" Left="519.75" Top="9.45" Width="160.65" Height="18.9" CanBreak="false" Text="[LvKumuliert.G_FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
                  <LineObject Name="Line2" Left="113.4" Top="4.45" Width="567"/>
                  <ChildBand Name="DataZuschlagNachlassAbsolutLV" Top="325.88" Width="680.4" Height="18.9">
                    <TextObject Name="TxtAbsolutTextLVEbene" Left="113.4" Width="548.1" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" Font="Arial, 10pt, style=Italic"/>
                    <TextObject Name="Text2567" Left="538.65" Width="141.75" Height="18.9" CanBreak="false" Text="([LvKumuliert.DISCOUNT_ABS_EBENE])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
                    <ChildBand Name="DataZuschlagNachlassProzentLV" Top="348.78" Width="680.4" Height="18.9">
                      <TextObject Name="TxtProzentTextLVEbene" Left="113.4" Width="264.6" Height="18.9" CanGrow="true" CanShrink="true" Text="Zuschlag / Nachlass" Font="Arial, 10pt, style=Italic"/>
                      <TextObject Name="Text2564" Left="576.45" Width="103.95" Height="18.9" CanBreak="false" Text="([LvKumuliert.FINALDISCOUNT])" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
                      <TextObject Name="Text2565" Left="434.7" Width="141.75" Height="18.9" CanBreak="false" Text="([LvKumuliert.DISCOUNT_PER_EBENE] %)" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Italic"/>
                      <ChildBand Name="DataLvKumSummeSpace" Top="371.68" Width="680.4" Height="18.9"/>
                    </ChildBand>
                  </ChildBand>
                </ChildBand>
              </ChildBand>
            </ChildBand>
          </ChildBand>
        </ChildBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter4" Top="394.58" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" BeforePrintEvent="GroupFooter4_BeforePrint" CanBreak="true"/>
    </GroupHeaderBand>
  </ReportPage>
  <ReportPage Name="PageLvKumuliertMitAufmass" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <GroupHeaderBand Name="GroupHeader2" Width="680.4" Height="86.94" CanShrink="true" RepeatOnEveryPage="true" Condition="[LvKumuliertMitAufmass.PRJ_BOQ_HEADER_FK]" SortOrder="None">
      <TextObject Name="Text40" Top="9.45" Width="680.4" Height="17.79" CanGrow="true" Text="Kumulierte Mengenermittlung" HorzAlign="Center" Font="Tahoma, 12pt, style=Bold"/>
      <TextObject Name="Text52" Top="37.8" Width="31.35" Height="18.9" CanGrow="true" Text="LV:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text53" Left="32.35" Top="37.8" Width="137.75" Height="18.9" CanGrow="true" CanBreak="false" Text="[LvKumuliertMitAufmass.BOQ_NUMMER]" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text54" Left="176.15" Top="37.8" Width="434.7" Height="18.9" CanGrow="true" CanBreak="false" Text="[LvKumuliertMitAufmass.BOQ_TITEL]" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text55" Top="63.13" Width="113.4" Height="18.9" Text="Erläuterung" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text64" Left="176.15" Top="63.13" Width="378" Height="18.9" Text="Rechenansatz" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text65" Left="548.1" Top="63.13" Width="132.3" Height="18.9" Text="Ergebnis" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text2497" Left="113.4" Top="63.13" Width="56.7" Height="18.9" Text="Faktor" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
      <LineObject Name="Line22" Top="85.05" Width="680.4"/>
      <GroupHeaderBand Name="GrphGesamtLV" Top="90.94" Width="680.4" Height="32.13" CanGrow="true" BeforePrintEvent="GrphGesamtLV_BeforePrint" KeepWithData="true" RepeatOnEveryPage="true" Condition="[LvKumuliertMitAufmass.BOQ_ITEM_FK]" SortOrder="None" KeepTogether="true">
        <TextObject Name="Text67" Top="10.96" Width="113.4" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.REFERENCE]" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text68" Left="176.15" Top="10.96" Width="378" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.BRIEF]" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text69" Left="595.35" Top="10.96" Width="85.05" Height="18.9" CanGrow="true" BeforePrintEvent="Text1806_BeforePrint" Text="[LvKumuliertMitAufmass.UOM]" HorzAlign="Right" WordWrap="false" Font="Tahoma, 10pt, style=Bold" Trimming="EllipsisCharacter">
          <Formats>
            <NumberFormat UseLocale="false" DecimalDigits="3" NegativePattern="1"/>
            <GeneralFormat/>
          </Formats>
        </TextObject>
        <DataBand Name="DataGesamtLvAufmassZeileTyp1" Top="127.07" Width="680.4" Height="27.75" CanGrow="true" CanShrink="true" BeforePrintEvent="DataGesamtLvAufmassZeileTyp1_BeforePrint" DataSource="Table14" KeepTogether="true" KeepDetail="true">
          <TextObject Name="Text72" Left="176.15" Top="1" Width="340.2" Height="18.9" Text="[LvKumuliertMitAufmass.LINE_TEXT]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" Font="Tahoma, 10pt" HideNullFields="true"/>
          <TextObject Name="Text73" Left="548.1" Top="1" Width="132.3" Height="18.9" Text="[LvKumuliertMitAufmass.RESULT]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt"/>
          <TextObject Name="Text2500" Left="113.4" Width="56.7" Height="18.9" VisibleExpression="[LvKumuliertMitAufmass.FACTOR] != 1" VisibleCondition="[LvKumuliertMitAufmass.FACTOR] != 1" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.FACTOR]" HorzAlign="Right" Font="Tahoma, 10pt">
            <Formats>
              <NumberFormat/>
              <GeneralFormat/>
            </Formats>
          </TextObject>
          <TextObject Name="Text2525" Width="113.4" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.REMARK_TEXT]" HorzAlign="Right" Font="Tahoma, 10pt">
            <Formats>
              <NumberFormat/>
              <GeneralFormat/>
            </Formats>
          </TextObject>
          <ChildBand Name="DataGesamtLvAufmassZeileTyp2" Top="158.82" Width="680.4" Height="27.59" CanGrow="true" CanShrink="true">
            <TextObject Name="Text2522" Left="176.15" Top="1" Width="340.2" Height="18.9" Text="[LvKumuliertMitAufmass.LINE_TEXT]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" Font="Tahoma, 10pt, style=Bold" HideNullFields="true"/>
            <TextObject Name="Text2523" Left="548.1" Top="1" Width="132.3" Height="18.9" Text="[LvKumuliertMitAufmass.RESULT]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
            <TextObject Name="Text2524" Left="113.4" Width="56.7" Height="18.9" VisibleExpression="[LvKumuliertMitAufmass.FACTOR] != 1" VisibleCondition="[LvKumuliertMitAufmass.FACTOR] != 1" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.FACTOR]" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold">
              <Formats>
                <NumberFormat/>
                <GeneralFormat/>
              </Formats>
            </TextObject>
          </ChildBand>
        </DataBand>
        <GroupFooterBand Name="GrphGesamtLVSumme" Top="190.41" Width="680.4" Height="40.82" CanGrow="true">
          <TextObject Name="Text74" Left="113.4" Top="9.45" Width="425.25" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvKumuliertMitAufmass.REFERENCE] [LvKumuliertMitAufmass.BRIEF]" Font="Tahoma, 10pt, style=Bold"/>
          <TextObject Name="Text75" Left="548.1" Top="9.45" Width="132.3" Height="18.9" Text="[SummeLvKumMenge] [LvKumuliertMitAufmass.UOM]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
          <LineObject Name="Line23" Top="4.45" Width="680.4"/>
        </GroupFooterBand>
      </GroupHeaderBand>
      <GroupFooterBand Name="GroupFooter3" Top="235.23" Width="680.4" Height="18.9" CanShrink="true"/>
    </GroupHeaderBand>
  </ReportPage>
  <ReportPage Name="PageZuwachsHeader" Visible="false" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <PageHeaderBand Name="PageHeader4" Width="680.4" Height="113.4" Border.Lines="Bottom">
      <TextObject Name="Text2478" Top="37.8" Width="113.4" Height="18.9" Text="Projekt-Nr.:" Font="Tahoma, 9pt"/>
      <TextObject Name="Text2479" Left="113.4" Top="37.8" Width="302.4" Height="18.9" Text="[Rechnung.PROJECTNO]" Font="Tahoma, 9pt">
        <Formats>
          <DateFormat/>
          <DateFormat/>
        </Formats>
      </TextObject>
      <PictureObject Name="Picture6" Left="415.8" Top="37.8" Width="264.6" Height="64.26" DataColumn="Company.LOGO" ImageAlign="Top_Right"/>
      <TextObject Name="Text2480" Top="56.7" Width="113.4" Height="18.9" Text="Rechnungs-Nr.:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text2481" Left="113.4" Top="56.7" Width="302.4" Height="18.9" Text="[Rechnung.RE_NR]" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text2482" Top="75.6" Width="113.4" Height="18.9" Text="Projektname:" Font="Tahoma, 9pt"/>
      <TextObject Name="Text2483" Left="113.4" Top="75.6" Width="302.4" Height="18.9" Text="[Rechnung.PROJECT_NAME]" Font="Tahoma, 9pt"/>
    </PageHeaderBand>
    <GroupHeaderBand Name="GroupHeader3" Top="117.4" Width="680.4" Height="40.07" CanShrink="true" RepeatOnEveryPage="true" Condition="[BoqHeader.BOQ_HEADER_FK]" SortOrder="None">
      <DataBand Name="DataLVHeaderLV" Top="161.47" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" BeforePrintEvent="DataLVHeaderLV_BeforePrint" DataSource="Table11">
        <SubreportObject Name="Subreport11" Width="680.4" Height="9.45" ReportPage="PageZuwachsLV"/>
        <ChildBand Name="DataLVHeaderZusammenfassung" Top="184.37" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" BeforePrintEvent="DataLVHeaderZusammenfassung_BeforePrint">
          <SubreportObject Name="Subreport10" Width="680.4" Height="9.45" ReportPage="PageZuwachsLvSummen"/>
          <ChildBand Name="DataLVHeaderAufmass" Top="207.27" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" BeforePrintEvent="DataLVHeaderAufmass_BeforePrint">
            <SubreportObject Name="Subreport13" Width="680.4" Height="9.45" ReportPage="PageZuwachsAufmass"/>
          </ChildBand>
        </ChildBand>
      </DataBand>
    </GroupHeaderBand>
    <PageFooterBand Name="PageFooter4" Top="230.17" Width="680.4" Height="24.19" Border.Lines="Bottom">
      <TextObject Name="Text2486" Top="5.29" Width="680.4" Height="18.9" Text="[Page]/[TotalPages#]" HorzAlign="Right" Font="Tahoma, 9pt"/>
      <ChildBand Name="Child13" Top="258.36" Width="680.4" Height="67.28"/>
    </PageFooterBand>
  </ReportPage>
  <ReportPage Name="PageZuwachsLV" Visible="false" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt" StartPageEvent="PageZuwachsLV_StartPage">
    <DataBand Name="DataTopLevel" Top="109.84" Width="680.4" Height="27.35" CanGrow="true" BeforePrintEvent="DataTopLevel_BeforePrint" DataSource="Table12" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text27" Top="4" Width="113.4" Height="18.9" CanGrow="true" CanShrink="true" Text="[LV.REFERENCE]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text28" Left="113.4" Top="4" Width="567" Height="18.9" CanGrow="true" CanShrink="true" Text="[LV.BRIEF]" Font="Arial, 10pt, style=Bold"/>
      <ChildBand Name="DataLVPosition" Top="141.19" Width="680.4" Height="27.35" CanGrow="true" CanShrink="true">
        <TextObject Name="Text29" Top="5.45" Width="113.4" Height="18.9" CanGrow="true" Text="[LV.REFERENCE]" Font="Arial, 10pt"/>
        <TextObject Name="Text31" Left="113.4" Top="5.45" Width="236.25" Height="18.9" CanGrow="true" Text="[LV.BRIEF]" Font="Arial, 10pt"/>
        <TextObject Name="Text32" Left="444.15" Top="5.45" Width="56.7" Height="18.9" CanGrow="true" BeforePrintEvent="Text1806_BeforePrint" Text="[LV.UOM]" WordWrap="false" Font="Arial, 10pt" Trimming="EllipsisCharacter">
          <Formats>
            <NumberFormat UseLocale="false" DecimalDigits="3" NegativePattern="1"/>
            <GeneralFormat/>
          </Formats>
        </TextObject>
        <TextObject Name="Text33" Left="500.85" Top="5.45" Width="75.6" Height="18.9" CanBreak="false" Text="[LV.PRICE]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
        <TextObject Name="Text34" Left="576.45" Top="5.45" Width="103.95" Height="18.9" CanBreak="false" Text="[LV.FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
        <TextObject Name="Text35" Left="321.3" Top="5.45" Width="122.85" Height="18.9" CanGrow="true" BeforePrintEvent="Text1806_BeforePrint" Text="[LV.QUANTITY]" HideZeros="true" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt" Trimming="EllipsisCharacter">
          <Formats>
            <NumberFormat UseLocale="false" DecimalDigits="3" NegativePattern="1"/>
            <GeneralFormat/>
          </Formats>
        </TextObject>
        <ChildBand Name="DataSpecification" Top="172.54" Width="680.4" Height="27.8" CanGrow="true" CanShrink="true" CanBreak="true">
          <RichObject Name="RichSPECIFICATION_FORMATTED" Left="116.4" Top="4.45" Width="557.55" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichSPECIFICATION_FORMATTED_AfterData" Text="{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1031{\fonttbl{\f0\fnil Tahoma;}}&#13;&#10;{\*\generator Riched20 10.0.20348}\viewkind4\uc1 &#13;&#10;\pard\f0\fs17\par&#13;&#10;}&#13;&#10;"/>
          <ChildBand Name="DataSpace" Top="204.34" Width="680.4" Height="9.45">
            <ChildBand Name="DataBottumLevel" Top="217.79" Width="680.4" Height="56.7">
              <TextObject Name="Text36" Left="113.4" Top="9.45" Width="406.35" Height="18.9" CanGrow="true" CanShrink="true" Text="[LV.REFERENCE] [LV.BRIEF]" Font="Arial, 10pt, style=Bold"/>
              <TextObject Name="Text37" Left="519.75" Top="9.45" Width="160.65" Height="18.9" CanBreak="false" Text="[LV.FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
              <LineObject Name="Line1" Left="113.4" Top="4.45" Width="567"/>
            </ChildBand>
          </ChildBand>
        </ChildBand>
      </ChildBand>
      <DataHeaderBand Name="DataHeader4" Width="680.4" Height="105.84" CanShrink="true" RepeatOnEveryPage="true">
        <TextObject Name="Text2421" Top="9.45" Width="680.4" Height="17.79" CanGrow="true" Text="Leistungsverzeichnis - Zuwachs" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text2422" Top="37.8" Width="31.35" Height="18.9" CanGrow="true" Text="LV:" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2423" Left="32.35" Top="37.8" Width="81.05" Height="18.9" CanGrow="true" Text="[BoqHeader.BOQ_NUMMER]" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2424" Left="113.4" Top="37.8" Width="567" Height="18.9" CanGrow="true" Text="[BoqHeader.BOQ_TITEL]" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2425" Top="66.15" Width="113.4" Height="18.9" Text="OZ" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2426" Left="113.4" Top="66.15" Width="245.7" Height="18.9" Text="Kurztext/Langtext" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2427" Left="368.55" Top="66.15" Width="75.6" Height="18.9" Text="Menge" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2428" Left="444.15" Top="66.15" Width="56.7" Height="18.9" Text="ME" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2429" Left="481.95" Top="66.15" Width="94.5" Height="18.9" Text="EP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2430" Left="576.45" Top="66.15" Width="103.95" Height="18.9" Text="GP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <LineObject Name="Line24" Top="88.05" Width="680.4"/>
      </DataHeaderBand>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageZuwachsLvSummen" Visible="false" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt" StartPageEvent="PageLvZusammenfassung_StartPage">
    <DataBand Name="DataSummaryBold" Top="109.84" Width="680.4" Height="27.59" BeforePrintEvent="DataSummaryBold_BeforePrint" DataSource="Table4">
      <TextObject Name="Text41" Top="4.16" Width="103.95" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvZusammenfassung.REFERENCE]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text42" Left="113.4" Top="4.16" Width="311.85" Height="18.9" CanShrink="true" CanBreak="false" Text="[LvZusammenfassung.BRIEF]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text43" Left="567" Top="4.16" Width="113.4" Height="18.9" CanBreak="false" Text="[LvZusammenfassung.FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold"/>
      <ChildBand Name="DataSummaryNormal" Top="141.43" Width="680.4" Height="27.59">
        <TextObject Name="Text44" Top="4.16" Width="103.95" Height="18.9" CanGrow="true" CanShrink="true" Text="[LvZusammenfassung.REFERENCE]" Font="Arial, 10pt"/>
        <TextObject Name="Text45" Left="113.4" Top="4.16" Width="311.85" Height="18.9" CanShrink="true" CanBreak="false" Text="[LvZusammenfassung.BRIEF]" Font="Arial, 10pt"/>
        <TextObject Name="Text46" Left="425.25" Top="4.16" Width="113.4" Height="18.9" CanBreak="false" Text="[LvZusammenfassung.FINALPRICE]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt"/>
        <ChildBand Name="Child7" Top="173.02" Width="680.4" Height="56.7" VisibleExpression="[LvZusammenfassung.PRINT_MARK] == &quot;LVSUM&quot;" VisibleCondition="[LvZusammenfassung.PRINT_MARK] == &quot;LVSUM&quot;">
          <TextObject Name="Text2419" Left="103.95" Top="18.9" Width="576.45" Height="18.9" Border.Lines="Bottom" Border.BottomLine.Style="Double" Text="[LvZusammenfassung.FINALPRICE]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
          <TextObject Name="Text2420" Left="103.95" Top="18.9" Width="283.5" Height="18.9" Text="Gesamt [BoqHeader.BOQ_TITEL]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
        </ChildBand>
      </ChildBand>
      <DataHeaderBand Name="DataHeader8" Width="680.4" Height="105.84" CanShrink="true" RepeatOnEveryPage="true">
        <TextObject Name="Text47" Top="9.45" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" Text="Zusammenfassung - Zuwachs" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text48" Left="576.45" Top="66.15" Width="103.95" Height="18.9" Text="GP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text49" Top="66.15" Width="103.95" Height="18.9" Text="OZ" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text50" Left="113.4" Top="66.15" Width="321.3" Height="18.9" Text="Kurztext" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text51" Left="425.25" Top="66.15" Width="113.4" Height="18.9" Text="GP" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2431" Top="37.8" Width="31.35" Height="18.9" CanGrow="true" Text="LV:" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2432" Left="32.35" Top="37.8" Width="81.05" Height="18.9" CanGrow="true" Text="[BoqHeader.BOQ_NUMMER]" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text2433" Left="113.4" Top="37.8" Width="567" Height="18.9" CanGrow="true" Text="[BoqHeader.BOQ_TITEL]" Font="Arial, 10pt, style=Bold"/>
        <LineObject Name="Line25" Top="88.05" Width="680.4"/>
      </DataHeaderBand>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageZuwachsAufmass" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt" StartPageEvent="PageAusmass_StartPage">
    <GroupHeaderBand Name="GHOz" Top="109.84" Width="680.4" Height="51.15" CanGrow="true" CanShrink="true" RepeatOnEveryPage="true" Condition="[Aufmass.OZ]" SortOrder="None">
      <TextObject Name="Text1" Top="10.9" Width="113.4" Height="18.9" Text="[Aufmass.OZ]" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text2" Left="176.1" Top="10.9" Width="415.8" Height="18.9" CanGrow="true" CanShrink="true" Text="[Aufmass.BEZEICHNUNG]" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text9" Left="595.35" Top="10.9" Width="85.05" Height="18.9" Text="[Aufmass.ME]" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
      <TextObject Name="Text2499" Left="113.4" Top="10.9" Width="66.15" Height="18.9" VisibleExpression="[Aufmass.FACTOR] != 1" VisibleCondition="[Aufmass.FACTOR] != 1" Text="[Aufmass.FACTOR]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
      <LineObject Name="Line27" Top="32.8" Width="680.4"/>
      <DataHeaderBand Name="DataHeader6" Width="680.4" Height="105.84" CanShrink="true" RepeatOnEveryPage="true">
        <TextObject Name="Text10" Top="9.45" Width="680.4" Height="18.9" CanGrow="true" Text="Mengenermittlung - Zuwachs" HorzAlign="Center" Font="Tahoma, 12pt, style=Bold"/>
        <TextObject Name="Text11" Top="37.8" Width="66.15" Height="18.9" CanGrow="true" Text="Aufmaß:" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text12" Left="66.15" Top="37.8" Width="151.2" Height="18.9" Text="[Aufmass.AUFMASSNUMMER]" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text13" Left="217.35" Top="37.8" Width="463.05" Height="18.9" CanBreak="false" Text="[Aufmass.AUFMASSBEZEICHNUNG]" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text14" Top="63.15" Width="113.4" Height="18.9" Text="Erläuterung" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text15" Left="113.4" Top="63.15" Width="66.15" Height="18.9" Text="Faktor" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text16" Left="179.55" Top="63.15" Width="368.55" Height="18.9" Text="Rechenansatz" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text17" Left="548.1" Top="63.15" Width="132.3" Height="18.9" Text="Ergebnis" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
        <LineObject Name="Line26" Top="85.05" Width="680.4"/>
      </DataHeaderBand>
      <DataBand Name="DataAufmass" Top="164.99" Width="680.4" Height="37.8" CanShrink="true" BeforePrintEvent="DataAufmass_BeforePrint" DataSource="Table13">
        <TextObject Name="Text18" Left="113.4" Top="2" Width="66.15" Height="18.9" VisibleExpression="[Aufmass.FACTOR] != 1" VisibleCondition="[Aufmass.FACTOR] != 1" Text="[Aufmass.FACTOR]" HideZeros="true" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt"/>
        <TextObject Name="Text19" Left="179.55" Top="2" Width="368.55" Height="18.9" Text="[Aufmass.RECHENANSATZ]" Font="Tahoma, 10pt"/>
        <TextObject Name="Text20" Left="548.1" Top="1" Width="132.3" Height="19.9" Text="[Aufmass.ERGEBNIS]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt"/>
        <ChildBand Name="DataAufmassInfoZeile" Top="206.79" Width="680.4" Height="37.8" CanShrink="true">
          <TextObject Name="Text21" Left="113.4" Top="9.45" Width="567" Height="18.9" Text="[Aufmass.RECHENANSATZ]" Font="Tahoma, 10pt, style=Italic"/>
        </ChildBand>
      </DataBand>
      <GroupFooterBand Name="GFAufmass" Top="248.59" Width="680.4" Height="40.8" CanGrow="true" AfterPrintEvent="GFAufmass_AfterPrint">
        <TextObject Name="Text23" Left="132.3" Top="9.45" Width="349.65" Height="18.9" CanGrow="true" Text="[Aufmass.OZ] [Aufmass.BEZEICHNUNG]" Font="Tahoma, 10pt, style=Bold"/>
        <TextObject Name="Text24" Left="548.1" Top="9.45" Width="132.3" Height="18.9" Text="[OZSummeAufmass] [Aufmass.ME]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="3" HorzAlign="Right" Font="Tahoma, 10pt, style=Bold"/>
        <LineObject Name="Line28" Top="3" Width="680.4"/>
      </GroupFooterBand>
    </GroupHeaderBand>
  </ReportPage>
  <ReportPage Name="PageZahlungen" Watermark.Font="Arial, 60pt">
    <DataBand Name="Data7" Width="718.2" Height="28.35" DataSource="Table6">
      <TextObject Name="Text2329" Width="368.55" Height="18.9" Text="[Zahlungen.ZAHLDATUM] zu [Zahlungen.BILL_NO]" Font="Tahoma, 10pt">
        <Formats>
          <DateFormat/>
          <GeneralFormat/>
        </Formats>
      </TextObject>
      <TextObject Name="Text2330" Left="378" Width="132.3" Height="18.9" Text="[[Zahlungen.BRUTTO] * -1]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 10pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2331" Left="18.9" Top="18.9" Width="548.1" Height="9.45" Text="inkl. [Zahlungen.USTEUER] € USt. auf [Zahlungen.NETTO] €" Font="Tahoma, 6pt">
        <Formats>
          <NumberFormat/>
          <NumberFormat/>
        </Formats>
      </TextObject>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageReHistorie" Watermark.Font="Arial, 60pt">
    <DataBand Name="Data6" Width="718.2" Height="28.35" DataSource="Table5">
      <TextObject Name="Text2322" Width="368.55" Height="18.9" Text="[RechnungsHistorie.BILL_NO] vom [RechnungsHistorie.BILL_DATE]" Font="Tahoma, 10pt">
        <Formats>
          <GeneralFormat/>
          <DateFormat/>
        </Formats>
      </TextObject>
      <TextObject Name="Text2323" Left="378" Width="132.3" Height="18.9" Text="[[RechnungsHistorie.FINALGROSS] * -1]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 10pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2324" Left="18.9" Top="18.9" Width="548.1" Height="9.45" Text="inkl. [RechnungsHistorie.FINALTAX] € USt auf [RechnungsHistorie.FINALPRICE] €" Font="Tahoma, 6pt">
        <Formats>
          <NumberFormat/>
          <NumberFormat/>
        </Formats>
      </TextObject>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PagePositionHeader" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <PageHeaderBand Name="PageHeader5" Width="680.4" Height="113.4" Border.Lines="Bottom">
      <TextObject Name="Text22" Top="37.8" Width="113.4" Height="18.9" Text="Projekt-Nr.:" Font="Tahoma, 9pt"/>
      <TextObject Name="Text86" Left="113.4" Top="37.8" Width="302.4" Height="18.9" Text="[Rechnung.PROJECTNO]" Font="Tahoma, 9pt">
        <Formats>
          <DateFormat/>
          <DateFormat/>
        </Formats>
      </TextObject>
      <PictureObject Name="Picture1" Left="415.8" Top="37.8" Width="264.6" Height="64.26" DataColumn="Company.LOGO" ImageAlign="Top_Right"/>
      <TextObject Name="Text88" Top="56.7" Width="113.4" Height="18.9" Text="Rechnungs-Nr.:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text89" Left="113.4" Top="56.7" Width="302.4" Height="18.9" Text="[Rechnung.RE_NR]" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text96" Top="75.6" Width="113.4" Height="18.9" Text="Projektname:" Font="Tahoma, 9pt"/>
      <TextObject Name="Text97" Left="113.4" Top="75.6" Width="302.4" Height="18.9" Text="[Rechnung.PROJECT_NAME]" Font="Tahoma, 9pt"/>
    </PageHeaderBand>
    <DataBand Name="Data2" Top="117.4" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" BeforePrintEvent="DataLVHeaderLV_BeforePrint">
      <SubreportObject Name="Subreport20" Top="9.45" Width="680.4" Height="18.9" ReportPage="PagePositionen"/>
    </DataBand>
    <PageFooterBand Name="PageFooter5" Top="159.2" Width="680.4" Height="24.19" Border.Lines="Bottom">
      <TextObject Name="Text100" Top="5.29" Width="680.4" Height="18.9" Text="[Page]/[TotalPages#]" HorzAlign="Right" Font="Tahoma, 9pt"/>
      <ChildBand Name="Child18" Top="187.39" Width="680.4" Height="67.28"/>
    </PageFooterBand>
  </ReportPage>
  <ReportPage Name="PagePositionen" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt">
    <DataBand Name="DataMatPosition" Top="46.25" Width="680.4" Height="28.35" BeforePrintEvent="DataMatPosition_BeforePrint" DataSource="Table9">
      <TextObject Name="Text2346" Top="6.45" Width="56.7" Height="18.9" Text="[Positionen.ITEMNO]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text2347" Left="56.7" Top="6.45" Width="330.75" Height="18.9" Text="[Positionen.DESCRIPTION1]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text2348" Left="387.45" Top="6.45" Width="75.6" Height="18.9" Text="[Positionen.QUANTITY]" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 9pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2349" Left="519.75" Top="6.45" Width="75.6" Height="18.9" Text="[Positionen.PRICE]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 9pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2350" Left="595.35" Top="6.45" Width="85.05" Height="18.9" Text="[Positionen.AMOUNT_NET]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 9pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2356" Left="463.05" Top="6.45" Width="56.7" Height="18.9" Text="[Positionen.UOM]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" WordWrap="false" Font="Tahoma, 9pt" Trimming="EllipsisCharacter"/>
      <ChildBand Name="ChildMatPositionRichText" Top="78.6" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" CanBreak="true">
        <RichObject Name="RichMatPosText" Left="56.7" Top="4.45" Width="330.75" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichMatPosText_AfterData"/>
        <ChildBand Name="ChildMatPositionPlainText" Top="120.4" Width="680.4" Height="37.8" CanGrow="true" CanShrink="true" BeforePrintEvent="ChildMatPositionPlainText_BeforePrint" CanBreak="true">
          <TextObject Name="Text2391" Left="56.7" Top="4.54" Width="330.75" Height="18.9" CanGrow="true" CanShrink="true" Text="[Positionen.SPECIFICATION]" Font="Arial, 10pt"/>
        </ChildBand>
      </ChildBand>
      <DataHeaderBand Name="DataHeader3" Width="680.4" Height="42.25" CanShrink="true" RepeatOnEveryPage="true">
        <TextObject Name="Text4" Top="4.45" Width="56.7" Height="18.9" Text="Pos." Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text5" Left="56.7" Top="4.45" Width="330.75" Height="18.9" Text="Bezeichnung" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text6" Left="387.45" Top="4.45" Width="75.6" Height="18.9" Text="Menge" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text7" Left="519.75" Top="4.45" Width="75.6" Height="18.9" Text="EP" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text8" Left="595.35" Top="4.45" Width="85.05" Height="18.9" Text="GP" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2352" Left="463.05" Top="4.45" Width="56.7" Height="18.9" Text="ME" Font="Tahoma, 9pt, style=Bold"/>
        <LineObject Name="Line29" Top="23.35" Width="680.4"/>
      </DataHeaderBand>
      <DataFooterBand Name="DataFooter1" Top="162.2" Width="680.4" Height="132.3" CanGrow="true" CanShrink="true">
        <TextObject Name="Text2353" Left="567" Top="9.45" Width="113.4" Height="18.9" Text="[PositionenNetto]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2354" Left="567" Top="37.8" Width="113.4" Height="18.9" Text="[PositionenMwSt]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2355" Left="567" Top="66.15" Width="113.4" Height="18.9" Text="[PositionenBrutto]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2357" Left="255.15" Top="9.45" Width="311.85" Height="18.9" Text="Betrag netto" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2358" Left="255.15" Top="37.8" Width="311.85" Height="18.9" Text="USt. [Rechnung.MWST_PROZENT] %" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2359" Left="255.15" Top="66.15" Width="311.85" Height="18.9" Text="Betrag Brutto" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="TxtZK2" Top="103.95" Width="680.4" Height="18.9" CanGrow="true" Text="Zahlungskondition" Font="Arial, 10pt"/>
        <LineObject Name="Line4" Left="255.15" Top="85.05" Width="425.25"/>
        <LineObject Name="Line5" Left="255.15" Top="56.7" Width="425.25"/>
        <LineObject Name="Line30" Width="680.4"/>
        <ChildBand Name="Child8" Top="298.5" Width="680.4" Height="37.8" Visible="false" CanGrow="true" CanShrink="true" CanBreak="true">
          <TextObject Name="TxtUst2" Top="9.45" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" Text="USt. Text" Font="Arial, 10pt"/>
          <ChildBand Name="DataUstText2" Top="340.3" Width="680.4" Height="37.8" Visible="false" CanGrow="true" CanShrink="true" CanBreak="true">
            <RichObject Name="RichUst2" Top="9.45" Width="680.4" Height="18.9" CanGrow="true" CanShrink="true" AfterDataEvent="RichUst2_AfterData" Text="{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1031{\fonttbl{\f0\fnil\fcharset0 Arial;}{\f1\fnil Tahoma;}}&#13;&#10;{\*\generator Riched20 10.0.20348}\viewkind4\uc1 &#13;&#10;\pard\fs20 Ust. Text\f1\fs17\par&#13;&#10;}&#13;&#10;"/>
          </ChildBand>
        </ChildBand>
      </DataFooterBand>
    </DataBand>
  </ReportPage>
  <ReportPage Name="PageGeneralien" RawPaperSize="9" LeftMargin="15" TopMargin="0" RightMargin="15" BottomMargin="0" Watermark.Font="Arial, 60pt" StartPageEvent="PageAusmass_StartPage">
    <DataBand Name="DataGeneralienBand" Top="79.6" Width="680.4" Height="37.8" CanShrink="true" BeforePrintEvent="DataGeneralienBand_BeforePrint" DataSource="Table18">
      <TextObject Name="Text2557" Top="9.45" Width="264.6" Height="18.9" Text="[Generalien.TYPE]" Font="Tahoma, 9pt"/>
      <TextObject Name="TxtGeneralienWert" Left="595.35" Top="9.45" Width="85.05" Height="18.9" Text="[Generalien.WERT] %" Format="Number" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Tahoma, 9pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2559" Left="264.6" Top="9.45" Width="321.3" Height="18.9" Text="[Generalien.KOMMENTAR]" Font="Tahoma, 9pt"/>
      <DataHeaderBand Name="DataHeader7" Width="680.4" Height="75.6" CanShrink="true">
        <TextObject Name="Text58" Top="9.45" Width="680.4" Height="18.9" CanGrow="true" Text="Generalien" Font="Tahoma, 12pt, style=Bold"/>
        <TextObject Name="Text2554" Top="37.8" Width="264.6" Height="18.9" Text="Bezeichnung" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2555" Left="264.6" Top="37.8" Width="321.3" Height="18.9" Text="Kommentar" Font="Tahoma, 9pt, style=Bold"/>
        <TextObject Name="Text2556" Left="595.35" Top="37.8" Width="85.05" Height="18.9" Text="Wert" HorzAlign="Right" Font="Tahoma, 9pt, style=Bold"/>
        <LineObject Name="Line19" Top="59.7" Width="680.4"/>
      </DataHeaderBand>
    </DataBand>
  </ReportPage>
</Report>
