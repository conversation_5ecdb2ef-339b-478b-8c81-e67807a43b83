/*
 * Copyright(c): RIB Software GmbH
 */

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Globalization;
using System.IO;
using System.Linq;
using RIB.Visual.Platform.Core;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using System.ComponentModel.Composition;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Platform.OperationalManagement;
using NLS = RIB.Visual.Model.Project.Localization.Properties.Resources;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Model.Project.Core;
using RIB.Visual.Services.Scheduler.BusinessComponents;
using RIB.Visual.Services.Scheduler.Core;
using Newtonsoft.Json;
using NLog;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Common.BusinessComponents;
using System.Data.Entity.Infrastructure;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Platform.Server.Common;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using SLE = System.Linq.Expressions;
using System.Data.SqlClient;
using System.Data;
using System.Transactions;
using RIB.Visual.Platform.Common;
using System.Linq.Dynamic;

// ReSharper disable once CheckNamespace

namespace RIB.Visual.Model.Project.BusinessComponents
{
	/// <summary>
	/// Model project Business Logic should be placed here
	/// 
	/// class derived from platform LogicBase Class
	/// </summary>
	[Export("model", typeof(IChangeStatus))]
	[Export(typeof(IModelLogic))]
	[Export("Model.Project.ModelEntity", typeof(IDataBaseLogic))]
	[Export("Model.Project.ModelUpdateEntity", typeof(IDataBaseUpdateLogic))]
	public partial class ModelProjectModelLogic : EntityUpdateLogic<ModelEntity, IdentificationData>, IModelLogic,
		IDataBaseLogic, IUpdateCompleteData, IEntityProvider, IChangeStatus
	{
		/// <summary>
		/// The GUID for profile settings for applications that must be run on the render server.
		/// </summary>
		public const String ModelStorageProfileApplicationGuid = "72c6257ab9ab44b78b9c1286ac9e4029";

		/// <summary>
		/// The GUID for profile settings for WDE conversion-related applications.
		/// </summary>
		public const String WdeModelStorageProfileApplicationGuid = "250ccd1f5f9b4ec88098b475679b815e";

		/// <summary>
		/// The access right descriptor GUID for the entity.
		/// </summary>
		public const String EntityPermissionGuid = "d4d807d4047e439d9ba536d7114e9009";

		/// <summary>
		/// The access right descriptor GUID for the expiry mechanics.
		/// </summary>
		public const String ExpiryPermissionGuid = "cc2c8d4010bd4a398623345423024bca";

		/// <summary>
		/// The access right descriptor GUID for SCS export.
		/// </summary>
		public const String ScsExportPermissionGuid = "1e62c715fbe94f3f86af969d9dacefb6";

		/// <summary>
		/// The singleton identifier instance for the <see cref="ModelEntity"/> type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<ModelEntity>> IdentifierInstance =
			IdentifierFactory.Create<ModelEntity>("Id");

		private static readonly LazyExportedValue<IProjectInfoProvider> ProjectInfoProvider = new();

		private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

		/// <summary>
		///  Model Logic constructor, doing some initialisation
		/// </summary>
		public ModelProjectModelLogic()
		{
			//InitializeLogic(ModelBuilder.DbModel, "MDL_MODEL", InitEntityFunc);
			CompleteUpdater = this;
			Identifier = IdentifierInstance.Value;
			PermissionGUID = EntityPermissionGuid;
			SetRelationInfoIdentifier(ModelRelationInfo.Identifier);
			OrderByExpressions = new[]
			{
				OrderTerm.Create(e => e.Code),
				OrderTerm.Create(e => e.Id)
			};
			OrderByKey = e => Identifier.GetEntityIdentification(e);

#if OLD_ONE
			FilterByPermission =
				(ctx, query) =>
					query.Join(new DbFunctions(ctx).UserAccess(), outer => outer.Id, inner => inner.Id, (outer, inner) => outer);
#endif //OLD_ONE

			FilterByPermission = (dbContext, query) =>
			{
				var requestId = BusinessApplication.BusinessEnvironment.RegisterTempIdsRequestUuid();
				Logger.Debug("execute ModelProjectModelLogic.FilterByPermission" + requestId);
				dbContext.ExecuteStoredProcedure<ModelUserAccessProcResult>("MDL_MODELUSERACCESS_SP", Context.UserId, Context.ClientId, Context.PermissionRoleId, requestId).ToList();
				var c1 = query.Count<ModelEntity>();
				var result = query.Where(a => dbContext.Entities<DdTempIdsEntity>().Any(tempId => tempId.RequestId == requestId && tempId.Id == a.Id));
				var c2 = result.Count<ModelEntity>();
				Logger.Debug("  c1=" + c1 + " c2=" + c2);
				return result;
			};
		}

		/// <summary>
		/// Returns the name of the underlying table, <c>CAL_CALENDAR</c>.
		/// </summary>
		/// <returns></returns>
		protected override string GetEntityTableName()
		{
			return "MDL_MODEL";
		}

		/// <summary>
		/// Gets the database model.
		/// </summary>
		/// <returns>The database model.</returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <param name="creationData">A structure with parent IDs.</param>
		/// <returns>The newly created entity.</returns>
		public override ModelEntity Create(IdentificationData creationData)
		{
			return Create(creationData.Id);
		}

		/// <summary>
		/// Creates an entity.
		/// </summary>
		/// <param name="projectId">The project ID.</param>
		/// <param name="compositeModel">Indicates whether the new model should be a composite model.</param>
		/// <param name="familyId">The ID of an existing model family, if any.</param>
		/// <param name="documentTypeId">The document Type Id of the model.</param>
		/// <returns>The newly created entity.</returns>
		public ModelEntity Create(Int32 projectId, Boolean compositeModel = false, Int32? familyId = null, Int32? documentTypeId = null)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);

			var entity = new ModelEntity
			{
				Id = this.SequenceManager.GetNext("MDL_MODEL"),
				IsComposite = compositeModel,
				ModelFamilyFk = familyId
			};

			// init default values here
			// code generation
			var codeSetting =
				new BasicsCompanyNumberLogic().GetGenerationSettingsForRubric(ModelConstants.ModelRubric)
					.FirstOrDefault(s => s.RubricCatID == ModelConstants.ModelRubricCategory);
			if (codeSetting != null && (codeSetting.HasToCreate || codeSetting.CanCreate))
			{
				entity.Code = new BasicsCompanyNumberLogic().CreateCompanyNumber(ModelConstants.ModelRubricCategory);
			}

			entity.ProjectFk = projectId;
			var projectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var prjListIds = new List<int?>
			{
				projectId
			};
			var projectEntities = projectLogic.GetItemsByKey(prjListIds);
			entity.ProjectItem = (IProjectEntity)projectEntities.FirstOrDefault(e => e.Id == entity.ProjectFk);
			if (entity.ProjectItem == null)
			{
				throw new BusinessLayerException(String.Format(CultureInfo.CurrentCulture, NLS.ERR_InvalidProjectId, projectId));
			}

			entity.IsLive = true;

			entity.DocumentTypeFk = documentTypeId;

			var uuid = Guid.NewGuid();
			entity.Uuid = uuid.ToString("N");
			AssignDefaultSettings(entity);
			return entity;
		}

		private static void AssignDefaultFk<TItem, TLogic>(ModelEntity entity, Action<ModelEntity, Int32> setValue, ModelProjectSettingsEntity pjSettings,
			Func<ModelProjectSettingsEntity, Int32?> getPjDefaultId)
		where TItem : EntityBase, IIdentifyable, ICloneable, new()
		where TLogic : EntityUpdateLogic<TItem, IdentificationData>, new()
		{
			var pjDefId = pjSettings != null ? getPjDefaultId(pjSettings) : null;
			if (pjDefId.HasValue)
			{
				setValue(entity, pjDefId.Value);
			}
			else
			{
				var logic = new TLogic();
				var defItem = logic.GetDefault();
				if (defItem != null)
				{
					setValue(entity, defItem.Id);
				}
				else
				{
					defItem = logic.GetByFilter(e => true).FirstOrDefault();
					if (defItem != null)
					{
						entity.LodFk = defItem.Id;
					}
				}
			}
		}

		private void AssignDefaultSettings(ModelEntity entity)
		{
			if (entity == null)
			{
				throw new ArgumentNullException("entity");
			}

			var pjId = entity.ProjectFk;
			var pjSettings = pjId.HasValue ? new ModelProjectSettingsLogic().GetByFilter(e => e.ProjectFk == pjId).FirstOrDefault() : null;

			AssignDefaultFk<BasicsCustomizeModelLevelOfDevelopmentEntity, BasicsCustomizeModelLevelOfDevelopmentLogic>(
				entity, (m, lodId) => m.LodFk = lodId, pjSettings, s => s.LodFk);
			AssignDefaultFk<BasicsCustomizeModelTypeEntity, BasicsCustomizeModelTypeLogic>(entity,
				(m, typeId) => m.TypeFk = typeId, pjSettings, s => s.TypeFk);

			var statLogic = new BasicsCustomizeModelStatusLogic();
			var statDef = statLogic.GetDefault();
			if (statDef != null)
			{
				entity.StatusFk = statDef.Id;
			}

			if (pjSettings != null && pjSettings.Active)
			{
				entity.ExpiryDate = pjSettings.ExpiryDate;
				entity.ExpiryDays = pjSettings.ExpiryDays;
			}
		}

		private static IQueryable<ModelEntity> FilterOutInternalModels(IQueryable<ModelEntity> query, DbContext dbCtx)
		{
			return query.Where(e => !e.IsTemporary && !dbCtx.Entities<ModelSourceDocumentEntity>().Any(sd => sd.ModelFk == e.Id));
		}

		/// <summary>
		/// Gets all models which reference the same project ID.
		/// </summary>
		/// <param name="projectId">The project ID.</param>
		/// <returns>An enumeration of all models in the project.</returns>
		public IEnumerable<IModelEntity> GetModelsByProjectId(Int32 projectId)
		{
			using (var dbcontext = new DbContext(ModelBuilder.DbModel))
			{
				IQueryable<ModelEntity> query = dbcontext.Entities<ModelEntity>();
				query = FilterOutInternalModels(query, dbcontext);

				query = query.Where(e => e.ProjectFk == projectId);

				return query.ToArray().OrderBy(e => e.Code);
			}
		}

		private static Int32[] GetPublicOpenStatusIds()
		{
			return new BasicsCustomizeModelStatusLogic().GetListByFilter(e => e.IsPublicOpen).Select(e => e.Id).ToArray();
		}

		/// <summary>
		/// Gets all models which reference the same project ID and are visible via the public API.
		/// </summary>
		/// <param name="projectId">The project ID.</param>
		/// <returns>An enumeration of all models in the project.</returns>
		public IEnumerable<IModelEntity> GetPublicOpenModelsByProjectId(Int32 projectId)
		{
			var statusIds = GetPublicOpenStatusIds();

			using (var dbcontext = CreateDbContext())
			{
				IQueryable<ModelEntity> query =
					dbcontext.Entities<ModelEntity>().Where(e => statusIds.Contains(e.StatusFk));
				query = FilterOutInternalModels(query, dbcontext);

				query = query.Where(e => e.ProjectFk == projectId);

				return query.ToArray().OrderBy(e => e.Code);
			}
		}

		private static SLE.Expression<Func<ModelEntity, Boolean>> CreatePublicOpenExpression()
		{
			var statusIds = GetPublicOpenStatusIds();
			return e => !e.IsTemporary && statusIds.Contains(e.StatusFk);
		}

		/// <summary>
		/// Indicates whether a given model is accessible via the public API.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>A value indicating whether the model is accessible.</returns>
		/// <remarks>
		/// <para>This method checks whether a given model can be accessed via the public API.
		///   In the case of a composite model, it does not check the sub-models.</para>
		/// </remarks>
		public PublicOpenInfoEntity IsModelPublicOpen(Int32 modelId)
		{
			using (var dbCtx = CreateDbContext())
			{
				var expr = CreatePublicOpenExpression();
				var result = new PublicOpenInfoEntity
				{
					ModelId = modelId,
					IsModelAccessible = dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Any(expr),
					AreSubModelsAccessible =
						dbCtx.Entities<SubModelEntity>()
							.Where(e => e.ModelFk == modelId)
							.Select(e => dbCtx.Entities<ModelEntity>().FirstOrDefault(m => m.Id == e.ModelPartFk))
							.Where(e => e != null)
							.All(expr)
				};
				return result;
			}
		}

		/// <summary>
		/// Update Composite Model Version
		/// </summary>
		/// <param name="projectId">The project ID.</param>
		/// <param name="isComposite">is it composite model or not</param>
		/// <param name="modelRootFk">Composite Root Model ID</param>
		/// <param name="versionCode">version code</param>
		/// <param name="versionDescription">version Description</param>
		/// <param name="modelVersion">model Version number</param>
		/// <param name="modelRevision">model Revision text </param>
		/// <param name="subModelIds">Sub Model Ids </param>
		/// <returns></returns>
		public ModelEntity UpdateCompositeModel(Int32 projectId, Boolean isComposite,
			Int32? modelRootFk, String versionCode, String versionDescription, Int32 modelVersion, String modelRevision,
			Int32[] subModelIds)
		{
			int versionNum;
			var selectedModelId = modelRootFk;
			ModelEntity rootModel;
			ModelEntity selectedModel;
			using (var dbCtx = CreateDbContext())
			{
				selectedModel = dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == selectedModelId);
				var modelId = selectedModel.Id;

				var rootInfo = dbCtx.Entities<ModelRootVEntity>().First(e => e.ModelFk == modelId);

				if (rootInfo.IsRoot)
				{
					rootModel = selectedModel;
				}
				else
				{
					rootModel = dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == rootInfo.ModelRootFk);
				}

				var versionModels = dbCtx.Entities<ModelEntity>().Where(e => e.ModelFamilyFk == selectedModel.ModelFamilyFk && !e.IsTemporary).ToArray();
				if (versionModels.Any())
				{
					// Get Max versionId
					versionNum = versionModels.Max(e => e.RevisionId);

				}
				else
				{
					versionNum = 0;
				}
			}


			var dto = Create(projectId, isComposite, selectedModel.ModelFamilyFk);
			dto.RevisionId = versionNum + 1;

			new ModelProjectSettingsLogic().AssignVersionedText(rootModel, dto, dto.RevisionId);

			if (!String.IsNullOrWhiteSpace(versionCode))
			{
				dto.Code = versionCode;
			}

			if (!String.IsNullOrEmpty(versionDescription))
			{
				dto.Description = versionDescription;
			}


			dto.ModelRevision = modelRevision;
			dto.ModelVersion = modelVersion;
			if (isComposite)
			{
				dto.IsImported = true;
			}
			ModelEntity parentModel = Save(dto);
			ModelProjectModelPartLogic modelPartLogic = new ModelProjectModelPartLogic();
			for (int i = 0; i < subModelIds.Count(); i++)
			{
				ModelPartEntity modelPartEntity = modelPartLogic.Create(parentModel.Id, subModelIds[i]);
				modelPartLogic.Save(modelPartEntity);
			}
			return dto;
		}

		/// <summary>
		/// ThrowException
		/// </summary>
		/// <param name="nlsResources"> for Resources </param>
		private void ThrowException(string nlsResources)
		{
			throw new BusinessLayerException()
			{
				ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError,
				ErrorMessage = nlsResources
			};
		}

		/// <summary>
		/// Default validation will not check anything.
		/// </summary>
		/// <param name="entity"></param>
		public override void Validate(ModelEntity entity)
		{
			if (entity.LodFk == 0)
			{
				ThrowException(NLS.ERR_NoLodDefined);
			}
			if (entity.StatusFk == 0)
			{
				ThrowException(NLS.ERR_NoStatusDefined);
			}
			if (entity.TypeFk == 0)
			{
				ThrowException(NLS.ERR_NoTypeDefined);
			}
			//CreateExecuteTask(entity);
		}

		/// <summary>
		/// Default validation will not check anything.
		/// </summary>
		/// <param name="entities"></param>
		public override void Validate(IEnumerable<ModelEntity> entities)
		{
			foreach (var item in entities)
			{
				Validate(item);
			}
		}

		/// <summary>
		/// deletes a given list of model entities
		/// </summary>
		/// <returns></returns>
		public void SetIsLiveFalse(IEnumerable<IIdentifyable> toDelete)
		{
			var entities = new List<ModelEntity>();

			foreach (var item in toDelete)
			{
				var ent = (ModelEntity)item;

				if (ent != null)
				{
					if (ent.IsLive)
					{
						ent.IsLive = false;
						entities.Add(ent);
					}
				}
			}
			Save(entities);
		}

		/// <summary>
		/// Saves entities.
		/// </summary>
		/// <param name="entities">The entities to save.</param>
		/// <param name="dbContext"></param>
		/// <returns>The list of saved entities.</returns>
		protected override void SavePostProcessing(IEnumerable<ModelEntity> entities, DbContext dbContext)
		{
			foreach (var e in entities)
			{
				dbContext.ExecuteStoredProcedure("MDL_CHANGESET_PROC", e.Id);
			}

			dbContext.ExecuteStoredProcedure("MDL_REMOVETMPMODELS_PROC");
		}

		/// <summary>
		/// Edit entity before saving.
		/// </summary>
		/// <param name="entity">The entity to edit.</param>
		protected override void SavePreProcessing(ModelEntity entity)
		{
			if (entity.CompanyFk < 1)
			{
				entity.CompanyFk = entity.ProjectFk.HasValue
					? BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>()
						.GetProjectById(entity.ProjectFk.Value).CompanyFk
					: Context.ClientId;
				base.SavePreProcessing(entity);
			}

			if (!entity.IsTemporary && !entity.ModelFamilyFk.HasValue)
			{
				var familyLogic = new ModelFamilyLogic();
				var family = familyLogic.Create();
				familyLogic.Save(family);
				entity.ModelFamilyFk = family.Id;
			}
		}

		/// <summary>
		/// Edit entities before saving.
		/// </summary>
		/// <param name="entities">The entity to edit.</param>
		protected override void SavePreProcessing(IEnumerable<ModelEntity> entities)
		{
			if (!entities.TryToNonEmptyArray(out var allEntities))
			{
				return;
			}

			foreach (var entity in allEntities)
			{
				if (entity.CompanyFk < 1)
				{
					entity.CompanyFk = entity.ProjectFk.HasValue ? BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>()
						.GetProjectById(entity.ProjectFk.Value).CompanyFk : Context.ClientId;
				}
			}

			var familyLogic = new Lazy<ModelFamilyLogic>();
			foreach (var entity in allEntities.Where(e => !e.IsTemporary && !e.ModelFamilyFk.HasValue))
			{
				var family = familyLogic.Value.Create();
				familyLogic.Value.Save(family);
				entity.ModelFamilyFk = family.Id;
			}

			base.SavePreProcessing(allEntities);
		}

		/// <summary>
		/// Deletes a model with a given ID, including all dependent data (e.g. modelfile, archivefile, objects, objects2property, property, marker).
		/// </summary>
		/// <param name="id">The ID of the model to delete.</param>
		/// <param name="useJob">Indicates whether a scheduler job should be spawned for removing the stream cache data from the model storage.</param>
		/// <param name="taskLogger">An optional task logger for receiving log output.</param>
		/// <remarks>
		/// <para>This method deletes a model with a given ID, including all dependent data (e.g. modelfile, archivefile, objects, objects2property, property, marker).
		///   If the model is the root of a model family, the entire model family will be deleted.</para>
		/// </remarks>
		public void DeleteComplete(Int32 id, bool useJob = true, ILogging taskLogger = null)
		{
			Permission.Ensure(PermissionGUID, Permissions.Delete);
			var partsLogic = new ModelProjectModelPartLogic();
			List<Int32> elementsIds = new List<Int32>();

			// Get Model Versions to be deleted
			ModelProjectModelVersionLogic modelVersionLogic = new ModelProjectModelVersionLogic();
			var modelVersions = modelVersionLogic.GetModelHistory(id).Select(e => e.Id);

			if (modelVersions.TryToNonEmptyArray(out var allModelVersions))
			{
				elementsIds.AddRange(allModelVersions);
			}
			else
			{
				elementsIds.Add(id);
			}

			var retryPolicy = new RetryPolicy
			{
				MaxRetryCount = 3,
				Delay = TimeSpan.FromSeconds(5)
			};

			retryPolicy.Execute(() =>
			{
				try
				{
					List<IdentificationData> specs = new List<IdentificationData>();
					foreach (var elementId in elementsIds)
					{
						specs.Add(new IdentificationData { Id = elementId });
					}

					var entities = GetByIds(specs).ToArray();
					var fileLogic = new ModelProjectModelFileLogic();
					var entitiesIds = entities.Select(x => x.Id).ToList();

					var startTime = DateTime.UtcNow;

					using (var dbCtx = CreateDbContext())
					{
						var files = from t in dbCtx.Entities<ModelFileEntity>()
										where entitiesIds.Contains(t.ModelFk)
										select t;
						var subParts = from t in dbCtx.Entities<ModelPartEntity>()
											where entitiesIds.Contains(t.ModelFk)
											select t;
						if (files.Any())
						{
							fileLogic.Delete(files);
						}

						if (subParts.Any())
						{
							partsLogic.Delete(subParts);
						}

						if (entities.Length > 0)
						{
							if (useJob)
							{
								ExecuteDeletionTask(entities);
							}
							else
							{
								// Directly use the deletion task without a job
								var modelDeletionActionTask = BusinessApplication.BusinessEnvironment.GetExportedValue<IModelActionTask>();
								modelDeletionActionTask.ExecuteType = ExecuteType.Single;
								if (taskLogger != null)
								{
									modelDeletionActionTask.TaskLogger = taskLogger;
								}

								modelDeletionActionTask.ModelDataNames = String.Join(",", entities.Select(GetModelDataName));
								modelDeletionActionTask.Execute();
							}

							BusinessApplication.BusinessEnvironment.GetExportedValue<IModelObject2PropertyLogic>()
								 .PurgePropertiesForModels(entities.Select(e => e.Id));

							Delete(entities);
						}
						dbCtx.SaveChanges();
					}

					var endTime = DateTime.UtcNow;
					var duration = endTime - startTime;
					Console.WriteLine($"DeleteComplete completed in {duration.TotalSeconds} seconds");

				}
				catch (Exception ex)
				{
					// Log exception
					Console.WriteLine($"DeleteComplete failed: {ex.Message}");
					throw;
				}
			});
		}
		/// <summary>
		/// RetryPolicy
		/// </summary>
		public class RetryPolicy
		{
			/// <summary>
			/// MaxRetryCount
			/// </summary>
			public int MaxRetryCount { get; set; }
			/// <summary>
			/// Delay
			/// </summary>
			public TimeSpan Delay { get; set; }

			/// <summary>
			/// Execute
			/// </summary>
			/// <param name="action"></param>
			public void Execute(Action action)
			{
				int retryCount = 0;
				while (true)
				{
					try
					{
						action();
						return;
					}
					catch (Exception ex) when (retryCount < MaxRetryCount && IsTransient(ex))
					{
						retryCount++;
						Console.WriteLine($"Retry {retryCount}/{MaxRetryCount} after exception: {ex.Message}");
						System.Threading.Thread.Sleep(Delay);
					}
				}
			}

			private bool IsTransient(Exception ex)
			{
				// Add logic to determine if the exception is transient
				return ex is SqlException sqlEx && sqlEx.Number == -2; // Example for SQL timeout
			}
		}

		/// <summary>
		/// Returns the settings for deletion of expired models and all its dependant data
		/// </summary>
		/// <returns></returns>
		public ModelExpiryDataEntity ExpiryLoadRecurring()
		{
			Permission.Ensure(ExpiryPermissionGuid, Permissions.Read);

			var modelExpiryDataEntity = new ModelExpiryDataEntity();

			// check for already present job
			int? jobId = LastExpiredModelsJobId(false);
			if (jobId.HasValue)
			{
				var jobLogic = new JobLogic();
				var job = jobLogic.GetJobById(jobId.Value);

				if (job.Enable == true)
				{
					modelExpiryDataEntity.Execution = (Frequency)(job.RepeatUnit ?? 0);
				}
				else
				{
					modelExpiryDataEntity.Execution = Frequency.None;
				}

				modelExpiryDataEntity.RepeatFactor = (int)(job.RepeatCount ?? 1);
				modelExpiryDataEntity.LogLevel = (LoggingLevel)job.LoggingLevel;
				modelExpiryDataEntity.StartTime = job.StartTime ?? DateTime.Now;
			}
			else
			{
				modelExpiryDataEntity.RepeatFactor = 1;
				modelExpiryDataEntity.Execution = Frequency.None;
				modelExpiryDataEntity.LogLevel = LoggingLevel.Warning;
				modelExpiryDataEntity.StartTime = DateTime.Now;
			}
			return modelExpiryDataEntity;
		}


		/// <summary>
		/// Save the settings for deletion of expired models and all its dependant data
		/// (e.g. modelfile, archivefile, objects, objects2property, property, marker)
		/// </summary>
		public bool ExpirySaveRecurring(ModelExpiryDataEntity modelExpiryDataEntity)
		{
			Permission.Ensure(ExpiryPermissionGuid, Permissions.Execute);

			var result = false;
			var jobLogic = new JobLogic();
			JobEntity job = null;

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				try
				{
					// check for already present job
					int? jobId = LastExpiredModelsJobId(false);
					if (jobId.HasValue)
					{
						job = jobLogic.GetJobById(jobId.Value);
					}
					else
					{
						// create a new job
						job = jobLogic.Create();
					}
					job.TaskType = ModelExpirySchedulerTask.TaskTypeId;
					job.Name = "Determine Expired Models Recurring";
					job.Description = "Scan all models for expiry settings and conditions.";
					job.Enable = modelExpiryDataEntity.Execution != Frequency.None;
					job.LoggingLevel = (Int16)modelExpiryDataEntity.LogLevel;
					job.Priority = (Int16)Priority.Normal;
					job.ParameterList = "[]";
					job.StartTime = modelExpiryDataEntity.StartTime;
					job.RepeatCount = (Int16)(modelExpiryDataEntity.RepeatFactor >= 1 ? modelExpiryDataEntity.RepeatFactor : 1);
					job.RepeatUnit = (Int16)modelExpiryDataEntity.Execution;
					job.JobState = (Int16)JobState.Repetitive;
					job.KeepDuration = AppSettingsReader.ReadInt("model-expiry:job-deletion-threshold-days", 14);
					jobLogic.AddJob(job);
					result = true;
				}
				catch (Exception e)
				{
					var message = String.Format("Expired Models removing - Exception: {0} Source: {1} Stacktrace: {2}",
						e.Message, e.Source, e.StackTrace);
					System.Diagnostics.Trace.WriteLine(message);
					result = false;
				}
				finally
				{
					transaction.Complete();
				}
			}

			/*	Don't start a repetitive job, done by scheduler
			if (job?.Id != 0)
			{
				if (jobLogic.StartJob(job.Id, out job) != SchedulerError.SchedulerStartSuccessfully)
				{
					result = true;
				}
			}
			*/

			return result;
		}


		/// <summary>
		/// Run one independant expiry job.
		/// </summary>
		/// <returns></returns>
		public bool ExpiryRunOnce()
		{
			Permission.Ensure(ExpiryPermissionGuid, Permissions.Execute);

			var result = false;
			var jobLogic = new JobLogic();

			// no transaction needed

			try
			{
				// create a new job
				JobEntity job = jobLogic.Create();
				job.TaskType = ModelExpirySchedulerTask.TaskTypeId;
				job.Name = "Determine Expired Models Once";
				job.Description = "Scan all models for expiry settings and conditions.";
				job.Enable = true;
				job.LoggingLevel = (short)LoggingLevel.Debug;
				job.Priority = (short)Priority.Normal;
				job.StartTime = DateTime.UtcNow;
				job.ParameterList = "[]";
				//job.MachineName = jobLogic.GetMachineNameFromApplicationId(ModelStorageProfileApplicationGuid); // rei@26.2.21, used function from JobLogic
				//job.MachineName = "RIB-W1200";
				jobLogic.AddJob(job);
				if (job?.Id != 0)
				{
					if (jobLogic.StartJob(job.Id, out job) != SchedulerError.SchedulerStartSuccessfully)
					{
						result = true;
					}
				}
			}
			catch (Exception e)
			{
				var message = String.Format("Expired Models once removing - Exception: {0} Source: {1} Stacktrace: {2}",
					e.Message, e.Source, e.StackTrace);
				System.Diagnostics.Trace.WriteLine(message);
				result = false;
			}

			return result;
		}


		/// <summary>
		/// the last job id that determined expired models
		/// </summary>
		/// <param name="includeRunOnce">Should the RunOnce jobs be included?</param>
		public int? LastExpiredModelsJobId(bool includeRunOnce = true)
		{
			int? jobId = null;

			using (var connection = DbContext.CreateConnection())
			{
				connection.Open();
				try
				{
					using (var cmd = connection.CreateCommand())
					{
						cmd.CommandText = "SELECT TOP 1 ID FROM FRM_JOB WHERE TASKTYPE = @taskTypeId AND JOB_FK IS NULL";
						if (!includeRunOnce)
						{
							cmd.CommandText += " AND JOBSTATE = 5"; // repeatable
						}
						cmd.CommandText += " ORDER BY INSERTED DESC";
						cmd.Parameters.Clear();
						cmd.Parameters.Add(new SqlParameter("@taskTypeId", SqlDbType.NVarChar, 32) { Value = ModelExpirySchedulerTask.TaskTypeId });
						using (var r = cmd.ExecuteReader())
						{
							// if there are more than one
							// take the newest one
							if (r.Read())
							{
								jobId = r.GetInt32(0);
							}
						}
					}
				}
				catch (Exception e)
				{
					var message = String.Format("LastExpiredModelsJobId - Exception: {0} Source: {1} Stacktrace: {2}",
						e.Message, e.Source, e.StackTrace);
					System.Diagnostics.Trace.WriteLine(message);
				}
				finally
				{
					connection.Close();
				}
			}

			return jobId;
		}




		/// <summary>
		/// deletes selected model version only
		/// </summary>
		/// <returns></returns>

		public void DeleteModelVersion(Int32 id)
		{
			Permission.Ensure(PermissionGUID, Permissions.Delete);
			var fileLogic = new ModelProjectModelFileLogic();
			var partsLogic = new ModelProjectModelPartLogic();
			List<ModelFileEntity> files = new();
			List<ModelPartEntity> subParts = new();
			// Get Model Entities for model version Id
			IdentificationData spec = new IdentificationData { Id = id };
			var entity = GetById(spec);
			if (entity != null)
			{
				files = fileLogic.GetListByFilter(e => e.ModelFk == entity.Id).ToList();

				if (entity.IsComposite)
				{
					subParts = partsLogic.GetListByFilter(e => e.ModelFk == entity.Id).ToList();
				}
			}
			else
			{
				Console.WriteLine($"DeleteModelVersion failed: Entity with id {id} not found.");
				return;
			}

			try
			{
				using (var dbCtx = CreateDbContext())
				{
					if (files.Any())
					{
						fileLogic.Delete(files);
					}
					ExecuteDeletionTask(new[] { entity });
					if (subParts.Any())
					{
						partsLogic.Delete(subParts);
					}
					BusinessApplication.BusinessEnvironment.GetExportedValue<IModelObject2PropertyLogic>()
						 .PurgePropertiesForModels(new[] { entity.Id });

					Delete(new[] { entity });

					dbCtx.ExecuteStoredProcedure("MDL_DELETEEMPTYFAMILIES_SP");

					dbCtx.SaveChanges();

				}
			}
			catch (Exception ex)
			{
				// Log exception
				Console.WriteLine($"DeleteModelVersion failed: {ex.Message}");
				throw;
			}
		}
		/// <summary>
		/// Hook for providíng additional data before element is send to the client
		/// </summary>
		/// <param name="entities">Entities to which data is to be added</param>
		protected override void PostProcess(IEnumerable<ModelEntity> entities)
		{
			FillModel(entities);
		}

		/// <summary>
		/// Fill address, telephone and country info.
		/// </summary>
		/// <param name="entities"></param>
		private static void FillModel(IEnumerable<ModelEntity> entities)
		{
			if (!entities.TryToNonEmptyArray(out var modelEntities))
			{
				return;
			}

			var projectIds = new List<int>();
			var scheduleIds = new List<int>();

			var projectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var scheduleLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetScheduleLogic>();
			var estimateLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainHeaderLogic>();
			foreach (var entity in modelEntities)
			{
				if (entity.ProjectFk.HasValue && !projectIds.Contains(entity.ProjectFk.Value))
				{
					projectIds.Add(entity.ProjectFk.Value);
				}
				if (entity.ScheduleFk.HasValue && !scheduleIds.Contains(entity.ScheduleFk.Value))
				{
					scheduleIds.Add(entity.ScheduleFk.Value);
				}
			}

			var projectEntities = projectLogic.GetItemsByKey(projectIds.Cast<Int32?>()).ToArray();
			var scheduleEntities = scheduleLogic.GetItemsByKey(scheduleIds.Cast<Int32?>()).ToArray();

			foreach (var entity in modelEntities)
			{
				entity.ProjectItem = entity.ProjectFk.HasValue ? (IProjectEntity)projectEntities.FirstOrDefault(e => e.Id == entity.ProjectFk) : null;
				entity.ScheduleItem = entity.ScheduleFk.HasValue ? (IScheduleEntity)scheduleEntities.FirstOrDefault(e => e.Id == entity.ScheduleFk) : null;
				if (entity.EstimateHeaderFk.HasValue)
				{
					entity.EstimateHeaderItem = estimateLogic.Get(entity.EstimateHeaderFk.GetValueOrDefault());
				}
			}

			var docTypesById = new BasicsCustomizeDocumentTypeLogic().GetListByFilter(e => true).ToDictionary(e => e.Id);
			foreach (var entity in modelEntities)
			{
				if (entity.DocumentTypeFk.HasValue && docTypesById.TryGetValue(entity.DocumentTypeFk.Value, out var docType))
				{
					entity.Is3D = docType.Is3DModel;
				}
				else
				{
					entity.Is3D = true;
				}
			}
		}

		private static readonly Lazy<Int32> MaxJobDescriptionLengthContainer = new Lazy<Int32>(() =>
		{
			var strPropInfo = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityStringPropertyInfo>();
			return strPropInfo.MaxLengthOf(typeof(JobEntity), typeof(JobEntity).GetProperty("Description")) ?? 42;
		});

		internal static Int32 MaxJobDescriptionLength
		{
			get { return MaxJobDescriptionLengthContainer.Value; }
		}

		/// <summary>
		/// starts the removing of  models by root model id
		/// </summary>
		/// <param name="modelEntities">modelEntities</param>
		public Boolean ExecuteDeletionTask(ModelEntity[] modelEntities)
		{
			//List of model Entities 
			var result = false;
			var jobLogic = new JobLogic();

			if (modelEntities != null)
			{
				try
				{
					var job1 = jobLogic.Create();
					job1.TaskType = "2207c10c59d34d2087bd6482c0ccf831";
					job1.Name = "Remove Model";
					{
						var jobDesc = String.Join("; ", modelEntities.Select(m => String.Format(CultureInfo.InvariantCulture,
							"[{0}] {1} ({2})",
							m.Id, m.Description ?? "", m.Code ?? "")));
						job1.Description = jobDesc.ShortenToLength(MaxJobDescriptionLength);
					}
					job1.Enable = true;
					job1.LoggingLevel = (Int16)LoggingLevel.Info;
					job1.Priority = (Int16)Priority.Normal;
					//Get Model Data names for the models

					var modelDataNames = String.Join(",", modelEntities.Select(GetModelDataName));

					job1.ParameterList = JsonConvert.SerializeObject(new List<ParameterEntity>()
					{
						new ParameterEntity() {Name = "ModelDataNames", Type = typeof(String).FullName, Value = modelDataNames}

					});
					job1.StartTime = DateTime.UtcNow;
					job1.MachineName = jobLogic.GetMachineNameFromApplicationId(ModelStorageProfileApplicationGuid); // rei@26.2.21, used function from JobLogic 

					jobLogic.AddJob(job1);
					if (job1.Id != 0)
					{
						if (jobLogic.StartJob(job1.Id, out job1) != SchedulerError.SchedulerStartSuccessfully)
						{
							result = true;
						}
					}

				}

				catch (Exception e)
				{
					var message = String.Format("Model removing - Exception: {0} Source: {1} Stacktrace: {2}", e.Message, e.Source,
						e.StackTrace);
					System.Diagnostics.Trace.WriteLine(message);
					result = false;
				}
			}

			return result;
		}

		/// <summary>
		/// starts the conversion of a file by Id
		/// </summary>
		/// <param name="id">Model id</param>
		/// <param name="jobId">id of the created job</param>
		public Boolean ExecuteHololensModelCreation(Int32 id, out Int32? jobId)
		{
			var result = false;
			var jobLogic = new JobLogic();
			var archivLogic = new FileArchiveDocLogic();
			jobId = null;

			using (var dbCtx = CreateDbContext())
			{
				var selectedModel = dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == id);
				if (selectedModel == null)
				{
					throw new InvalidOperationException(String.Format(CultureInfo.CurrentCulture,
						NLS.ERR_UnknownModel,
						id));
				}

				Int32 modelId = selectedModel.Id;
				var rootModelHasFiles = dbCtx.Entities<ModelFileEntity>().Any(e => e.ModelFk == modelId);
				if (rootModelHasFiles)
				{
					var entity = dbCtx.Entities<ModelFileEntity>().FirstOrDefault(e => e.ModelFk == modelId);
					if (entity != null && entity.FileArchiveDocFk.HasValue)
					{
						var modelEntity = new ModelProjectModelLogic().GetById(new Platform.Core.IdentificationData { Id = entity.ModelFk });
						var filePath = archivLogic.GetArchiveDocumentPath(entity.FileArchiveDocFk.GetValueOrDefault());
						var taskType = "67ee043158d8474bb162c4df20a435aa";
						var taskName = "Hololens Model Creation";

						try
						{
							var job1 = jobLogic.Create();

							job1.TaskType = taskType;
							job1.Name = taskName;
							job1.Enable = true;
							job1.LoggingLevel = (Int16)LoggingLevel.Info;
							job1.Priority = (Int16)Priority.Normal;
							job1.ParameterList = JsonConvert.SerializeObject(new List<ParameterEntity>()
							{
								new ParameterEntity() {Name = "FileName", Type = typeof(String).FullName, Value = filePath},
								new ParameterEntity() {Name = "FileId", Type = typeof(String).FullName, Value = entity.FileArchiveDocFk.GetValueOrDefault().ToString()},
								new ParameterEntity() {Name = "ModelName", Type = typeof(String).FullName, Value = modelEntity.Code},
								new ParameterEntity() {Name = "ModelId", Type = typeof(Int32).FullName, Value = modelEntity.Id.ToString()},
								new ParameterEntity() {Name = "Uuid", Type = typeof(String).FullName, Value = modelEntity.Uuid}
							});
							job1.StartTime = DateTime.UtcNow;

							jobLogic.AddJob(job1);

							if (job1.Id != 0)
							{
								Save(selectedModel);

								jobId = job1.Id;

								SchedulerError scheduleError = jobLogic.StartJob(job1.Id, out job1);
								if (scheduleError != SchedulerError.SchedulerStartSuccessfully)
								{
									result = true;
								}
							}
						}
						catch (Exception e)
						{
							var message = String.Format("Model File Conversion - Exception: {0} Source: {1} Stacktrace: {2}", e.Message, e.Source, e.StackTrace);
							System.Diagnostics.Trace.WriteLine(message);
							result = false;
						}
					}
				}
			}

			return result;
		}

		private void ForceDeleteJob(int id)
		{
			var jobLogic = new JobLogic();
			var job = jobLogic.GetJobById(id);
			if (job != null)
			{
				jobLogic.DeleteJob(job);
			}
		}

		#region GetModel

		/// <summary>
		/// Retrieves a model entity.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The requested object, or <see langword="null"/> if <paramref name="modelId"/> was not found.</returns>
		public ModelEntity GetModel(int modelId)
		{
			using (var dbCtx = new DbContext(ModelBuilder.DbModel))
			{
				return dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == modelId);
			}
		}

		/// <summary>
		/// Retrieves a model entity.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The requested object, or <see langword="null"/> if <paramref name="modelId"/> was not found.</returns>
		/// <seealso cref="GetModel"/>
		IModelEntity IModelLogic.GetModel(int modelId)
		{
			return GetModel(modelId);
		}

		#endregion

		#region ICreateModelWithProject
		/// <summary>
		/// 
		/// </summary>
		IModelEntity IModelLogic.CreateAndSaveByPrjId(int prjId, string modelCode, string commentText)
		{
			var creationData = new IdentificationData { Id = prjId };
			var entity = Create(creationData);
			entity.Code = modelCode;
			entity.CommentText = commentText;

			entity = Save(entity);

			return entity;
		}
		#endregion

		#region GetModelWithProject

		/// <summary>
		/// Retrieves a model entity with its project entity.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The requested object, or <see langword="null"/> if <paramref name="modelId"/> was not found.</returns>
		public ModelEntity GetModelWithProject(int modelId)
		{
			using (var dbCtx = new DbContext(ModelBuilder.DbModel))
			{
				var model = dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == modelId);
				if (model != null)
				{
					if (model.ProjectFk.HasValue)
					{
						model.ProjectItem = ProjectInfoProvider.Value.GetProjectById(model.ProjectFk.Value);
					}
				}
				return model;
			}
		}

		/// <summary>
		/// Retrieves model entities with their project entities.
		/// </summary>
		/// <param name="modelIds">The model IDs to retrieve.</param>
		/// <returns>The model entity objects.</returns>
		public IEnumerable<ModelEntity> GetModelsWithProjects(IEnumerable<Int32> modelIds)
		{
			if (modelIds.TryToNonEmptyArray(out var allModelIds))
			{
				allModelIds = allModelIds.Distinct().ToArray();

				using var dbCtx = CreateDbContext();

				var models = dbCtx.Entities<ModelEntity>().Where(m => allModelIds.Contains(m.Id)).ToArray();

				var allProjectIds = models.Where(m => m.ProjectFk.HasValue).Select(m => m.ProjectFk.Value).Distinct().ToArray();

				if (allProjectIds.Length > 0)
				{
					var projects = allProjectIds.Select(pId => ProjectInfoProvider.Value.GetProjectById(pId)).ToDictionary(p => p.Id);
					foreach (var model in models)
					{
						if (model.ProjectFk.HasValue)
						{
							model.ProjectItem = projects[model.ProjectFk.Value];
						}
					}
				}

				return models;
			}

			return Array.Empty<ModelEntity>();
		}

		/// <summary>
		/// Retrieves a model entity with its project entity.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The requested object, or <see langword="null"/> if <paramref name="modelId"/> was not found.</returns>
		/// <seealso cref="GetModelWithProject"/>
		IModelEntity IModelLogic.GetModelWithProject(int modelId) => GetModelWithProject(modelId);

		/// <summary>
		/// Retrieves model entities with their project entities.
		/// </summary>
		/// <param name="modelIds">The model IDs to retrieve.</param>
		/// <returns>The model entity objects.</returns>
		IEnumerable<IModelEntity> IModelLogic.GetModelsWithProjects(IEnumerable<Int32> modelIds) => GetModelsWithProjects(modelIds);

		#endregion

		/// <summary>
		/// Returns IDs of all projects that contain at least one model.
		/// </summary>
		/// <returns>The project IDs.</returns>
		public IEnumerable<Int32> GetProjectIdsWithModels()
		{
			return GetListByFilter(null).Where(mdl => mdl.ProjectFk.HasValue).Select(mdl => mdl.ProjectFk.Value).Distinct();
		}

		#region IEntityFacade members

		/// <summary>
		/// Creates a workflow-suitable dictionary representation of a model entity.
		/// </summary>
		/// <param name="entity">The entity object.</param>
		/// <param name="includeSubModels">Specifies whether a list of sub-models is to be included.</param>
		/// <returns>The dictionary representation.</returns>
		public IDictionary<String, Object> ToDictionary(ModelEntity entity, Boolean includeSubModels = true)
		{
			var objectDic = entity.AsDictionary(_entityProperties);

			if (includeSubModels)
			{
				var subModelIds = new SubModelLogic().GetSubModelIdsForModel(entity.Id);
				var subModelEntities = GetListByFilter(e => subModelIds.Contains(e.Id)).ToArray();
				objectDic["SubModels"] = subModelEntities.Select(sme => ToDictionary(sme, false)).ToArray();
			}

			var objectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IModelObjectLogic>();
			objectDic["ObjectsFilterChain"] = objectLogic.CreateWorkflowFilterChain(entity.Id);

			return objectDic;
		}

		private static readonly ConvertProperties _entityProperties = new ConvertProperties()
			.Add(nameof(ModelEntity.Id), true)
			.Add(nameof(ModelEntity.StatusFk))
			.Add(nameof(ModelEntity.IsComposite), true)
			.Add(nameof(ModelEntity.Code))
			.Add(nameof(ModelEntity.Description))
			.Add(nameof(ModelEntity.ProjectFk), true)
			.Add(nameof(ModelEntity.LodFk))
			.Add(nameof(ModelEntity.TypeFk))
			.Add(nameof(ModelEntity.CommentText))
			.Add(nameof(ModelEntity.Remark))
			.Add(nameof(ModelEntity.Uuid))
			.Add(nameof(ModelEntity.IsLive))
			.Add(nameof(ModelEntity.ScheduleFk))
			.Add(nameof(ModelEntity.EstimateHeaderFk))
			.Add(nameof(ModelEntity.CadApp))
			.Add(nameof(ModelEntity.CadFileName))
			.Add(nameof(ModelEntity.IsImported), true)
			.Add(nameof(ModelEntity.RevisionId), true)
			.Add(nameof(ModelEntity.ModelVersion))
			.Add(nameof(ModelEntity.ModelRevision))
			.Add(nameof(ModelEntity.ProjectItem), true)
			.Add(nameof(ModelEntity.ScheduleItem), true)
			.Add(nameof(ModelEntity.EstimateHeaderItem), true);

		internal static ConvertProperties EntityProperties
		{
			get { return _entityProperties; }
		}

		#endregion


		#region GetProjectModelInfo

		/// <summary>
		/// Retrieves all project with open revit models assigned to the logged in user and logged in client.
		/// </summary>
		/// <returns>output from view MDL_PROJECTMODEL_INFO_V</returns>
		public IEnumerable<ProjectModelInfoVEntity> GetProjectModelInfo()
		{
			// logged in Company coming from:  this.Context.ClientId
			// logged in User coming from:		 this.Context.UserId

			using (var dbCtx = new DbContext(ModelBuilder.DbModel))
			{
				var resultSet = dbCtx.Entities<ProjectModelInfoVEntity>()
					.Where(e =>
						e.CompanyFk == Context.ClientId
						&& e.IsLive
						&& !e.ModelIsReadOnly
						&& e.ModelIsPublicOpen);

				return resultSet.ToList();
			}
		}

		#endregion GetProjectModelInfo

		/// <summary>
		/// Changes the status of a model.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <param name="newStatusId">The new status ID.</param>
		/// <returns>The model entity in its resulting state.</returns>
		public ModelEntity ChangeStatus(Int32 modelId, Int32 newStatusId)
		{
			Permission.EnsureWrite(PermissionGUID);

			var entity = GetById(new IdentificationData { Id = modelId });
			entity.StatusFk = newStatusId;
			Save(entity);
			return entity;
		}

		void IDataBaseLogic.Delete(IEnumerable<IIdentifyable> toDelete)
		{
			var typedToDelete = toDelete.OfType<ModelEntity>().ToArray();
			SetIsLiveFalse(typedToDelete.Where(e => !e.IsComposite));

			foreach (var cmpModel in typedToDelete.Where(e => e.IsComposite))
			{
				DeleteComplete(cmpModel.Id);
			}
		}

		IEnumerable<IIdentifyable> IDataBaseLogic.Save(IEnumerable<IIdentifyable> toSave)
		{
			IEnumerable<ModelEntity> list = toSave.ToList().OfType<ModelEntity>();
			return base.Save(list);
		}

		#region IUpdateCompleteData members

		IEnumerable<IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<IIdentifyable> completeData)
		{
			var handleUpdate = completeData as IIdentifyable[] ?? completeData.ToArray();
			var entities = handleUpdate.OfType<ModelUpdateEntity>().ToArray();

			var modelLogic = new ModelProjectModelLogic();

			foreach (var item in entities)
			{
				if (item.Models != null)
				{
					if (item.Models.LodFk == 0)
					{
						ThrowException(NLS.ERR_NoLodDefined);
					}
					if (item.Models.StatusFk == 0)
					{
						ThrowException(NLS.ERR_NoStatusDefined);
					}
					if (item.Models.TypeFk == 0)
					{
						ThrowException(NLS.ERR_NoTypeDefined);
					}
					//modelLogic.CreateExecuteTask(item.Models);
					item.Models = modelLogic.Save(item.Models);
				}
				if (item.ModelFilesToSave != null || item.ModelFilesToDelete != null)
				{
					var modelFilesLogic = new ModelProjectModelFileLogic();

					if (item.ModelFilesToSave != null)
					{
						item.ModelFilesToSave = modelFilesLogic.Save(item.ModelFilesToSave);
					}

					if (item.ModelFilesToDelete != null)
					{
						modelFilesLogic.Delete(item.ModelFilesToDelete);
					}
				}

				if (item.ModelPartsToSave != null || item.ModelPartsToDelete != null)
				{
					var modelPartLogic = new ModelProjectModelPartLogic();

					if (item.ModelPartsToSave != null)
					{
						item.ModelPartsToSave = modelPartLogic.Save(item.ModelPartsToSave);
					}

					if (item.ModelPartsToDelete != null)
					{
						modelPartLogic.Delete(item.ModelPartsToDelete);
					}
				}

				EntityUpdateDispatcher.Handle(item);

				if (item.Models != null)
				{
					using (var dbCtx = new DbContext(ModelBuilder.DbModel))
					{
						dbCtx.ExecuteStoredProcedure("MDL_CHANGESET_PROC", item.Models.Id);
					}
				}
			}
			return handleUpdate;
		}

		#endregion

		#region IEntityProvider implementation

		IIdentifyable IEntityProvider.GetById(int id)
		{
			return GetListByFilter(e => e.Id == id).FirstOrDefault();
		}

		IEnumerable<IIdentifyable> IEntityProvider.GetByIds(IEnumerable<int> ids)
		{
			var idArray = ids.ToArray();
			return GetListByFilter(e => idArray.Contains(e.Id));
		}

		#endregion IEntityProvider members

		/// <summary>
		/// Checks whether a given model is a composite model.
		/// </summary>
		/// <param name="modelId">The ID of the model to check.</param>
		/// <returns>A value that indicates whether the model is a composite model.</returns>
		public Boolean IsCompositeModel(Int32 modelId)
		{
			using (var dbCtx = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Select(e => e.IsComposite).Single();
			}
		}

		/// <summary>
		/// Initiates the generation of a downloadable SCS file for a given model.
		/// </summary>
		/// <param name="modelId">The ID of the model.</param>
		/// <returns>An object that provides some information about the download.</returns>
		/// <exception cref="ArgumentException"><paramref name="modelId"/> does not denote a non-composite model.</exception>
		/// <exception cref="InvalidOperationException">The file generation job cannot be started.</exception>
		public ModelFileExportEntity CreateScsFile(Int32 modelId)
		{
			Permission.EnsureCreate(ScsExportPermissionGuid);

			var result = new ModelFileExportEntity();

			var modelEntity = GetById(new IdentificationData { Id = modelId });
			if (modelEntity == null)
			{
				throw new ArgumentException(String.Format(System.Globalization.CultureInfo.InvariantCulture,
					"Model {0} not found.",
					modelId));
			}
			if (modelEntity.IsComposite)
			{
				throw new ArgumentException(String.Format(System.Globalization.CultureInfo.InvariantCulture,
					"Model {0} is a composite model.",
					modelId));
			}

			var modelName = modelEntity.Uuid + "-" +
							modelEntity.Id.ToString("x8", System.Globalization.CultureInfo.InvariantCulture);

			var jobLogic = new JobLogic();

			result.FileName = Guid.NewGuid().ToString("N", System.Globalization.CultureInfo.InvariantCulture) + ".scs";
			var targetName = Path.Combine(DownloadLogic.DownloadsPath, "model", result.FileName);

			var job = jobLogic.Create();

			job.TaskType = GenerateScsFileSchedulerTask.TaskTypeId;
			job.Name = "SCS Model File Generation";
			job.Enable = true;
			job.LoggingLevel = (Int16)LoggingLevel.Info;
			job.Priority = (Int16)Priority.Normal;
			job.ParameterList = JsonConvert.SerializeObject(new List<ParameterEntity>()
			{
				new ParameterEntity() {Name = "ModelName", Type = typeof(String).FullName, Value = modelName},
				new ParameterEntity() {Name = "TargetFileName", Type = typeof(String).FullName, Value = targetName}
			});
			job.StartTime = DateTime.UtcNow;
			job.MachineName = jobLogic.GetMachineNameFromApplicationId(ModelStorageProfileApplicationGuid); // rei@26.2.21, used function from JobLogic 

			job.InsertedBy = Context.UserId;

			jobLogic.AddJob(job);

			if (job.Id != 0)
			{
				if (jobLogic.StartJob(job.Id, out job) == SchedulerError.SchedulerStartSuccessfully)
				{
					result.JobId = job.Id;
					return result;
				}
				else
				{
					throw new InvalidOperationException("Unable to start the conversion job.");
				}
			}
			else
			{
				throw new InvalidOperationException("Unable to create job.");
			}
		}

		/// <summary>
		/// Returns the current state of a job that is responsible for exporting a model into an SCS file.
		/// </summary>
		/// <param name="jobId">The job ID.</param>
		/// <returns>The current state of the job.</returns>
		/// <exception cref="ArgumentException"><paramref name="jobId"/> does not denote an SCS file generation job.</exception>
		public JobState GetScsFileJobState(Int32 jobId)
		{
			Permission.EnsureRead(ScsExportPermissionGuid);

			var jobLogic = new JobLogic();
			var job = jobLogic.LoadJobsByIds(new[] { jobId }).FirstOrDefault();
			if (job == null)
			{
				throw new ArgumentException(String.Format(System.Globalization.CultureInfo.InvariantCulture,
					"Unknown job ID: {0}",
					jobId));
			}

			if (job.TaskType != GenerateScsFileSchedulerTask.TaskTypeId)
			{
				throw new ArgumentException(String.Format(System.Globalization.CultureInfo.InvariantCulture,
					"Job ID {0} does not denote an SCS file generation job.",
					jobId));
			}

			return (JobState)job.JobState;
		}

		/// <summary>
		/// Get all models from the db context filtered by permissions.
		/// </summary>
		/// <param name="dbCtx">data base context</param>
		/// <returns></returns>
		public IQueryable<ModelEntity> GetFilteredModels(DbContext dbCtx)
		{
			return FilterByPermission(dbCtx, dbCtx.Entities<ModelEntity>());
		}

		/// <summary>
		/// Get all models from the db context NOT filtered by permissions.
		/// This is made to switch code fast between GetFilteredModels back to unfiltered.
		/// </summary>
		/// <param name="dbCtx">data base context</param>
		/// <returns></returns>
		public IQueryable<ModelEntity> GetUnfilteredModels(DbContext dbCtx)
		{
			return dbCtx.Entities<ModelEntity>();
		}

		/// <summary>
		/// Retrieves the internal names of all models required to display a composite or a monolithic model with a given ID.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>An enumeration of involved model names.</returns>
		public IEnumerable<String> GetAllModelNames(Int32 modelId)
		{
			Permission.EnsureRead(ScsExportPermissionGuid);

			var subModelIds = new SubModelLogic().GetSubModelIdsForModel(modelId);
			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Entities<ModelEntity>().Where(e => subModelIds.Contains(e.Id) && !e.IsTemporary).Select(e => new
				{
					ModelId = e.Id,
					e.Uuid
				}).AsEnumerable().Select(e => String.Format(CultureInfo.InvariantCulture,
					"{0}-{1:x8}",
					e.Uuid, e.ModelId)).ToArray();
			}
		}

		/// <summary>
		/// Retrieves the project ID a model is assigned to.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The project ID.</returns>
		/// <exception cref="ArgumentException"><paramref name="modelId"/> is unknown.</exception>
		public Int32 GetModelProjectId(Int32 modelId)
		{
			using (var dbCtx = CreateDbContext())
			{
				var projectId = dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Select(e => e.ProjectFk).FirstOrDefault();
				if (projectId <= 0)
				{
					throw new ArgumentException(String.Format(CultureInfo.CurrentCulture,
						NLS.ERR_UnknownModel,
						modelId));
				}
				return projectId ?? throw new ArgumentException(String.Format(CultureInfo.InvariantCulture,
					"Model {0} does not have a project.",
					modelId));
			}
		}


		/// <summary>
		/// Retrieves mapping of model IDs to their respective project IDs.
		/// </summary>
		/// <param name="modelIds">The model IDs.</param>
		/// <returns>A mapping from model IDs to project IDs.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="modelIds"/> is <see langword="null"/>.</exception>
		public IDictionary<Int32, Int32> GetProjectIdsByModelIds(IEnumerable<Int32> modelIds)
		{
			if (modelIds == null)
			{
				throw new ArgumentNullException("modelIds");
			}

			var allModelIds = modelIds.Distinct().ToArray();
			var result = new Dictionary<Int32, Int32>(allModelIds.Length);

			using (var dbCtx = CreateDbContext())
			{
				foreach (var idChunk in allModelIds.ToSizedChunks(50))
				{
					var ids = idChunk.ToArray();
					foreach (
						var idPair in
						dbCtx.Entities<ModelEntity>().Where(e => ids.Contains(e.Id) && e.ProjectFk.HasValue).Select(e => new { ModelFk = e.Id, e.ProjectFk }))
					{
						result.Add(idPair.ModelFk, idPair.ProjectFk!.Value);
					}
				}
			}

			return result;
		}

		/// <summary>
		/// Retrieves the all model versions of a project.
		/// </summary>
		/// <param name="mainItemId">The project ID.</param>
		/// <returns>List of Models.</returns>
		/// <exception cref="ArgumentException"><paramref name="mainItemId"/> is unknown.</exception>
		public IEnumerable<ModelEntity> GetAllModelVersions(Int32 mainItemId)
		{
			if (mainItemId <= 0)
			{
				return GetListByFilter(null);
			}
			else
			{
				IEnumerable<ModelEntity> allModelVersionsArr;
				using (var dbCtx = CreateDbContext())
				{
					var allModelVersions = dbCtx.Entities<ModelEntity>()
						.Where(t => t.ProjectFk == mainItemId);
					allModelVersions = FilterOutInternalModels(allModelVersions, dbCtx);

					allModelVersionsArr = allModelVersions.ToArray();
					PostProcess(allModelVersionsArr);
					DoTranslate(allModelVersionsArr);
				}
				return allModelVersionsArr;
			}
		}

		/// <summary>
		/// Retrieves version of up to four models (typically 3D) for a given project.
		/// </summary>
		/// <param name="mainItemId">The ID of the project to retrieve models for.</param>
		/// <param name="include3D">Specifies whether to include 3D models.</param>
		/// <param name="include2D">Specifies whether to include 2D models.</param>

		public IEnumerable<ModelEntity> GetAllPrjModelVersions(int mainItemId, bool? include3D, bool? include2D)
		{
			if (mainItemId <= 0)
			{
				return GetListByFilter(null);
			}
			else
			{
				IEnumerable<ModelEntity> mylatestModelVersionsArr;
				using (var dbCtx = CreateDbContext())
				{
					var mylatestModelVersions = GetFilteredModels(dbCtx).Where(t => t.ProjectFk == mainItemId).GroupBy(t => t.ModelFamilyFk)
											  .SelectMany(g => g.OrderByDescending(e => e.RevisionId));
					mylatestModelVersions = FilterOutInternalModels(mylatestModelVersions, dbCtx);

					mylatestModelVersionsArr = mylatestModelVersions.Take(4).ToArray();
					PostProcess(mylatestModelVersionsArr);
					switch (include3D, include2D)
					{
						case (true, false):
							mylatestModelVersionsArr = mylatestModelVersionsArr.Where(e => e.Is3D == true).ToArray();
							break;
						case (false, true):
							mylatestModelVersionsArr = mylatestModelVersionsArr.Where(e => e.Is3D == false).ToArray();
							break;
						case (false, false):
							mylatestModelVersionsArr = Array.Empty<ModelEntity>();
							break;
					}
					DoTranslate(mylatestModelVersionsArr);
				}
				return mylatestModelVersionsArr;
			}
		}



		/// <summary>
		/// Retrieves the latest model versions of a project
		/// </summary>
		/// <param name="mainItemId">The project ID.</param>
		/// <param name="include3D">Get 3D model</param>
		/// <param name="include2D">Get 2D model.</param>
		/// <returns>List of Models.</returns>
		/// <exception cref="ArgumentException"><paramref name="mainItemId"/> is unknown.</exception>
		public IEnumerable<ModelEntity> GetLatestModelVersions(Int32 mainItemId, Boolean? include3D, Boolean? include2D)
		{
			if (mainItemId <= 0)
			{
				return GetListByFilter(null);
			}
			else
			{
				IEnumerable<ModelEntity> mylatestModelVersionsArr;
				using (var dbCtx = CreateDbContext())
				{
					var mylatestModelVersions = from t in GetFilteredModels(dbCtx)
														 where t.ProjectFk == mainItemId
														 group t by t.ModelFamilyFk
														 into g
														 select g.OrderByDescending(e => e.RevisionId).FirstOrDefault();
					mylatestModelVersions = FilterOutInternalModels(mylatestModelVersions, dbCtx);

					mylatestModelVersionsArr = mylatestModelVersions.ToArray();
					PostProcess(mylatestModelVersionsArr);

					switch (include3D, include2D)
					{
						case (true, false):
							mylatestModelVersionsArr = mylatestModelVersionsArr.Where(e => e.Is3D == true).ToArray();
							break;
						case (false, true):
							mylatestModelVersionsArr = mylatestModelVersionsArr.Where(e => e.Is3D == false).ToArray();
							break;
						case (false, false):
							mylatestModelVersionsArr = Array.Empty<ModelEntity>();
							break;
					}
					DoTranslate(mylatestModelVersionsArr);
				}
				return mylatestModelVersionsArr;
			}
		}

		/// <summary>
		/// Retrieves all the Composite Models of a Project
		/// </summary>
		/// <param name="mainItemId">The project ID.</param>
		/// <returns>List of Models.</returns>
		/// <exception cref="ArgumentException"><paramref name="mainItemId"/> is unknown.</exception>
		public IEnumerable<ModelEntity> GetCompositeModels(Int32 mainItemId)
		{
			IEnumerable<ModelEntity> compositeModels;
			using (var dbCtx = CreateDbContext())
			{
				var models = from t in GetFilteredModels(dbCtx)
								 where t.ProjectFk == mainItemId && t.IsComposite
								 select t;
				models = FilterOutInternalModels(models, dbCtx);
				compositeModels = models.ToArray();
			}
			PostProcess(compositeModels);
			DoTranslate(compositeModels);
			return compositeModels;
		}

		/// <summary>
		/// Loads all models from a given project, grouped by families.
		/// </summary>
		/// <param name="mainItemId">The project ID.</param>
		/// <param name="includeComposite">Indicates whether or not to include composite models.</param>
		/// <returns>The list of models.</returns>
		public IEnumerable<IEnumerable<ModelEntity>> GetAllModelsGroupedByRoots(Int32 mainItemId, Boolean includeComposite)
		{
			ModelEntity[][] allModels;
			using (var dbCtx = CreateDbContext())
			{
				var rawModels = GetFilteredModels(dbCtx)
					.Where(t => t.ProjectFk == mainItemId && (includeComposite || !t.IsComposite) && t.IsLive);
				rawModels = FilterOutInternalModels(rawModels, dbCtx);

				var models = rawModels
						.GroupBy(t => t.ModelFamilyFk)
						.Select(g => g.OrderByDescending(e => e.RevisionId));

				allModels = models.AsEnumerable().Select(family => family.ToArray()).ToArray();
			}

			var allModelsSequentially = allModels.SelectMany(m => m).ToArray();
			PostProcess(allModelsSequentially);
			DoTranslate(allModelsSequentially);

			return allModels;
		}

		/// <summary>
		/// Loads all model headers from a given project, grouped by families.
		/// </summary>
		/// <param name="projectIds">If specified, only models from the listed project IDs will be considered.</param>
		/// <param name="includeComposite">Indicates whether or not to include composite models.</param>
		/// <param name="include2D">Determines whether or not to include 2D models.</param>
		/// <param name="include3D">Determines whether or not to include 3D models.</param>
		/// <param name="searchText">An optional text to search for the in the description and code of the models.</param>
		/// <returns>The list of models.</returns>
		public IEnumerable<IEnumerable<ModelHeaderEntity>> GetAllModelHeadersGroupedByRoots(IEnumerable<Int32> projectIds, Boolean includeComposite, Boolean include2D = true, Boolean include3D = true, String searchText = null)
		{
			ModelHeaderEntity[][] allModels;

			using (var dbCtx = CreateDbContext())
			{
				var rawModels = GetFilteredModels(dbCtx).Where(m => m.ProjectFk.HasValue);
				if (projectIds != null)
				{
					var allProjectIds = projectIds.ToArray();
					rawModels = rawModels.Where(m => allProjectIds.Contains(m.ProjectFk.Value));
				}

				if (!String.IsNullOrEmpty(searchText))
				{
					rawModels = rawModels.Where(m => (m.Description != null && m.Description.Contains(searchText))
																|| (m.Code != null && m.Code.Contains(searchText)));
				}

				rawModels = rawModels.Where(t => (includeComposite || !t.IsComposite) && t.IsLive);
				rawModels = FilterOutInternalModels(rawModels, dbCtx);

				var models = rawModels
						.Select(e => new
						{
							e.ProjectFk,
							e.Id,
							e.Code,
							e.Description,
							e.ModelFamilyFk,
							e.IsComposite,
							e.RevisionId,
							e.ModelVersion,
							e.ModelRevision,
							e.StatusFk,
							e.TypeFk,
							e.DocumentTypeFk,
							e.Remark,
							e.CommentText
						})
						.GroupBy(t => t.ModelFamilyFk)
						.Select(g => g.OrderByDescending(e => e.RevisionId));

				allModels = models.AsEnumerable().Select(family => family.Select(m => new ModelHeaderEntity
				{
					ProjectFk = m.ProjectFk!.Value,
					Id = m.Id,
					Code = m.Code,
					Description = m.Description,
					ModelFamilyFk = m.ModelFamilyFk,
					IsComposite = m.IsComposite,
					RevisionId = m.RevisionId,
					ModelVersion = m.ModelVersion,
					ModelRevision = m.ModelRevision,
					StatusFk = m.StatusFk,
					TypeFk = m.TypeFk,
					DocumentTypeFk = m.DocumentTypeFk,
					Remark = m.Remark,
					CommentText = m.CommentText


				}).ToArray()).ToArray();
			}

			// determine kind
			{
				var docTypes = new BasicsCustomizeDocumentTypeLogic().GetListByFilter(e => true).ToImmutableDictionary(t => t.Id);
				foreach (var models in allModels)
				{
					foreach (var model in models)
					{
						if (model.DocumentTypeFk.HasValue && docTypes.TryGetValue(model.DocumentTypeFk.Value, out var dType))
						{
							model.Kind = GetModelKind(dType);
						}
						else
						{
							model.Kind = ModelKind.Unknown;
						}
					}
				}
			}

			allModels = allModels.Select(models => models.Where(m => (m.Kind == ModelKind.Model2D && include2D) || (m.Kind == ModelKind.Model3D && include3D) || (m.Kind == ModelKind.Unknown && includeComposite) && m.IsComposite).ToArray()).ToArray();

			return allModels;
		}


		/// <summary>
		/// Loads all model headers from a given project, grouped by families.
		/// </summary>
		/// <param name="mainItemId">The project ID.</param>
		/// <param name="includeComposite">Indicates whether or not to include composite models.</param>
		/// <returns>The list of models.</returns>
		public IEnumerable<ModelHeaderEntity> GetAllModelFilteredHeadersGroupedByRoots(Int32 mainItemId, Boolean includeComposite)
		{
			ModelHeaderEntity[] allModels;
			using (var dbCtx = CreateDbContext())
			{
				var rawModels = GetFilteredModels(dbCtx)
					.Where(t => t.ProjectFk == mainItemId && (includeComposite || !t.IsComposite) && t.IsLive);
				rawModels = FilterOutInternalModels(rawModels, dbCtx);

				var models = rawModels
						.Select(e => new
						{
							e.ProjectFk,
							e.Id,
							e.Code,
							e.Description,
							e.ModelFamilyFk,
							e.IsComposite,
							e.RevisionId,
							e.ModelVersion,
							e.ModelRevision,
							e.StatusFk,
							e.TypeFk,
							e.DocumentTypeFk
						})
						.GroupBy(t => t.ModelFamilyFk.HasValue ? new { FamilyFk = t.ModelFamilyFk.Value, ModelFk = -1 } : new { FamilyFk = -1, ModelFk = t.Id })
						.Select(g => g.OrderByDescending(e => e.RevisionId).FirstOrDefault());

				allModels = models.Select(m => new ModelHeaderEntity
				{
					ProjectFk = m.ProjectFk!.Value,
					Id = m.Id,
					Code = m.Code,
					Description = m.Description,
					ModelFamilyFk = m.ModelFamilyFk,
					IsComposite = m.IsComposite,
					RevisionId = m.RevisionId,
					ModelVersion = m.ModelVersion,
					ModelRevision = m.ModelRevision,
					StatusFk = m.StatusFk,
					TypeFk = m.TypeFk,
					DocumentTypeFk = m.DocumentTypeFk
				}).ToArray();
			}

			List<ModelHeaderEntity> newData = new List<ModelHeaderEntity>();
			// determine kind
			{
				var docTypes = new BasicsCustomizeDocumentTypeLogic().GetListByFilter(e => true).ToImmutableDictionary(t => t.Id);
				foreach (var model in allModels)
				{
					if (model.DocumentTypeFk.HasValue && docTypes.TryGetValue(model.DocumentTypeFk.Value, out var dType))
					{
						model.Kind = GetModelKind(dType);
					}
					else
					{
						model.Kind = ModelKind.Unknown;
					}

					newData.Add(model);
				}
			}

			return newData;
		}


		/// <summary>
		/// Retrieves all the Models
		/// </summary>
		/// <returns>List of Models.</returns>
		public IEnumerable<ModelEntity> GetAllModels()
		{
			IEnumerable<ModelEntity> modelsArray;
			using (var dbCtx = CreateDbContext())
			{
				var models = from t in GetFilteredModels(dbCtx)
								 select t;
				modelsArray = models.ToArray();
			}
			//PostProcess(compositeModels);
			//DoTranslate(compositeModels);
			return modelsArray;
		}


		#region IChangeStatus members

		/// <summary>
		/// ChangeStatus call by widzard
		/// </summary>
		/// <param name="identification"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			return ChangeStatus(identification.Id, statusId);
		}

		/// <summary>
		/// GetCurrentStatus
		/// </summary>
		/// <param name="identification"></param>
		/// <returns></returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var entity = this.GetByFilter(e => e.Id == identification.Id).First();
			return entity != null ? entity.StatusFk : 0;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}

		#endregion

		/// <summary>
		/// Returns all models that reference one of the given project IDs.
		/// </summary>
		/// <param name="projectIds">The project IDs.</param>
		/// <returns>Any model IDs from any of the indicated projects.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="projectIds"/> is <see langword="null"/>.</exception>
		public IEnumerable<Int32> GetModelIdsByProjectIds(IEnumerable<Int32> projectIds)
		{
			if (projectIds == null)
			{
				throw new ArgumentNullException("projectIds");
			}

			var allPjIds = projectIds.Distinct().ToArray();
			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Entities<ModelEntity>().Where(e => e.ProjectFk.HasValue && allPjIds.Contains(e.ProjectFk.Value) && !e.IsTemporary).Select(e => e.Id).ToArray();
			}
		}

		/// <summary>
		/// Returns all models that reference one of the given company IDs.
		/// </summary>
		/// <param name="companyIds">The company IDs.</param>
		/// <returns>Any model IDs from any of the indicated companies.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="companyIds"/> is <see langword="null"/>.</exception>
		public IEnumerable<Int32> GetModelIdsByCompanyIds(IEnumerable<Int32> companyIds)
		{
			if (companyIds == null)
			{
				throw new ArgumentNullException("companyIds");
			}

			var allCompanyIds = companyIds.Distinct().ToArray();
			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Entities<ModelEntity>().Where(e => allCompanyIds.Contains(e.CompanyFk) && !e.IsTemporary).Select(e => e.Id).ToArray();
			}
		}

		/// <summary>
		/// Retrieves models based on an expression and filtered by flags.
		/// </summary>
		/// <param name="filter">An optional filter expression.</param>
		/// <param name="requiredFlags">An optional dictionary with required flags and their values.
		///   For flags that have <see langword="null"/> specified as their value, only presence of the flag will be checked.</param>
		/// <returns>An enumeration of models that match the specified filters.</returns>
		public IEnumerable<ModelEntity> GetListByFilter(SLE.Expression<Func<ModelEntity, Boolean>> filter,
			IDictionary<String, String> requiredFlags)
		{
			Permission.EnsureRead(EntityPermissionGuid);

			using (var dbCtx = CreateDbContext())
			{
				IQueryable<ModelEntity> results = dbCtx.Entities<ModelEntity>();
				if (filter != null)
				{
					results = results.Where(filter);
				}
				if (requiredFlags != null)
				{
					foreach (var pair in requiredFlags)
					{
						var key = pair.Key;
						var val = pair.Value;
						if (val == null)
						{
							results =
								results.Where(
									e => dbCtx.Entities<ModelFlagEntity>().Any(f => (f.ModelFk == e.Id) && (f.Key == key)));
						}
						else
						{
							results =
								results.Where(
									e => dbCtx.Entities<ModelFlagEntity>().Any(f => (f.ModelFk == e.Id) && (f.Key == key) && (f.Value == val)));
						}
					}
				}

				var resultsEnum = DoTranslate(results.AsEnumerable());
				PostProcess(results);
				return resultsEnum.ToArray();
			}
		}

		/// <summary>
		/// Creates a temporary model.
		/// </summary>
		/// <param name="projectId">The project ID the model should be associated with.</param>
		/// <returns>The newly created model object.</returns>
		/// <remarks>
		/// <para>This method creates a temporary model.
		///   It does not require any special permissions as the function may be called under the hood by other modules.</para>
		/// </remarks>
		public ModelEntity CreateTemporaryModel(Int32 projectId)
		{
			var result = new ModelEntity
			{
				Id = NextSequenceId(),
				ProjectFk = projectId,
				IsTemporary = true,
				Uuid = Guid.Empty.ToString("N", CultureInfo.InvariantCulture)
			};
			AssignDefaultSettings(result);
			return result;
		}

		/// <summary>
		/// Saves a temporary model.
		/// </summary>
		/// <param name="temporaryModel">The model to save.</param>
		/// <returns>The saved model.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="temporaryModel"/> is <see langword="null"/>.</exception>
		/// <exception cref="ArgumentException"><paramref name="temporaryModel"/> is not marked as temporary.</exception>
		/// <remarks>
		/// <para>This method saves a temporary model.
		///   It does not require any special permissions as the function may be called under the hood by other modules.</para>
		/// </remarks>
		public ModelEntity SaveTemporaryModel(ModelEntity temporaryModel)
		{
			temporaryModel.CompanyFk = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>()
					.GetProjectById(temporaryModel.ProjectFk!.Value).CompanyFk;

			if (temporaryModel == null)
			{
				throw new ArgumentNullException("temporaryModel");
			}
			if (!temporaryModel.IsTemporary)
			{
				throw new ArgumentException("The supplied model is not marked as temporary.");
			}

			temporaryModel.Code = "tmp";
			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Save(temporaryModel);
			}
		}

		/// <summary>
		/// Renews a temporary model, causing to be kept alive for a while starting at the current time.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>A value that indicates whether the model was found, identified as temporary, and successfully renewed.</returns>
		public Boolean RenewTemporaryModel(Int32 modelId)
		{
			using (var dbCtx = CreateDbContext())
			{
				var model = dbCtx.Entities<ModelEntity>().FirstOrDefault(e => e.Id == modelId);
				if (model != null)
				{
					if (model.IsTemporary)
					{
						dbCtx.Save(model);
						return true;
					}
				}
			}
			return false;
		}

		/// <summary>
		/// Checks whether a given model is marked as temporary.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>A value that indicates whether the model is marked as temporary.</returns>
		public Boolean IsTemporaryModel(Int32 modelId)
		{
			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Select(e => e.IsTemporary).First();
			}
		}

		IModelConversionStateEntity IModelLogic.GetConversionState(Int32 modelId)
		{
			return GetConversionState(modelId);
		}

		/// <summary>
		/// Retrieves the conversion state of a model.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>An object that contains some information on the conversion state, or <see langword="null"/> if <paramref name="modelId"/> is not found.</returns>
		public ModelConversionStateEntity GetConversionState(Int32 modelId)
		{
			var subModelIds = new SubModelLogic().GetSubModelIdsForModel(modelId);
			var states = DoGetConversionStates(subModelIds.PreprendOne(modelId));
			ModelConversionStateEntity result;
			if (states.TryGetValue(modelId, out result))
			{
				if (result.IsComposite)
				{
					result.SubModelStates = states.Where(pair => subModelIds.Contains(pair.Key)).Select(pair => pair.Value).ToArray();
				}
			}
			return result;
		}

		private IDictionary<Int32, ModelConversionStateEntity> DoGetConversionStates(IEnumerable<Int32> modelIds)
		{
			var allIds = modelIds.Distinct().ToArray();

			using (var dbCtx = CreateDbContext())
			{
				var mInfos = dbCtx.Entities<ModelEntity>().Where(e => allIds.Contains(e.Id) && !e.IsTemporary).Select(e => new
				{
					ModelId = e.Id,
					e.IsComposite,
					e.IsImported,
					ModelFileInfo = dbCtx.Entities<ModelFileEntity>().FirstOrDefault(mf => mf.ModelFk == e.Id)
				}).AsEnumerable();

				var jobIds =
					mInfos.Where(mInfo => (mInfo.ModelFileInfo != null) && mInfo.ModelFileInfo.JobFk.HasValue)
						.Select(mInfo => mInfo.ModelFileInfo.JobFk.Value)
						.ToArray();

				var jobsById = new JobLogic().GetFilteredJobs(j => jobIds.Contains(j.Id)).ToDictionary(j => j.Id);

				return mInfos.Select(mInfo =>
				{
					var result = new ModelConversionStateEntity
					{
						ModelId = mInfo.ModelId,
						IsComposite = mInfo.IsComposite,
						IsImported = mInfo.IsImported
					};

					if ((mInfo.ModelFileInfo != null) && mInfo.ModelFileInfo.JobFk.HasValue)
					{
						JobEntity job;
						if (jobsById.TryGetValue(mInfo.ModelFileInfo.JobFk.Value, out job))
						{
							result.JobState = (JobState)job.JobState;
						}
					}

					return result;
				}).ToDictionary(mInfo => mInfo.ModelId);
			}
		}

		/// <summary>
		/// Generates the name used for storing data about a model.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The data name.</returns>
		public String GetModelDataName(Int32 modelId)
		{
			using (var dbCtx = CreateDbContext())
			{
				var uuid = dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Select(e => e.Uuid).FirstOrDefault() ?? "";
				return uuid + '-' + modelId.ToString("x8");
			}
		}

		/// <summary>
		/// Generates the name used for storing data about a model.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <param name="uuid">The UUID of the model.</param>
		/// <returns>The data name.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="uuid"/> is <see langword="null"/>.</exception>
		public static String GetModelDataName(Int32 modelId, String uuid)
		{
			if (uuid == null)
			{
				throw new ArgumentNullException("uuid");
			}

			return uuid + '-' + modelId.ToString("x8");
		}

		/// <summary>
		/// Generates the name of the isolated model with the subfolder from a given model.
		/// </summary>
		/// <param name="model">The model whose name is computed by the method.</param>
		/// <returns>The isolated model name with subfolder.</returns>
		public static String GetIsolatedModelDataName(ModelEntity model) => GetIsolatedModelDataName(GetModelDataName(model));

		/// <summary>
		/// Generates the name of the isolated model with the subfolder from a given modelname.
		/// </summary>
		/// <param name="modelDataName">Model name with uuid and model ID.</param>
		/// <returns>The isolated model name with subfolder.</returns>
		public static String GetIsolatedModelDataName(String modelDataName)
		{
			String subName;

			if (modelDataName.StartsWith("internal$"))
			{
				subName = modelDataName.Substring(9);
			}
			else
			{
				int len = modelDataName.Length;
				if (len < 9)
				{
					throw new ArgumentException("modelDataName");
				}

				int pos = modelDataName.LastIndexOf('-');
				if (pos == -1)
				{
					throw new ArgumentException("modelDataName");
				}

				subName = modelDataName.Substring(pos + 1);
			}

			return Path.Combine(modelDataName, subName);
		}

		/// <summary>
		/// Generates the name used for storing data about a model based on an entity.
		/// </summary>
		/// <param name="entity">The entity object.</param>
		/// <returns>The data name.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="entity"/> is <see langword="null"/>.</exception>
		public static String GetModelDataName(ModelEntity entity)
		{
			if (entity == null)
			{
				throw new ArgumentNullException("entity");
			}

			return GetModelDataName(entity.Id, entity.Uuid);
		}

		/// <summary>
		/// Processes a lookup search request.
		/// </summary>
		/// <param name="request">The request data.</param>
		/// <returns>The response.</returns>
		public override LookupSearchResponse<ModelEntity> GetLookupSearchList(LookupSearchRequest request)
		{
			var result = base.GetLookupSearchList(request);

			if (result != null && result.SearchList != null)
			{
				var allProjectIds = result.SearchList.Where(e => e.ProjectFk.HasValue).Select(e => e.ProjectFk.Value).Distinct().ToArray();
				var pjLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
				var pjById = pjLogic.GetProjectsById(allProjectIds).ToDictionary(p => p.Id);
				foreach (var m in result.SearchList)
				{
					IProjectEntity pj;
					pjById.TryGetValue(m.ProjectFk ?? 0, out pj);
					m.ProjectItem = pj;
				}
			}

			return result;
		}

		/// <summary>
		/// Filters models based on the current lookup request.
		/// </summary>
		/// <param name="commonQuery">The original query.
		///   This must not be <see langword="null"/>.</param>
		/// <param name="request">The request.
		///   This must not be <see langword="null"/>.</param>
		/// <param name="context">A database context.
		///   This must not be <see langword="null"/>.</param>
		/// <returns>The filtered query.</returns>
		protected override IQueryable<ModelEntity> DoBuildLookupSearchFilter(IQueryable<ModelEntity> commonQuery, LookupSearchRequest request, DbContext context)
		{
			var query = base.DoBuildLookupSearchFilter(commonQuery, request, context);

			var companyId = Context.ClientId;
			query = query.Where(e => e.CompanyFk == companyId);

			Object rawProjectId;
			if (request.AdditionalParameters.TryGetValue("projectId", out rawProjectId))
			{
				Int32 projectId;
				if (Int32.TryParse((rawProjectId as String) ?? "", out projectId))
				{
					query = query.Where(e => e.ProjectFk == projectId);
				}
			}

			if (!String.IsNullOrEmpty(request.SearchText))
			{
				var searchText = request.SearchText;
				query =
					query.Where(e => e.Code.Contains(searchText) || (e.Description != null && e.Description.Contains(searchText)));
			}

			return query;
		}

		/// <summary>
		/// Checks whether the current user can access all of the specified model IDs.
		/// </summary>
		/// <param name="modelIds">The model IDs.</param>
		/// <returns>A value that indicates whether the current user can access all of the specified models.</returns>
		public Boolean CanAccessModels(IEnumerable<Int32> modelIds)
		{
			if (modelIds == null)
			{
				throw new ArgumentNullException("modelIds");
			}

			var rawModelIds = modelIds.ToArray();
			var subModelLogic = new SubModelLogic();
			var allModelIds =
				rawModelIds.Concat(rawModelIds.SelectMany(mId => subModelLogic.GetSubModelIdsForModel(mId))).Distinct().ToArray();

			var userId = Context.UserId;

			using (var dbCtx = CreateDbContext())
			{
				var modelCount =
					new DbFunctions(dbCtx).UserAccess()
						.Where(ua => (ua.Userid == userId) && allModelIds.Contains(ua.Id))
						.Select(ua => ua.Id)
						.Distinct()
						.Count();

				return modelCount == allModelIds.Length;
			}
		}

		/// <summary>
		/// Retrieves some statistics about a given model.
		/// </summary>
		/// <param name="modelId">The model.</param>
		/// <returns>An object that contains some statistics on the model, or <see langword="null"/> if the model was not found.</returns>
		public ModelCoreDataVEntity GetMoreCoreStats(Int32 modelId)
		{
			Permission.EnsureRead(EntityPermissionGuid);

			using (var dbCtx = CreateDbContext())
			{
				return dbCtx.Entities<ModelCoreDataVEntity>().FirstOrDefault(e => e.ModelFk == modelId);
			}
		}

		/// <summary>
		/// Retrieves information about a model name.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>An object indicating the code and description of the specified model, or <see langword="null"/> if the model was not found.</returns>
		public NamedItemEntity GetModelName(Int32 modelId)
		{
			using var dbCtx = CreateDbContext();

			var modelInfo = dbCtx.Entities<ModelEntity>().Select(m => new
			{
				ModelId = m.Id,
				ModelCode = m.Code,
				ModelDescription = m.Description,
				IsPreview = dbCtx.Entities<ModelSourceDocumentEntity>().Any(sd => sd.ModelFk == m.Id)
			}).FirstOrDefault(mInfo => mInfo.ModelId == modelId);

			if (modelInfo == null)
			{
				return null;
			}

			return new NamedItemEntity
			{
				Code = modelInfo.IsPreview ? NLS.PreviewModel : modelInfo.ModelCode,
				DescriptionInfo = modelInfo.ModelDescription != null ? new DescriptionTranslateType(modelInfo.ModelDescription, modelInfo.ModelDescription) : null,
				Id = new IdentificationData(modelInfo.ModelId)
			};
		}

		/// <summary>
		/// Retrieves the family ID for a given model ID.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The family ID, if any.</returns>
		public Int32? GetModelFamilyId(Int32 modelId)
		{
			using var dbCtx = CreateDbContext();
			return dbCtx.Entities<ModelEntity>().Where(e => e.Id == modelId).Select(e => e.ModelFamilyFk).FirstOrDefault();
		}

		/// <summary>
		/// Get model kind
		/// </summary>
		/// <param name="modelId"></param>
		/// <returns>which kind of model</returns>
		public ModelKind GetModelKind(Int32 modelId)
		{
			var docTypeLogic = new BasicsCustomizeDocumentTypeLogic();
			var model = GetModel(modelId);
			var docType = model.DocumentTypeFk.HasValue ? docTypeLogic.GetById(model.DocumentTypeFk.Value) as BasicsCustomizeDocumentTypeEntity : null;
			return GetModelKind(docType);
		}

		private ModelKind GetModelKind(BasicsCustomizeDocumentTypeEntity docType)
		{
			return (docType?.Is2DModel, docType?.Is3DModel) switch
			{
				(true, false) => ModelKind.Model2D,
				(false, true) => ModelKind.Model3D,
				_ => ModelKind.Unknown
			};
		}

		/// <summary>
		/// Retrieves the CAD file name for a given model ID.
		/// </summary>
		/// <param name="modelId">The model ID.</param>
		/// <returns>The CAD file name or <see langword="null"/>.</returns>
		public String GetModelCadFileName(Int32 modelId)
		{
			Permission.EnsureRead(EntityPermissionGuid);

			using var dbCtx = CreateDbContext();

			return dbCtx.Entities<ModelEntity>().FirstOrDefault(m => m.Id == modelId)?.CadFileName;
		}
	}
}
