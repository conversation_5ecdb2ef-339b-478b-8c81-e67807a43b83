﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Controlling.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Controlling.PublicApi.BusinessComponents.ControllingUnitApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("MDC_CONTROLLINGUNITAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(2)]
    public partial class ControllingUnitApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new ControllingUnitApiEntity object.
        /// </summary>
        public ControllingUnitApiEntity()
        {
          this.ControllingCatId = 1;
          this.ForTimekeeping = true;
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTRUNITSTATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingunitstatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ControllingUnitStatusId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitStatusDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTRUNITSTATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingUnitStatusDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingunitFk")]
        public virtual int? ControllingUnitId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingUnitCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingTemplateId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLTEMPLATE_FK", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControltemplateFk")]
        public virtual int? ControllingTemplateId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingTemplateUnitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLTEMPLATE_UNIT_FK", TypeName = "int", Order = 6)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControltemplateUnitFk")]
        public virtual int? ControllingTemplateUnitId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingUnitDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContextId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.InternalApiField("ContextFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ContextId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContextDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_DESC", TypeName = "nvarchar(2000)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ContextDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(32)", Order = 10)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.IdentifyingCode]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(2000)", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DescriptionInfo")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 12)]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 14)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Quantity in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QUANTITY", TypeName = "numeric(19,6)", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Quantity {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UomId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_ID", TypeName = "int", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UomFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int UomId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UomDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_DESC", TypeName = "nvarchar(2000)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string UomDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsBillingElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISBILLINGELEMENT", TypeName = "bit", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsBillingElement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsAccountingElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISACCOUNTINGELEMENT", TypeName = "bit", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsAccountingElement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsPlanningElement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANNINGELEMENT", TypeName = "bit", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsPlanningElement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsStockManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSTOCKMANAGEMENT", TypeName = "bit", Order = 21)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsStockmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsStockManagement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StockId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_ID", TypeName = "int", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StockFk")]
        public virtual int? StockId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StockCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_CODE", TypeName = "nvarchar(16)", Order = 23)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string StockCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StockDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_STOCK_DESC", TypeName = "nvarchar(252)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string StockDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsAssetManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISASSETMANAGEMENT", TypeName = "bit", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsAssetmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsAssetManagement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsPlantManagement in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANTMANAGEMENT", TypeName = "bit", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsPlantmanagement")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsPlantManagement {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlantId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_ID", TypeName = "int", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EtmPlantFk")]
        public virtual int? PlantId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlantCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_CODE", TypeName = "nvarchar(16)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PlantCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlantDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ETM_PLANT_DESC", TypeName = "nvarchar(2000)", Order = 29)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PlantDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? PlannedStart {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? PlannedEnd {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedDuration in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_DURATION", TypeName = "numeric(19,6)", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal PlannedDuration {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment01 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT01", TypeName = "nvarchar(32)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment01 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail01Fk")]
        public virtual int? ControllingGrpDetail01Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_CODE", TypeName = "nvarchar(32)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail01Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail01Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL01_DESC", TypeName = "nvarchar(2000)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail01Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment02 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT02", TypeName = "nvarchar(32)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment02 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail02Fk")]
        public virtual int? ControllingGrpDetail02Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_CODE", TypeName = "nvarchar(32)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail02Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail02Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL02_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail02Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment03 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT03", TypeName = "nvarchar(32)", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment03 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDdetail03Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_ID", TypeName = "int", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail03Fk")]
        public virtual int? ControllingGrpDdetail03Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail03Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_CODE", TypeName = "nvarchar(32)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail03Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail03Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL03_DESC", TypeName = "nvarchar(2000)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail03Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment04 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT04", TypeName = "nvarchar(32)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment04 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_ID", TypeName = "int", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail04Fk")]
        public virtual int? ControllingGrpDetail04Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_CODE", TypeName = "nvarchar(32)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail04Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail04Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL04_DESC", TypeName = "nvarchar(2000)", Order = 48)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail04Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment05 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT05", TypeName = "nvarchar(32)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment05 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_ID", TypeName = "int", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail05Fk")]
        public virtual int? ControllingGrpDetail05Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_CODE", TypeName = "nvarchar(32)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail05Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail05Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL05_DESC", TypeName = "nvarchar(2000)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail05Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment06 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT06", TypeName = "nvarchar(32)", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment06 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail056Fk")]
        public virtual int? ControllingGrpDetail06Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_CODE", TypeName = "nvarchar(32)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail06Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail06Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL06_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail06Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment07 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT07", TypeName = "nvarchar(32)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment07 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail07Fk")]
        public virtual int? ControllingGrpDetail07Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_CODE", TypeName = "nvarchar(32)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail07Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail07Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL07_DESC", TypeName = "nvarchar(2000)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail07Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment08 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT08", TypeName = "nvarchar(32)", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment08 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail08Fk")]
        public virtual int? ControllingGrpDetail08Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_CODE", TypeName = "nvarchar(32)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail08Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail08Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL08_DESC", TypeName = "nvarchar(2000)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail08Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment09 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT09", TypeName = "nvarchar(32)", Order = 65)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment09 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail09Fk")]
        public virtual int? ControllingGrpDetail09Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_CODE", TypeName = "nvarchar(32)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail09Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail09Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL09_DESC", TypeName = "nvarchar(2000)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail09Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Assignment10 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ASSIGNMENT10", TypeName = "nvarchar(32)", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string Assignment10 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_ID", TypeName = "int", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingGrpDetail10Fk")]
        public virtual int? ControllingGrpDetail10Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_CODE", TypeName = "nvarchar(32)", Order = 71)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string ControllingGrpDetail10Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingGrpDetail10Desc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGGRPDETAIL10_DESC", TypeName = "nvarchar(2000)", Order = 72)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingGrpDetail10Desc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CommentText in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMMENT_TEXT", TypeName = "nvarchar(255)", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string CommentText {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Budget in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET", TypeName = "numeric(19,7)", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Budget {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsFixedBudget in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFIXED_BUDGET", TypeName = "bit", Order = 85)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsFixedBudget {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsDefault in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDEFAULT", TypeName = "bit", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsDefault {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BudgetDifference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET_DIFFERENCE", TypeName = "numeric(19,7)", Order = 87)]
        public virtual decimal? BudgetDifference {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for EstimateCost in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ESTIMATE_COST", TypeName = "numeric(19,7)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual decimal? EstimateCost {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BudgetCostDiff in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BUDGET_COST_DIFF", TypeName = "numeric(19,7)", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual decimal? BudgetCostDiff {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsIntercompany in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISINTERCOMPANY", TypeName = "bit", Order = 90)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsIntercompany {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CONTROLLINGCAT_ID", TypeName = "int", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingCatFk")]
        public virtual int ControllingCatId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingCatDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CONTROLLINGCAT_DESC", TypeName = "nvarchar(2000)", Order = 92)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ControllingCatDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_ID", TypeName = "int", Order = 93)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
		[RIB.Visual.Platform.Common.InternalApiField("ClerkFk")]
		public virtual int? ClerkId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_CODE", TypeName = "nvarchar(16)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_DESC", TypeName = "nvarchar(252)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProfitCenterId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_RESPONSIBLE_ID", TypeName = "int", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyResponsibleFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ProfitCenterId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProfitCenterCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_RESPONSIBLE_CODE", TypeName = "nvarchar(16)", Order = 97)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProfitCenterCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 98)]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 99)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ForTimekeeping in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISTIMEKEEPINGELEMENT", TypeName = "bit", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsTimekeepingElement")]
        public virtual bool ForTimekeeping {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 101)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            ControllingUnitApiEntity obj = new ControllingUnitApiEntity();
            obj.Id = Id;
            obj.ControllingUnitStatusId = ControllingUnitStatusId;
            obj.ControllingUnitStatusDesc = ControllingUnitStatusDesc;
            obj.ControllingUnitId = ControllingUnitId;
            obj.ControllingUnitCode = ControllingUnitCode;
            obj.ControllingTemplateId = ControllingTemplateId;
            obj.ControllingTemplateUnitId = ControllingTemplateUnitId;
            obj.ControllingUnitDesc = ControllingUnitDesc;
            obj.ContextId = ContextId;
            obj.ContextDesc = ContextDesc;
            obj.Code = Code;
            obj.Description = Description;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDesc = ProjectDesc;
            obj.Quantity = Quantity;
            obj.UomId = UomId;
            obj.UomDesc = UomDesc;
            obj.IsBillingElement = IsBillingElement;
            obj.IsAccountingElement = IsAccountingElement;
            obj.IsPlanningElement = IsPlanningElement;
            obj.IsStockManagement = IsStockManagement;
            obj.StockId = StockId;
            obj.StockCode = StockCode;
            obj.StockDesc = StockDesc;
            obj.IsAssetManagement = IsAssetManagement;
            obj.IsPlantManagement = IsPlantManagement;
            obj.PlantId = PlantId;
            obj.PlantCode = PlantCode;
            obj.PlantDesc = PlantDesc;
            obj.PlannedStart = PlannedStart;
            obj.PlannedEnd = PlannedEnd;
            obj.PlannedDuration = PlannedDuration;
            obj.Assignment01 = Assignment01;
            obj.ControllingGrpDetail01Id = ControllingGrpDetail01Id;
            obj.ControllingGrpDetail01Code = ControllingGrpDetail01Code;
            obj.ControllingGrpDetail01Desc = ControllingGrpDetail01Desc;
            obj.Assignment02 = Assignment02;
            obj.ControllingGrpDetail02Id = ControllingGrpDetail02Id;
            obj.ControllingGrpDetail02Code = ControllingGrpDetail02Code;
            obj.ControllingGrpDetail02Desc = ControllingGrpDetail02Desc;
            obj.Assignment03 = Assignment03;
            obj.ControllingGrpDdetail03Id = ControllingGrpDdetail03Id;
            obj.ControllingGrpDetail03Code = ControllingGrpDetail03Code;
            obj.ControllingGrpDetail03Desc = ControllingGrpDetail03Desc;
            obj.Assignment04 = Assignment04;
            obj.ControllingGrpDetail04Id = ControllingGrpDetail04Id;
            obj.ControllingGrpDetail04Code = ControllingGrpDetail04Code;
            obj.ControllingGrpDetail04Desc = ControllingGrpDetail04Desc;
            obj.Assignment05 = Assignment05;
            obj.ControllingGrpDetail05Id = ControllingGrpDetail05Id;
            obj.ControllingGrpDetail05Code = ControllingGrpDetail05Code;
            obj.ControllingGrpDetail05Desc = ControllingGrpDetail05Desc;
            obj.Assignment06 = Assignment06;
            obj.ControllingGrpDetail06Id = ControllingGrpDetail06Id;
            obj.ControllingGrpDetail06Code = ControllingGrpDetail06Code;
            obj.ControllingGrpDetail06Desc = ControllingGrpDetail06Desc;
            obj.Assignment07 = Assignment07;
            obj.ControllingGrpDetail07Id = ControllingGrpDetail07Id;
            obj.ControllingGrpDetail07Code = ControllingGrpDetail07Code;
            obj.ControllingGrpDetail07Desc = ControllingGrpDetail07Desc;
            obj.Assignment08 = Assignment08;
            obj.ControllingGrpDetail08Id = ControllingGrpDetail08Id;
            obj.ControllingGrpDetail08Code = ControllingGrpDetail08Code;
            obj.ControllingGrpDetail08Desc = ControllingGrpDetail08Desc;
            obj.Assignment09 = Assignment09;
            obj.ControllingGrpDetail09Id = ControllingGrpDetail09Id;
            obj.ControllingGrpDetail09Code = ControllingGrpDetail09Code;
            obj.ControllingGrpDetail09Desc = ControllingGrpDetail09Desc;
            obj.Assignment10 = Assignment10;
            obj.ControllingGrpDetail10Id = ControllingGrpDetail10Id;
            obj.ControllingGrpDetail10Code = ControllingGrpDetail10Code;
            obj.ControllingGrpDetail10Desc = ControllingGrpDetail10Desc;
            obj.CommentText = CommentText;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.Budget = Budget;
            obj.IsFixedBudget = IsFixedBudget;
            obj.IsDefault = IsDefault;
            obj.BudgetDifference = BudgetDifference;
            obj.EstimateCost = EstimateCost;
            obj.BudgetCostDiff = BudgetCostDiff;
            obj.IsIntercompany = IsIntercompany;
            obj.ControllingCatId = ControllingCatId;
            obj.ControllingCatDesc = ControllingCatDesc;
            obj.ClerkId = ClerkId;
            obj.ClerkCode = ClerkCode;
            obj.ClerkDesc = ClerkDesc;
            obj.ProfitCenterId = ProfitCenterId;
            obj.ProfitCenterCode = ProfitCenterCode;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ForTimekeeping = ForTimekeeping;
            obj.LanguageId = LanguageId;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(ControllingUnitApiEntity clonedEntity);

    }


}
