using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Scheduling.Calendar.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using System.ComponentModel.Composition;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IEstimateMainQuantitySCurveLogic))]
	public class EstimateMainQuantitySCurveLogic : RVPBizComp.LogicBase, IEstimateMainQuantitySCurveLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="activities"></param>
		/// <exception cref="BusinessLayerException"></exception>
		public IDictionary<int, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal>> CalculateScurveByBinWeightList(IEnumerable<IActivityEntity> activities)
		{
			var min = activities.Select(a => a.PlannedStart).Min().AddDays(-100);

			var max = activities.Select(a => a.PlannedFinish.HasValue ? a.PlannedFinish.Value : a.PlannedStart).Max().AddDays(100);

			Dictionary<int, NonWorkingDayCheck> allCalendarData = new Dictionary<int, NonWorkingDayCheck>();

			NonWorkingDayCheck nonWorkingDay;

			var calendarIds = activities.Select(e => e.CalendarFk).Distinct().ToArray();

			for (int i = 0; i < calendarIds.Length; i++)
			{
				if (!allCalendarData.TryGetValue(calendarIds[i], out nonWorkingDay))
				{
					//set non working days 
					var filter = new UtilitiesDataFilter();
					filter.Calendar = calendarIds[i];
					filter.Start = min;
					filter.End = max;
					nonWorkingDay = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;
					allCalendarData.Add(calendarIds[i], nonWorkingDay);
				}
			}

			var result = new Dictionary<int, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal>>();

			foreach (var activity in activities)
			{
				NonWorkingDayCheck nonWorkingDaysCheck = null;

				if (!allCalendarData.TryGetValue(activity.CalendarFk, out nonWorkingDaysCheck))
				{
					var filter = new UtilitiesDataFilter();
					filter.Calendar = activity.CalendarFk;
					filter.Start = activity.PlannedStart;
					filter.End = activity.PlannedFinish.Value;
					nonWorkingDaysCheck = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;
				}

				if (nonWorkingDaysCheck is null || activity.SCurveFk is null) { continue; }

				decimal duration = new CalendarUtilitiesLogic().GetDuration(activity.PlannedStart, activity.PlannedFinish.Value, nonWorkingDaysCheck);

				int scurveFk = activity.SCurveFk.Value;

				var binWeightList = GetscurveDetailEntityList().Where(b => b.ScurveFk == scurveFk).Select(b => new { b.Percentoftime, b.Percentofcost }).ToList();

				if (binWeightList.Count > 0)
				{
					var binDuration = duration / binWeightList.Count;

					decimal weightSum = binWeightList.Sum(i => i.Percentofcost);

					if (binDuration > 0 && weightSum > 0)
					{
						var estQuantityScurveResults = new List<EstQuantityScurveResults>();

						int scurveResultsCount = 0;

						binWeightList = binWeightList.OrderBy(item => item.Percentoftime).ToList();

						int sequentialBinNumber = 1;

						foreach (var item in binWeightList)
						{
							EstQuantityScurveResults estQuantityScurveResult = new EstQuantityScurveResults();

							estQuantityScurveResult.Id = scurveResultsCount++;

							estQuantityScurveResult.Bin = sequentialBinNumber++;

							estQuantityScurveResult.Weight = item.Percentofcost;

							estQuantityScurveResult.ScurveFk = (int)scurveFk;

							estQuantityScurveResult.Percentage = item.Percentofcost / weightSum;

							estQuantityScurveResult.Dailyspend = estQuantityScurveResult.Percentage / binDuration;

							estQuantityScurveResults.Add(estQuantityScurveResult);
						}

						result.Add(activity.Id, Tuple.Create((IEnumerable<IEstQuantityScurveResults>)estQuantityScurveResults, duration));
					}
				}
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="activity"></param>
		/// <param name="scurveData"></param>
		/// <exception cref="BusinessLayerException"></exception>
		public IEnumerable<IEstQuantityDailyFractionResults> CalculateScurveLogicForQuantityOld(IActivityEntity activity, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal> scurveData)
		{
			var scurveDataList = scurveData.Item1;

			var estEscalationDailyFractionResultsEntityObj = new List<EstQuantityDailyFractionResults>();

			int? scurveFk = activity?.SCurveFk;

			if (scurveFk != null)
			{
				var binWeightList = GetscurveDetailEntityList().Where(b => b.ScurveFk == scurveFk).Select(b => new { b.Percentoftime, b.Percentofcost }).ToList();

				if (binWeightList.Count > 0)
				{
					decimal duration = scurveData.Item2;

					var binDuration = duration / binWeightList.Count;

					if (binDuration > 0)
					{
						decimal weightSum = binWeightList.Sum(i => i.Percentofcost);

						int currentDuration = Convert.ToInt32(duration);

						int scurveFractionCount = 0;

						for (int i = currentDuration; i > 0; i--)
						{
							EstQuantityDailyFractionResults estEscalationDailyFractionResultsEntity = new EstQuantityDailyFractionResults();

							var escDailyId = scurveFractionCount++;

							estEscalationDailyFractionResultsEntity.Id = escDailyId;

							estEscalationDailyFractionResultsEntity.BeginDuration = i;

							estEscalationDailyFractionResultsEntity.EndDuration = estEscalationDailyFractionResultsEntity.BeginDuration - 1;

							estEscalationDailyFractionResultsEntity.ScurveFk = (int)scurveFk;

							if (i == currentDuration)
							{
								estEscalationDailyFractionResultsEntity.BinStart = binDuration;

								estEscalationDailyFractionResultsEntity.BinFinish = estEscalationDailyFractionResultsEntity.BinStart - 1;

								// Bin means the id of index of S-Curve Detail
								estEscalationDailyFractionResultsEntity.Bin = 1;

								estEscalationDailyFractionResultsEntity.DailyFraction = scurveDataList.Where(e => e.Bin == estEscalationDailyFractionResultsEntity.Bin).
									  Select(e => e.Dailyspend).FirstOrDefault();
							}
							else
							{
								var count = estEscalationDailyFractionResultsEntityObj[estEscalationDailyFractionResultsEntityObj.Count - 1];

								if (count.BinFinish > 1 || count.EndDuration <= 1)
								{
									estEscalationDailyFractionResultsEntity.BinStart = count.BinFinish;
								}
								else
								{
									estEscalationDailyFractionResultsEntity.BinStart = count.BinStart + estEscalationDailyFractionResultsEntityObj[0].BinFinish;
								}

								estEscalationDailyFractionResultsEntity.BinFinish = estEscalationDailyFractionResultsEntity.BinStart - 1;

								if (estEscalationDailyFractionResultsEntity.BinFinish > count.BinFinish)
								{
									estEscalationDailyFractionResultsEntity.Bin = count.Bin + 1;
								}
								else
								{
									estEscalationDailyFractionResultsEntity.Bin = count.Bin;
								}

								if (estEscalationDailyFractionResultsEntity.Bin != count.Bin)
								{
									estEscalationDailyFractionResultsEntity.DailyFraction = (count.BinFinish
										  * (scurveDataList.Where(e => e.Bin == count.Bin).Select(e => e.Dailyspend).FirstOrDefault()))
										  + ((1 - count.BinFinish) *
												 (scurveDataList.Where(e => e.Bin == estEscalationDailyFractionResultsEntity.Bin).Select(e => e.Dailyspend).FirstOrDefault()));
								}
								else
								{
									estEscalationDailyFractionResultsEntity.DailyFraction = scurveDataList.Where(e => e.Bin == count.Bin)
										  .Select(e => e.Dailyspend).FirstOrDefault();
								}
							}

							estEscalationDailyFractionResultsEntityObj.Add(estEscalationDailyFractionResultsEntity);
						}
					}
				}
			}

			return estEscalationDailyFractionResultsEntityObj;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="activity"></param>
		/// <param name="scurveData"></param>
		/// <returns></returns>
		public IEnumerable<IEstQuantityDailyFractionResults> CalculateScurveLogicForQuantity(IActivityEntity activity, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal> scurveData)
		{
			var scurveDataList = scurveData.Item1;

			var retVal = new List<EstQuantityDailyFractionResults>();

			int? scurveFk = activity?.SCurveFk;

			if (scurveFk == null)
			{
				return retVal;
			}

			var scurveDetails = GetscurveDetailEntityList().Where(b => b.ScurveFk == scurveFk && b.Percentofcost > 0).OrderBy(e => e.Percentoftime).ToList();

			if (!scurveDetails.Any())
			{
				return retVal;
			}

			decimal duration = scurveData.Item2;

			decimal weightSum = scurveDetails.Sum(i => i.Percentofcost);

			if ((duration / scurveDetails.Count) <= 0 || weightSum <= 0)
			{
				return retVal;
			}

			// Total Duration Working Days
			int totalDurationWorkdays = Convert.ToInt32(duration);

			// % of Time (Current date/Total date)
			decimal dailyTimePercentage = 1m / totalDurationWorkdays;

			// max time
			var maxPercentoftime = scurveDetails.Last().Percentoftime <= 0 ? 100m : scurveDetails.Last().Percentoftime;

			// % of Time Cumulative
			var timeCumulative = 0m;

			int currentScurveIndex = 0;

			var currentScurveDetail = scurveDetails.ElementAt(0);

			var scurveDetail2WorkDays = new Dictionary<BasScurveDetailEntity, List<EstQuantityDailyFractionResults>>();

			// create workday entity and allocate workdays to different S-curve time periods
			for (int i = 0; i < totalDurationWorkdays; i++)
			{
				EstQuantityDailyFractionResults dailyFractionEntity = new EstQuantityDailyFractionResults();

				dailyFractionEntity.Id = i;

				dailyFractionEntity.ScurveFk = (int)scurveFk;

				timeCumulative += dailyTimePercentage;

				// add the current scurve detail to dictionary if it does not exist
				if (!scurveDetail2WorkDays.ContainsKey(currentScurveDetail))
				{
					scurveDetail2WorkDays.Add(currentScurveDetail, new List<EstQuantityDailyFractionResults>());
				}

				scurveDetail2WorkDays[currentScurveDetail].Add(dailyFractionEntity);

				// use the max Percentoftime one as the end, and calculate the percentage of time
				var percentageOfTime = currentScurveDetail.Percentoftime / maxPercentoftime;

				// if the time cumulative more then the percentOfTime, then move to next scurve detail
				if (timeCumulative >= percentageOfTime && (scurveDetails.Count - 1) > currentScurveIndex)
				{
					currentScurveDetail = scurveDetails.ElementAt(++currentScurveIndex);
				}

				retVal.Add(dailyFractionEntity);
			}

			// Calculate the daily fraction for each working day
			foreach (var kvp in scurveDetail2WorkDays)
			{
				var costPercentageInWorkingDay = kvp.Key.Percentofcost / weightSum / (kvp.Value.Count == 0 ? 1 : kvp.Value.Count);

				foreach (var workingDayEntity in kvp.Value)
				{
					workingDayEntity.DailyFraction = costPercentageInWorkingDay;
				}
			}

			return retVal;
		}

		private List<BasScurveDetailEntity> GetscurveDetailEntityList()
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<BasScurveDetailEntity>().ToList();
			}
		}
	}

	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IEstQuantityScurveResults))]
	public class EstQuantityScurveResults : IEstQuantityScurveResults
	{
		/// <summary>
		/// There are no comments for Id in the schema.
		/// </summary>
		public int Id
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for ScurveFk in the schema.
		/// </summary>
		public int ScurveFk
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Bin in the schema.
		/// </summary>
		public decimal? Bin
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Weight in the schema.
		/// </summary>
		public decimal? Weight
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Percentage in the schema.
		/// </summary>
		public decimal? Percentage
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Dailyspend in the schema.
		/// </summary>
		public decimal? Dailyspend
		{
			get; set;
		}
	}

	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IEstQuantityDailyFractionResults))]
	public class EstQuantityDailyFractionResults : IEstQuantityDailyFractionResults
	{
		/// <summary>
		/// There are no comments for Id in the schema.
		/// </summary>
		public int Id
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for ScurveFk in the schema.
		/// </summary>
		public int ScurveFk
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for BeginDuration in the schema.
		/// </summary>
		public int? BeginDuration
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for EndDuration in the schema.
		/// </summary>
		public int? EndDuration
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for BinStart in the schema.
		/// </summary>
		public decimal? BinStart
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for BinFinish in the schema.
		/// </summary>
		public decimal? BinFinish
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for Bin in the schema.
		/// </summary>
		public int? Bin
		{
			get; set;
		}

		/// <summary>
		/// There are no comments for DailyFraction in the schema.
		/// </summary>
		public decimal? DailyFraction
		{
			get; set;
		}

	}
}
