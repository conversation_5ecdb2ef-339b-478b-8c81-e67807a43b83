using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using Newtonsoft.Json.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Common.Import;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.Import.Common;
using RIB.Visual.Basics.Import.Common.Helper;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Boq.Main.Common;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.Core;
using static RIB.Visual.Basics.Core.Common.EntityIdentifier;
using ImportHelper = RIB.Visual.Basics.Import.Common.Helper;
using NLS = RIB.Visual.Boq.Main.Localization.Properties.Resources;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
namespace RIB.Visual.Boq.Main.BusinessComponents
{

	/// <summary>
	/// ItemTypeValueProvider
	/// </summary>
	class ItemTypeValueProvider : IExportPropertyValueFormatter
	{
		readonly IEnumerable<BasicsCustomizeItemTypeEntity> _itemTypeList;

		internal ItemTypeValueProvider(IEnumerable<BasicsCustomizeItemTypeEntity> itemTypeList)
		{
			_itemTypeList = itemTypeList;
		}


		/// <summary>FormatValue</summary>
		public String FormatValue(object id)
		{
			return _itemTypeList.Where(it => it.Id.Equals(id)).Select(it => it.Code.Translated).FirstOrDefault() ?? String.Empty;
		}

		public int GetIdByTranslatedValue(string value)
		{
			int ret = 0;

			if (!String.IsNullOrEmpty(value))
			{
				var itemType = _itemTypeList.Where(it => (it.Code.Translated == value)
																	|| (it.Code.Translated == null && it.Code.Description == value)).FirstOrDefault();
				if (itemType != null)
				{
					ret = itemType.Id;
				}
			}

			return ret;
		}
	}

	/// <summary>
	/// ItemType2ValueProvider
	/// </summary>
	class ItemType2ValueProvider : IExportPropertyValueFormatter
	{
		readonly IEnumerable<BasicsCustomizeItemType2Entity> _itemType2List;

		internal ItemType2ValueProvider(IEnumerable<BasicsCustomizeItemType2Entity> itemType2List)
		{
			_itemType2List = itemType2List;
		}

		/// <summary>FormatValue</summary>
		public String FormatValue(object id)
		{
			return _itemType2List.Where(it => it.Id.Equals(id)).Select(it => it.Code.Translated).FirstOrDefault() ?? String.Empty;
		}

		public int? GetIdByTranslatedValue(string value)
		{
			int? ret = null;
			if (!String.IsNullOrEmpty(value))
			{
				var itemType2 = _itemType2List.Where(it => (it.Code.Translated == value)
																	|| (it.Code.Translated == null && it.Code.Description == value)).FirstOrDefault();
				if (itemType2 != null)
				{
					ret = itemType2.Id;
				}
			}
			return ret;
		}
	}
	
	/// <summary>
	/// BoqExcelImportLogic
	/// </summary>
	[Export("boq.main", typeof(IImportLogic))]
	public class BoqExcelImportLogic : LogicBase, IImportLogic
	{
		private const string DefaultAccessGuid = "3dc98cfebf2f4540be90a255e6eb8b26";

		#region properties

		BoqItemLogic _boqLogic = null;
		private BoqItemLogic BoqLogic
		{
			get
			{
				if (_boqLogic == null)
				{
					_boqLogic = new BoqItemLogic();
				}
				return _boqLogic;
			}
		}

		BoqHeaderLogic _BoqHeaderLogic = null;
		private BoqHeaderLogic BoqHeaderLogic
		{
			get
			{
				if (_BoqHeaderLogic == null)
				{
					_BoqHeaderLogic = new BoqHeaderLogic();
				}
				return _BoqHeaderLogic;
			}
		}

		TranslationLogic _TranslationLogic = null;
		private TranslationLogic TranslationLogic
		{
			get
			{
				if (_TranslationLogic == null)
				{
					_TranslationLogic = new TranslationLogic();
				}
				return _TranslationLogic;
			}
		}

		private class IdParentIdLineType
		{
			public string Id { get; set; }
			public string ParentId { get; set; }
			public int BoQLineType { get; set; }
		}

		const string ID_MAPPING_NAME = "Id";
		const string PARENTID_MAPPING_NAME = "ParentId";
		const string LINETYPE_MAPPING_NAME = "BoQLineType";
		const string REFERENCE_PROPERTY_NAME = "ReferenceNo";
		const string ID_MAPPING_VALUE = "Id_MappingValue";
		const string PARENTID_MAPPING_VALUE = "ParentId_MappingValue";
		const string LINETYPE_NEW = "BoQLineType_New";

		string _idMappingName;
		string _referenceNoMappingName;
		string _lineTypeMappingName = String.Empty;
		int _outlineSpecExcelImportLength = 2000;


		// for faster boq lookup ...
		Dictionary<int, BoqItemEntity> _itemIdMap;

		#endregion

		private static Tuple<int, BoqStructureEntity, int, int> _structureData;

		ItemTypeValueProvider ItemTypeValueProv
		{
			get
			{
				if (_itemTypeValueProv == null)
				{
					_itemTypeValueProv = new ItemTypeValueProvider(new BasicsCustomizeItemTypeLogic().GetListByFilter(null));
				}
				return _itemTypeValueProv;
			}
		}
		ItemTypeValueProvider _itemTypeValueProv;

		ItemType2ValueProvider ItemType2ValueProv
		{
			get
			{
				if (_itemType2ValueProv == null)
				{
					_itemType2ValueProv = new ItemType2ValueProvider(new BasicsCustomizeItemType2Logic().GetListByFilter(null));
				}
				return _itemType2ValueProv;
			}
		}
		ItemType2ValueProvider _itemType2ValueProv;

		/// <summary>
		/// Try to find matching entities and extend the import object
		/// </summary>
		/// <param name="importDescriptor"></param>
		/// <param name="importObject"></param>
		void IImportLogic.CompletePrewiewObject(IImportDescriptor importDescriptor, IDictionary<string, object> importObject)
		{
			if (BoqImExportHelper.IsRibExcelFormat((importDescriptor as ImportDescriptor).ImportFormat))
			{
				object h1 = importObject["ReferenceH1_New"];
				object h2 = importObject["ReferenceH2_New"];
				object h3 = importObject["ReferenceH3_New"];
				object h4 = importObject["ReferenceH4_New"];
				object h5 = importObject["ReferenceH5_New"];
				object itm = importObject["ReferenceItem_New"];
				object idx = importObject["ReferenceIx_New"];

				// concat splitted reference and fill properties
				string parentId = BoqImExportHelper.GetParentReference("" + h1.ToString(), "" + h2.ToString(), "" + h3.ToString(), "" + h4.ToString(), "" + h5.ToString(), "" + itm.ToString());
				importObject[PARENTID_MAPPING_NAME] = parentId;

				// Transfer RIB Excel keywords to Fk's
				object lineType = importObject["BoQLineType_MappingValue"]; // _New returns int
				importObject["BoQLineType_New"] = BoqImExportHelper.GetBoqLineTypeFkFromExcelLineType(System.Convert.ToString(lineType), "" + h1.ToString(), "" + h2.ToString(), "" + h3.ToString(), "" + h4.ToString(), "" + h5.ToString(), "" + itm.ToString(), "" + idx.ToString());

				#region prepare H1-H5 BOQ structure dependent

				var impDesc = importDescriptor as ImportDescriptor;
				if (impDesc.ImportFormat != ImportFormats.XLSX && !String.IsNullOrEmpty(itm.ToString()))
				{
					int boqHeaderId = impDesc.MainId.Value;
					BoqStructureEntity boqStructure = null;
					int levelCount = 1;
					if ((_structureData != null && _structureData.Item1 == boqHeaderId && _structureData.Item4 == DateTime.Now.Second))
					{
						boqStructure = _structureData.Item2;
						levelCount = _structureData.Item3;
					}
					else
					{
						BoqHeaderEntity header = BoqHeaderLogic.GetEntityById(boqHeaderId);
						boqStructure =
							new BoqStructureLogic2().GetBoqStructureById(header.BoqStructureFk.Value, true);

						levelCount = new BoqStructureDetailLogic().GetByFilter(r =>
								r.BoqStructureFk == header.BoqStructureFk && r.BoqLineTypeFk >= 1 && r.BoqLineTypeFk <= 9)
							.Count();

						_structureData = new Tuple<Int32, BoqStructureEntity, Int32, int>(boqHeaderId, boqStructure, levelCount, DateTime.Now.Second);
					}

					if (boqStructure.EnforceStructure && boqStructure.SkippedHierarchiesAllowed)
					{
						if (String.IsNullOrEmpty(h1.ToString()) && levelCount >= 1)
						{
							h1 = " ";
						}

						if (String.IsNullOrEmpty(h2.ToString()) && levelCount >= 2)
						{
							h2 = " ";
						}

						if (String.IsNullOrEmpty(h3.ToString()) && levelCount >= 3)
						{
							h3 = " ";
						}

						if (String.IsNullOrEmpty(h4.ToString()) && levelCount >= 4)
						{
							h4 = " ";
						}

						if (String.IsNullOrEmpty(h5.ToString()) && levelCount >= 5)
						{
							h5 = " ";
						}
					}
				}

				#endregion
				string id = BoqImExportHelper.BuildReference("" + h1.ToString(), "" + h2.ToString(), "" + h3.ToString(), "" + h4.ToString(), "" + h5.ToString(), "" + itm.ToString(), "" + idx.ToString());
				importObject[ID_MAPPING_NAME] = id;

				importDescriptor.Fields.Where(e => e.PropertyName == REFERENCE_PROPERTY_NAME).SingleOrDefault().MappingName = ID_MAPPING_NAME;
				importObject[REFERENCE_PROPERTY_NAME + "_New"] = id;

				// ignore possible id for root row
				if (importObject["BoQLineType_New"].ToString() == "103")
				{
					importObject[ID_MAPPING_NAME] = String.Empty;
				}

				#region CostGroups

				ExpandoObject exp = (ExpandoObject)importObject["_ImportItem"];
				var costGroups = exp.Where(r => r.Key.StartsWith("CGC") && r.Value != null && !String.IsNullOrEmpty(r.Value.ToString())).ToList();
				importObject.AddRange(costGroups);

				#endregion
			}

			importObject["ItemTypeStandOpt_New"] = ItemTypeValueProv.GetIdByTranslatedValue(System.Convert.ToString(importObject["ItemTypeStandOpt_MappingValue"]));
			importObject["ItemTypeBaseAlt_New"] = ItemType2ValueProv.GetIdByTranslatedValue(System.Convert.ToString(importObject["ItemTypeBaseAlt_MappingValue"]));

			importObject["Material_New"] =
				new MaterialLogic().GetMaterialEntityFkByCode(System.Convert.ToString(importObject["Material_MappingValue"]),
					BusinessApplication.BusinessEnvironment.CurrentContext.ClientId, null);

			_idMappingName = importDescriptor.Fields.Where(e => e.PropertyName == ID_MAPPING_NAME).SingleOrDefault().MappingName;
			if (String.IsNullOrEmpty(_idMappingName))
			{
				throw new Exception(NLS.ExcelImport_Id_MustBe_Mapped);
			}

			string mappingName = importDescriptor.Fields.Where(e => e.PropertyName == PARENTID_MAPPING_NAME).SingleOrDefault().MappingName;
			if (String.IsNullOrEmpty(mappingName))
			{
				throw new Exception(NLS.ExcelImport_ParentId_MustBe_Mapped);
			}

			_referenceNoMappingName = importDescriptor.Fields.Where(e => e.PropertyName == "ReferenceNo").SingleOrDefault().MappingName;
			_lineTypeMappingName = importDescriptor.Fields.Where(e => e.PropertyName == "BoQLineType").SingleOrDefault().MappingName;

		}

		/// <summary>
		/// ProcessImport
		/// </summary>
		/// <param name="options"></param>
		/// <param name="importObjects"></param>
		/// <param name="simulate"></param>
		/// <returns></returns>
		IImportResult IImportLogic.ProcessImport(IImportRequest options, List<dynamic> importObjects, bool simulate)
		{
			BoqImExportHelper.ResetAvailableUoms();

			_itemTypeValueProv = null;
			_itemType2ValueProv = null;

			var importRequest = (options as ImportRequest);
			int rowNumber = 1;
			_outlineSpecExcelImportLength = (int)new SystemOptionLogic().GetItemByKeyAsInt(SystemOption.OutlineSpecExcelImportLength, 2000);

			#region remove visible header row
			if (importRequest.ImportFormat != ImportFormats.XLSX)
			{
				var firstObject = importObjects.FirstOrDefault();
				if (firstObject != null && firstObject["BoQLineType_MappingValue"] != "ROOT")
				{
					importObjects.RemoveAt(0);
					rowNumber = 2;
				}
			}
			#endregion

			List<DbFunctions.BoqCostGroupCatAssignResult> assignedCostGroupCats = new List<DbFunctions.BoqCostGroupCatAssignResult>();
			List<BoqItem2CostGroupEntity> boqItem2CostGroupsToInsert = new List<BoqItem2CostGroupEntity>();
			List<BoqItem2CostGroupEntity> boqItem2CostGroupsToDelete = new List<BoqItem2CostGroupEntity>();

			// Permission.Ensure(DefaultAccessGuid, Permissions.Execute);
			if (!importRequest.ImportDescriptor.MainId.HasValue)
			{
				throw new Exception(NLS.ExcelImport_BoqHeaderId_Is_Not_Set);
			}

			int boqHeaderId = importRequest.ImportDescriptor.MainId.Value;
			BoqHeaderEntity header = BoqHeaderLogic.GetEntityById(boqHeaderId);
			if (header == null)
			{
				throw new Exception(String.Format(NLS.ExcelImport_BoqHeader_Not_Found, boqHeaderId));
			}

			if (importObjects == null)
			{
				throw new Exception(String.Format(NLS.ExcelImport_No_Import_Data, boqHeaderId));
			}

			var result = new Basics.Import.Common.ImportResult(importRequest.File2Import);
			BoqItemEntity rootItem;

			if (importRequest.ImportFormat == ImportFormats.XLSX_RibPlanner)
			{
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					assignedCostGroupCats = new DbFunctions(dbContext).BOQ_COSTGRPCAT_ASSIGN_F(boqHeaderId).OrderBy(r => r.COSTGRPCAT_CODE).ToList();
					boqItem2CostGroupsToDelete = new BoqItem2CostGroupLogic().GetListByBoqHeaderId(boqHeaderId).ToList();
				}
			}

			//try
			//{
			using (var transaction = TransactionScopeFactory.Create(TransactionManager.MaximumTimeout))
			//using (TransactionScope transaction = TransactionScopeFactory.Create())
			{

				List<BlobEntity> modifiedBlobs = null;
				List<BoqCharacterContentEntity> modifiedContents = null;
				int msgCount = 0;

				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					var boqItemStructureOption = importRequest.ImportFormat == ImportFormats.XLSX_RibPes ? new BoqItemStructureOption() { IsPes = true } : null;
					rootItem = BoqLogic.GetBoqItemsHierchical(dbContext, boqHeaderId, boqItemStructureOption).FirstOrDefault(e => e.BoqHeaderFk == boqHeaderId && e.BoqItemFk == null);  // root with tree
					if (rootItem == null)
					{
						throw new Exception(String.Format(NLS.ExcelImport_RootItem_With_BoqHeader_Not_Found, boqHeaderId));
					}

					if (BoqLogic.IsCrbBoq(rootItem))
					{
						throw new Exception(NLS.Crb_DisabledFunc);
					}
					else if (BoqLogic.IsOenBoq(rootItem))
					{
						throw new Exception(NLS.Oen_DisabledFunc);
					}

					BoqHeaderContextVEntity boqHeaderContext = null;

					var myBoqType = BoqLogic.GetBoqType(rootItem.BoqHeaderFk, out boqHeaderContext);

					bool canModifyStructure = (BoqStructureLogic2.CanModifyStructure(boqHeaderId)); // fetch status BEFORE im

					if (importRequest.ImportFormat == ImportFormats.XLSX && !BoqLogic.IsFreeBoq(rootItem) && !canModifyStructure)
					{
						throw new Exception(NLS.FreeExcelImportIsNotPossible);
					}

					bool targetIsEmpty = !rootItem.HasChildren;
					int recordsAdded = 0;
					int recordsUpdated = 0;

					_itemIdMap = new Dictionary<int, BoqItemEntity>();
					BuildIdMap(rootItem);

					List<ImportFieldDescriptor> importFieldDescriptorList = importRequest.ImportDescriptor.Fields;
					Dictionary<string, int> idMapping = new Dictionary<string, int>();   // old Id -> new Id
					bool allowAddItems = true;
					BoqItemEntity entity = rootItem; // stores previous BoQ entity
					BoqItemEntity parentEntity = rootItem; // stores parent BoQ entity

					string prevId = string.Empty;
					string prevParentId = string.Empty;
					int? prevLineType = null;

					List<string> listOfParentIds = new List<string>(); // prefetched list of parent id's of the import file (to auto-detect line types)
					if (String.IsNullOrEmpty(_lineTypeMappingName)) // no linetypeinfo available
					{
						listOfParentIds = BuildListOfParentIds(importObjects);
					}

					string structurePattern = String.Empty;
					if (!BoqLogic.IsFreeBoq(rootItem))
					{
						structurePattern = new BoqStructureLogic2().GetBoqStructurePattern(header.BoqStructureFk);
					}

					List<IdParentIdLineType> listOfIdParentIdLineTypes = new List<IdParentIdLineType>();
					listOfIdParentIdLineTypes = BuildListOfIdParentIdLineTypes(importObjects, importRequest.ImportFormat);

					var sequenceManager = SequenceManager;
					IEnumerator<Int32> boqItemIds = new List<Int32>().GetEnumerator();
					if (importRequest.ImportFormat != ImportFormats.XLSX_RibBidder) // bidder can't add new items!
					{
						if (importObjects.Count > 0)
						{
							int multplier;
						 	if(myBoqType == BoqConstants.EBoqType.Project || myBoqType == BoqConstants.EBoqType.Package || myBoqType == BoqConstants.EBoqType.Wic)
							{
								multplier = 1;
							}
							else
							{
								multplier = 2;
							}
							boqItemIds = sequenceManager.GetNextList<Int32>("BOQ_ITEM", importObjects.Count * multplier, "ID").GetEnumerator();
						}
					}

					// iterate thru all import objects
					string msg;
					foreach (var importItem in importObjects)
					{

						rowNumber++;

						if (BoqImExportHelper.IsRibExcelFormat(importRequest.ImportFormat))  // always import all!
						{
							importItem.Selected = true;
							allowAddItems = importRequest.ImportFormat == ImportFormats.XLSX_RibPlanner;  // bidder can't add new items!
						}

						ImportObjectResult importObjectResult = new ImportObjectResult() { Status = ImportObjectStatus.NoChanges }; // default

						if (!(bool)importItem.Selected)  // skip unselected rows
						{
							importObjectResult.Status = ImportObjectStatus.NotSelected;
							importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_Row_Not_Selected, simulate));
							importItem.ImportResult = JObject.FromObject(importObjectResult);
							continue;
						}

						string id = importItem[ID_MAPPING_NAME];
						string parentId = importItem[PARENTID_MAPPING_NAME];
						string rootId;

						int? lineType;
						string lineTypeDescr = String.Empty;
						if (String.IsNullOrEmpty(_lineTypeMappingName)) // no linetypeinfo available
						{
							if (String.IsNullOrEmpty(parentId))
							{
								lineType = (int)BoqConstants.EBoqLineType.Root;
								rootId = id;
							}
							else
							{
								//lineType = (int)BoqConstants.EBoqLineType.Position;	// todo: detect levels and positions
								// Does any row reference to me?
								if (listOfParentIds.Contains(id))
								{
									lineType = (int)BoqConstants.EBoqLineType.DivisionLevelFirst;
								}
								else
								{
									lineType = (int)BoqConstants.EBoqLineType.Position;
								}
							}
						}
						else
						{
							lineType = importItem.BoQLineType_New ?? -1;
							lineTypeDescr = importItem.BoQLineType_MappingValue;
						}

						bool modified = false;
						bool newRecord = false;
						int errors = 0; // error counter per import object
						string entityName = "BoqItem";
						BoqItemCreationData creationData;

						switch ((int)lineType)
						{

							case (int)BoqConstants.EBoqLineType.Root:

								entity = rootItem;
								parentEntity = rootItem;
								Add2Reference2IdMap(idMapping, (int)lineType, id, entity.Id);

								if (allowAddItems)
								{
									ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "OutlineSpecification", new Action<string>(val => entity.BriefInfo.Description = val), entity.BriefInfo.Description, importItem, ref modified);
									AdjustOutlineSpec(entity);
								}

								prevLineType = (int)lineType;
								prevId = id;
								prevParentId = parentId;
								break;

							case 99: // dummy line type for SPEC row

								if (!allowAddItems)
								{
									importObjectResult.Status = ImportObjectStatus.Warning;
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_Spec_Skipped, simulate));
									break;
								}

								if (entity == null)
								{
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_Specification_Dont_Have_BoqItem, simulate));
								}
								else
								{
									string spec = importItem.OutlineSpecification_New;
									ImportSpec(entity, spec, ref modifiedBlobs, ref modified);
								}
								break;

							case (int)BoqConstants.EBoqLineType.Position:
							case 1:  // level
							case 2:
							case 3:
							case 4:
							case 5:
							case 6:
							case 7:
							case 8:
							case 9:
							case (int)BoqConstants.EBoqLineType.Index:
							case (int)BoqConstants.EBoqLineType.SurchargeItemType1:
							case (int)BoqConstants.EBoqLineType.SurchargeItemType2:
							case (int)BoqConstants.EBoqLineType.SurchargeItemType3:
							case (int)BoqConstants.EBoqLineType.SurchargeItemType4:

								string errorMessage;
								if (ValidateHierarchy((int)lineType, id, parentId, prevLineType, prevId, prevParentId,
										 structurePattern, importRequest.ImportFormat, out errorMessage, listOfIdParentIdLineTypes) == false)
								{
									msg = String.Format(NLS.ExcelImport_Hierarchy_mismatch, rowNumber);
									if (!String.IsNullOrEmpty(errorMessage))
									{
										msg = String.Format(NLS.ExcelImport_ErrMsg, msg, errorMessage);
									}

									if (simulate)
									{
										importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, msg, simulate));
										importObjectResult.Status = ImportObjectStatus.Failed;
										errors++;
										break;
									}
									else
									{
										throw new Exception(msg);
									}
								}

								if (importRequest.ImportFormat == ImportFormats.XLSX)
								{
									if (string.IsNullOrEmpty((string)importItem["ReferenceNo_MappingValue"]))
									{
										msg = String.Format(NLS.ExcelImport_RefNo_IsEmpty, rowNumber);
										if (simulate)
										{
											importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, msg, simulate));
											importObjectResult.Status = ImportObjectStatus.Failed;
											errors++;
											break;
										}
										else
										{
											throw new Exception(msg);
										}
									}

									if (!string.IsNullOrEmpty((string)importItem["ReferenceNo_MappingValue"]))
									{
										List<dynamic> importItemList = importObjects.Where(w => w.ReferenceNo_MappingValue == (string)importItem["ReferenceNo_MappingValue"]).ToList();
										if (importItemList.Count > 1)
										{
											msg = String.Format(NLS.ExcelImportWarning_DuplicateRefNo, (string)importItem["ReferenceNo_MappingValue"]);
											if (simulate)
											{
												importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, msg, simulate));
												importObjectResult.Status = ImportObjectStatus.Failed;
												errors++;
												break;
											}
											else
											{
												throw new Exception(msg);
											}
										}
									}
								}

								entity = null;
								if (targetIsEmpty || (entity = TryFindExistingBoqItem(rootItem, importRequest.ImportDescriptor, importItem)) == null)
								{
									if (allowAddItems)
									{
										creationData = new BoqItemCreationData()
										{
											BoqHeaderFk = rootItem.BoqHeaderFk,
											BoqItemPrjBoqFk = rootItem.BoqItemPrjBoqFk,
											SyncBaseBoq = false,
											DoSave = false,
											BoqItemIds = boqItemIds
										};
										entity = BoqLogic.Create(creationData);
										entity.BoqLineTypeFk = lineType.Value;
										entity.CopyInfo = String.Empty;
										// update index map
										Add2IdMap(entity);
										newRecord = true;
									}
								}

								if (entity == null)
								{
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_CantFind_TargetItem, simulate));
									importObjectResult.Status = ImportObjectStatus.Warning;
									importItem.ImportResult = JObject.FromObject(importObjectResult);

									prevLineType = (int)lineType;
									prevId = id;
									prevParentId = parentId;

									continue;   // skip unmapped items
								}

								// update mapping table (sourceId -> new targetId)
								Add2Reference2IdMap(idMapping, (int)lineType, id, entity.Id);

								int newParentId;
								if (idMapping.TryGetValue("L_" + parentId, out newParentId))   // lookup for parent item
								{
									entity.BoqItemFk = newParentId;
									if (newRecord)  // update parent item
									{
										if (newParentId == rootItem.Id)
										{
											rootItem.BoqItemChildren.Add(entity);
										}
										else
										{
											BoqItemEntity pEntity = GetMatchingBoqItemById(rootItem, newParentId);
											if (pEntity != null)
											{
												pEntity.BoqItemChildren.Add(entity);
											}
										}
									}
								}
								else
								{
									msg = String.Format(NLS.ExcelImport_Failed_To_Get_ParentItem, rowNumber, parentId);
									if (simulate)
									{
										importObjectResult.LogEntries.Add(ImportHelper.Logging.GetFailedPhrase(rowNumber, msg, simulate));
										importObjectResult.Status = ImportObjectStatus.Failed;
										errors++;
										break;
									}
									else
									{
										throw new Exception(msg);
									}
								}

								ImportPos(entity, importItem, importRequest.ImportFormat, importFieldDescriptorList, ref modified, rowNumber, ref importObjectResult);

								string overSizedOutLineSpec = AdjustOutlineSpec(entity);

								if (importRequest.ImportFormat == ImportFormats.XLSX && (!String.IsNullOrEmpty(overSizedOutLineSpec)))
								{
									ImportSpec(entity, overSizedOutLineSpec, ref modifiedBlobs, ref modified);
								}

								#region CostGroups
								if (importRequest.ImportFormat == ImportFormats.XLSX_RibPlanner)
								{
									foreach (var costGroupCat in assignedCostGroupCats)
									{
										string importdedCostGroupCode = importItem[String.Format("CGC{0}", costGroupCat.COSTGRPCAT_ID)];
										if (!String.IsNullOrEmpty(importdedCostGroupCode))
										{
											var costGroup = new CostGroupLogic()
												.GetByFilter(r => r.CostGroupCatFk == costGroupCat.COSTGRPCAT_ID && r.Code == importdedCostGroupCode).FirstOrDefault();
											if (costGroup != null)
											{
												var boqItem2CostGroup = new BoqItem2CostGroupEntity();
												boqItem2CostGroup.BoqHeaderFk = boqHeaderId;
												boqItem2CostGroup.BoqItemFk = entity.Id;
												boqItem2CostGroup.CostGroupCatFk = costGroupCat.COSTGRPCAT_ID;
												boqItem2CostGroup.CostGroupFk = costGroup.Id;
												boqItem2CostGroupsToInsert.Add(boqItem2CostGroup);
											}
										}
									}
								}
								#endregion

								if (importRequest.ImportFormat == ImportFormats.XLSX)
								{
									if (simulate)
									{
										if (entity.BoqCharacterContentPrjFk.HasValue)
										{
											var item = new BoqCharacterContentLogic().GetItembyKey(entity.BoqCharacterContentPrjFk.Value);
											if (item != null)
											{
												importItem["ProjectCharacteristic"] = item.Content;
											}
										}

										if (entity.BoqCharacterContentWorkFk.HasValue)
										{
											var item = new BoqCharacterContentLogic().GetItembyKey(entity.BoqCharacterContentWorkFk.Value);
											if (item != null)
											{
												importItem["WorkContent"] = item.Content;
											}
										}
									}
									else
									{
										string characterContentPrj = importItem["ProjectCharacteristic_New"];
										string characterContentWork = importItem["WorkContent_New"];
										ImportCharacterContent(entity, characterContentPrj, characterContentWork, ref modifiedContents, ref modified);
									}
								}

								// remember current parent entity
								if ((int)lineType >= (int)BoqConstants.EBoqLineType.DivisionLevelFirst && (int)lineType <= (int)BoqConstants.EBoqLineType.DivisionLevelLast)
								{
									parentEntity = entity;
								}

								prevLineType = (int)lineType;
								prevId = id;
								prevParentId = parentId;
								break;

							case (int)BoqConstants.EBoqLineType.Note:

								if (!allowAddItems)
								{
									importObjectResult.Status = ImportObjectStatus.Warning;
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_NoteSkipped, simulate));
									break;
								}

								if (!targetIsEmpty)
								{
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_Updated_Note_NotSupported, simulate));
									importObjectResult.Status = ImportObjectStatus.Warning;
									break;
								}

								//int newParentId;
								if (!idMapping.TryGetValue("L_" + parentId, out newParentId))  // lookup for parent item
								{
									msg = String.Format(NLS.ExcelImport_Failed_To_Get_ParentItem, rowNumber, parentId);
									if (simulate)
									{
										importObjectResult.LogEntries.Add(ImportHelper.Logging.GetFailedPhrase(rowNumber, msg, simulate));
										importObjectResult.Status = ImportObjectStatus.Failed;
										errors++;
										break;
									}
									else
									{
										throw new Exception(msg);
									}
								}

								creationData = new BoqItemCreationData()
								{
									BoqHeaderFk = rootItem.BoqHeaderFk,
									BoqItemPrjBoqFk = rootItem.BoqItemPrjBoqFk,
									Predecessor = parentEntity.BoqItemChildren.Any() ? parentEntity.BoqItemChildren.Last().Id : parentEntity.Id,
									SyncBaseBoq = false,
									DoSave = false
								};
								entity = BoqLogic.Create(creationData);
								entity.BoqLineTypeFk = lineType.Value;
								entity.BoqItemFk = parentEntity.Id;
								entity.CopyInfo = String.Empty;
								ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "OutlineSpecification", new Action<string>(val => entity.BriefInfo.Description = val), entity.BriefInfo.Description, importItem, ref modified);
								// update index map
								//Add2IdMap(entity);
								newRecord = true;
								parentEntity.BoqItemChildren.Add(entity);

								break;

							case -1:

								msg = String.Format(NLS.ExcelImport_LineType_Not_Found, lineTypeDescr);
								if (simulate)
								{
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetFailedPhrase(rowNumber, msg, simulate));
									importObjectResult.Status = ImportObjectStatus.Failed;
									errors++;
									break;
								}
								else
								{
									throw new Exception(msg);
								}

							case 98: // Total sum row

								break;

							case 97: // N/A
							default:

								importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, String.Format(NLS.ExcelImport_LineType_Not_Supported, lineType.Value), simulate));
								importObjectResult.Status = ImportObjectStatus.Warning;
								break;

						}

						if (modified)
						{
							try
							{
								if (!simulate)
								{
									dbContext.Entities<BoqItemEntity>().Add(entity);
									if (entity.Version == 0)
									{
										dbContext.Entry(entity).State = System.Data.Entity.EntityState.Added;
										recordsAdded++;
									}
									else
									{
										dbContext.Entry(entity).State = System.Data.Entity.EntityState.Modified;
										recordsUpdated++;
									}
								}

								if (simulate)
								{
									importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSuccessPhrase(entityName, entity.Reference, newRecord, simulate, rowNumber));
								}
							}
							catch (Exception ex)
							{
								importObjectResult.LogEntries.Add(ImportHelper.Logging.GetFailedPhrase(entityName, entity.Reference, newRecord, simulate, ex));
								++errors;
							}
						}
						else
						{
							importObjectResult.LogEntries.Add(ImportHelper.Logging.GetSkippedPhrase(rowNumber, NLS.ExcelImport_NoChanges, simulate));
							importObjectResult.Status = ImportObjectStatus.NoChanges;
						}

						// cumulate total erros
						if (errors == 0)
						{
							importObjectResult.Status = ImportObjectStatus.Ok;
						}
						else
						{
							importObjectResult.Status = ImportObjectStatus.Failed;
							result.ErrorCounter += errors;
						}

						// add ImportResult entity
						importItem.ImportResult = JObject.FromObject(importObjectResult);

						msgCount = msgCount + importObjectResult.LogEntries.Count + importObjectResult.LogErrorMsg.Count;
					}

					result.ImportObjects = importObjects;

					if (!simulate)
					{
						result.ImportResult_Message += String.Format(NLS.ExcelImport_lines_Added + "</br>", recordsAdded);
						result.ImportResult_Message += String.Format(NLS.ExcelImport_lines_Updated + "</br>", recordsUpdated);
						if (msgCount > 0)
						{
							result.ImportResult_Message += NLS.Please_Heed_Warnings;
						}
					}

					// save all
					if (!simulate && result.ErrorCounter == 0)
					{
						List<BoqItemEntity> addedBoqPositions = null;
						List<BoqItemEntity> modifiedBoqPositions = null;
						BoqItemCalculationOption boqItemCalculationOption = new BoqItemCalculationOption();
						boqItemCalculationOption.TaxCodeId = boqHeaderContext.ContextMdcTaxCodeId;
						boqItemCalculationOption.ExchangeRate = boqHeaderContext.ContextExchangeRate;
						boqItemCalculationOption.IsBasedOnOcValue = false;

						BoqLogic.CalculateBoqTree(rootItem, dbContext, boqItemCalculationOption);

						if (header.BoqHeaderFk!=null && myBoqType!=BoqConstants.EBoqType.Quote)
						{
							var flattenBoqItems = new List<BoqItemEntity>() { rootItem }.Flatten(e => e.BoqItemChildren).ToList();
							Dictionary<int, BoqItemEntity> existingBaseBoqItemDic = new Dictionary<int, BoqItemEntity>();
							var existingBaseBoqItemIds = flattenBoqItems.Select(bi => bi.BoqItemPrjItemFk).ToList();
							var baseBoqHeaderId = flattenBoqItems.FirstOrDefault().BoqItemPrjBoqFk;
							existingBaseBoqItemDic = dbContext.Entities<BoqItemEntity>().Where(bi => bi.BoqHeaderFk == baseBoqHeaderId && existingBaseBoqItemIds.Contains(bi.Id)).ToDictionary(bi => bi.Id);
							
							SyncBaseBoqAfterImport(dbContext, rootItem, importFormat: importRequest.ImportFormat, boqItemIds: boqItemIds, existingBaseBoqItemDic: existingBaseBoqItemDic, options: new SyncBoqItemOptions { IncludeQuantities = true, IncludePrices = true });
						}

						if (importRequest.ImportFormat == ImportFormats.XLSX_RibPlanner && !BoqLogic.IsFreeBoq(rootItem))
						{
							CompleteReferences(rootItem, header.BoqStructureFk);
						}

						try
						{
							if (modifiedBlobs != null && modifiedBlobs.Count > 0)
							{
								new BlobLogic().UpdateBlobs(modifiedBlobs);
							}
							if (boqItem2CostGroupsToDelete != null && boqItem2CostGroupsToDelete.Count > 0)
							{
								new BoqItem2CostGroupLogic().DeleteEntities(boqItem2CostGroupsToDelete);
							}

							BulkSaveHelper bulkSaveHelper = new BulkSaveHelper();
							var modifiedBoqItemEntities = GetTrackedEntitiesFromDbContext(dbContext, EntityState.Modified);
							modifiedBoqPositions = modifiedBoqItemEntities != null ? modifiedBoqItemEntities.Where(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position && e.BoqItemPrjBoqFk == null).ToList() : null;
							bulkSaveHelper.BulkUpdate(dbContext, modifiedBoqItemEntities);
							ChangeBoqItemEntitiesStateFromTo(dbContext, EntityState.Modified, EntityState.Unchanged); // Avoids double updating of items by next SaveChanges for these items are still marked as to being updated after BulkUpdate

							var addedBoqItemEntities = GetTrackedEntitiesFromDbContext(dbContext, EntityState.Added);
							addedBoqPositions = addedBoqItemEntities != null ? addedBoqItemEntities.Where(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position && e.BoqItemPrjBoqFk == null).ToList() : null;	
							bulkSaveHelper.BulkInsert(dbContext, addedBoqItemEntities);
							ChangeBoqItemEntitiesStateFromTo(dbContext, EntityState.Added, EntityState.Unchanged); // Avoids double adding of items by next SaveChanges for these items are still marked as to being added after BulkInsert

							dbContext.SaveChanges();

							if (modifiedContents != null && modifiedContents.Count > 0)
							{
								new BoqCharacterContentLogic().UpdateContents(modifiedContents);
							}
							if (boqItem2CostGroupsToInsert != null && boqItem2CostGroupsToInsert.Count > 0)
							{
								foreach (var bi2cg in boqItem2CostGroupsToInsert)
								{
									bi2cg.Id = SequenceManager.GetNext("BOQ_ITEM2COSTGRP", boqItem2CostGroupsToInsert.Count);
								}
								new BulkSaveHelper().BulkInsert(dbContext, boqItem2CostGroupsToInsert);
							}

							if (recordsAdded > 0)
							{
								dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", rootItem.BoqHeaderFk);

								if (rootItem.BoqItemPrjBoqFk.HasValue)
								{
									dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", rootItem.BoqItemPrjBoqFk.Value);
								}
							}
						}
						catch (Exception ex)
						{
							ex = BoqHelper.GetInnermostException(ex);
							if (ex is DbEntityValidationException)
							{
								throw new Exception(BoqHelper.GetValidationErrors(ex));
							}
							else
							{
								throw ex;
							}
						}

						if (importRequest.ImportFormat == ImportFormats.XLSX && !BoqLogic.IsFreeBoq(rootItem) && canModifyStructure)
						{
							BoqStructureLogic2 structureLogic = new BoqStructureLogic2();
							BoqStructureEntity structureEntity = structureLogic.GetById(header.BoqStructureFk.Value);
							BoqStructureCompleteEntity structureCompleteEntity = structureLogic.DoCreateDeepCopy(structureEntity);

							structureEntity = structureCompleteEntity.BoqStructure.FirstOrDefault();
							structureEntity.BoqStandardFk = (int)BoqConstants.EBoqStandard.Free;
							structureLogic.Update(new List<IIdentifyable>() { structureCompleteEntity });

							if (!header.BoqStructureFk.HasValue || header.BoqStructureFk.Value != structureEntity.Id)
							{
								header.BoqStructureFk = structureEntity.Id; // update relation
								BoqHeaderLogic.Save(header);

								if (header.BoqHeaderFk.HasValue)
								{
									BoqHeaderEntity baseHeader = BoqHeaderLogic.GetEntityById(header.BoqHeaderFk.Value);
									baseHeader.BoqStructureFk = structureEntity.Id;
									BoqHeaderLogic.Save(baseHeader);
								}
							}
						}

						BoqUpdateData data = new BoqUpdateData();
						data.HeaderId = boqHeaderContext.ContextMainHeaderId;
						data.ExchangeRate = boqHeaderContext.ContextExchangeRate != null ? (Decimal)boqHeaderContext.ContextExchangeRate : 1;
						data.Module = boqHeaderContext.GetModuleId();

						BoqLogic.UpdateAmountByBoQs(data);

						transaction.Complete();

						//Create estimate line items for newly added/updated boq items from excel import for project boqs only and only if Is Estimate Boq Driven flag is true.
						if (new[] { BoqConstants.EBoqType.Project, BoqConstants.EBoqType.Bid, BoqConstants.EBoqType.Ord, BoqConstants.EBoqType.Wip, BoqConstants.EBoqType.Bill }.Contains(myBoqType) && ((addedBoqPositions != null && addedBoqPositions.Count > 0) || (modifiedBoqPositions != null && modifiedBoqPositions.Count > 0)))
						{
							var estBoqData = new EstimateMainBoqDrivenData() { TargetProjectId = (int)boqHeaderContext.ContextProjectId, GaebBoqItems = addedBoqPositions, GaebBoqItemsToUpdate = modifiedBoqPositions, IsFromBoqImport = true };
							Injector.Get<IEstimateMainLineItemLogic>().CreateEstLineItemsFromBoq(estBoqData);
						}
					}

				}
			}

			return result;

		}

		/// <summary>sync version BoQ to base BoQ (updates and inserts)</summary>
		private void SyncBaseBoqAfterImport(RVPBizComp.DbContext dbContext, BoqItemEntity item, ImportFormats importFormat, IEnumerator<Int32> boqItemIds = null, Dictionary<int, BoqItemEntity> existingBaseBoqItemDic = null, decimal? source2BaseBoqExchangeRate = null, SyncBoqItemOptions options = null)
		{
			if (item != null)
			{
				if (item.BoqItemPrjBoqFk != null)
				{
					if (source2BaseBoqExchangeRate == null)
					{
						source2BaseBoqExchangeRate = BoqLogic.GetBoqSource2TargetExchangeRate(item.BoqHeaderFk, item.BoqItemPrjBoqFk.Value);
					}

					BoqItemEntity baseBoqItem = null;
					existingBaseBoqItemDic.TryGetValue(item.BoqItemPrjItemFk ?? -1, out baseBoqItem);
					BoqLogic.SyncBaseBoqItem(ref baseBoqItem, item, null, dbContext, source2BaseBoqExchangeRate, options: options, newBaseBoqItemIds: boqItemIds);
				}

				foreach (var childItem in item.BoqItemChildren)
				{
					SyncBaseBoqAfterImport(dbContext, childItem, importFormat, boqItemIds, existingBaseBoqItemDic, source2BaseBoqExchangeRate, options);
				}
			}
		}

		/// <summary>Retrieve changed boq item entities from given dbContext having the requested EntityState</summary>
		private IEnumerable<BoqItemEntity> GetTrackedEntitiesFromDbContext(RVPBizComp.DbContext dbContext, EntityState entityState)
		{
			List<BoqItemEntity> trackedItems = null;

			if (dbContext != null)
			{
				// Retrieve currently tracked entities having given entityState
				trackedItems = dbContext.ChangeTracker.Entries<BoqItemEntity>().Where(e=>e.State==entityState).Select(e=>e.Entity).ToList();
			}

			return trackedItems;
		}

		/// <summary>Change entity state of boq item entities from given dbContext from the requested EntityState to the given EntityState</summary>
		private void ChangeBoqItemEntitiesStateFromTo(RVPBizComp.DbContext dbContext, EntityState fromEntityState, EntityState toEntityState)
		{
			if (dbContext != null)
			{
				var changedEntriesCopy = dbContext.ChangeTracker.Entries<BoqItemEntity>().Where(e => e.State == fromEntityState).ToList();

				foreach (var entry in changedEntriesCopy)
				{
					entry.State = toEntityState;
				}
			}
		}

		#region helper

		private string AdjustOutlineSpec(BoqItemEntity entity)
		{
			string ret = "";

			if (entity.BriefInfo.Description != null && entity.BriefInfo.Description.Length > _outlineSpecExcelImportLength)
			{
				ret = entity.BriefInfo.Description;

				entity.BriefInfo.Description = StringExtension.Truncate(entity.BriefInfo.Description, _outlineSpecExcelImportLength);
			}

			return ret;
		}

		private bool ValidateHierarchy(int lineType, string id, string parentId, int? prevLineType, string prevId, string prevParentId,
										string structurePattern, ImportFormats importFormat, out string errorMessage, List<IdParentIdLineType> listOfIdParentIdLineTypes)
		{
			errorMessage = String.Empty;

			if (importFormat == ImportFormats.XLSX_RibPlanner && !Regex.IsMatch(id, structurePattern))
			{
				errorMessage = String.Format(NLS.ExcelImport_ErrorMsg1, id);
				return false;
			}

			if (id.Equals(parentId, StringComparison.OrdinalIgnoreCase))
			{
				errorMessage = NLS.ExcelImport_ErrMsg_Not_Identical;
				return false;
			}

			switch (lineType)
			{

				case (int)BoqConstants.EBoqLineType.Root:

					return String.IsNullOrEmpty(parentId);

				case 1:  // level
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
				case 7:
				case 8:
				case 9:

					List<IdParentIdLineType> lst = listOfIdParentIdLineTypes.Where(e => e.Id == parentId).ToList();
					if (lst.Count == 0)
					{
						return false;
					}
					if (lst.Count > 0)
					{
						int lineTypeToCompare = lineType - 1 == 0 ? (int)BoqConstants.EBoqLineType.Root : lineType - 1;
						if (lineTypeToCompare == lst.First().BoQLineType)
						{
							return true;
						}
						else
						{
							return false;
						}
					}

					break;

				case (int)BoqConstants.EBoqLineType.Position:
				case (int)BoqConstants.EBoqLineType.Index:

					if (!prevLineType.HasValue)
					{
						return false;
					}

					if (BoqImExportHelper.IsRibExcelFormat(importFormat))
					{
						if ((int)prevLineType == (int)BoqConstants.EBoqLineType.Position
							|| (int)prevLineType == (int)BoqConstants.EBoqLineType.Index)
						{
							return parentId.Equals(prevParentId, StringComparison.OrdinalIgnoreCase);
						}
						else // prev. item must be a level
						{
							return parentId.Equals(prevId, StringComparison.OrdinalIgnoreCase);
						}
					}

					break;
				default:

					break;
			}

			return true;
		}

		private void ImportSpec(BoqItemEntity item, string spec, ref List<BlobEntity> modifiedBlobs, ref bool modified)
		{
			var blobLogic = new BlobLogic();

			if (item.BasBlobsSpecificationFk.HasValue)
			{
				item.SpecificationBlob = blobLogic.GetItemByKey(item.BasBlobsSpecificationFk.Value);
			}
			else
			{
				item.SpecificationBlob = blobLogic.GetNewBlob();
				item.BasBlobsSpecificationFk = item.SpecificationBlob.Id;
			}

			item.SpecificationBlob.Content = new UTF8Encoding().GetBytes(spec);
			if (modifiedBlobs == null)
			{
				modifiedBlobs = new List<BlobEntity>();
			}
			modifiedBlobs.Add(item.SpecificationBlob);

			modified = true;
		}

		private void ImportCharacterContent(BoqItemEntity item, string characterContentPrj, string characterContentWork,
				ref List<BoqCharacterContentEntity> modifiedContents, ref bool modified)
		{
			var logic = new BoqCharacterContentLogic();

			if (!String.IsNullOrEmpty(characterContentPrj))
			{
				if (item.BoqCharacterContentPrjFk.HasValue)
				{
					item.BoqCharacterContentEntity_BoqCharacterContentPrjFk = logic.GetItembyKey(item.BoqCharacterContentPrjFk.Value);
				}
				else
				{
					item.BoqCharacterContentEntity_BoqCharacterContentPrjFk = new BoqCharacterContentEntity();
					item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Id = logic.GetNextCharacterContentId();
					item.BoqCharacterContentPrjFk = item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Id;
				}

				item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Content = characterContentPrj;
				if (modifiedContents == null)
				{
					modifiedContents = new List<BoqCharacterContentEntity>();
				}
				modifiedContents.Add(item.BoqCharacterContentEntity_BoqCharacterContentPrjFk);

				modified = true;
			}

			if (!String.IsNullOrEmpty(characterContentWork))
			{
				if (item.BoqCharacterContentWorkFk.HasValue)
				{
					item.BoqCharacterContentEntity_BoqCharacterContentWorkFk = logic.GetItembyKey(item.BoqCharacterContentWorkFk.Value);
				}
				else
				{
					item.BoqCharacterContentEntity_BoqCharacterContentWorkFk = new BoqCharacterContentEntity();
					item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Id = logic.GetNextCharacterContentId();
					item.BoqCharacterContentWorkFk = item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Id;
				}

				item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Content = characterContentWork;
				if (modifiedContents == null)
				{
					modifiedContents = new List<BoqCharacterContentEntity>();
				}
				modifiedContents.Add(item.BoqCharacterContentEntity_BoqCharacterContentWorkFk);

				modified = true;
			}
		}

		private void ImportPos(BoqItemEntity entity, dynamic importItem, ImportFormats importFormat, List<ImportFieldDescriptor> importFieldDescriptorList,
										ref bool modified, int row, ref ImportObjectResult importObjectResult)
		{
			if (importFormat != ImportFormats.XLSX_RibBidder && importFormat != ImportFormats.XLSX_RibPes)
			{
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "ReferenceNo", new Action<string>(value => entity.Reference = value), entity.Reference, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Reference2", new Action<string>(value => entity.Reference2 = value), entity.Reference2, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "BoQLineType", new Action<int>(value => entity.BoqLineTypeFk = value), entity.BoqLineTypeFk, importItem, ref modified);
				if (entity.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Index)
				{
					entity.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Position;
				}

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "OutlineSpecification", new Action<string>(val => entity.BriefInfo.Description = val), entity.BriefInfo.Description, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "CommentClient", new Action<string>(value => entity.CommentClient = value), entity.CommentClient, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "IsFreeQuantity", new Action<bool>(value => entity.IsFreeQuantity = value), entity.IsFreeQuantity, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Quantity", new Action<decimal>(value => entity.Quantity = value), entity.Quantity, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "QuantityAdj", new Action<decimal>(value => entity.QuantityAdj = value), entity.QuantityAdj, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "UoM", new Action<int>(value => entity.BasUomFk = value), entity.BasUomFk, importItem, ref modified);
				string importedUom = importItem["UoM_MappingValue"];
				string externalUom = null;

				int uomFk = BoqImExportHelper.GetUomFk(importedUom != null ? importedUom.Trim() : importedUom, out externalUom);

				if (uomFk == 0 && entity.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position)
				{
					importObjectResult.Status = ImportObjectStatus.Warning;
					if (!String.IsNullOrEmpty(importedUom != null ? importedUom.Trim() : importedUom))
					{
						importObjectResult.LogEntries.Add(ImportHelper.Logging.GetWarningPhrase(row, String.Format(NLS.ExcelImport_UoM_Not_Known, importedUom)));
					}
					else
					{
						importObjectResult.LogEntries.Add(ImportHelper.Logging.GetWarningPhrase(row, NLS.ExcelImport_No_Uom_Delivered));
					}
				}

				if (entity.BasUomFk != uomFk)
				{
					entity.BasUomFk = uomFk;
					modified = true;
				}
				if (entity.ExternalUom != externalUom)
				{
					entity.ExternalUom = externalUom;
					modified = true;
				}

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "ItemTypeStandOpt", new Action<int>(value => entity.BasItemTypeFk = value), entity.BasItemTypeFk, importItem, ref modified);
				if (!entity.IsItem)
				{
					entity.BasItemTypeFk = (int)BoqConstants.EBoqItemType1.Empty;
				}
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "ItemTypeBaseAlt", new Action<int?>(value => entity.BasItemType2Fk = value), entity.BasItemType2Fk, importItem, ref modified);
				if (entity.IsWicRoot)
				{
					entity.BasItemType2Fk = null;
				}
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Material", new Action<int?>(value => entity.MdcMaterialFk = value), entity.MdcMaterialFk, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Factor", new Action<decimal>(value => entity.Factor = value), entity.Factor, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "IsUrb", new Action<bool>(value => entity.IsUrb = value), entity.IsUrb, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "CommentContractor", new Action<string>(value => entity.CommentContractor = value), entity.CommentContractor, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "DeliveryDate", new Action<DateTime?>(value => entity.DeliveryDate = value), entity.DeliveryDate, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "ExternalCode", new Action<string>(value => entity.ExternalCode = value), entity.ExternalCode, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Userdefined1", new Action<string>(value => entity.Userdefined1 = value), entity.Userdefined1, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Userdefined2", new Action<string>(value => entity.Userdefined2 = value), entity.Userdefined2, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Userdefined3", new Action<string>(value => entity.Userdefined3 = value), entity.Userdefined3, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Userdefined4", new Action<string>(value => entity.Userdefined4 = value), entity.Userdefined4, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Userdefined5", new Action<string>(value => entity.Userdefined5 = value), entity.Userdefined5, importItem, ref modified);

				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "DiscountPercent", new Action<decimal>(value => entity.DiscountPercent = value), entity.DiscountPercent, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "DiscountPercentIt", new Action<decimal>(value => entity.DiscountPercentIt = value), entity.DiscountPercentIt, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Discount", new Action<decimal>(value => entity.Discount = value), entity.Discount, importItem, ref modified);
				ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Discount", new Action<decimal>(value => entity.DiscountOc = value), entity.DiscountOc, importItem, ref modified); // Discount field value is mapped to DiscountOc field to resolve the issue with Excel Import wizard. SyncMonetaryPropertiesBasedOnCurrentExchangeRate function has calculate method in which discount and discount oc fields are calculated and if in Excel Import we only have discount field it sets its value to 0 which is incorrect, so we have to add discount oc field and map its value with discount field.

			}

			if (entity.IsItem)
			{
				if (importFormat != ImportFormats.XLSX_RibPes)
				{
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "UnitRate", new Action<decimal>(value => entity.SetMonetaryValue("Price", value)), entity.Price, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb1", new Action<decimal>(value => entity.SetMonetaryValue("Urb1", value)), entity.Urb1, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb2", new Action<decimal>(value => entity.SetMonetaryValue("Urb2", value)), entity.Urb2, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb3", new Action<decimal>(value => entity.SetMonetaryValue("Urb3", value)), entity.Urb3, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb4", new Action<decimal>(value => entity.SetMonetaryValue("Urb4", value)), entity.Urb4, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb5", new Action<decimal>(value => entity.SetMonetaryValue("Urb5", value)), entity.Urb5, importItem, ref modified);
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Urb6", new Action<decimal>(value => entity.SetMonetaryValue("Urb6", value)), entity.Urb6, importItem, ref modified);

					if (entity.IsUrb)
					{
						entity.SetMonetaryValue("Price", entity.Urb1 + entity.Urb2 + entity.Urb3 + entity.Urb4 + entity.Urb5 + entity.Urb6);
					}

					if (entity.Price != 0)
					{
						entity.NotSubmitted = false;
					}
				}

				if (importFormat == ImportFormats.XLSX_RibBidder)
				{
					if (entity.IsFreeQuantity)
					{
						ImportHelper.Mapping.Check4DecimalContent(importItem, "IsFreeQuantity", row);
						ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "IsFreeQuantity", new Action<decimal>(value => entity.Quantity = value), entity.Quantity, importItem, ref modified);
					}
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "CommentContractor", new Action<string>(value => entity.CommentContractor = value), entity.CommentContractor, importItem, ref modified);
				}

				if (importFormat == ImportFormats.XLSX_RibPes)
				{
					ImportHelper.Mapping.CheckAndSetValue(importFieldDescriptorList, "Quantity", new Action<decimal>(value => entity.Quantity = value), entity.Quantity, importItem, ref modified);
				}
			}

		}

		private void CompleteReferences(BoqItemEntity rootItem, int? boqStructureFk)
		{
			BoqStructureEntity boqStructure;
			BoqStructureDetailEntity[] boqStructureDetails = null;
			char paddingChar = ' ';
			if (boqStructureFk.HasValue)
			{
				boqStructure = new BoqStructureLogic2().GetBoqStructureById(boqStructureFk.Value, true);
				boqStructureDetails = boqStructure.BoqStructureDetailEntities.OrderBy(r => r.BoqLineTypeFk == 0 ? 9.5 : r.BoqLineTypeFk).ToArray();
				paddingChar = boqStructure.LeadingZeros ? '0' : ' ';

				CompleteReferencesRecursively(rootItem, boqStructureDetails, paddingChar, 0);
			}
		}

		private void CompleteReferencesRecursively(BoqItemEntity boqItem, BoqStructureDetailEntity[] boqStructureDetails, char paddingChar, int level)
		{
			if (!boqItem.IsWicRoot)
			{
				boqItem.Reference = GetCompletedRefence(boqItem.Reference, boqStructureDetails, paddingChar);
			}

			if (level <= 6) // H1-H5 and POS
			{
				foreach (var boqChildItem in boqItem.BoqItemChildren)
				{
					CompleteReferencesRecursively(boqChildItem, boqStructureDetails, paddingChar, level + 1);
				}
			}
		}

		private string GetCompletedRefence(string reference, BoqStructureDetailEntity[] boqStructureDetails, char paddingChar)
		{
			string newReference = reference;
			if (!String.IsNullOrEmpty(reference) && boqStructureDetails.Length > 0)
			{
				bool referenceEndsWithPoint = reference.EndsWith(".");
				List<string> referenceParts = reference.Split('.').ToList();
				string newReferencePart;
				List<string> newReferenceParts = new List<String>();
				int i = 0;
				foreach (var referencePart in referenceParts)
				{
					if (!String.IsNullOrEmpty(referencePart))
					{
						var boqStructureDetail = boqStructureDetails[i];
						if (String.IsNullOrEmpty(referencePart.Trim()))
						{
							newReferencePart = referencePart.Trim().PadLeft(boqStructureDetail.LengthReference, ' ');
						}
						else
						{
							newReferencePart = referencePart.Trim().PadLeft(boqStructureDetail.LengthReference, paddingChar);
						}

						newReferenceParts.Add(newReferencePart);
					}
					i = i + 1;
				}

				newReference = String.Join(".", newReferenceParts);
				if (referenceEndsWithPoint)
				{
					newReference = String.Format("{0}.", newReference);
				}
			}

			return newReference;
		}

		#endregion

		#region boq lookup

		private BoqItemEntity TryFindExistingBoqItem(BoqItemEntity rootWithTree, IImportDescriptor importDescriptor, dynamic importObject)
		{

			BoqItemEntity result = null;

			var searchMethods = importDescriptor.DoubletFindMethods.ToList();
			if (searchMethods[0].Selected)   // Id
			{
				int id;
				try
				{
					id = importObject[ID_MAPPING_NAME];
				}
				catch (Exception)
				{
					throw new Exception(String.Format(NLS.ExcelImport_Column_Only_Contains_Numeric_Values, _idMappingName));
				}
				result = GetMatchingBoqItemById(rootWithTree, id);
			}

			// if (result == null && searchMethods[1].Selected)	// Reference
			if (result == null)  // always search by reference to avoid duplicate references
			{
				if (String.IsNullOrEmpty(_referenceNoMappingName))
				{
					throw new Exception(NLS.ExcelImport_Exception_Msg);
				}
				string referenceNo = importObject["ReferenceNo_New"];
				result = GetMatchingBoqItemByRef(rootWithTree, referenceNo);
			}

			return result;

		}

		private void WalkTheBoqTree(BoqItemEntity boqItem, Func<BoqItemEntity, bool> fn)
		{
			if (boqItem == null || fn == null)
			{
				return;
			}

			var found = fn.Invoke(boqItem);
			if (!found)
			{
				foreach (var boqChildItem in boqItem.BoqItemChildren)
				{
					WalkTheBoqTree(boqChildItem, fn);
				}
			}
		}

		private BoqItemEntity GetMatchingBoqItemByRef(BoqItemEntity rootWithTree, string referenceNo)
		{
			BoqItemEntity result = null;

			WalkTheBoqTree(rootWithTree, boqItem =>
			{
				if (result == null && boqItem.BoqLineTypeFk != (int)BoqConstants.EBoqLineType.Root)
				{
					string ref1 = ("" + boqItem.Reference).Replace(" ", "");
					string ref2 = ("" + referenceNo).Replace(" ", "");
					if (ref1.Equals(ref2, StringComparison.OrdinalIgnoreCase))
					{
						result = boqItem;
					}
				}
				return result != null;
			});

			return result;
		}

		private BoqItemEntity GetMatchingBoqItemById(BoqItemEntity rootWithTree, int id)
		{
			// using map works faster
			// BoqItemEntity result = null;
			//WalkTheBoqTree(rootWithTree, boqItem =>
			//{
			//    if (boqItem.Id == id) 
			//    {
			//        result = boqItem;
			//        return true;
			//    }
			//    else
			//    {
			//        return false;
			//    }
			//});

			if (_itemIdMap.ContainsKey(id))
			{
				return _itemIdMap[id];
			}
			else
			{
				return null;
			}
		}

		#endregion

		#region id map

		private void BuildIdMap(BoqItemEntity rootWithTree)
		{
			WalkTheBoqTree(rootWithTree, boqItem =>
			{
				if (!boqItem.IsWicRoot) // skip root item
				{
					Add2IdMap(boqItem);
				}
				return false;
			});
		}

		private void Add2IdMap(BoqItemEntity boqItem)
		{
			if (_itemIdMap.ContainsKey(boqItem.Id))
			{
				throw new Exception(String.Format(NLS.ExcelImport_Duplicate_Exception, boqItem.Id));
			}
			_itemIdMap.Add(boqItem.Id, boqItem);
		}

		private void Add2Reference2IdMap(Dictionary<string, int> idMapping, int lineType, string reference, int id)
		{
			string prefix = (lineType == (int)BoqConstants.EBoqLineType.Root || (lineType >= (int)BoqConstants.EBoqLineType.DivisionLevelFirst && lineType <= (int)BoqConstants.EBoqLineType.DivisionLevelLast)) ? "L_" : "P_";
			string key = prefix + reference;

			if (idMapping.ContainsKey(key))
			{
				throw new Exception(String.Format(NLS.ExcelImportWarning_DuplicateRefNo, reference));
			}
			idMapping.Add(key, id);
		}

		private List<string> BuildListOfParentIds(List<dynamic> importObjects)
		{

			List<string> res = new List<string>();

			foreach (var importItem in importObjects)
			{
				string parentId = importItem[PARENTID_MAPPING_NAME];
				res.Add(parentId);
			}
			return res;
		}

		private List<IdParentIdLineType> BuildListOfIdParentIdLineTypes(List<dynamic> importObjects, ImportFormats importFormat)
		{
			List<IdParentIdLineType> res = new List<IdParentIdLineType>();

			foreach (var importItem in importObjects)
			{
				IdParentIdLineType idParentIdLineType = new IdParentIdLineType();
				idParentIdLineType.Id = importFormat == ImportFormats.XLSX ? importItem[ID_MAPPING_VALUE] : importItem[ID_MAPPING_NAME];
				idParentIdLineType.ParentId = importFormat == ImportFormats.XLSX ? importItem[PARENTID_MAPPING_VALUE] : importItem[PARENTID_MAPPING_NAME];
				idParentIdLineType.BoQLineType = importItem[LINETYPE_NEW];
				res.Add(idParentIdLineType);
			}
			return res;
		}

		#endregion
	}
}