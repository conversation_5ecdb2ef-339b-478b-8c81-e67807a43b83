using GAEB_Toolbox_33;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.Common;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects;
using System.Globalization;
using System.Transactions;
using NLS = RIB.Visual.Boq.Main.Localization.Properties.Resources;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using System.Text;
using RIB.Visual.Boq.Main.Common;
using RIB.Visual.Boq.Main.BusinessComponents.Logic;
using System.Data.Entity.Infrastructure;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using System.Xml.Linq;
using System.Data.Entity.Validation;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Boq.Main.Localization.Properties;
using System.Text.RegularExpressions;
using System.Data.Entity;
using static RIB.Visual.Boq.Main.Core.BoqConstants;
using RIB.Visual.Platform.BusinessComponents;
using System.Xml;

namespace RIB.Visual.Boq.Main.BusinessComponents
{
	/// <summary>
    ///  
	/// </summary>
	public partial class BoqGaebImportLogic	//	 : BoqGaebLogicBase, IBoqGaebImportLogic
	{

		private int _blobsAdded = 0;
		private int _specsUpdated = 0;
		// private int _specsSkipped = 0;
		private int _specsTotal = 0;
				
		// counters for divisions
		private int _divisionsAdded;
		private int _divisionsUpdated;
		private int _divisionsSkipped = 0;
		private int _divisionsTotal;

		// counters for all items (except ept divisions)
		private int _ItemsAdded;
		private int _itemsUpdated;
		private int _itemsSkipped = 0;
		private int _itemsTotal = 0;
		
		private int _tcsAdded = 0;
		private int _tcsUpdated = 0;
		private int _tcsSkipped = 0;
		private int _tcsTotal= 0;

		private int _sqsAdded = 0;	// counter for new split quantities items
		private int _sqsUpdated = 0;
		private int _sqsSkipped = 0;
		private int _sqsTotal = 0;

		private int _truncatedCounter = 0;
		private int _invalideLumpsums = 0;
		private List<string> _duplicateRefNo = new List<string>();

		// private int _txtAdded = 0;
		//private int _txtSkipped = 0;
		//private int _txtTotal= 0;

		private string _lumpSumBilling = "LumpSumBilling";

		private IEnumerable<GX_BoQBkdn> _GaebBoqStructureDetails;
		private BoqStructureEntity _BoqStructure;

		private bool _revisedLogicIsUsed;
		private bool _targetHasChilds;

		private readonly Lazy<BoqSplitQuantityLogic> SplitQuantityLogic = new Lazy<BoqSplitQuantityLogic>();
		private readonly Lazy<BoqTextComplementLogic> TextComplementLogic = new Lazy<BoqTextComplementLogic>();      

		private BoqItemEntity _lastFirstLevelDivision = null;

		private MainItem2CostGroupLogicWrapper _boqItem2CostGroupLogic;
		private MainItem2CostGroupLogicWrapper _boqSplitQty2CostGroupLogic;
		private readonly Lazy<CostGroupCatLogic> _CostGroupCatLogic = new Lazy<CostGroupCatLogic>();

		private int? _index4Item;

		#region revised import

		/// <summary>
		/// If DeleteMissingItems is true, then we have to update N/A column to true
		/// </summary>
		/// <param name="boqItem"></param>
		/// <param name="boqItemIdsPresentInGaebFile"></param>
		/// <param name="lineTypes"></param>
		/// <returns></returns>
		private BoqItemEntity SetNAForMissingBoqItems(BoqItemEntity boqItem, List<int> boqItemIdsPresentInGaebFile, int[] lineTypes)
		{
			if (boqItem.BoqItemChildren != null && boqItem.BoqItemChildren.Any())
			{
				foreach (var sourceBoqItemChild in boqItem.BoqItemChildren)
				{
					if (!boqItemIdsPresentInGaebFile.Contains(sourceBoqItemChild.Id) && !lineTypes.Contains(sourceBoqItemChild.BoqLineTypeFk))
					{
						sourceBoqItemChild.IsNotApplicable = true;
						sourceBoqItemChild.State = System.Data.Entity.EntityState.Modified;
					}

					SetNAForMissingBoqItems(sourceBoqItemChild, boqItemIdsPresentInGaebFile, lineTypes);
				}
			}

			return boqItem;
		}

		private bool RevisedImport(GX_GAEB gaebXml, int targetBoqHeaderId, out string importReport, ref List<ImportWarning> warnings)
		{
			Clear();
			_revisedLogicIsUsed = true;

			BoqHeaderEntity targetBaseBoqHeader = null;
			BoqItemEntity targetBaseBoq = null;
			BoqHeaderEntity targetBoqHeader = null;
			BoqItemEntity targetBoq = null;
			List<int> boqItemIdsPresentInGaebFile = new List<int>();

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{

				targetBoqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(e => e.Id == targetBoqHeaderId);
				if (targetBoqHeader == null)
				{
					throw new Exception(String.Format("Target BoQ Header with Id={0} must exist!", targetBoqHeaderId));
				}

				targetBoq = BoqItemLogic.GetBoqItemsHierchical(dbContext, targetBoqHeaderId).FirstOrDefault(e => e.BoqHeaderFk == targetBoqHeaderId && e.BoqItemFk == null);	// root with tree
				if (targetBoq == null)
				{
					throw new Exception(String.Format("Target BoQ with BoqHeaderFk={0} must exist!", targetBoqHeaderId));
				}

				if (targetBoq.BoqItemPrjBoqFk != null)
				{
					targetBaseBoqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(e => e.Id == targetBoqHeader.BoqHeaderFk);
					targetBaseBoq = BoqItemLogic.GetBoqItemsHierchical(dbContext, (int)targetBoq.BoqItemPrjBoqFk).FirstOrDefault(e => e.BoqHeaderFk == (int)targetBoq.BoqItemPrjBoqFk && e.BoqItemFk == null);
				}

				_BoqStructure = dbContext.Entities<BoqStructureEntity>().FirstOrDefault(e => e.Id == targetBoqHeader.BoqStructureFk);
				_BoqStructure.BoqStructureDetailEntities = dbContext.Entities<BoqStructureDetailEntity>().Where(e => e.BoqStructureFk == targetBoqHeader.BoqStructureFk).ToList();
				
			}

			_targetHasChilds = targetBoq.HasChildren;

			GX_Award gaebAward = gaebXml.Award;
			GX_BoQ gaebBoq = gaebAward.BoQ;
			GX_BoQInfo gaebBoqInfo = gaebBoq == null ? null : gaebBoq.BoQInfo;

			if (gaebBoqInfo != null)
			{
				_GaebBoqStructureDetails = gaebBoqInfo.gNodes.Where(node => node is GX_BoQBkdn).Cast<GX_BoQBkdn>();
			}

			if (_targetHasChilds || (targetBaseBoq != null && targetBaseBoq.HasChildren))	// Can't modify structure!
			{
				var result = ValidateStructure(targetBoqHeader, gaebAward, _BoqStructure, out importReport);
				if (!result) return result;
			}
			else 
			{
				OverwriteStructure_2(targetBoqHeader, gaebBoqInfo, _BoqStructure);
			}

			if (!_targetHasChilds)	// merge always!
			{
				_ImportOptions.AddNewItems = true;
				_ImportOptions.OverwriteExistingItems = true;
				targetBoqHeader.Gaebtype = gaebAward.DP;
			}

			_itemRefMap = new Dictionary<string, BoqItemEntity>();
			BuildReferenceMap_2(targetBoq);
			_isFreeBoq = _BoqStructure.BoqStandardFk == (int)BoqConstants.EBoqStandard.Free;

			IterateGaebFile_2(gaebXml, "", targetBoq, null, 0, ref warnings, ref boqItemIdsPresentInGaebFile);

			// If DeleteMissingItems is true, then we have to update N/A column to true
			if (_ImportOptions.DeleteMissingItems)
			{
				int[] lineTypes;
				lineTypes = new int[] { (int)BoqConstants.EBoqLineType.DesignDescription,
					(int)BoqConstants.EBoqLineType.SubDescription,
					(int)BoqConstants.EBoqLineType.Note,
					(int)BoqConstants.EBoqLineType.TextElement};
				SetNAForMissingBoqItems(targetBoq, boqItemIdsPresentInGaebFile, lineTypes);
			}

			// todo
			//sub quantities of items to be surcharged 
			// CreateSurchargedItemsType3(); 

			BoqItemLogic.CalculateBoqTree(targetBoq);
			SaveAll_2(targetBoq, targetBaseBoq, targetBoqHeader, targetBaseBoqHeader);

			importReport = String.Format(NLS.IMP_Gaeb_85_Ok, GaebFormat, targetBoq.Reference, 
											_divisionsTotal, _divisionsUpdated, _divisionsAdded,
											_itemsTotal, _itemsUpdated, _ItemsAdded,
											_tcsTotal, _tcsUpdated, _tcsAdded,
											_sqsTotal, _sqsUpdated, _sqsAdded,
											_specsTotal, _specsUpdated, _blobsAdded);

			if (_truncatedCounter > 0)
			{
				warnings.Add(new ImportWarning(nameof(NLS.GaebImportWarning_StringsTruncated),
							String.Format(NLS.GaebImportWarning_StringsTruncated, _truncatedCounter)));
			}

			return true;
		}

		#region iterate gaeb file

		private void IterateGaebFile_2(G_Object gaebObject, String parentReferenceNo, BoqItemEntity parentItem, BoqItemEntity prevItem, int level, ref List<ImportWarning> warnings, ref List<int> boqItemIdsPresentInGaebFile)
		{
			if (gaebObject.gNodes == null) return;

			BoqItemEntity predecessor = prevItem;
			bool goDeeper = false;
			bool skip = false;
			foreach (var gaebChildObject in gaebObject.gNodes)
			{

				// todo
				// import (not GAEB conform!) specification of divisions
				if (gaebChildObject is GX_BoQText)
				{
					GX_BoQText obj = gaebChildObject as GX_BoQText;
					// SetSpecification(boqItem, obj.gText, obj.gXml());
					goDeeper = false;
				}

				if (gaebChildObject is GX_Award)	// --> AwardInfo --> BoQ
				{
					goDeeper = true;
				}
				
				if (gaebChildObject is GX_BoQ)	//	--> BoQInfo
				{
					goDeeper = true;
				}

				if (gaebChildObject is GX_AddText)	//	--> Add. text BoQInfo
				{
					_itemsTotal++;
					if (_targetHasChilds)	// only supported for new BoQs!
					{
						_itemsSkipped++;
					}
					else
					{

						//special for "Final Remarks"	
						bool isFinalRemark = gaebObject.gNodes.Count(node => node is GX_Award) > 0;
						if (isFinalRemark)
						{
							predecessor = _lastFirstLevelDivision;
						}

						string referenceNo = Guid.NewGuid().ToString();	// dummy 
						var item = CreateBoqItemAndAdd2ReferenceMap_2(referenceNo, parentItem, predecessor, ref _ItemsAdded);
						SetTextElement_2(item, gaebChildObject as GX_AddText, parentItem, predecessor);
						predecessor = item;
						boqItemIdsPresentInGaebFile.Add(item.Id);
					}
				}

				if (gaebChildObject is GX_BoQInfo)			// boq root
				{
					if (String.IsNullOrEmpty(parentReferenceNo))	// root item
					{
						var gaebBoQInfo = gaebChildObject as GX_BoQInfo;
						parentItem.SetValue(e => e.ExternalCode, parentItem.ExternalCode, gaebBoQInfo.Name);
						if (String.IsNullOrEmpty(parentItem.Reference))
						{
							parentItem.Reference = StringExtension.Truncate(gaebBoQInfo.Name, 42);
						}
						SetBriefInfo_2(parentItem, gaebBoQInfo.LblBoQ);
						SetUserDefinedInfos(gaebChildObject, parentItem); // set user defined
						SetTotals_2(gaebBoQInfo.Totals, parentItem);
						SetBoqBudget(gaebBoQInfo.gNodes, parentItem);
						boqItemIdsPresentInGaebFile.Add(parentItem.Id);
					}
				}

				if (gaebChildObject is GX_BoQBody)
				{
					goDeeper = true;
				}
				
				if (gaebChildObject is GX_BoQCtgy)	// Division
				{
					var gaebBoQCtgy = gaebChildObject as GX_BoQCtgy;
					_divisionsTotal++;
					string referenceNo = BuildReferenceNo(parentReferenceNo, gaebBoQCtgy.att_RNoPart, "", level);
					var item = GetOrCreateMatchingBoqItem_2(GetDefiniteReferenceNo_2(referenceNo), 
															parentItem, 
															_ImportOptions.AddNewItems, 
															_ImportOptions.OverwriteExistingItems, 
															ref _divisionsAdded, 
															ref _divisionsSkipped, 
															out skip, 
															predecessor);
					if (!skip)
					{
						SetDivision_2(item, gaebBoQCtgy, parentItem, predecessor);
						SetTotals_2(gaebBoQCtgy.Totals, item);
						SetBoqBudget(gaebBoQCtgy.gNodes, item);
					}

					// remember last first level disvion as predecessor for final remarks
					if (item.BoqLineTypeFk == 1)
					{
						_lastFirstLevelDivision = item;
					}

					predecessor = item;
					boqItemIdsPresentInGaebFile.Add(item.Id);
					IterateGaebFile_2(gaebChildObject, referenceNo, item, predecessor, ++level, ref warnings, ref boqItemIdsPresentInGaebFile);	// one level deeper
					level--;
					goDeeper = false;
				}

				if (gaebChildObject is GX_Itemlist)
				{
					goDeeper = true;
				}

				if (gaebChildObject is GX_Item)
				{
					var gaebItem = gaebChildObject as GX_Item;
					_itemsTotal++;
					string referenceNo = BuildReferenceNo(parentReferenceNo, gaebItem.att_RNoPart, gaebItem.att_RNoIndex, level, (int)BoqConstants.EBoqLineType.Position);
					var item = GetOrCreateMatchingBoqItem_2(GetDefiniteReferenceNo_2(referenceNo), 
															parentItem, 
															_ImportOptions.AddNewItems, 
															_ImportOptions.OverwriteExistingItems, 
															ref _ItemsAdded, 
															ref _itemsSkipped, 
															out skip,
															predecessor);
					if (!skip)
					{
						//If OverwriteExistingItemsOnlyAqQuantities is true then only update AQ Quantity data and nothing else. PredQty in gaeb is mapped to AQ Quantity field. As discussed with Jonas if PredQty is not available in gaeb file then use Qty from gaeb file for AQ Quantity.
						if (_ImportOptions.OverwriteExistingItemsOnlyAqQuantities)
						{
							if (!gaebItem.PredQty_isEmpty())
							{
								SetValueIfNotEmpty(item, gaebItem.PredQty_isEmpty(), e => e.QuantityAdj, item.QuantityAdj, System.Convert.ToDecimal(gaebItem.PredQty)); //PredQty=>AQ Quantity
							}
							else
							{
								SetValueIfNotEmpty(item, gaebItem.Qty_isEmpty(), e => e.QuantityAdj, item.QuantityAdj, System.Convert.ToDecimal(gaebItem.Qty)); //Qty=>AQ Quantity
							}
						}
						else
						{
							SetItem_2(item, gaebItem, parentItem, prevItem, ref warnings, _ImportOptions.OverwriteExistingItemsKeepBidderData);
							//set PrjCharater
							SetItemPrjCharacter_2(item, gaebItem);
							//set workcontent
							SetItemWorkContent_2(item, gaebItem);
							// set user-defined infos
							SetUserDefinedInfos(gaebItem, item);
							// set split quantities
							var itemData = GetItemDataQtyAndQtySplits(gaebItem, item);

							// work category and classification --> see GetItemDataQtyAndQtySplits
							//if (gaebChildObject is GX_CtlgAssign)
							//{
							//	SetPrcStructureAssignment(item, gaebChildObject as GX_CtlgAssign);
							//}

							AddBoQItemData(item, itemData, true);  // only for chinese requirements
							SetSplitQuantities_2(item, itemData, gaebItem);
							item.MdcControllingUnitFk = null;
							WriteRibExtensionControllingUnitToMap(gaebChildObject as GX_Item, item);
							goDeeper = false; // --> not here!
						}
					}
					predecessor = item;
					boqItemIdsPresentInGaebFile.Add(item.Id);
				}

				if (gaebChildObject is GX_SubDescr)			// TextElement
				{
					_itemsTotal++;
					if (_targetHasChilds)	// only supported for new BoQs!
					{
						_itemsSkipped++;
					}
					else
					{
						var gaebSubDescr = gaebChildObject as GX_SubDescr;
						string dummyKey = GetDummySubDescRef(GetDefiniteReferenceNo_2(parentReferenceNo), gaebSubDescr.SubDNo.ToString()); // SubDescr do not have a reference!
						var item = GetOrCreateMatchingBoqItem_2(dummyKey, 
																parentItem, 
																_ImportOptions.AddNewItems, 
																_ImportOptions.OverwriteExistingItems, 
																ref _ItemsAdded, 
																ref _itemsSkipped, 
																out skip,
																predecessor);
						if (!skip)
						{
							SetSubDescription_2(item, gaebSubDescr, parentItem, predecessor);
						}
						boqItemIdsPresentInGaebFile.Add(item.Id);
					}
				}

				if (gaebChildObject is GX_PerfDescr)		// Design Description 
				{

					var gaebPerfDescr = gaebChildObject as GX_PerfDescr;
					string dummyKey = GetDummyDesignDescRef(GetDefiniteReferenceNo_2(parentReferenceNo), gaebPerfDescr.PerfNo.ToString());
					var item = GetOrCreateMatchingBoqItem_2(dummyKey,
															parentItem,
															_ImportOptions.AddNewItems,
															_ImportOptions.OverwriteExistingItems,
															ref _ItemsAdded,
															ref _itemsSkipped,
															out skip,
															predecessor);
					if (!skip)
					{
						SetDesignDescription_2(item, gaebPerfDescr, parentItem, predecessor);
					}
					boqItemIdsPresentInGaebFile.Add(item.Id);
				}

				if (gaebChildObject is GX_MarkupItem)		// Surcharge Item
				{
					var gaebMarkupItem = gaebChildObject as GX_MarkupItem;
					_itemsTotal++;
					string referenceNo = BuildReferenceNo(parentReferenceNo, gaebMarkupItem.att_RNoPart, gaebMarkupItem.att_RNoIndex, level);
					var item = GetOrCreateMatchingBoqItem_2(GetDefiniteReferenceNo_2(referenceNo), parentItem, _ImportOptions.AddNewItems, _ImportOptions.OverwriteExistingItems, ref _ItemsAdded, ref _itemsSkipped,
															out skip,
															predecessor);
					if (!skip)
					{
						SetSurchargeItem_2(item, gaebMarkupItem, null);
					}

					predecessor = item;
					boqItemIdsPresentInGaebFile.Add(item.Id);
					goDeeper = false;
				}

				if (gaebChildObject is GX_Remark)			// Note 
				{
					var gaebRemark = gaebChildObject as GX_Remark;
					_itemsTotal++;

					if (gaebRemark.att_ID.Length > 32)
					{
						throw new Exception(String.Format("Remark ID must not be longer than 32 characters: {0}", gaebRemark.att_ID));
					}

					var item = GetOrCreateMatchingBoqItem_2(GetDummyNoteRef(gaebRemark.att_ID), parentItem, _ImportOptions.AddNewItems, _ImportOptions.OverwriteExistingItems,
									ref _ItemsAdded, ref _itemsSkipped,	out skip, predecessor);
					if (!skip)
					{
						SetNote_2(item, gaebRemark, parentItem, predecessor, gaebRemark.att_ID);
					}

					predecessor = item;
					boqItemIdsPresentInGaebFile.Add(item.Id);
					goDeeper = false;
				}


				if (gaebChildObject is GX_Description)
				{
					// handled in GX_Item ...
					goDeeper = false;
				}

				if (gaebChildObject is GX_TextComplement)
				{
					// handled in GX_Item ...
					goDeeper = false;
				}

				if (gaebChildObject is GX_QtySplit)
				{
					// handled in GX_Item ...
					goDeeper = false;
				}

				if (gaebChildObject is GX_CtlgAssign)
				{
					// handled in GetItemDataQtyAndQtySplits
					goDeeper = false;
				}

				if (goDeeper == true)
				{
					IterateGaebFile_2(gaebChildObject, parentReferenceNo, parentItem, predecessor, level, ref warnings, ref boqItemIdsPresentInGaebFile);
				}

			}
			AssignRibExtensionControllingUnit();
		}

		#endregion

		#region boq structure

		private bool ValidateStructure(BoqHeaderEntity boqHeader, GX_Award gaebAward, BoqStructureEntity boqStructure, out string errors)
		{

			errors = String.Empty;

			GX_BoQ gaebBoq;
			GX_BoQInfo gaebBoqInfo;

			if (((gaebBoq = gaebAward.BoQ) == null) || ((gaebBoqInfo = gaebBoq.BoQInfo) == null))
			{
				return true;
			}

			// check for match with default structure
			bool indexAdded;
			String gaebBoqMask = GetGaebBoqMask(gaebBoqInfo, out indexAdded);

			bool structureEquals = gaebBoqMask.Equals(boqStructure.Boqmask);
			if ((!structureEquals) && indexAdded)
			{
				string originalGaebBoQMask = gaebBoqMask.Substring(0, gaebBoqMask.Length - 1);
				structureEquals = originalGaebBoQMask.Equals(boqStructure.Boqmask);
			}

			if (!structureEquals)
			{
				bool hasTargetBoqAnIndex = boqStructure.Boqmask.Contains("I");
				bool hasImportBoqAnIndex = gaebBoqMask.Contains("I");
				var targetGaebBoqMask = boqStructure.Boqmask.TrimEnd('I');
				var importGaebBoqMask = gaebBoqMask.TrimEnd('I');

				structureEquals = importGaebBoqMask.Equals(targetGaebBoqMask) && (hasTargetBoqAnIndex || !hasImportBoqAnIndex);
			}

			if (structureEquals)
			{
				// check data types
				var gxBoQBkdns = gaebBoqInfo.gNodes.Where(node => node is GX_BoQBkdn).Cast<GX_BoQBkdn>();
				if (gxBoQBkdns != null)
				{
					// todo: validation skipped due to problems when import GAEB2000 p files 
					//for (var j = 0; j < gxBoQBkdns.Count(); j++)
					//{
					//	GX_BoQBkdn gaebBoqStructureDetail = gxBoQBkdns.ElementAt(j);
					//	var boqStructureDetail = boqStructure.BoqStructureDetailEntities.ElementAt(j);
					//	if (!gaebBoqStructureDetail.Num_isEmpty())
					//	{
					//		if (gaebBoqStructureDetail.Num.Equals("Yes") && boqStructureDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric ||
					//				gaebBoqStructureDetail.Num.Equals("No") && boqStructureDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
					//		//|| gaebBoqStructureDetail.Num.Equals("No") && BoqStructure.LeadingZeros)
					//		{
					//			errors += String.Format("Data type mismatch in GAEB structure!");
					//			break;
					//		}
					//	}
					//}
				}
			}
			else
			{
				errors += String.Format(NLS.IMP_Structure_mismatch, gaebBoqMask, boqStructure.Boqmask);
			}

			return String.IsNullOrEmpty(errors) ? true : false;

		}

		private void OverwriteStructure_2(BoqHeaderEntity boqHeader, GX_BoQInfo gaebBoqInfo, BoqStructureEntity boqStructure)
		{

			if (gaebBoqInfo == null)
			{
				return;
			}

			bool indexAdded;
			string gaebBoqMask = GetGaebBoqMask(gaebBoqInfo, out indexAdded);
			boqStructure.Boqmask = gaebBoqMask;
			boqStructure.Description = "GAEB " + gaebBoqMask;
			boqStructure.DescriptionTr = null;
			boqStructure.Sorting = Int16.MaxValue;
			boqStructure.Isdefault = false;
			boqStructure.PredefinedStructure = false;
			boqStructure.EnforceStructure = !_isFreeBoq; //#!# todo:...have to considere also the "left out hierarchy"
			boqStructure.Version = 0;	// force save to create a new record
			boqStructure.BoqStructureDetailEntities = new List<BoqStructureDetailEntity>();

			bool indexFound = false;
			if (_GaebBoqStructureDetails != null)
			{
				var boqLevel = 0;
				foreach (var gaebBoqStructureDetail in _GaebBoqStructureDetails)
				{
					var boqStructureDetail = new BoqStructureDetailEntity
					{
						DescriptionInfo = new DescriptionTranslateType(gaebBoqStructureDetail.LblBoQBkdn),
						LengthReference = (Int32)gaebBoqStructureDetail.Length,
						DataType = "Yes".Equals(gaebBoqStructureDetail.Num) ? (int)BoqConstants.EBoqStructureDetailDataType.Numeric : (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric,
						IsLeftAlignment = "No".Equals(gaebBoqStructureDetail.Num) && "left".Equals(gaebBoqStructureDetail.Alignment.ToLower()),
						DiscountAllowed = true
					};

					if ("Item".Equals(gaebBoqStructureDetail.Type))
					{
						boqStructureDetail.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Position;
						boqStructureDetail.Stepincrement = 10;
						boqStructureDetail.StartValue = "10";
						BoqStructure.LeadingZeros = false; // default, may be overwritten in DetermineLeadingZeros
						if (String.IsNullOrEmpty(boqStructureDetail.DescriptionInfo.Translated))
						{
							boqStructureDetail.DescriptionInfo.Description = NLS.IMP_Structure_Item;
						}
					}
					else if ("BoQLevel".Equals(gaebBoqStructureDetail.Type) || "Lot".Equals(gaebBoqStructureDetail.Type))
					{
						boqStructureDetail.BoqLineTypeFk = boqLevel + 1;
						boqStructureDetail.Stepincrement = 1;
						boqStructureDetail.StartValue = "1";
					}
					else if ("Index".Equals(gaebBoqStructureDetail.Type))
					{
						boqStructureDetail.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Index;
						boqStructureDetail.Stepincrement = 1;
						boqStructureDetail.StartValue = "1";
						boqStructureDetail.DescriptionInfo.Description = NLS.IMP_Structure_Index;
						indexFound = true;
					}
					boqStructure.BoqStructureDetailEntities.Add(boqStructureDetail);
					boqLevel++;
				}

				if (!indexFound)
				{
					var boqStructureDetail = new BoqStructureDetailEntity
					{
						DescriptionInfo = new DescriptionTranslateType(NLS.IMP_Structure_Index),
						LengthReference = BoqCommonLogic.GetForceBoqReferenceNoIndex() ? 1 : 0,
						DataType = (int)BoqConstants.EBoqStructureDetailDataType.Alphanumeric,
						DiscountAllowed = true,
						BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Index,
						Stepincrement = 1,
						StartValue = "1"
					};
					boqStructure.BoqStructureDetailEntities.Add(boqStructureDetail);
				}
			}

			SetNameUrb_2(boqStructure, gaebBoqInfo);
			SetUserDefinedLabels_2(boqStructure, gaebBoqInfo);

			// copy cat assign
			// set BOQ_CAT_ASSIGN_CONFTYPE_FK ih Structure = NULL;
			// später neuen BOQ_CAT_ASSIGN_CONFTYPE_FK setzen
			bool isMatchingCatAssign = CatalogAssignmentsAreEqual(boqHeader);
			if (isMatchingCatAssign == false)
			{
				// copy cat assign --> see later
				boqStructure.BoqCatAssignConfTypeFk = null;	// structure was overwritten by user
			}

		}

		/// <summary>
		/// Returns the GAEB BOQ mask
		/// </summary>
		/// <param name="gaebBoqInfo"></param>
		/// <param name="indexAdded"></param>
		/// <returns></returns>
		private string GetGaebBoqMask(GX_BoQInfo gaebBoqInfo, out bool indexAdded)
		{
			indexAdded = false;

			String gaebBoqMask = String.Empty;
			var gaebLevel = 0;

			var gxBoQBkdns = gaebBoqInfo.gNodes.Where(node => node is GX_BoQBkdn).Cast<GX_BoQBkdn>();
			if (gxBoQBkdns != null)
			{
				foreach (var gaebBoqStructureDetail in gxBoQBkdns)
				{
					for (var j = 0; j < gaebBoqStructureDetail.Length; j++)		// why do we need this further loop???
					{
						if (gaebBoqStructureDetail.Type.Equals("Lot"))
							gaebBoqMask += (gaebLevel + 1).ToString();
						else if (gaebBoqStructureDetail.Type.Equals("Item"))
						{
							gaebBoqMask += "P";
							_index4Item = gaebLevel;
						}
						else if (gaebBoqStructureDetail.Type.Equals("BoQLevel"))
							gaebBoqMask += (gaebLevel + 1).ToString();
						else if (gaebBoqStructureDetail.Type.Equals("Index"))
							gaebBoqMask += "I";
					}
					gaebLevel++;
				}
			}

			if ((!"I".Equals(gaebBoqMask.Right(1))) && BoqCommonLogic.GetForceBoqReferenceNoIndex())
			{
				gaebBoqMask += "I";
				indexAdded = true;
			}

			return gaebBoqMask;
		}

		private void SetNameUrb_2(BoqStructureEntity strucEntity, GX_BoQInfo gaebBoqInfo)
		{

			if (gaebBoqInfo.NoUPComps_isEmpty()) return;

			String[] urbName = new String[6];
			int upComps = System.Convert.ToInt32(gaebBoqInfo.NoUPComps);

			if (upComps >= 1 && gaebBoqInfo.LblUPComp1 != null)
			{
				urbName[1] = gaebBoqInfo.LblUPComp1.gValue;
			}
			if (upComps >= 2 && gaebBoqInfo.LblUPComp2 != null)
			{
				urbName[2] = gaebBoqInfo.LblUPComp2.gValue;
			}
			if (upComps >= 3 && gaebBoqInfo.LblUPComp3 != null)
			{
				urbName[3] = gaebBoqInfo.LblUPComp3.gValue;
			}
			if (upComps >= 4 && gaebBoqInfo.LblUPComp4 != null)
			{
				urbName[4] = gaebBoqInfo.LblUPComp4.gValue;
			}
			if (upComps >= 5 && gaebBoqInfo.LblUPComp5 != null)
			{
				urbName[5] = gaebBoqInfo.LblUPComp5.gValue;
			}
			if (upComps >= 6 && gaebBoqInfo.LblUPComp6 != null)
			{
				urbName[6] = gaebBoqInfo.LblUPComp6.gValue;
			}

			for (int i = 1; i < 6; i++)
			{
				if (!String.IsNullOrEmpty(urbName[i]))
				{
					strucEntity.SetValue("NameUrb" + i.ToString(), urbName[i]);
				}
			}
		}

		private void SetUserDefinedLabels_2(BoqStructureEntity strucEntity, GX_BoQInfo gxGaebBoqInfo)
		{
			for (int i = 1; i <= 5; i++)
			{
				G_Object lbl = gxGaebBoqInfo.gNodes.Where(node => node.gName.Equals(String.Format("LblUserDef{0}", i))).FirstOrDefault();
				if (lbl != null)
				{
					strucEntity.SetNameUserdefined(i, lbl.gValue);
				}
			}
		}

		#endregion

		#region save entity

		private void SaveAll_2(BoqItemEntity targetBoq, BoqItemEntity targetBaseBoq, BoqHeaderEntity targetBoqHeader, BoqHeaderEntity targetBaseBoqHeader)
		{
			using (var transactionScope = TransactionScopeFactory.Create())
			{
				List<BoqItemEntity> addedBoqPositions = null, modifiedBoqPositions = null;
				BoqHeaderContextVEntity boqHeaderContext;
				EBoqType myBoqType;

				IEnumerator<Int32> ids;
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					if (_modifiedBlobs.Count > 0)
					{
						ids = _blobsAdded == 0 ? new List<Int32>().GetEnumerator() : SequenceManager.GetNextList<Int32>("BAS_BLOBS", _blobsAdded, "ID").GetEnumerator();
						foreach (var blob in _modifiedBlobs)
						{
							if (blob.Id == 0)
							{
								ids.MoveNext();
								blob.Id = ids.Current;
							}
						}
						new BlobLogic().UpdateBlobs(_modifiedBlobs);
					}

					// pre-fetch id's
					int totalAdded = _divisionsAdded + _ItemsAdded;
					var boqIds = totalAdded == 0 ? new List<Int32>().GetEnumerator() : SequenceManager.GetNextList<Int32>("BOQ_ITEM", totalAdded, "ID").GetEnumerator();
					var tcIds = _tcsAdded == 0 ? new List<Int32>().GetEnumerator() : SequenceManager.GetNextList<Int32>("BOQ_TEXT_COMPLEMENT", _tcsAdded).GetEnumerator();
					var sqIds = _sqsAdded == 0 ? new List<Int32>().GetEnumerator() : new BoqSplitQuantityLogic().GetNextIds(_sqsAdded).GetEnumerator();

					// must be done here - later navigation entities will be removed!
					// (re-) calculate boq
					// if (_ItemsAdded > 0 || _itemsUpdated > 0)
					//{
					//	BoqItemLogic.CalculateBoqTree(targetBoq);
					//}

					List<BoqItemEntity> newAndChangedBoqItems = new List<BoqItemEntity>();

					foreach (var pair in _itemRefMap)
					{
						bool add2Modified = false;
						var item = pair.Value;

						if (item.Id == 0 || item.State == System.Data.Entity.EntityState.Modified)
						{
							if (item.Id == 0)	// new item
							{
								boqIds.MoveNext();
								item.Id = boqIds.Current;
							}
							else
							{
								if (item.IsDivision)
								{
									_divisionsUpdated++;
								}
								else
								{
									_itemsUpdated++;
								}
							}
						
							// dbContext.Entities<BoqItemEntity>().Add(item); --> must be done later - after removed navigation properties
							add2Modified = true;	
						}

						// set reference to newly added blobs
						if (item.SpecificationBlob != null && !item.BasBlobsSpecificationFk.HasValue)
						{
							item.BasBlobsSpecificationFk = item.SpecificationBlob.Id;
							add2Modified = true;	
						}

						if (item.BoqSplitQuantityEntities != null)
						{
							foreach (var sq in item.BoqSplitQuantityEntities.Where(e => e.Id == 0 || e.State == System.Data.Entity.EntityState.Modified))
							{
								if (sq.Id == 0)
								{
									sqIds.MoveNext();
									sq.Id = sqIds.Current;
									// _sqsAdded++; --> see SetSplitQuantities
								}
								else
								{
									_sqsUpdated++;
								}
								sq.BoqItemFk = item.Id;
								sq.BoqHeaderFk = item.BoqHeaderFk;
								dbContext.Entities<BoqSplitQuantityEntity>().Add(sq);
								dbContext.Entry(sq).State = sq.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;

								if (sq.BoqSplitQuantity2CostGroupEntities != null && sq.BoqSplitQuantity2CostGroupEntities.Any())
								{
									foreach (var sq2CostGroup in sq.BoqSplitQuantity2CostGroupEntities)
									{
										if (sq2CostGroup.Id == 0)
										{
											sq2CostGroup.Id = SequenceManager.GetNext("BOQ_SPLIT_Q2COSTGRP");
										}
										dbContext.Entities<BoqSplitQuantity2CostGroupEntity>().Add(sq2CostGroup);
										dbContext.Entry(sq2CostGroup).State = sq2CostGroup.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
									}
								}
							}
						}
						
						if (item.BoqTextComplementEntities != null)
						{
							foreach (var tc in item.BoqTextComplementEntities.Where(e => e.Id == 0 || e.State == System.Data.Entity.EntityState.Modified))
							{
								if (tc.Id == 0)
								{
									tcIds.MoveNext();
									tc.Id = tcIds.Current;
									// _tcsAdded++; --> see SetTextComplements
								}
								else
								{
									_tcsUpdated++;
								}
								tc.BoqItemFk = item.Id;
								tc.BoqHeaderFk = item.BoqHeaderFk;
								tc.BoqHeaderEntity = null;
								tc.BoqItemEntity = null;
								dbContext.Entities<BoqTextComplementEntity>().Add(tc);
								dbContext.Entry(tc).State = tc.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
							}
						}

						if (item.BoqSurchardedItemEntities != null)
						{

							if (item.BoqSurchardedItemEntities.Count > 0)
							{
								foreach(var sboq in item.BoqSurchardedItemEntities)
								{
									sboq.BoqSurchargedItem = null;
								}
							}

							// todo
							//foreach (var siEntity in boqItem.BoqSurchardedItemEntities)
							//{
							//	surchargedItemIds.MoveNext();
							//	siEntity.BoqItemFk = boqItem.Id;
							//	siEntity.Id = surchargedItemIds.Current;
							//	siEntity.BoqSurcharedItemFk = siEntity.BoqSurchargedItem.Id;

							//	surchargedItems.Add(siEntity);
							//}

						}

						if (item.BoqItem2CostGroupEntities != null && item.BoqItem2CostGroupEntities.Any())
						{
							foreach (var boq2CostGroup in item.BoqItem2CostGroupEntities)
							{
								if (boq2CostGroup.Id == 0)
								{
									boq2CostGroup.Id = SequenceManager.GetNext("BOQ_ITEM2COSTGRP");
								}
								boq2CostGroup.BoqHeaderFk = item.BoqHeaderFk;
								boq2CostGroup.BoqItemFk = item.Id;
								dbContext.Entities<BoqItem2CostGroupEntity>().Add(boq2CostGroup);
								dbContext.Entry(boq2CostGroup).State = boq2CostGroup.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
							}
						}

                        if (item.BoqCharacterContentEntity_BoqCharacterContentPrjFk != null)
                        {
                            if (item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Id == 0)
                            {
                                item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Id = SequenceManager.GetNext("BOQ_CHARACTER_CONTENT");
                            }
                            item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.BoqItemEntities_BoqCharacterContentPrjFk = null;
                            item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.BoqItemEntities_BoqCharacterContentWorkFk = null;
                            dbContext.Entities<BoqCharacterContentEntity>().Add(item.BoqCharacterContentEntity_BoqCharacterContentPrjFk);
                            dbContext.Entry(item.BoqCharacterContentEntity_BoqCharacterContentPrjFk).State = item.BoqCharacterContentEntity_BoqCharacterContentPrjFk.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
                        }

                        if (item.BoqCharacterContentEntity_BoqCharacterContentWorkFk != null)
                        {
                            if (item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Id == 0)
                            {
                                item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Id = SequenceManager.GetNext("BOQ_CHARACTER_CONTENT");
                            }
                            item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.BoqItemEntities_BoqCharacterContentPrjFk = null;
                            item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.BoqItemEntities_BoqCharacterContentWorkFk = null;
                            dbContext.Entities<BoqCharacterContentEntity>().Add(item.BoqCharacterContentEntity_BoqCharacterContentWorkFk);
                            dbContext.Entry(item.BoqCharacterContentEntity_BoqCharacterContentWorkFk).State = item.BoqCharacterContentEntity_BoqCharacterContentWorkFk.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
                        }

						if (add2Modified)
						{
							newAndChangedBoqItems.Add(item);
						}					
					}

					foreach (var pair in _itemRefMap)
					{
						var item = pair.Value;

						// set fk for navigation properties
						if (item.BoqItemParent != null)
						{
							item.BoqItemFk = item.BoqItemParent.Id;
							// item.BoqItemParent = null;
						}

						if (item.BoqItemBasisParent != null)
						{
							item.BoqItemBasisFk = item.BoqItemBasisParent.Id;
							item.BoqItemBasisParent = null;
						}

						if (item.BoqItemReferenceParent != null)
						{
							item.BoqItemReferenceFk = item.BoqItemReferenceParent.Id;
							item.BoqItemReferenceParent = null;
						}

						// remove navigation entities - otherwise dbContext set all navigation entities as new/changed?
						ClearNavigationProperties(item);
					}

					foreach (var boqItem in newAndChangedBoqItems)
					{
						dbContext.Entry(boqItem).State = boqItem.Version > 0 ? System.Data.Entity.EntityState.Modified : System.Data.Entity.EntityState.Added;
						if (boqItem.BriefInfo.Modified)
						{
							boqItem.SaveTranslate(this.UserLanguageId, new Func<BoqItemEntity, DescriptionTranslateType>[] { e => e.BriefInfo });
						}
					}

					// must remove navigation properties first!
					ClearNavigationProperties(targetBoq);
					dbContext.Entry(targetBoq).State = System.Data.Entity.EntityState.Modified;
					if (targetBoq.BriefInfo.Modified)
					{
						targetBoq.SaveTranslate(this.UserLanguageId, new Func<BoqItemEntity, DescriptionTranslateType>[] { e => e.BriefInfo });
					}
					
					// alternate solution:
					//var rootEntity = dbContext.Entities<BoqItemEntity>().Find(targetBoq.BoqHeaderFk, targetBoq.Id);
					//if (rootEntity == null)
					//{
					//	rootEntity = dbContext.Entities<BoqItemEntity>().Add(targetBoq);
					//}
					//dbContext.Entry(rootEntity).State = System.Data.Entity.EntityState.Modified;
					//dbContext.Entry(rootEntity).CurrentValues.SetValues(targetBoq);	--> NO DEEPCOPY!

					// update boq structure
					if (_BoqStructure != null && _BoqStructure.BoqStructureDetailEntities != null && _BoqStructure.Version == 0)
					{
						_BoqStructure.Id = SequenceManager.GetNext<Int32>("BOQ_STRUCTURE", 1, "ID");
						dbContext.Entities<BoqStructureEntity>().Add(_BoqStructure);
						dbContext.Entry(_BoqStructure).State = System.Data.Entity.EntityState.Added;
						foreach (var boqStructureDetail in _BoqStructure.BoqStructureDetailEntities)
						{
							boqStructureDetail.Id = SequenceManager.GetNext<Int32>("BOQ_STRUCTURE_DETAIL", 1, "ID");
							boqStructureDetail.BoqStructureFk = _BoqStructure.Id;
							dbContext.Entities<BoqStructureDetailEntity>().Add(boqStructureDetail);
							dbContext.Entry(boqStructureDetail).State = System.Data.Entity.EntityState.Added;
						}
						// _BoqStructure.BoqStructureDetailEntities = null;

						// The settings for LeadingZeros are not always easy to determine from the imported BoqStructure alone.
						// To have a better behavior we check the created reference numbers for occurrences of leading zeros.
						_BoqStructure.LeadingZeros = DetermineLeadingZeros(_itemRefMap, _BoqStructure, _BoqStructure.BoqStructureDetailEntities);

						// save cat_Assign, cat_assign_details
						if (!CatalogAssignmentsAreEqual(targetBoqHeader))
						{
							UpdateCatAssignments(_BoqStructure, dbContext);
						}

						try
						{
							dbContext.SaveChanges();	// otherwise fk could not be changed?
						}
						catch (Exception ex)
						{
							ex = BoqHelper.GetInnermostException(ex);
							if (ex is DbEntityValidationException)
							{
								throw new Exception(BoqHelper.GetValidationErrors(ex));
							}
							else
							{
								throw ex;
							}
						}

						// update header(s)
						dbContext.Entities<BoqHeaderEntity>().Add(targetBoqHeader);
						targetBoqHeader.BoqTypeFk = null;
						targetBoqHeader.BoqStructureFk = _BoqStructure.Id;
						dbContext.Entry(targetBoqHeader).State = System.Data.Entity.EntityState.Modified;
						
						if (targetBaseBoqHeader != null)
						{
							targetBaseBoqHeader.SyncItem(targetBoqHeader);
							dbContext.Entities<BoqHeaderEntity>().Add(targetBaseBoqHeader);
							dbContext.Entry(targetBaseBoqHeader).State = System.Data.Entity.EntityState.Modified;
						}
					}

					addedBoqPositions = BoqItemLogic.GetTrackedEntitiesFromDbContext<BoqItemEntity>(dbContext, EntityState.Added).Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position && e.BoqItemPrjBoqFk == null).ToList();
					modifiedBoqPositions = BoqItemLogic.GetTrackedEntitiesFromDbContext<BoqItemEntity>(dbContext, EntityState.Modified).Where(e => e.BoqLineTypeFk == (int)EBoqLineType.Position && e.BoqItemPrjBoqFk == null).ToList();
					myBoqType = BoqItemLogic.GetBoqType(targetBoq.BoqHeaderFk, out boqHeaderContext);

					dbContext.SaveChanges();

					if (totalAdded > 0)
					{
						var boqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(e => e.Id == targetBoq.BoqHeaderFk);
						dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", boqHeader.Id);
						if (boqHeader.BoqHeaderFk.HasValue)
						{
							BoqHeaderEntity baseBoqHeader = dbContext.Entities<BoqHeaderEntity>().FirstOrDefault(e => e.Id == (int)boqHeader.BoqHeaderFk);
							if (baseBoqHeader != null)
							{
								dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", baseBoqHeader.Id);
							}
						}
					}
				}
				transactionScope.Complete();

				//DEV-23300-Create estimate line items for newly added/updated boq items from gaeb import for project boqs only and only if Is Estimate Boq Driven flag is true in project container
				if (new[] { EBoqType.Project, EBoqType.Bid, EBoqType.Ord, EBoqType.Wip, EBoqType.Bill }.Contains(myBoqType)
				    && ((addedBoqPositions != null && addedBoqPositions.Count > 0) || (modifiedBoqPositions != null && modifiedBoqPositions.Count > 0)))
				{
					var estBoqData = new EstimateMainBoqDrivenData() { TargetProjectId = (int)boqHeaderContext.ContextProjectId, GaebBoqItems = addedBoqPositions, GaebBoqItemsToUpdate = modifiedBoqPositions, IsFromBoqImport = true };
					Injector.Get<IEstimateMainLineItemLogic>().CreateEstLineItemsFromBoq(estBoqData);
				}

			}
		}

		#endregion

		/// <summary>
		/// 
		/// </summary>
		/// <param name="structureEntity"></param>
		/// <param name="dbContext"></param>
		protected void UpdateCatAssignments(BoqStructureEntity structureEntity, RVPBizComp.DbContext dbContext)
		{

			if (ImportOptions == null || ImportOptions.Catalogs == null)
			{
				return;
			}

			BoqCatAssignConfEntity catAssignEntity = structureEntity.BoqCatAssignFk.HasValue ? BoqTypeLogic.GetBoqCatAssignConfById(structureEntity.BoqCatAssignFk.Value) : null;
			if (catAssignEntity == null) // create new entry
			{
				catAssignEntity = BoqTypeLogic.CreateBoqCatAssign();
				// catAssignEntity.DescriptionInfo = ?
			}
			else  // clone existing entry
			{
				catAssignEntity.Id = SequenceManager.GetNext<Int32>("BOQ_CAT_ASSIGN", 1, "ID");
				catAssignEntity.Version = 0;
			}
			catAssignEntity.BoqCatAssignDetailEntities = null;
			dbContext.Entities<BoqCatAssignConfEntity>().Add(catAssignEntity);
			dbContext.Entry(catAssignEntity).State = System.Data.Entity.EntityState.Added;

			foreach (var importCat in ImportOptions.Catalogs)
			{
				if (importCat.BoqCatalogFk.HasValue)
				{
					BoqCatAssignDetailEntity catAssignDetailEntity = BoqTypeLogic.CreateBoqCatAssignDetail();
					catAssignDetailEntity.GaebId = (BoqCatalogLogic.GetGaebIdByCatalogType(importCat.CtlgType) ?? (int)BoqConstants.GaebType.Miscellaneous);
					catAssignDetailEntity.BoqCatalogFk = importCat.BoqCatalogFk.Value;
					if (BoqHelper.IsCostGroupCatalog(catAssignDetailEntity.BoqCatalogFk))
					{
						if (importCat.BasCostgroupCatFk.HasValue && importCat.BasCostgroupCatFk.Value > 0)	// todo: Update importCat.BasCostgroupCatFk for new cats
						{
							catAssignDetailEntity.BasCostgroupCatFk = importCat.BasCostgroupCatFk;
						}
					}
					catAssignDetailEntity.BoqCatAssignFk = catAssignEntity.Id;
					catAssignDetailEntity.GaebName = importCat.CtlgName;
					catAssignDetailEntity.SearchMode = (int)importCat.CatalogAssignmentMode;
					dbContext.Entities<BoqCatAssignDetailEntity>().Add(catAssignDetailEntity);
					dbContext.Entry(catAssignDetailEntity).State = System.Data.Entity.EntityState.Added;
				}
			}

			structureEntity.BoqCatAssignFk = catAssignEntity.Id;
			structureEntity.BoqCatAssignConfTypeFk = null;	// structure was overwritten by user
			//dbContext.Entry(structureEntity).State = System.Data.Entity.EntityState.Modified;

		}

		private void ClearNavigationProperties(BoqItemEntity item)	// todo: exchange entity framework!
		{
			item.BoqItemParent = null;
			item.BoqItemChildren = null;
			item.BoQItems = null;
			item.BoqTextComplementEntities = null;
			item.BoqSplitQuantityEntities = null;
			item.BoqItemBasisParent = null;
			item.BoqItem2CostGroupEntities = null;
            item.BoqCharacterContentEntity_BoqCharacterContentPrjFk = null;
            item.BoqCharacterContentEntity_BoqCharacterContentWorkFk = null;
			
			if (item.BoqSurcharedItemFk != null)
			{
				item.BoqSurcharedItemFk.Clear();
				item.BoqSurcharedItemFk = null;
			}

			if (item.BoqSurchardedItemEntities != null)
			{
				item.BoqSurchardedItemEntities.Clear();
				item.BoqSurchardedItemEntities = null;
			}
		}

		#endregion

		#region reference map

		private void BuildReferenceMap_2(BoqItemEntity boqRootItem)
		{
			ForEachExceptTheRoot(boqRootItem, boqItem =>
			{
				if (boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Note)   // has no Reference no.
				{
					Add2ReferenceMap(GetDummyNoteRef(boqItem), boqItem);
				}
				else if (boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.SubDescription)	// has no Reference no.
				{
					Add2ReferenceMap(GetDummySubDescRef(GetDefiniteReferenceNo_2(boqItem.BoqItemParent.Reference), boqItem.DesignDescriptionNo), boqItem);
				}
				else if (boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.DesignDescription) // has no Reference no.
				{
					Add2ReferenceMap(GetDummyDesignDescRef(GetDefiniteReferenceNo_2(boqItem.BoqItemParent.Reference), boqItem.DesignDescriptionNo), boqItem);
				}
				else
				{
					if (!String.IsNullOrEmpty(boqItem.Reference))
					{
						Add2ReferenceMap(GetDefiniteReferenceNo_2(boqItem.Reference), boqItem);
					}
				}
			});
		}

		private void Add2ReferenceMap(string referenceNo, BoqItemEntity boqItem, bool ignoreException = false)
		{
			string justifiedRefNr = BoqImExportHelper.JustifyReference(referenceNo);
			if (_itemRefMap.ContainsKey(justifiedRefNr))
			{
				_duplicateRefNo.Add(justifiedRefNr);
				if (ignoreException)
				{
					return;
				}
				else
				{
					throw new Exception(String.Format("Duplicate Reference-No. {0} in BoQ!", justifiedRefNr));
				}
			}
			_itemRefMap.Add(justifiedRefNr, boqItem);
		}
		
		private BoqItemEntity CreateBoqItemAndAdd2ReferenceMap_2(string referenceNo, BoqItemEntity parentItem, BoqItemEntity prevItem, ref int createCounter)
		{
			var item = CreateBoqItem_2(referenceNo, parentItem, prevItem, ref createCounter);
			Add2ReferenceMap(referenceNo, item);
			return item;
		}

		private BoqItemEntity CreateBoqItem_2(string referenceNo, BoqItemEntity parentItem, BoqItemEntity prevItem, ref int createCounter)
		{
			var item = new BoqItemEntity();
			createCounter++;

			item.BoqItemParent = parentItem;
			if (item.BoqItemParent != null)
			{
				item.BoqItemParent.AddChild(item);
			}

			// item.Reference = referenceNo; --> must be done with SetReference!
			item.BoqHeaderFk = parentItem.BoqHeaderFk;
			item.BoqItemPrjBoqFk = parentItem.BoqItemPrjBoqFk;
			item.BasItemTypeFk = 0;
			item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Normal;

			if (prevItem != null && item.IsTextElementWithoutReference)
			{
				item.BoqItemBasisParent = prevItem;
			}
			
			return item;
		}

		private BoqItemEntity GetOrCreateMatchingBoqItem_2(string referenceNo, BoqItemEntity parentItem, bool addNew, bool overwriteExisting, ref int createCounter, ref int skippeCounter, out bool skipped, BoqItemEntity prevItem)
		{
			skipped = false;
			BoqItemEntity item = GetMatchingBoqItem(referenceNo);
			if (item == null)
			{
				if (addNew && parentItem != null)	// maybe parent item was skipped - so we can't create a sub-item!)
				{
					item = CreateBoqItemAndAdd2ReferenceMap_2(referenceNo, parentItem, prevItem, ref createCounter);
				}
				else
				{
					skipped = true;
				}
			}
			else
			{
				if (!overwriteExisting)
				{
					skippeCounter++;
					skipped = true;
				}
				else
				{
					// ALM 133678 all boq items are marked as modified.	
					// Checking all properties is time - consuming and error - dependent
					item.State = System.Data.Entity.EntityState.Modified;
				}
			}

			return item;
		}

		private BoqItemEntity GetMatchingBoqItem(string referenceNo)
		{
			BoqItemEntity boqItem = null;

			string justifiedRefNr = BoqImExportHelper.JustifyReference(referenceNo);
			if (_itemRefMap.ContainsKey(justifiedRefNr))
			{
				boqItem = _itemRefMap[justifiedRefNr];
			}

			return boqItem;
		}

		private static string GetGaebID(int no)
		{
			return "ID" + no;
		}

		#endregion

		#region update entity

		private void SetTotals_2(GX_Totals totals, BoqItemEntity boqItem)
		{
			TotalCount++;

			if (totals != null && boqItem != null)
			{

				SetMonetaryValueIfNotEmpty(boqItem, totals.DiscountAmt_isEmpty(), e => e.Discount, boqItem.Discount, System.Convert.ToDecimal(totals.DiscountAmt));
                SetValueIfNotEmpty(boqItem, totals.DiscountPcnt_isEmpty(), e => e.DiscountPercentIt, boqItem.DiscountPercentIt, System.Convert.ToDecimal(totals.DiscountPcnt));
                SetMonetaryValueIfNotEmpty(boqItem, totals.TotAfterDisc_isEmpty(), e => e.Finalprice, boqItem.Finalprice, System.Convert.ToDecimal(totals.TotAfterDisc));
                SetMonetaryValueIfNotEmpty(boqItem, totals.Total_isEmpty(), e => e.DiscountedPrice, boqItem.DiscountedPrice, System.Convert.ToDecimal(totals.Total));

				if (!totals.TotalLSUM_isEmpty())
				{
                    boqItem.SetMonetaryValue(e => e.LumpsumPrice, boqItem.LumpsumPrice, System.Convert.ToDecimal(totals.TotalLSUM));
					boqItem.IsLumpsum = true;

					//if (boqItem.Finalprice == 0)
					//{
						boqItem.SetValue(e => e.Finalprice, boqItem.Finalprice, boqItem.LumpsumPrice);
					//}
				}

				//if (boqItem.Finalprice == 0)
				//{
                    boqItem.SetMonetaryValue(e => e.Finalprice, boqItem.Finalprice, boqItem.DiscountedPrice);
				//}
			}
		}

		private void SetBriefInfo_2(BoqItemEntity item, string spec)
		{

			bool isNewitem = item.Version == 0;
			string newSpec = BoqGaebImportLogic.TruncateString(spec, 2000, ref _truncatedCounter);
			if (isNewitem || item.BriefInfo.Translated != newSpec)
			{
				item.BriefInfo.Translated = newSpec;
				item.BriefInfo.Modified = true;
				item.State = System.Data.Entity.EntityState.Modified;
			}

		}

		/// <summary>
		/// Standardleistungsnummer
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxDescription"></param>
		private void SetStlNo(BoqItemEntity item, GX_Description gxDescription)
		{
			string stlno = string.Empty;

			if (gxDescription.STLBBau != null)
			{
				stlno = gxDescription.STLBBau.getInnerXML(false);
			}
			else
			{
				stlno = gxDescription.StLNo == null ? String.Empty : gxDescription.StLNo.gValue;
			}

			if (!String.IsNullOrEmpty(stlno))
			{
				item.SetValue<string>(e => e.Stlno, item.Stlno, stlno);
			}
		}

		/// <summary>
		/// Updates BriefInfo, Specification and TextComplements
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxDescription"></param>
		/// <param name="overwriteExistingItemsKeepBidderData"></param>
		private void SetDescription_2(BoqItemEntity item, GX_Description gxDescription, bool overwriteExistingItemsKeepBidderData = false)
		{

			bool newItem = item.Version == 0;

			if (gxDescription != null)
			{
				SetBriefInfo_2(item, gxDescription.gTextOutlineText);

				SetStlNo(item, gxDescription);

				var gxCompleteText = gxDescription.CompleteText;
				if (gxCompleteText != null)
				{
					var gxBoQText = gxCompleteText.DetailTxt;

					if (gxBoQText != null)
					{
						string plainSpec = "";
						try
						{
							 plainSpec = System.Net.WebUtility.HtmlEncode(gxBoQText.gText);
						}
						catch (Exception)
						{

						}
						
						string xmlSpec = gxBoQText.gXml;
						if (ContainsTextComplements_2(gxCompleteText))
						{
							foreach (var gaebChildObject in gxBoQText.gNodes)
							{
								if (gaebChildObject is GX_TextComplement)
								{
									var gxTextComplement = gaebChildObject as GX_TextComplement;
									_tcsTotal++;

									//if (IsBidderFile && gxTextComplement.att_Kind.Equals("Owner"))
									//{
									//	return; // ignore changes made by bidder
									//}

									int tcNr = System.Convert.ToInt32(gxTextComplement.att_MarkLbl);
									BoqTextComplementEntity tcItem = item.BoqTextComplementEntities.FirstOrDefault(e => e.Sorting == tcNr);
									if (tcItem == null)
									{
										if (newItem)	// adding tc's only supported for new boq items! 
										{
											tcItem = new BoqTextComplementEntity { Sorting = tcNr, Version = 0, BoqHeaderFk = item.BoqHeaderFk, BoqItemFk = item.Id };
											item.BoqTextComplementEntities.Add(tcItem);
											_tcsAdded++;
										}
										else
										{
											_tcsSkipped++;
											return;
										}
									}

									//If overwriteExistingItemsKeepBidderData is true, then we don't overwrite the text complement
									if (overwriteExistingItemsKeepBidderData == false || newItem)
									{
										if (gxTextComplement.att_Kind.Equals("Owner"))
										{
											tcItem.SetValue(e => e.ComplType, tcItem.ComplType, (int)BoqConstants.EBoqTextComplementType.Owner);
										}
										else
										{
											tcItem.SetValue(e => e.ComplType, tcItem.ComplType, (int)BoqConstants.EBoqTextComplementType.Bidder);
										}

										GX_MLText gxComplBody = gxTextComplement.ComplBody;
										if (gxComplBody != null)
										{
											tcItem.SetValue(e => e.ComplBody, tcItem.ComplBody, BoqGaebImportLogic.TruncateString4000(gxComplBody.gText.Trim('\''), ref _truncatedCounter));      // remove single-quotes
										}

										if (tcItem.Version == 0)   // Existing Caption + Tail will not be updated!
										{
											GX_MLText gxComplCaption = gxTextComplement.ComplCaption;
											if (gxComplCaption != null)
											{
												tcItem.SetValue(e => e.ComplCaption, tcItem.ComplCaption, BoqGaebImportLogic.TruncateString4000(gxComplCaption.gText, ref _truncatedCounter));
											}

											GX_MLText gyComplTail = gxTextComplement.ComplTail;
											if (gyComplTail != null)
											{
												tcItem.SetValue(e => e.ComplTail, tcItem.ComplTail, BoqGaebImportLogic.TruncateString4000(gyComplTail.gText, ref _truncatedCounter));
											}
										}

										// remove TA/Bxx prefix added by GAEB dll only on plain text
										plainSpec = plainSpec.Replace(String.Format("[T{0}{1:00}{2}[",
											tcItem.ComplType == (int)BoqConstants.EBoqTextComplementType.Owner ? "A" : "B", tcItem.Sorting, tcItem.ComplCaption), "[" + tcItem.ComplCaption + "[");

									}

								}
							}

							// format and paste bidder text complements to spec
							xmlSpec = new BoqTextComplementLogic().AdjustBlobBody2Table(xmlSpec, item.BoqTextComplementEntities.ToList());

						}

						SetSpecification(item, plainSpec, xmlSpec);

					}
				}

				// WICNo
				if (!gxDescription.WICNo_isEmpty() && !IsBidderFile)
				{
					SetWicNo(item, gxDescription.WICNo);
				}
			}
		}

		private void SetDivision_2(BoqItemEntity item, GX_BoQCtgy gxBoqCtgy, BoqItemEntity parentItem, BoqItemEntity prevItem)
		{

			bool isNewitem = item.Version == 0;
			if (isNewitem)
			{
				int newLevel = parentItem.BoqItemParent == null ? 1 : parentItem.BoqLineTypeFk + 1;
				if (newLevel > (int)BoqConstants.EBoqLineType.DivisionLevelLast)
				{
					throw new Exception(String.Format("Division {0} will exceed maximum level ({1})!", item.Reference, (int)BoqConstants.EBoqLineType.DivisionLevelLast));
				}
				item.BoqLineTypeFk = newLevel;
				//Ref.-no.
				SetItemReferenceNo(item, parentItem, gxBoqCtgy.att_RNoPart);
				//Ref.-no2.
				SetItemReferenceNo2_2(item, gxBoqCtgy);
			}

			// Outline Spec
			if (gxBoqCtgy.LblTx != null)
			{
				SetBriefInfo_2(item, gxBoqCtgy.LblTx.gValueInTree);
			}

			// division flags
			if (!gxBoqCtgy.NotApplBoQ_isEmpty() && gxBoqCtgy.NotApplBoQ.Equals("Yes"))
			{
				item.IsNotApplicable = true;
			}

			SetBasItemType2Fk_2(item, gxBoqCtgy.ALNBSerNo_isEmpty(), System.Convert.ToUInt32(gxBoqCtgy.ALNBSerNo), System.Convert.ToUInt32(gxBoqCtgy.ALNBGroupNo), gxBoqCtgy.Accepted_isEmpty(), gxBoqCtgy.Accepted);

			//DivisionType (not GAEB)
			foreach (var gaebObject in gxBoqCtgy.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtDivisionType))
				{
					var divisionTypeId = GetDivisionTypeFk(gaebObject.gValue);
					if (divisionTypeId >= 0)
						item.BoqDivisionTypeFk = divisionTypeId;

					break;
				}
			}
		}

		private void SetItem_2(BoqItemEntity item, GX_Item gxItem, BoqItemEntity parentItem, BoqItemEntity prevItem, ref List<ImportWarning> warnings, bool overwriteExistingItemsKeepBidderData)
		{

			bool isNewItem = item.Id == 0;
			if (isNewItem)
			{

				if (gxItem.QU!=null && gxItem.QU.ToUpper().Equals("TEXT")) // Bosch 
				{
					item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Note;
				}
				else
				{
					item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Position;
				}
				SetItemReferenceNo(item, parentItem, gxItem.att_RNoPart, gxItem.att_RNoIndex);
				SetItemReferenceNo2_2(item, gxItem);
			}

			SetDescription_2(item, gxItem.Description, overwriteExistingItemsKeepBidderData);

			// quantity WQ
			G_Object gxSubItem = null;
			if (isNewItem || item.IsFreeQuantity || GaebTypeOverwritesQty)
			{
				SetValueIfNotEmpty(item, gxItem.Qty_isEmpty(), e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxItem.Qty));

				gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtQtyDetail)).FirstOrDefault();
				if (gxSubItem != null)
				{
					item.QuantityDetail = gxSubItem.gValue;
				}

				gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtFactorDetail)).FirstOrDefault();
				if (gxSubItem != null)
				{
					item.FactorDetail = gxSubItem.gValue;
				}

				gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtFactor)).FirstOrDefault();
				if (gxSubItem != null)
                {
                    var numberDecimalSep = TransLogic.Context.CultureInfo.NumberFormat.NumberDecimalSeparator;
                    var tempValue = numberDecimalSep == "," ? gxSubItem.gValue.Replace(".", numberDecimalSep) : gxSubItem.gValue.Replace(",", numberDecimalSep);
                    item.Factor = Decimal.Parse(tempValue);   
                }
			}

			// quantity AQ
			SetValueIfNotEmpty(item, gxItem.PredQty_isEmpty(), e => e.QuantityAdj, item.QuantityAdj, System.Convert.ToDecimal(gxItem.PredQty));

			SetQuantityAdjDetail(gxItem, item);

			// hours
			SetValueIfNotEmpty(item, gxItem.TimeQu_isEmpty(), e => e.HoursUnit, item.HoursUnit, System.Convert.ToDecimal(gxItem.TimeQu));

			bool isUom4Lumpsum = IsLumpsumUnit(gxItem.QU);
			if (isUom4Lumpsum || IsLumpsumItem(gxItem))	//unit of the imported item is marked as lumpsum or imported item is marked as lumpsum
			{
				// treat imported item as lumpsum (even if it's not marked as lumpsum item!)
				item.SetValue(e => e.IsLumpsum, item.IsLumpsum, true);
				if (IsLumpsumItem(gxItem))	// take IT as price
				{
					SetPriceItIfNotEmpty(item, gxItem);
					if (System.Convert.ToDecimal(gxItem.Qty) != 1)
					{
						_invalideLumpsums++;
					}
					item.SetValue(e => e.Quantity, item.Quantity, 1.0M);	// set qty to 1 (instructed by savic Aug. 6, 2019)
				}
				else // take UP as price
				{
					SetPriceIfNotEmpty(item, gxItem);
					if (System.Convert.ToDecimal(gxItem.Qty) != 1)
					{
						_invalideLumpsums++;
					}
					item.SetValue(e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxItem.Qty));
				}
			}
			else
			{
				SetPriceIfNotEmpty(item, gxItem);
			}

			// discount %
			SetValueIfNotEmpty(item, gxItem.DiscountPcnt_isEmpty(), e => e.DiscountPercent, item.DiscountPercent, System.Convert.ToDecimal(gxItem.DiscountPcnt));

			// handling of "Nebenangebot"
			if (!gxItem.AlterBidStatus_isEmpty())
			{
				switch (gxItem.AlterBidStatus.ToLower())
				{
					case "new":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.NewlyOffered);
						break;

					case "modified":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.ModifiedOffered);
						break;

					case "identical":

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.IdenticalOffered);
						break;

					default:

						item.SetValue<int>(e => e.BasItemType85Fk, item.BasItemType85Fk, (int)BoqConstants.GaebType85.NotRequired);
						break;
				}
			}

			// ur breakdown
			SetValueIfNotEmpty(item, gxItem.UPBkdn_isEmpty(), e => e.IsUrb, item.IsUrb, true);
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp1_isEmpty(), e => e.Urb1, item.Urb1, System.Convert.ToDecimal(gxItem.UPComp1));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp2_isEmpty(), e => e.Urb2, item.Urb2, System.Convert.ToDecimal(gxItem.UPComp2));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp3_isEmpty(), e => e.Urb3, item.Urb3, System.Convert.ToDecimal(gxItem.UPComp3));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp4_isEmpty(), e => e.Urb4, item.Urb4, System.Convert.ToDecimal(gxItem.UPComp4));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp5_isEmpty(), e => e.Urb5, item.Urb5, System.Convert.ToDecimal(gxItem.UPComp5));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPComp6_isEmpty(), e => e.Urb6, item.Urb6, System.Convert.ToDecimal(gxItem.UPComp6));

			// up from/to
            SetMonetaryValueIfNotEmpty(item, gxItem.UPFrom_isEmpty(), e => e.UnitRateFrom, item.UnitRateFrom, System.Convert.ToDecimal(gxItem.UPFrom));
            SetMonetaryValueIfNotEmpty(item, gxItem.UPTo_isEmpty(), e => e.UnitRateTo, item.UnitRateTo, System.Convert.ToDecimal(gxItem.UPTo));

			// Bidder comment
			SetCommentContractor(item, gxItem);
			SetCommentClient(item, gxItem);

			// item flags
			if (!gxItem.NotAppl_isEmpty())
			{
				item.SetValue(e => e.IsNotApplicable, item.IsNotApplicable, gxItem.NotAppl.Equals("Yes") ? true : false);
			}
			if (!gxItem.KeyIt_isEmpty())
			{
				item.SetValue(e => e.IsKeyitem, item.IsKeyitem, gxItem.KeyIt.Equals("Yes") ? true : false);
			}
			if (!gxItem.SumDescr_isEmpty())
			{
				item.SetValue(e => e.IsLeadDescription, item.IsLeadDescription, gxItem.SumDescr.Equals("Yes") ? true : false);
			}
			if (!gxItem.QtyTBD_isEmpty())
			{
				item.SetValue(e => e.IsFreeQuantity, item.IsFreeQuantity, gxItem.QtyTBD.Equals("Yes") ? true : false);
			}
			if (!gxItem.MarkupIt_isEmpty())
			{
				item.SetValue(e => e.IsSurcharged, item.IsSurcharged, gxItem.MarkupIt.Equals("Yes") ? true : false);
			}
			if (!gxItem.HourIt_isEmpty() && gxItem.HourIt.Equals("Yes"))
			{
				item.SetValue(e => e.IsDaywork, item.IsDaywork, gxItem.HourIt.Equals("Yes") ? true : false);
			}

			SetBasItemTypeFk(item, gxItem.Provis);
			SetPriceRequest(item, gxItem);
			SetBasItemType2Fk_2(item, gxItem.ALNSerNo_isEmpty(), System.Convert.ToUInt32(gxItem.ALNSerNo), System.Convert.ToUInt32(gxItem.ALNGroupNo), gxItem.Accepted_isEmpty(), gxItem.Accepted);

			// uom
			SetUoM(item, gxItem.QU, ref warnings);

			//Bezug-/Wiederholungsbeschreibung ??
			//public bool RefDescr_isEmpty(); public string RefDescr { get; set; }   <RefDescr>Ref</RefDescr> <RefDescr>Rep</RefDescr>

			// ref to design description / item
			SetReferenceDd(item, gxItem.RefPerfNo, gxItem.RefRNo);

			//BoqItemFlag (not GAEB)
			foreach (var gaebObject in gxItem.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqItemFlag))
				{
					var boqItemFlagId = GetBoqItemFlagFk(gaebObject.gValue);
					if (boqItemFlagId >= 0)
						item.BoqItemFlagFk = boqItemFlagId;

					break;
				}
			}

			//Included (not GAEB)
			var gInclude = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebIncluded));
			if (gInclude != null)
			{
				item.Included = gInclude.gValue == "Yes" ? true : false;
			}

			//NotSubmitted (not GAEB)
			var gNotSubmitted = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebNotSubmitted));
			if (gNotSubmitted != null && item.Price == 0)
			{
				item.NotSubmitted = gNotSubmitted.gValue == "Yes" ? true : false;
			}

			SetBoqBudget(gxItem.gNodes, item);

			//ItemStatus (not GAEB)
			var gItemStatus = gxItem.gNodes.ToList().FirstOrDefault(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtItemStatus));
			if (gItemStatus != null)
			{
				item.BasItemStatusFk = GetItemStatusFk(gItemStatus.gValue);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="gxItem"></param>
		/// <param name="item"></param>
		protected void SetQuantityAdjDetail(GX_Item gxItem, BoqItemEntity item)
		{
			var gxSubItem = gxItem.gNodes.ToList().Where(e => !string.IsNullOrEmpty(e.Schema) && e.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(e.gName) && e.gName.Equals(BoqConstants.RibGaebExtAQQtyDetail)).FirstOrDefault();
			if (gxSubItem != null)
			{
				item.QuantityAdjDetail = gxSubItem.gValue;
				decimal qty;
				if (item.QuantityAdj == 0 && Decimal.TryParse(item.QuantityAdjDetail, out qty))
				{
					item.QuantityAdj = qty;
				}
			}
		}

		// BidComm => CommentContractor
		private void SetCommentContractor(BoqItemEntity item, GX_Item gxItem)
        {
			if (gxItem.BidComm != null)
			{
				item.SetValue(e => e.CommentContractor, item.CommentContractor, gxItem.BidComm.gText);
			}
        }

		// OwnComm => CommentClient
		private void SetCommentClient(BoqItemEntity item, GX_Item gxItem)
		{
			foreach (var gaebObject in gxItem.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibClientComment))
				{
					// value could be surrounded by span tags
					// item.SetValue(e => e.CommentContractor, item.CommentContractor, gaebObject.gValue);
					if (gaebObject.gNodes == null || gaebObject.gNodes.Count == 0)
					{
						item.SetValue(e => e.CommentClient, item.CommentClient, gaebObject.gValue);
					}
					else
					{
						item.SetValue(e => e.CommentClient, item.CommentClient, gaebObject.gNodes[0].gValue);
					}
					break;
				}
			}
		}

		// done in CreateBoqItem
		//private void SetItemBasics_2(BoqItemEntity item, BoqItemEntity parent, BoqItemEntity prevItem, int lineTypeFk)
		//{
		//	item.BoqItemBasisParent = prevItem;
		//	item.BoqItemParent = parent;
		//	if (item.BoqItemParent != null)
		//	{
		//		item.BoqItemParent.AddChild(item);
		//	}
		//	item.BoqHeaderFk = parent.BoqHeaderFk;
		//	item.BasItemTypeFk = 0;
		//	item.BoqLineTypeFk = lineTypeFk;
		//}

		private void SetSubDescription_2(BoqItemEntity item, GX_SubDescr gxSubDescr, BoqItemEntity parent, BoqItemEntity prevItem)
		{

			bool isNewitem = item.Version == 0;
			if (!isNewitem)
			{
				throw new NotSupportedException("SetSubDescription");
			}

			item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.SubDescription;
			SetDescription_2(item, gxSubDescr.Description);

			item.Reference = String.Empty;

			if (!gxSubDescr.SubDNo_isEmpty())
			{
				item.DesignDescriptionNo = StringExtension.Truncate(gxSubDescr.SubDNo.ToString(CultureInfo.InvariantCulture), 4);
			}

			//UserDefined1-5 (not GAEB)
			if (parent.IsLeadDescription)
			{
				SetUserDefinedInfos(gxSubDescr, item);
			}
		}

		private void SetDesignDescription_2(BoqItemEntity item, GX_PerfDescr gxPerfDescr, BoqItemEntity parent, BoqItemEntity prevItem)
		{

			bool isNewitem = item.Version == 0;
			//if (!isNewitem)
			//{
			//	throw new NotSupportedException("SetSubDescription");
			//}

			item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.DesignDescription;
			SetDescription_2(item, gxPerfDescr.Description);
			item.Reference = String.Empty;

			if (!gxPerfDescr.PerfNo_isEmpty())
			{
				item.DesignDescriptionNo = StringExtension.Truncate(gxPerfDescr.PerfNo.ToString(CultureInfo.InvariantCulture), 4);
			}
		}


		private void SetTextElement_2(BoqItemEntity item, GX_AddText gxAddText, BoqItemEntity parent, BoqItemEntity prevItem)
		{

			bool isNewitem = item.Version == 0;
			if (!isNewitem)
			{
				throw new NotSupportedException("SetTextElement");
			}

			item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.TextElement;
			item.Reference = String.Empty;

			GX_MLText gaebOutlineAddText = gxAddText.OutlineAddText;
			if (gaebOutlineAddText != null)
			{
				SetBriefInfo_2(item, gaebOutlineAddText.gText);
			}

			GX_FText gaebDetailAddText = gxAddText.DetailAddText;
			if (gaebDetailAddText != null)
			{
				SetSpecification(item, System.Net.WebUtility.HtmlEncode(gxAddText.gTextCompleteText), gaebDetailAddText.gXml);
			}

		}

		private void SetNote_2(BoqItemEntity item, GX_Remark gxRemark, BoqItemEntity parent, BoqItemEntity prevItem, string gaebId)
		{
			item.BoqLineTypeFk = (int)BoqConstants.EBoqLineType.Note;
			item.Reference = String.Empty;
			item.GaebId = gaebId;
			item.BoqItemBasisParent = prevItem;
			item.BasItemTypeFk = (int)BoqConstants.EBoqItemType1.Empty;
			item.BasItemType2Fk = null;
			SetDescription_2(item, gxRemark.Description);
			SetUserDefinedInfos(gxRemark, item);
		}
	
		private void SetItemWorkContent_2(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqWorkContent))
				{

					BoqCharacterContentEntity entityContent = characterLogic.Create();
					entityContent.Content = gaebObject.gValue;
					item.BoqCharacterContentWorkFk = entityContent.Id;
					characterLogic.Update(entityContent);
					break;
				}
			}
		}

		private void SetItemPrjCharacter_2(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqPrjCharacter))
				{
					BoqCharacterContentEntity entityPrjCharater = characterLogic.Create();
					entityPrjCharater.Content = gaebObject.gValue;
					item.BoqCharacterContentPrjFk = entityPrjCharater.Id;
					characterLogic.Update(entityPrjCharater);
					break;
				}
			}
		}

		private void SetReferenceDd(BoqItemEntity item, GX_RefItem gxRefPerf, GX_RefItem gxRefR)
		{
			var gxRefItem = gxRefPerf ?? gxRefR;

			if (gxRefItem != null)
			{
				string justifiedRefNr = BoqImExportHelper.JustifyReference(gxRefItem.att_IDRef);
				if (_itemRefMap.ContainsKey(justifiedRefNr)) //this kind of reference is only valid to items above the current item, so the referenced item already exist and it is in map
					item.BoqItemReferenceParent = _itemRefMap[justifiedRefNr];
				//else
				//{
				//	//todo error
				//}
			}
		}

		static private void SetBasItemType2Fk_2(BoqItemEntity item, bool isNormal, uint serNo, uint groupNo, bool isAcceptedEmpty, string accepted)
		{
			if (!isNormal)
			{
				if (isAcceptedEmpty)
				{
					if (serNo == 0)
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Base;
					}
					else
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.Alternative;
					}
				}
				else
				{
					if ("AltAccept".Equals(accepted))
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.AlternativeAwarded;
					}
					else // "BasReject"
					{
						item.BasItemType2Fk = (int)BoqConstants.EBoqItemType2.BasePostponed;
					}
				}

				item.AGN = groupNo<=999 ? groupNo.ToString() : "100";	// the GAEB Toolbox converts invalid values in gaeb files to 2147483647???

				if (serNo < 9999)
					item.AAN = System.Convert.ToInt16(serNo).ToString();
				else
					item.AAN = "1";
			}
		}

		/// <summary>
		/// Set Price Request
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gxItem"></param>
		private void SetPriceRequest(BoqItemEntity item, GX_Item gxItem)
		{
			foreach (var node in gxItem.gNodes)
			{
				if (node.gName.Equals(_PriceRequest))
				{
					if (node.gValue.Equals("Yes"))
					{
						item.BasItemTypeFk = (int)BoqConstants.EBoqItemType1.PriceRequest;
						break;
					}
				}
			}
		}



		private void SetSplitQuantities_2(BoqItemEntity item, BoqQtySplitData splitData, GX_Item gaebObject)
		{

			bool isNewitem = item.Version == 0;
			bool isLocationUnique = true;
			int prjLocationId = -1;
			List<int> ids = new List<int>();

			if (splitData != null && splitData.QtySplits != null && splitData.QtySplits.Count > 1)
			{
				foreach (var splitEntity in splitData.QtySplits)
				{
					BoqSplitQuantityEntity entity = new BoqSplitQuantityEntity()
					{
						BoqHeaderFk = item.BoqHeaderFk,
						BoqItemFk = item.Id,
						Quantity = System.Convert.ToDecimal(splitEntity.Qty),
						QuantityAdj = System.Convert.ToDecimal(splitEntity.PredQty)
					};

					if (ImportOptions != null && ImportOptions.Catalogs != null)
					{
						for (int i = 0; i < splitEntity.CtlgIDs.Count; i++)
						{
							// get catalog mappings
							GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == splitEntity.CtlgIDs[i]);
							if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
							{
								if (catalogInfo.IsCostGroupCatalog)
								{
									int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
									entity.SetCostGroup(_boqSplitQty2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, splitEntity.CtlgCodes[i], (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
									catalogInfo.BasCostgroupCatFk = costgroupCatFk;
								}
								else
								{
									BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
									int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, splitEntity.CtlgCodes[i]);
									if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
									{
										referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, splitEntity.CtlgCodes[i]);
									}
									entity.SetPropertyValue(boqCatalogEntity.AssignedCol, referenceId);
								}

								// #97701 no assignment for boq item
								// set catalog references also to boq item
								//if (i == 0)
								//{
								//	item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
								//}

							}
						}
					}

					if (IsQtySplits)
					{
						// set locations in qty splits
						for (int i = 0; i < splitEntity.CtlgIDs.Count; i++)
						{
							if (_ctlgIdandTypeDic != null && _ctlgIdandTypeDic.ContainsKey(splitEntity.CtlgIDs[i]))
							{
								string ctlgType = _ctlgIdandTypeDic[splitEntity.CtlgIDs[i]].ToString();
								foreach (var ctlgKey in _ctlgTypeandKey.Keys)
								{
									if (ctlgKey.Contains(ctlgType))
									{
										foreach (KeyValuePair<int, string> ctlgIdandCode in _ctlgTypeandKey[ctlgType])
										{
											if (splitEntity.CtlgCodes[i] == ctlgIdandCode.Value)
											{
												if (ctlgType == "locality")
												{
													entity.PrjLocationFk = (int?)ctlgIdandCode.Key;
													if (prjLocationId > 0)
													{
														if (prjLocationId != ctlgIdandCode.Key)
															isLocationUnique = false;
													}
													else
														prjLocationId = ctlgIdandCode.Key;

													ids.Add(ctlgIdandCode.Key);
												}
												break;
											}
										}
									}
								}
							}
						}
					}

					if (isNewitem)	// Merging of split Quantities not yet supported! 
					{
						item.BoqSplitQuantityEntities.Add(entity);
						_sqsAdded++;
					}
					else
					{
						_sqsSkipped++;
					}

					_sqsTotal++;
				}

				// if the location are same, set as boq item locaiton
				if ((ids.Count > 1 && isLocationUnique && prjLocationId > 0) || (splitData.QtySplits != null && splitData.QtySplits.Count == 1 && prjLocationId > 0))
				{
					item.PrjLocationFk = (int?)prjLocationId;
				}
			}

			else
			{
				// item has no split quantities but the user has assigned catalog(s). 
				if (ImportOptions != null && ImportOptions.HasAssignedCatalog)
				{
					Dictionary<string, string> itemCatalogs = GetItemDataCtlgAssign(gaebObject);
					if (itemCatalogs.Count > 0)
					{
						foreach (var catalog in itemCatalogs)
						{
							// get catalog mappings
							GaebCatalogInfo catalogInfo = ImportOptions.Catalogs.FirstOrDefault(e => e.CtlgID == catalog.Key);
							if (catalogInfo != null && catalogInfo.CatalogAssignmentMode != BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore)
							{
								if (catalogInfo.IsCostGroupCatalog)
								{
									int? costgroupCatFk = catalogInfo.BasCostgroupCatFk;
									item.SetCostGroup(_boqItem2CostGroupLogic, ref costgroupCatFk, catalogInfo.CtlgID, catalog.Value, (int)catalogInfo.CatalogAssignmentMode, catalogInfo.IsNewCatalog, catalogInfo.CostGroupCatalogCode, catalogInfo.CostGroupCatalogDescription);
									catalogInfo.BasCostgroupCatFk = costgroupCatFk;
								}
								else
								{
									BoqCatalogEntity boqCatalogEntity = _catalogLogic.Value.GetById((int)catalogInfo.BoqCatalogFk);
									int? referenceId = _catalogLogic.Value.GetReferenceIdByCode(boqCatalogEntity.Id, catalog.Value);
									if (!referenceId.HasValue && catalogInfo.CatalogAssignmentMode == BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Add)
									{
										referenceId = _catalogLogic.Value.AddReference(boqCatalogEntity.Id, catalog.Value);
									}
									item.SetValue(boqCatalogEntity.AssignedCol, referenceId);
								}
							}
						}
					}
				}

			}
		}

		/// <summary>
		/// #98709
		/// </summary>
		/// <param name="item"></param>
		/// <param name="gaebObject"></param>
		private void SetPrcStructureAssignment(BoqItemEntity item, GX_CtlgAssign gaebObject)
		{
			var found = _ImportOptions.PrcStructureAssignments.Where(e => e.CtlgID == gaebObject.CtlgID && e.CtlgCode == gaebObject.CtlgCode).FirstOrDefault();
			if (found != null)
			{
				//item.PrcStructureFk = BoqPrcStructureLookup.GetIdByCode(found.PropItemKey);
				item.SetValue(e => e.PrcStructureFk, item.PrcStructureFk, BoqPrcStructureLookup.GetIdByCode(found.PropItemKey));
			}
		}

		private Dictionary<string, string> GetItemDataCtlgAssign(G_Object gaebObject, Dictionary<string, string> result = null)
		{
			// Dictionary<string, string> result = new Dictionary<string, string>();
			if (result == null)
			{
				result = new Dictionary<string, string>();
			}

			foreach (var node in gaebObject.gNodes)
			{
				if (node is GX_CtlgAssign)
				{
					var ctlg = node as GX_CtlgAssign;
					if (!result.ContainsKey(ctlg.CtlgID))
					{
						result.Add(ctlg.CtlgID, ctlg.CtlgCode);
					}
				}
				else if (node is GX_QtySplit)
				{
					GetItemDataCtlgAssign(node, result);
				}
			}
			return result;
		}

		private void SetSurchargeItem_2(BoqItemEntity item, GX_MarkupItem gxMarkupItem, BoqItemEntity parent)
		{

			int surchargeType;
			if ("IdentAsMark".Equals(gxMarkupItem.MarkupType))
			{
				surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType1;
			}
			else if ("AllInCat".Equals(gxMarkupItem.MarkupType))
			{
				surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType2;
			}
			else if ("ListInSubQty".Equals(gxMarkupItem.MarkupType))
			{
				surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType3;
			}
			else //error
			{
				surchargeType = (int)BoqConstants.EBoqLineType.SurchargeItemType1;
			}
			item.SetValue(e => e.BoqLineTypeFk, item.BoqLineTypeFk, surchargeType);

			// Text, Parent, Item-Type
			// SetItemBasics(item, gxMarkupItem.Description(), parent, null, surchargeType);
			SetDescription_2(item, gxMarkupItem.Description);

			//Ref.-no.
			SetItemReferenceNo(item, parent, gxMarkupItem.att_RNoPart, gxMarkupItem.att_RNoIndex);
			//Ref.-no2.
			SetItemReferenceNo2_2(item, gxMarkupItem);

			// markup %
            SetMonetaryValueIfNotEmpty(item, gxMarkupItem.Markup_isEmpty(), e => e.Price, item.Price, System.Convert.ToDecimal(gxMarkupItem.Markup));
			// total
            SetMonetaryValueIfNotEmpty(item, gxMarkupItem.IT_isEmpty(), e => e.Finalprice, item.Finalprice, System.Convert.ToDecimal(gxMarkupItem.IT));
			// ITMarkup sum of to be surcharged
			SetValueIfNotEmpty(item, gxMarkupItem.ITMarkup_isEmpty(), e => e.Quantity, item.Quantity, System.Convert.ToDecimal(gxMarkupItem.ITMarkup));
			// discount %
			SetValueIfNotEmpty(item, gxMarkupItem.DiscountPcnt_isEmpty(), e => e.DiscountPercent, item.DiscountPercent, System.Convert.ToDecimal(gxMarkupItem.DiscountPcnt));

			// item flags
			if (!gxMarkupItem.NotAppl_isEmpty() && gxMarkupItem.NotAppl.Equals("Yes"))
			{
				item.SetValue(e => e.IsNotApplicable, item.IsNotApplicable, true);
			}

			if (!gxMarkupItem.KeyIt_isEmpty() && gxMarkupItem.KeyIt.Equals("Yes"))
			{
				item.SetValue(e => e.IsKeyitem, item.IsKeyitem, true);
			}

			if (!gxMarkupItem.HourIt_isEmpty() && gxMarkupItem.HourIt.Equals("Yes"))
			{
				item.SetValue(e => e.IsDaywork, item.IsDaywork, true);
			}

			SetBasItemTypeFk(item, gxMarkupItem.Provis);
			SetBasItemType2Fk_2(item, gxMarkupItem.ALNSerNo_isEmpty(), System.Convert.ToUInt32(gxMarkupItem.ALNSerNo), System.Convert.ToUInt32(gxMarkupItem.ALNGroupNo), gxMarkupItem.Accepted_isEmpty(), gxMarkupItem.Accepted);

			// ref to design description / item
			//SetReferenceDd(item, gxMarkupItem);
			SetReferenceDd(item, gxMarkupItem.RefPerfNo, gxMarkupItem.RefRNo);

		}

		/// <summary>
		/// Set item reference
		/// </summary>
		///  <param name="item"></param>
		/// <param name="parent"></param>
		/// <param name="referencePart"></param>
		/// <param name="index"></param>
		private void SetItemReferenceNo(BoqItemEntity item, BoqItemEntity parent, String referencePart, String index = "")
		{
			if (_isFreeBoq)
			{
				item.Reference = referencePart;
			}
			else
			{
				var level = 0;
				var parentForIndex = parent;
				while (parentForIndex != null && !parentForIndex.IsWicRoot)
				{
					parentForIndex = parentForIndex.BoqItemParent;
					level++;
				}

				var parentReferencePart = String.Empty;
				if (item.BoqItemParent != null && !item.BoqItemParent.IsWicRoot)
					parentReferencePart = item.BoqItemParent.Reference;

				item.Reference = BuildReferenceNo(parentReferencePart, referencePart, index, level, item.BoqLineTypeFk);
			}

			if (!String.IsNullOrEmpty(item.Reference) && item.Reference.Length > 42)
				item.Reference = item.Reference.Substring(0, 42);
		}

		//private void SetItemReferenceNo_2(BoqItemEntity item, BoqItemEntity parent, String referencePart, String index = "")
		//{
		//	if (_isFreeBoq)
		//	{
		//		item.Reference = referencePart;
		//	}
		//	else
		//	{
		//		var level = 0;
		//		var parentForIndex = parent;
		//		while (parentForIndex != null && !parentForIndex.IsWicRoot)
		//		{
		//			parentForIndex = parentForIndex.BoqItemParent;
		//			level++;
		//		}

		//		var parentReferencePart = String.Empty;
		//		if (item.BoqItemParent != null && !item.BoqItemParent.IsWicRoot)
		//		{
		//			parentReferencePart = item.BoqItemParent.Reference;
		//		}

		//		// magic AI!
		//		if (item.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position && _index4Item.HasValue)
		//		{
		//			level = _index4Item.Value;
		//		}

		//		item.Reference = BuildReferenceNo(parentReferencePart, referencePart, index, level);
		//	}

		//	if (!String.IsNullOrEmpty(item.Reference) && item.Reference.Length > 42)
		//	{
		//		item.Reference = item.Reference.Substring(0, 42);
		//	}
		//}

		private void SetItemReferenceNo2_2(BoqItemEntity item, G_Object gObject)
		{
			//DivisionType (not GAEB)
			foreach (var gaebObject in gObject.gNodes)
			{
				if (!string.IsNullOrEmpty(gaebObject.Schema) && gaebObject.Schema.Equals(BoqConstants.RibGaebExtSchema) && !string.IsNullOrEmpty(gaebObject.gName) && gaebObject.gName.Equals(BoqConstants.RibGaebExtBoqRef2))
				{
					item.Reference2 = gaebObject.gValue;
					break;
				}
			}
		}

		#endregion

		#region build reference no

		private string BuildReferenceNo(string parentReferencePart, string itemPartNo, string index, int level, int? BoqLineTypeFk = null)
		{
			//return BuildReferenceNo(parentReferencePart, itemPartNo, index, GaebBoqStructureDetails.ElementAt(level), _isFreeBoq);

			// deal with skipped levels
			if (BoqLineTypeFk.HasValue && BoqLineTypeFk.Value == (int)BoqConstants.EBoqLineType.Position && _index4Item.HasValue)
			{
				while (level < _index4Item.Value)	// level seems to be skipped
				{
					parentReferencePart = BuildReferenceNo(parentReferencePart, "", "", level, null);
					level++;
				}
			}

			GX_BoQBkdn boqStructureDetail = _GaebBoqStructureDetails.Count() > level ? _GaebBoqStructureDetails.ElementAt(level) : null;
			return BuildReferenceNo(parentReferencePart, itemPartNo, index, boqStructureDetail, _isFreeBoq);
		}

		/// <summary>
		/// BuildReferenceNo
		/// </summary>
		/// <returns></returns>
		static public string BuildReferenceNo(string parentReferencePart, string itemPartNo, string index, GX_BoQBkdn boqStructureDetail, bool isFreeBoq = false)
		{
			var result = string.Empty;
			string resultPart = string.Empty;

			if (isFreeBoq)
			{
				return itemPartNo;
			}

			if (boqStructureDetail != null)
			{
				if ("Yes".Equals(boqStructureDetail.Num) || "right".Equals(boqStructureDetail.Alignment))
				{
					if (itemPartNo.Length < System.Convert.ToInt32(boqStructureDetail.Length))
						resultPart = new string(' ', System.Convert.ToInt32(boqStructureDetail.Length) - itemPartNo.Length);

					resultPart += itemPartNo;
				}
				else
				{
					resultPart = itemPartNo;
				}

				result = string.IsNullOrEmpty(parentReferencePart) ? String.Format("{0}.", resultPart) : String.Format("{0}{1}.", parentReferencePart, resultPart);

				if (!string.IsNullOrEmpty(index))
					result = String.Format("{0}{1}", result, index);
			}

			return result;
		}

		//private string BuildReferenceNo_2(string parentReferencePart, string itemPartNo, string index, int level)
		//{
		//	GX_BoQBkdn boqStructureDetail = _GaebBoqStructureDetails.Count() > level ? _GaebBoqStructureDetails.ElementAt(level) : null;
		//	return BuildReferenceNo_2(parentReferencePart, itemPartNo, index, boqStructureDetail);
		//}

		//private string BuildReferenceNo_2(string parentReferencePart, string itemPartNo, string index, GX_BoQBkdn boqStructureDetail)
		//{
		//	var result = string.Empty;
		//	string resultPart = string.Empty;

		//	if (_isFreeBoq)
		//	{
		//		return itemPartNo;
		//	}

		//	if (boqStructureDetail != null)
		//	{
		//		if ("Yes".Equals(boqStructureDetail.Num) || "right".Equals(boqStructureDetail.Alignment))
		//		{
		//			if (itemPartNo.Length < System.Convert.ToInt32(boqStructureDetail.Length))
		//			{
		//				resultPart = new string(' ', System.Convert.ToInt32(boqStructureDetail.Length) - itemPartNo.Length);
		//			}
		//			resultPart += itemPartNo;
		//		}
		//		else
		//		{
		//			resultPart = itemPartNo;
		//		}

		//		result = string.IsNullOrEmpty(parentReferencePart) ? String.Format("{0}.", resultPart) : String.Format("{0}{1}.", parentReferencePart, resultPart);

		//		if (!string.IsNullOrEmpty(index))
		//		{
		//			result = String.Format("{0}{1}", result, index);
		//		}
		//	}

		//	return result;
		//}

		private string GetDefiniteReferenceNo_2(string reference)
		{
			var result = reference;

			var refParts = reference.Split('.');
			if (refParts.Any())
			{
				for (var i = 0; i < refParts.Count(); i++)
				{
					if (i < _BoqStructure.BoqStructureDetailEntities.Count())
					{
						var structDetail = _BoqStructure.BoqStructureDetailEntities.ElementAt(i);
						int tmpValuePart;
						if (structDetail != null && structDetail.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric && Int32.TryParse(refParts[i], out tmpValuePart))
						{
							refParts[i] = System.Convert.ToInt32(refParts[i]).ToString(CultureInfo.InvariantCulture);
						}
					}
				}
				result = String.Join(".", refParts);
			}
			return result;
		}

		#endregion

		#region gaeb info

        private GaebInfoData _ImportOptions = null;
        private GaebInfoData ImportOptions
        {
            get
            {
                return _ImportOptions;
            }
        }

		/// <summary>
		/// Parse Gaeb File 
		/// </summary>
		/// <param name="gaebFile"></param>
		/// <param name="gaebFileContainsContent"></param>
		/// <param name="initialData"></param>
		/// <returns></returns>
		public GaebInfoData GetGaebInfo(string gaebFile, bool gaebFileContainsContent = false, GaebInfoData initialData = null)
		{
			List<string> criticalErrorCodes = new List<string>() { "GXML_MESS_NOVALIDCLOSETAG", "GXML_MESS_OBJBOQORITEMLISTWRONG" }; // Elements of enum G_MessageCode from namespace GAEB_Toolbox_33

			if (initialData == null)
			{
				initialData = new GaebInfoData();
			}

			var catalogLogic = new BoqCatalogLogic(initialData.ProjectId);
			GaebXml = SetupGaebToolbox(true);

			if (gaebFileContainsContent)
			{
				GaebXml.gRead(gaebFile.ToByteArray());
			}
			else
			{
				GaebXml.gRead(gaebFile);
			}

			if (GaebXml.Award == null || GaebXml.Award.BoQ == null)
			{
				throw new Exception("The import file does not have a valid structure.");
			}

			initialData.SuppressGaebImportErrorCheck = SystemOptions.SharedInstance.GetValueAsBool(SystemOption.SuppressGaebImportErrorCheck, false);

			//Check for errors
			GaebXml.gCheck("full");
			if (GaebXml.gErrors != null && GaebXml.gErrors.Count > 0)
			{
				XDocument doc = XDocument.Parse(GaebXml.gGetErrorInXml);
				foreach (var msg in doc.Root.Elements())
				{
					var row = msg.Attribute("Row").Value;
					var msgElement = msg.Element("String") != null ? msg.Element("String") : (msg.Element("string") != null ? msg.Element("string") : null);
					if (msgElement != null)
					{
						if (!(msg.Name == "GAEB_ERROR" && msg.Attribute("MessageCode") != null && msg.Attribute("MessageCode").Value == "GXML_MESS_MISSINGELEMENT"
							&& msgElement.Value.Contains("QtySplit") && msgElement.Value.Contains("\"CtlgAssign\""))) // WORKAROUND for a Toolbox bug
						{
							initialData.Errors.Add(String.Format(" Line {0}: {1}", row, msgElement.Value));
						}

						if (msg.Name == "GAEB_ERROR" && msg.Attribute("MessageCode") != null && criticalErrorCodes.Contains(msg.Attribute("MessageCode").Value))
						{
							throw new Exception(msgElement.ToString());
						}

						if (msg.Name == "GAEB_WARNING" && msg.Attribute("MessageCode") != null && msg.Attribute("MessageCode").Value.Equals("GXML_MESS_ELEMENTNOTFOUND"))
						{
							// Check the element name ("XXX") in the msgElement: Das Element "XXX" ist nicht definiert im GAEB DA XML
							var match = Regex.Match(msgElement.Value, "\"(.*?)\"");
							if (!(match.Success && IsValidXmlName(match.Value.Replace("\"", ""))))
							{
								throw new Exception(msgElement.ToString());
							}
						}
					}
				}
			}

			var gaebBoq = GaebXml.Award.BoQ;
			var gaebInfo = gaebBoq.BoQInfo;

			initialData.Date = gaebInfo.Date;
			initialData.LblBoQ = gaebInfo.LblBoQ;
			initialData.Name = gaebInfo.Name;

			// does not find divisons?
			//int divisonCounter = 0;
			//var divisons = gaebInfo.Nodes.Where(node => node is GX_BoQCtgy).Cast<GX_BoQCtgy>();
			//if (divisons != null)
			//{
			//	foreach (var divison in divisons)
			//	{
			//		divisonCounter++;		
			//	}
			//}

			// Einheitspreisanteile
			var upComps = gaebInfo.gNodes.Where(node => node is GX_LblUpComp).Cast<GX_LblUpComp>();
			if (upComps != null)
			{
				foreach (var upComp in upComps)
				{
					initialData.UpComps.Add(new GaebUpCompInfo() { UpName = upComp.gValue });
				}
			}

			var catalogs = gaebInfo.gNodes.Where(node => node is GX_Ctlg).Cast<GX_Ctlg>();
			if (catalogs != null)
			{
				//check if has any boq catalog assign default config
				var boqheaderId = initialData.BoqHeaderId;
				IEnumerable<BoqCatAssignDetailEntity> assignDetails = null;
				if (boqheaderId > 0)
				{
					assignDetails = new BoqTypeLogic().GetBoqCatAssignDetailsByHeaderId(boqheaderId);
				}

				CostGroupCatLogic costGroupCatLogic = new CostGroupCatLogic();

				bool hasProject = initialData.ProjectId > 0;
				var knownCatalogs = catalogLogic.GetList(!hasProject).Where(e => e.Islive == true).OrderBy(e => e.Sorting).ToList();
				//int group = 0;
				int z = 0;
				foreach (var catalog in catalogs)
				{
					var gaebCatalogInfo = new GaebCatalogInfo();
					gaebCatalogInfo.CtlgID = (catalog as GX_Ctlg).CtlgID;
					gaebCatalogInfo.CtlgName = (catalog as GX_Ctlg).CtlgName;//->assign detail:description
					gaebCatalogInfo.CtlgType = (catalog as GX_Ctlg).CtlgType;//->assign detail:gaeb_id

					BoqCatAssignDetailEntity mappingDetail = null;
					// try to find a suitable assignment in the doc properties
					if (assignDetails != null)		// && !String.IsNullOrEmpty(gaebCatalogInfo.CtlgName)) --> may occur in gaeb 90 files?
					{

						// compare gaebCatalogInfo.CtlgName with GaebName
						if (!String.IsNullOrEmpty(gaebCatalogInfo.CtlgName))
						{
							mappingDetail = assignDetails.FirstOrDefault(ad => ad.GaebName!=null && new Regex(ad.GaebName, RegexOptions.IgnoreCase).IsMatch(gaebCatalogInfo.CtlgName));
						}

						if (mappingDetail == null && !String.IsNullOrEmpty(gaebCatalogInfo.CtlgName))
						{
							// compare gaebCatalogInfo.CtlgName with CostGroupCat code
							mappingDetail = assignDetails.Where(e => {
								if (e.BasCostgroupCatFk.HasValue)
								{
									CostGroupCatEntity costGroupCatEntity = costGroupCatLogic.GetById(new Platform.Core.IdentificationData() { Id = e.BasCostgroupCatFk.Value });
									return costGroupCatEntity != null && costGroupCatEntity.Code.Equals(gaebCatalogInfo.CtlgName, StringComparison.OrdinalIgnoreCase);
								}
								else
								{
									return false;
								}
							}).FirstOrDefault();
						}

						if (mappingDetail == null && !String.IsNullOrEmpty(gaebCatalogInfo.CtlgName))
						{
							// compare gaebCatalogInfo.CtlgName with BoqCatalogEntity.DescriptionInfo
							BoqCatalogEntity knownCatalog = knownCatalogs.FirstOrDefault(c => c.DescriptionInfo.Translated.Equals(gaebCatalogInfo.CtlgName));
							if (knownCatalog != null)
							{
								mappingDetail = assignDetails.Where(e => e.BoqCatalogFk == knownCatalog.Id).FirstOrDefault();
							}
						}

						if (mappingDetail == null)
						{
							// last chance: search catalog assignment by gaeb type
							int? gaebTypeId = catalogLogic.GetGaebIdByCatalogType(gaebCatalogInfo.CtlgType);
							if (gaebTypeId.HasValue)
							{
								mappingDetail = assignDetails.Where(e => e.GaebId == gaebTypeId.Value).FirstOrDefault();
							}
						}

					}

					z++;
					if (mappingDetail == null)
					{
						mappingDetail = new BoqCatAssignDetailEntity();
						gaebCatalogInfo.IsNewCatalog = true;	// default: New Catalog
						gaebCatalogInfo.BoqCatalogFk = 15;		// Cost Group (Project)
						gaebCatalogInfo.BasCostgroupCatFk = 0;	//  null;
						gaebCatalogInfo.CostGroupCatalogCode = String.Format(Resources.DefaultCatalogCode, z);
						gaebCatalogInfo.CostGroupCatalogCode =  String.Format("CODE{0:00}", z);
						gaebCatalogInfo.CostGroupCatalogDescription = gaebCatalogInfo.CtlgName;
						int defaultCatalogAssignmentMode = SystemOptions.SharedInstance.GetValueAs<int>(SystemOption.DefaultCatalogAssignmentMode, 2);
						gaebCatalogInfo.CatalogAssignmentMode = (BoqConstants.CatalogAssignmentMode)defaultCatalogAssignmentMode;
					}
					else
					{
						gaebCatalogInfo.BasCostgroupCatFk = mappingDetail.BasCostgroupCatFk;
						gaebCatalogInfo.BoqCatalogFk = mappingDetail.BoqCatalogFk;
						gaebCatalogInfo.IsNewCatalog = false;
						gaebCatalogInfo.CostGroupCatalogCode = mappingDetail.Code;
						//gaebCatalogInfo.CostGroupCatalogDescription = mappingDetail.DescriptionInfo.Translated;
						gaebCatalogInfo.CatalogAssignmentMode = (BoqConstants.CatalogAssignmentMode)mappingDetail.SearchMode;
					}
					initialData.Catalogs.Add(gaebCatalogInfo);
				}
			}

			AddVerificationData(initialData);

			// WalkTheTree(gaebInfo, initialData);
			return initialData;

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="gaebObject"></param>
		/// <param name="gaebInfoData"></param>
		private void WalkTheTree(G_Object gaebObject, GaebInfoData gaebInfoData)
		{

			var parent = gaebObject;
			if (parent.gNodes != null)
			{
				foreach (var node in parent.gNodes)
				{

					if (node is GX_BoQCtgy)	// Division
					{

					}

					//if (node is GX_Item)
					//{
					//	var gxItem = node as GX_Item;
					//	if (String.IsNullOrEmpty(gxItem.Description().gTextOutlineText()))
					//	{
					//		gaebInfoData.Errors.Add(String.Format("Item {%1} must have a outline specification!", gxItem.att_RNoPart));
					//	}
					//}

					if (node is GX_QtySplit)
					{
						gaebInfoData.HasSplitQuantities = true;
					}

					// collect used catalogs
					if (node is GX_Ctlg)
					{
						gaebInfoData.Catalogs.Add(new GaebCatalogInfo()
						{
							CtlgID = (node as GX_Ctlg).CtlgID,
							CtlgName = (node as GX_Ctlg).CtlgName,
							CtlgType = (node as GX_Ctlg).CtlgType,
							CatalogAssignmentMode = BoqConstants.CatalogAssignmentMode.CatalogAssignmentMode_Ignore	// default
						});
					}

					WalkTheTree(node, gaebInfoData);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="gaebInfoData"></param>
		private void AddVerificationData(GaebInfoData gaebInfoData)
		{
			if (gaebInfoData.UpComps.Count > 0)
			{
				int MaxUnitRateBreakDownCountAllowed = SystemOptions.SharedInstance.GetMaxUnitRateBreakDownCountAllowed() ?? -1;
				if (MaxUnitRateBreakDownCountAllowed >= 0)
				{
					if (gaebInfoData.UpComps.Count > MaxUnitRateBreakDownCountAllowed)
					{
						gaebInfoData.Errors.Add(String.Format(NLS.IMP_MaxUnitRateBreakDownCount_Validation_Failed, MaxUnitRateBreakDownCountAllowed, gaebInfoData.UpComps.Count));
						if (SystemOptions.SharedInstance.GetForceAbortTooManyURB())
						{
							gaebInfoData.PreventImport = true;
						}
					}
				}
			}

		}

		private static bool IsValidXmlName(string name)
		{
			try
			{
				return name == XmlConvert.VerifyName(name);
			}
			catch (Exception)
			{
				return false;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		private void CollectPrcStructureAssignments(G_Object gaebObject, GaebInfoData gaebInfoData, string ctlgId = "", string ctlgCode = "")
		{
			var parent = gaebObject;
			if (parent != null && parent.gNodes != null)
			{
				foreach (var node in parent.gNodes)
				{
					string schema = !string.IsNullOrEmpty(node.Schema) ? node.Schema.ToUpper() : "";
					string name = node.gName;

					if (name.Equals("CtlgID", StringComparison.OrdinalIgnoreCase))
					{
						ctlgId = node.gValue.Trim();
					}

					if (schema == BoqConstants.RibGaebExtSchema && name.Equals("CtlgCode", StringComparison.OrdinalIgnoreCase))
					{
						ctlgCode = node.gValue.Trim();
					}

					if (schema == BoqConstants.RibGaebExtSchema
						&& node.gParentObject.gName.Equals("PropItem", StringComparison.OrdinalIgnoreCase)
						&& name.Equals("Key", StringComparison.OrdinalIgnoreCase))
					{
						gaebInfoData.PrcStructureAssignments.Add(new PrcStructureAssignment() { CtlgCode = ctlgCode, CtlgID = ctlgId, PropItemKey = node.gValue.Trim() });
					}

					CollectPrcStructureAssignments(node, gaebInfoData, ctlgId, ctlgCode);
				}
			}
		}

		/// <summary>
		/// get node for location ctlg data
		/// </summary>
		/// <param name="gaebObjects"></param>
		/// <returns></returns>
		private G_Object GetLocationCtlgData(IEnumerable<G_Object> gaebObjects)
		{
			G_Object locationCtlgData = null;
			if (gaebObjects != null)
			{
				foreach (var gaebObject in gaebObjects)
				{
					if (gaebObject.gNodes != null)
					{
						foreach (var node in gaebObject.gNodes)
						{
							string name = node.gName;
							if (name == "CtlgType" && node.gValue == "locality")
							{
								locationCtlgData = gaebObject.gNodes.Where(e => e.Schema == BoqConstants.RibGaebExtSchema && e.gName == BoqConstants.RibGaebCtlgData).FirstOrDefault();
								break;
							}
						}

						if (locationCtlgData != null)
							break;
					}
				}
			}

			return locationCtlgData;
		}

		/// <summary>
		/// get list for location ctlg data: model object
		/// </summary>
		/// <param name="gaebObject"></param>
		/// <param name="gaebInfoData"></param>
		private void CollectLocationCtlgDatas(G_Object gaebObject, GaebInfoData gaebInfoData)
		{
			var parent = gaebObject;
			if (parent != null && parent.gNodes != null)
			{
				string ctlgCode = "", ctlgDesc = "", cpiId = "";
				foreach (var node in parent.gNodes)
				{
					string schema = node.Schema.ToUpper();
					string name = node.gName;

					if (schema == BoqConstants.RibGaebExtSchema && name == BoqConstants.RibGaebCtlgCode)
					{
						ctlgCode = node.gValue;
						continue;
					}

					if (schema == BoqConstants.RibGaebExtSchema && name == BoqConstants.RibGaebCtlgDesc)
					{
						ctlgDesc = node.gValue;
						continue;
					}

					if (schema == BoqConstants.RibGaebExtSchema && name == BoqConstants.RibGaebCpiID)
					{
						cpiId = node.gValue;
						if (gaebInfoData.LocationCtlgDatas == null)
							gaebInfoData.LocationCtlgDatas = new List<GaebLocationCtlgDataInfo>();
						if (!string.IsNullOrEmpty(ctlgCode))
							gaebInfoData.LocationCtlgDatas.Add(new GaebLocationCtlgDataInfo() { CtlgCode = ctlgCode, CtlgDesc = ctlgDesc, CpiId = cpiId });
						continue;
					}

					if (schema == BoqConstants.RibGaebExtSchema && name == BoqConstants.RibGaebCtlgData)
					{
						CollectLocationCtlgDatas(node, gaebInfoData);
					}
				}
			}
		}

		#endregion

		#region helper

		private bool IsLumpsumItem(GX_Item gxItem)
		{

			// return (!gxItem.LumpSumItem_isEmpty() && IsGaebYes(gxItem.LumpSumItem));	// || gxItem.QU.ToLower().Equals("psch");	// support of GAEB2000  
			bool res = (!gxItem.LumpSumItem_isEmpty() && IsGaebYes(gxItem.LumpSumItem));

			if (gxItem.gName.Equals(_lumpSumBilling))
			{
				res = gxItem.gValue.Equals("Yes") ? true : false;
			}
			return res;
		}

		private bool IsLumpsumUnit(string unit)
		{
			bool res = false;
			var uom = BoqImExportHelper.GetUomByUnit(unit);
			if (uom != null)
			{
				res = uom.UomTypeFk == 8;
			}
			return res;
		}

		private bool IsGaebYes(string val)
		{
			return val.Equals("Yes");
		}

		private bool GaebTypeOverwritesQty
		{
			get
			{
				string[] whiteList = { "81", "82", "83", "85", "86" };
				return whiteList.Contains(this.GaebFormat);
			}
		}

		private void Add2ModifiedBlobs(BlobEntity blob)
		{
			_modifiedBlobs.Add(blob);
		}

		private bool IsRootItem(BoqItemEntity boqItem)
		{
			return boqItem.BoqItemFk == null;
		}

		private bool ContainsTextComplements_2(GX_CompleteText completeText)
		{
			// ComplTSA,ComplTSB could
			//if ((completeText.ComplTSA_isEmpty() && completeText.ComplTSB_isEmpty()) || completeText.DetailTxt().Nodes == null)
			if (completeText.DetailTxt.gNodes != null && completeText.DetailTxt.gNodes.Where(node => node is GX_TextComplement).Count() > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		static internal string TruncateString4000(string s, ref int truncatedCounter)
		{
			return TruncateString(s, 4000, ref truncatedCounter);
		}

		static internal string TruncateString(string s, int maxStringLength, ref int truncatedCounter)
		{
			if (String.IsNullOrEmpty(s) || s.Length<=maxStringLength)
			{
				return s;
			}

			truncatedCounter++;
			return StringExtension.Truncate(s, maxStringLength);
		}

		/// <summary>
		/// 
		/// </summary>
		protected Boolean IsBidderFileOrUpdate85
		{
			get
			{
				return IsGaeb84 || (IsGaeb85 && _targetHasChilds);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		protected Boolean GaebTypeSupportsUpsert
		{
			get
			{
				string[] whiteList = { "81", "82", "83", "84", "85", "86" };
				return whiteList.Contains(this.GaebFormat);
			}
		}

		private static string GetDummySubDescRef (string parentReference, string subNo)
		{
			return String.Format("{0}#{1}", parentReference, subNo);
		}

		private static string GetDummyDesignDescRef(string parentReference, string perfNo)
		{
			return String.Format("{0}§{1}", parentReference, perfNo);
		}

		private static string GetDummyNoteRef(BoqItemEntity boqItem)
		{
			return GetDummyNoteRef(String.IsNullOrEmpty(boqItem.GaebId) ? GetGaebID(boqItem.Id) : boqItem.GaebId);
		}

		private static string GetDummyNoteRef(string GaebId)
		{
			return String.Format("Remark-{0}", GaebId);
		}

		#endregion
	}
}
