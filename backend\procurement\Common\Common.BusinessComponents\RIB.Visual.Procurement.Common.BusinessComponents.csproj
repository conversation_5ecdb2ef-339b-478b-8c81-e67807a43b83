﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B725F817-02F4-4DE3-89A4-8C7999A9F6D9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Procurement.Common.BusinessComponents</RootNamespace>
    <AssemblyName>RIB.Visual.Procurement.Common.BusinessComponents</AssemblyName>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFramework>net8.0</TargetFramework>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Procurement.Common.BusinessComponents.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Constants\CopyType.cs" />
    <Compile Include="Constants\ItemType.cs" />
    <Compile Include="Entities\BasicsAddressEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\BasicsAddressEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>BasicsAddressEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\CopyCertificatesFromMaterialResult.cs" />
    <Compile Include="Entities\CreateInterCompany\InterCompanyBaseGridEntity.cs" />
    <Compile Include="Entities\CreateInterCompany\InterCompanyGridEntity.cs" />
    <Compile Include="Entities\CreateInterCompany\InterCompanyUiEntity.cs" />
    <Compile Include="Entities\CreateInterCompany\DrillDownGridEntity.cs" />
    <Compile Include="Entities\CreateItemAssignmentFromPackage2EstimateParam.cs" />
    <Compile Include="Entities\Customize\MainItemCopyInfo.cs" />
    <Compile Include="Entities\Customize\PrcDependentDtata.cs" />
    <Compile Include="Entities\DeliveryScheduleEntity\DeliveryScheduleParameter.cs" />
    <Compile Include="Entities\PrcDocumentStatusEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcDocumentStatusEntity.Generated.cs">
      <DependentUpon>PrcDocumentStatusEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItem2PlantEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItem2PlantEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PrcItem2PlantEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemCreateParameterEntity.cs" />
    <Compile Include="Entities\PrcPackage2ExtBidderComplete.cs" />
    <Compile Include="Entities\PrcPackage2ExtBidderEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcPackage2ExtBidderEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PrcPackage2ExtBidderEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcPackage2ExtBpContactEntity.cs">
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcPackage2ExtBpContactEntity.Generated.cs">
      <DependentUpon>PrcPackage2ExtBpContactEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcPsStatusEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcPsStatusEntity.Generated.cs">
      <DependentUpon>PrcPsStatusEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\OptionProfile\PrcCommonOptionProfileParameter.cs" />
    <Compile Include="Entities\UpdateItemPriceEntity.cs" />
    <Compile Include="Entities\UpdatePackageOption.cs" />
    <Compile Include="Entities\UpdateVersionBoqEntity.cs" />
    <Compile Include="EntityModel\ModelBuilder.cs" />
    <Compile Include="Enums\CreateContractOption.cs" />
    <Compile Include="Enums\GenerateDeliverySchedule.cs" />
    <Compile Include="Enums\UpdateItemsFromModule.cs" />
    <Compile Include="IEntityFacade\PrcSuggestedBidderEntityFacade.cs" />
    <Compile Include="Logic\Account\AccassignAccTypeLogic.cs" />
    <Compile Include="Logic\Account\AccassignFactoryLogic.cs" />
    <Compile Include="Entities\AccassignAccountEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignAccountEntity.Generated.cs">
      <DependentUpon>AccassignAccountEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignAccTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignAccTypeEntity.Generated.cs">
      <DependentUpon>AccassignAccTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignBusinessEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignBusinessEntity.Generated.cs">
      <DependentUpon>AccassignBusinessEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignControlEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignControlEntity.Generated.cs">
      <DependentUpon>AccassignControlEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignConTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignConTypeEntity.Generated.cs">
      <DependentUpon>AccassignConTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignFactoryEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignFactoryEntity.Generated.cs">
      <DependentUpon>AccassignFactoryEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignItemTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignItemTypeEntity.Generated.cs">
      <DependentUpon>AccassignItemTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\AccassignMatGroupEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\AccassignMatGroupEntity.Generated.cs">
      <DependentUpon>AccassignMatGroupEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Logic\Account\AccassignAccountLogic.cs" />
    <Compile Include="Logic\Account\AccassignBusinessLogic.cs" />
    <Compile Include="Logic\Account\AccassignControlLogic.cs" />
    <Compile Include="Logic\Account\AccassignConTypeLogic.cs" />
    <Compile Include="Logic\Account\AccassignItemTypeLogic.cs" />
    <Compile Include="Logic\Account\AccassignMatGroupLogic.cs" />
    <Compile Include="Configurations\ProcurementCommonModuleDataConfiguration.cs" />
    <Compile Include="Configurations\OverviewDataDto.cs" />
    <Compile Include="Configurations\OverviewDataSqlConfiguration.cs" />
    <Compile Include="Configurations\PrcItem2CostGroupConfiguration.cs" />
    <Compile Include="Configurations\PrcItemScopeDetailCostGroupConfiguration.cs" />
    <Compile Include="Configurations\OverviewDataConfiguration.cs" />
    <Compile Include="Configurations\ProcurementCommonOverviewDataConfiguration.cs" />
    <Compile Include="Configurations\ProcurementCommonLookupDataConfiguration.cs" />
    <Compile Include="Configurations\StatusWorkflowConfigurationProvider.cs" />
    <Compile Include="Constants\PrcItem4CreateType.cs" />
    <Compile Include="Constants\ProcurementModuleConstants.cs" />
    <Compile Include="Constants\UpdateStatusFromModuleType.cs" />
    <Compile Include="Entities\AIReplaceMaterialEntity.cs" />
    <Compile Include="Entities\AIUpdateMaterialEntity.cs" />
    <Compile Include="Entities\Customize\BoqRecalculationOptions.cs" />
    <Compile Include="Entities\ExchangeRateLookupEntity.cs" />
    <Compile Include="Constants\Wizards.cs" />
    <Compile Include="Entities\BpdBp2prcStructureVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\BpdBp2prcStructureVEntity.Generated.cs">
      <DependentUpon>BpdBp2prcStructureVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\DuplicateObjectEntity.cs" />
    <Compile Include="Entities\ItemBaseEntity.cs" />
    <Compile Include="Entities\ItemType2Entity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ItemType2Entity.Generated.cs">
      <DependentUpon>ItemType2Entity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ItemType85Entity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ItemType85Entity.Generated.cs">
      <DependentUpon>ItemType85Entity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ItemTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ItemTypeEntity.Generated.cs">
      <DependentUpon>ItemTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\MaterialPriceVersionListEntity.cs" />
    <Compile Include="Entities\AIMdcCommoditySearchVEntity.cs" />
    <Compile Include="Entities\PaymentScheduleEntity\IPaymentScheduleEntity.cs" />
    <Compile Include="Entities\PaymentScheduleEntity\PaymentScheduleCalcParams.cs" />
    <Compile Include="Entities\PrcBoqComplete.cs" />
    <Compile Include="Entities\PrcBoqExtendedEntity.cs" />
    <Compile Include="Entities\PrcBoqLookupVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcBoqLookupVEntity.Generated.cs">
      <DependentUpon>PrcBoqLookupVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcCallOffAgreementEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcCallOffAgreementEntity.Generated.cs">
      <DependentUpon>PrcCallOffAgreementEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcDependentDataVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcDependentDataVEntity.Generated.cs">
      <DependentUpon>PrcDependentDataVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcDocumentTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcDocumentTypeEntity.Generated.cs">
      <DependentUpon>PrcDocumentTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItem2MdlObjectVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItem2MdlObjectVEntity.Generated.cs">
      <DependentUpon>PrcItem2MdlObjectVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemAssignmentEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemAssignmentEntity.Generated.cs">
      <DependentUpon>PrcItemAssignmentEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemComplete.cs" />
    <Compile Include="Entities\PrcItemEvaluationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemEvaluationEntity.Generated.cs">
      <DependentUpon>PrcItemEvaluationEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemInfoBLEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemInfoBLEntity.Generated.cs">
      <DependentUpon>PrcItemInfoBLEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemLookupVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemLookupVEntity.Generated.cs">
      <DependentUpon>PrcItemLookupVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemMergedLookupEntity.cs" />
    <Compile Include="Entities\PrcItemScopeComplete.cs" />
    <Compile Include="Entities\PrcItemScopeDetailComplete.cs" />
    <Compile Include="Entities\PrcItemScopeDetailEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemScopeDetailEntity.Generated.cs">
      <DependentUpon>PrcItemScopeDetailEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemScopeDetailPcEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemScopeDetailPcEntity.Generated.cs">
      <DependentUpon>PrcItemScopeDetailPcEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemScopeDtlBlobEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemScopeDtlBlobEntity.Generated.cs">
      <DependentUpon>PrcItemScopeDtlBlobEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemScopeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemScopeEntity.Generated.cs">
      <DependentUpon>PrcItemScopeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemstatus2externalEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemstatus2externalEntity.Generated.cs">
      <DependentUpon>PrcItemstatus2externalEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcMandatoryDeadlineEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcMandatoryDeadlineEntity.Generated.cs">
      <DependentUpon>PrcMandatoryDeadlineEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcOverviewVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcOverviewVEntity.Generated.cs">
      <DependentUpon>PrcOverviewVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcPaymentScheduleEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcPaymentScheduleEntity.Generated.cs">
      <DependentUpon>PrcPaymentScheduleEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcPostconHistoryEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcPostconHistoryEntity.Generated.cs">
      <DependentUpon>PrcPostconHistoryEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcReplaceMaterialEntity.cs" />
    <Compile Include="Entities\PrcReplaceNautralMaterialEntity.cs" />
    <Compile Include="Entities\PrcStckTranType2RubCatEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcStckTranType2RubCatEntity.Generated.cs">
      <DependentUpon>PrcStckTranType2RubCatEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcStocktransactionEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcStocktransactionEntity.Generated.cs">
      <DependentUpon>PrcStocktransactionEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcStocktransactionLookupVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcStocktransactionLookupVEntity.Generated.cs">
      <DependentUpon>PrcStocktransactionLookupVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcStocktransactiontypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcStocktransactiontypeEntity.Generated.cs">
      <DependentUpon>PrcStocktransactiontypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcStructure2ClerkEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcStructure2clerkEntity.Generated.cs">
      <DependentUpon>PrcStructure2ClerkEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcSuggestedBidderEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcSuggestedBidderEntity.Generated.cs">
      <DependentUpon>PrcSuggestedBidderEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcWarrantyEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcWarrantyEntity.Generated.cs">
      <DependentUpon>PrcWarrantyEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\HistoricalPriceForBoqEntity.cs" />
    <Compile Include="Entities\PrjGeneralsEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrjGeneralsEntity.Generated.cs">
      <DependentUpon>PrjGeneralsEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcBoqEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcBoqEntity.Generated.cs">
      <DependentUpon>PrcBoqEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcCertificateEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcCertificateEntity.Generated.cs">
      <DependentUpon>PrcCertificateEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcConfiguration2strategyVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcConfiguration2strategyVEntity.Generated.cs">
      <DependentUpon>PrcConfiguration2strategyVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcConfiguration2texttypeVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcConfiguration2texttypeVEntity.Generated.cs">
      <DependentUpon>PrcConfiguration2texttypeVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcContactEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcContactEntity.Generated.cs">
      <DependentUpon>PrcContactEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcDataViewEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcDataViewEntity.Generated.cs">
      <DependentUpon>PrcDataViewEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcDocumentEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcDocumentEntity.Generated.cs">
      <DependentUpon>PrcDocumentEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcHeader2prcStructureVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcHeader2prcStructureVEntity.Generated.cs">
      <DependentUpon>PrcHeader2prcStructureVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcRadiusEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcRadiusEntity.Generated.cs">
      <DependentUpon>PrcRadiusEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ProcurementComplete.cs" />
    <Compile Include="Entities\ProcurementIncotermEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ProcurementIncotermEntity.Generated.cs">
      <DependentUpon>ProcurementIncotermEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\QtnRequisitionEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\QtnRequisitionEntity.Generated.cs">
      <DependentUpon>QtnRequisitionEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ReqHeaderLookupVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ReqHeaderLookupVEntity.Generated.cs">
      <DependentUpon>ReqHeaderLookupVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ReqHeaderUserAccessVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ReqHeaderUserAccessVEntity.Generated.cs">
      <DependentUpon>ReqHeaderUserAccessVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\TotalEntity\TotalCalcParams.cs" />
    <Compile Include="Entities\TotalEntity\TotalEntityInfo.cs" />
    <Compile Include="Entities\TranslationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\TranslationEntity.Generated.cs">
      <DependentUpon>TranslationEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\UpdatePricePrcItemComplete.cs" />
    <Compile Include="Entities\HistoricalPriceForItemEntity.cs" />
    <Compile Include="Entities\WarrantyObligationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\WarrantyObligationEntity.Generated.cs">
      <DependentUpon>WarrantyObligationEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\WarrantySecurityEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\WarrantySecurityEntity.Generated.cs">
      <DependentUpon>WarrantySecurityEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Enums\PrcItemStatus.cs" />
    <Compile Include="Extension\EntityExtension.cs" />
    <Compile Include="Entities\PrcGeneralsEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcGeneralsEntity.Generated.cs">
      <DependentUpon>PrcGeneralsEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcHeaderblobEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcHeaderblobEntity.Generated.cs">
      <DependentUpon>PrcHeaderblobEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcHeaderEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcHeaderEntity.Generated.cs">
      <DependentUpon>PrcHeaderEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemblobEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemblobEntity.Generated.cs">
      <DependentUpon>PrcItemblobEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemdeliveryEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemdeliveryEntity.Generated.cs">
      <DependentUpon>PrcItemdeliveryEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemEntity.Generated.cs">
      <DependentUpon>PrcItemEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItempriceconditionEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItempriceconditionEntity.Generated.cs">
      <DependentUpon>PrcItempriceconditionEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcItemstatusEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcItemstatusEntity.Generated.cs">
      <DependentUpon>PrcItemstatusEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcMilestoneEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcMilestoneEntity.Generated.cs">
      <DependentUpon>PrcMilestoneEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcMilestonetypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcMilestonetypeEntity.Generated.cs">
      <DependentUpon>PrcMilestonetypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcSubreferenceEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcSubreferenceEntity.Generated.cs">
      <DependentUpon>PrcSubreferenceEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PrcTexttypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PrcTexttypeEntity.Generated.cs">
      <DependentUpon>PrcTexttypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ReqStatusEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ReqStatusEntity.Generated.cs">
      <DependentUpon>ReqStatusEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ReqTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ReqTypeEntity.Generated.cs">
      <DependentUpon>ReqTypeEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="EntityModel\EntityModel.ModelBuilder.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>EntityModel</DependentUpon>
    </Compile>
    <Compile Include="Logic\Base\CreateInterCompanyBase.cs" />
    <Compile Include="Logic\Base\PrcCommonUpdateBlobLogicBase.cs" />
    <Compile Include="Logic\Base\PrcSortExpression.cs" />
    <Compile Include="Logic\Base\ProcurementCommonLogicBaseNew.cs" />
    <Compile Include="Logic\Base\ProcurementStockLogicBase.cs" />
    <Compile Include="Logic\CalculationHelper\PrcCalculationHelper.cs" />
    <Compile Include="Logic\ChangeConfiguration\ChangeConfigurationExtendedTotalLogicBase.cs" />
    <Compile Include="Logic\ChangeConfiguration\ChangeConfigurationLogicBase.cs" />
    <Compile Include="Logic\ClonePrcHeaderLogic.cs" />
    <Compile Include="Logic\Base\ProcurementCommonLogicBase.cs" />
    <Compile Include="Logic\CommonUpdateExchangeRateLogic.cs" />
    <Compile Include="Logic\CommonUpdateTaxCodeLogic.cs" />
    <Compile Include="Logic\Comparer\Comparer.cs" />
    <Compile Include="Logic\Comparer\PrcReplaceMaterialComparer.cs" />
    <Compile Include="Logic\Controlling\ContrItemTotalEntity.cs" />
    <Compile Include="Logic\Controlling\ContrHeaderTotalEntity.cs" />
    <Compile Include="Logic\Controlling\ControllingTotalEntity.cs" />
    <Compile Include="Logic\Controlling\ControllingTotalLogic.cs" />
    <Compile Include="Logic\Dto\ExternalSourceDto.cs" />
    <Compile Include="Logic\Dto\PartialBoqAndPrcItemCopy.cs" />
    <Compile Include="Logic\ExcelImport\Dynamic2ConcreteTypeConvertor.cs" />
    <Compile Include="Logic\ExcelImport\ImportConstant.cs" />
    <Compile Include="Logic\ExcelImport\ImportTools.cs" />
    <Compile Include="Logic\ExcelImport\MemoryCacheForImport.cs" />
    <Compile Include="Logic\ExcelImport\ImportPrcHeaderEntity.cs" />
    <Compile Include="Logic\ExcelImport\PrcItemExcelImportPrjCostGroupsProcessor.cs" />
    <Compile Include="Logic\ExcelImport\PrcItemExcelImportFieldsProcessor.cs" />
    <Compile Include="Logic\ExcelImport\PrcItemExcelImportLogic.cs" />
    <Compile Include="Logic\Export\PrcItemExportLogic.cs" />
    <Compile Include="Logic\GenerateCodeParameter.cs" />
    <Compile Include="Logic\ObjectPermissionHelper.cs" />
    <Compile Include="Logic\ObjectToXmlLogic.cs" />
    <Compile Include="Logic\PrcBoqChangeOverviewLogic.cs" />
    <Compile Include="Logic\PrcCharacteristicLogic.cs" />
    <Compile Include="Logic\CommonOverviewLogic.cs" />
    <Compile Include="Logic\PrcCommon2Stock.cs" />
    <Compile Include="Logic\PrcCommonGetVatPercentLogic.cs" />
    <Compile Include="Logic\PrcCommonItemUpdateBlobLogic.cs" />
    <Compile Include="Logic\PrcContrctAddressLogic.cs" />
    <Compile Include="Logic\PrcDocumentStatusLogic.cs" />
    <Compile Include="Logic\PrcItem2PlantLogic.cs" />
    <Compile Include="Logic\PrcPackage2ExtBidderLogic.cs" />
    <Compile Include="Logic\PrcPackageExibidder2ContactLogic.cs" />
    <Compile Include="Logic\PrcPsStatusLogic.cs" />
    <Compile Include="Logic\PrcUomLogic.cs" />
    <Compile Include="Logic\ProcurementCommonColumnConfigLogic.cs" />
    <Compile Include="Logic\ProcurementCommonOverallDiscountLogic.cs" />
    <Compile Include="Logic\ProcurementCommonSalesTaxMethodLogic.cs" />
    <Compile Include="Logic\RelationInfo\PrcPackage2ExtBidderRelationInfo.cs" />
    <Compile Include="Logic\Request\CreateSuggestedBidderRequest.cs" />
    <Compile Include="Logic\Request\CreateWicFromBoqWizardRequest.cs" />
    <Compile Include="Logic\HistoricalPriceLogic.cs" />
    <Compile Include="Logic\Request\GetBoqItemWicItemCountRequest.cs" />
    <Compile Include="Logic\Request\UpdateExchangeRateRequest.cs" />
    <Compile Include="Logic\Request\UpdatePackageMaterialRequest.cs" />
    <Compile Include="Logic\Request\UpdateWicFromBoqOfQtnRequest.cs" />
    <Compile Include="Logic\SplitOverAllDiscountLogic.cs" />
    <Compile Include="Logic\ImportPrcItemCommonLogic.cs" />
    <Compile Include="Logic\MobilityApiResponseLogic.cs" />
    <Compile Include="Logic\PrcClerkLogic.cs" />
    <Compile Include="Logic\PrcCommonUpdateBlobLogic.cs" />
    <Compile Include="Logic\PrcDocumentTypeLogic.cs" />
    <Compile Include="Logic\PrcItemInfoBLLogic.cs" />
    <Compile Include="Logic\PrcPostconHistoryLogic.cs" />
    <Compile Include="Logic\PrcStockTranType2RubCatLogic.cs" />
    <Compile Include="Logic\RelationInfo\PrcBoqRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcCallOffAgreementRelationInfo.cs" />
    <Compile Include="Logic\PrcCommonOptionProfileLogic.cs" />
    <Compile Include="Logic\AccrualTransaction\AccrualTransactionBaseLogic.cs" />
    <Compile Include="Logic\AccrualTransaction\PrcStructureAccountResolver.cs" />
    <Compile Include="Logic\UpdateEstimateLogic.cs" />
    <Compile Include="Logic\UpdatePesEstimateData.cs" />
    <Compile Include="Logic\PrcItemAssignmentLogic.cs" />
    <Compile Include="Logic\PrcItemdeliverySqlLogic.cs" />
    <Compile Include="Logic\PrcItemMergedLookupLogic.cs" />
    <Compile Include="Logic\PrcItemScopeCreationData.cs" />
    <Compile Include="Logic\PrcItemScopeDetailLogic.cs" />
    <Compile Include="Logic\PrcItemScopeDetailPcLogic.cs" />
    <Compile Include="Logic\PrcItemScopeDtlBlobLogic.cs" />
    <Compile Include="Logic\PrcItemScopeLogic.cs" />
    <Compile Include="Logic\PrcItemstatus2externalLogic.cs" />
    <Compile Include="Logic\PrcItemType2Logic.cs" />
    <Compile Include="Logic\PrcItemType85Logic.cs" />
    <Compile Include="Logic\PrcItemTypeLogic.cs" />
    <Compile Include="Logic\PrcMandatoryDeadlineLogic.cs" />
    <Compile Include="Logic\PrcCallOffAgreementLogic.cs" />
    <Compile Include="Logic\PrcStocktransactionLogic.cs" />
    <Compile Include="Logic\PrcStocktransactionLookupVLogic.cs" />
    <Compile Include="Logic\PrcStocktransactiontypeLogic.cs" />
    <Compile Include="Logic\PrcStructure2ClerkLogic.cs" />
    <Compile Include="Logic\PrcSuggestedBidderLogic.cs" />
    <Compile Include="Logic\ProcurementCommonControllingUnitLogic.cs.cs" />
    <Compile Include="Logic\ProcurementCommonUpdateItemPriceWizardLogic.cs" />
    <Compile Include="Logic\ProcurementCommonWizardLogic.cs" />
    <Compile Include="Logic\QueryModelObjectLogic.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemAssignmentRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemScopeDetailPcRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemScopeDetailRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemScopeDtlBlobRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemScopeRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcSuggestedBidderRelationInfo.cs" />
    <Compile Include="Logic\ProcurementIncotermLogic.cs" />
    <Compile Include="Logic\PrcPaymentScheduleCalculator.cs" />
    <Compile Include="Logic\PrcPaymentScheduleLogic.cs" />
    <Compile Include="Logic\DeletePrcHeaderLogic.cs" />
    <Compile Include="Logic\DuplicatePrcItemChildLogic.cs" />
    <Compile Include="Logic\HeaderSubDataLogicBase.cs" />
    <Compile Include="Logic\ImportBaseLogic.cs" />
    <Compile Include="Logic\PrcCertificatLogic.cs" />
    <Compile Include="Logic\PrcContactLogic.cs" />
    <Compile Include="Logic\PrcDependentDataVLogic.cs" />
    <Compile Include="Logic\PrcItemEvalutionLogic.cs" />
    <Compile Include="Logic\PrcOverviewLogic.cs" />
    <Compile Include="Logic\PrcDataLogic.cs" />
    <Compile Include="Logic\PrcDocumentLogic.cs" />
    <Compile Include="Logic\PrcGeneralsLogic.cs" />
    <Compile Include="Logic\PrcHeaderblobLogic.cs" />
    <Compile Include="Logic\PrcHeaderLogic.cs" />
    <Compile Include="Logic\PrcItemblobLogic.cs" />
    <Compile Include="Logic\PrcItemdeliveryLogic.cs" />
    <Compile Include="Logic\PrcBoqLookupVLogic.cs" />
    <Compile Include="Logic\PrcItemLookupVLogic.cs" />
    <Compile Include="Logic\PrcItemLogic.cs" />
    <Compile Include="Logic\PrcItemPriceConditionLogic.cs" />
    <Compile Include="Logic\PrcItemSubDataLogicBase.cs" />
    <Compile Include="Logic\GetBusinesspartnerForSuppliersLogic.cs" />
    <Compile Include="Logic\PrcBoqLogic.cs" />
    <Compile Include="Logic\PrcConfiguration2StrategyViewLogic.cs" />
    <Compile Include="Logic\PrcConfiguration2TextTypeViewLogic.cs" />
    <Compile Include="Logic\PrcItemstatusLogic.cs" />
    <Compile Include="Logic\PrcMilestoneLogic.cs" />
    <Compile Include="Logic\PrcMilestoneTypeLogic.cs" />
    <Compile Include="Logic\PrcRadiusLogic.cs" />
    <Compile Include="Logic\PrcSubreferenceLogic.cs" />
    <Compile Include="Logic\PrcTexttypeLogic.cs" />
    <Compile Include="Logic\PrcTotalCalculator.cs" />
    <Compile Include="Logic\PrcTotalLogic.cs" />
    <Compile Include="Logic\PrjGeneralsLogic.cs" />
    <Compile Include="Logic\ProcurementCommonCodeLogic.cs" />
    <Compile Include="Logic\ProcurementCommonExchangeRateLogic.cs" />
    <Compile Include="Logic\RelationInfo\PrcCertificateRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcContractRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcDocumentRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcGeneralsRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcHeaderblobRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcHeaderRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemblobRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemdeliveryRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemPriceConditionRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcItemRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcMilestoneRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcPaymentScheduleRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PrcSubreferenceRelationInfo.cs" />
    <Compile Include="Logic\ReqHeaderLookupViewLogic.cs" />
    <Compile Include="Logic\ReqStatusLogic.cs" />
    <Compile Include="Logic\ReqTypeLogic.cs" />
    <Compile Include="Logic\UpdateVersionBoQLogic.cs" />
    <Compile Include="Logic\UpdateVersionBoQProcessor.cs" />
    <Compile Include="Logic\UpdateVersionBoQProcessorFactory.cs" />
    <Compile Include="Logic\Wizard\GenerateDeliveryScheduleLogic.cs" />
    <Compile Include="Logic\Wizard\RenumberItemLogic.cs" />
    <Compile Include="Logic\WorkflowHandler.cs" />
    <Compile Include="Logic\PrcWarrantyLogic.cs" />
    <Compile Include="Logic\RelationInfo\PrcWarrantyRelationInfo.cs" />
    <Compile Include="MaterialFilter\ProcurementMaterialFilter.cs" />
    <Compile Include="PriceCondition\PrcItemPriceConditionClaculationLogic.cs" />
    <Compile Include="PriceCondition\PrcItemPriceConditionPrcItemLogic.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScriptHandler\CodeTemplateHandler.cs" />
    <Compile Include="ScriptHandler\PrcTotalJavaScriptHandler.cs" />
    <Compile Include="Logic\WarrantyObligationLogic.cs" />
    <Compile Include="Logic\WarrantySecurityLogic.cs" />
    <Compile Include="WorkflowEvent\PrcCommonWorkflowEventHelper.cs" />
    <Compile Include="Workflow\RecalculateProcurementAction.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Basics.Api.Common">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Api.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Currency.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Currency.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Export.Common">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Export.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Common">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MaterialCatalog.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MaterialCatalog.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.PriceCondition.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.PriceCondition.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.Core">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Boq.Main.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Boq.Main.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.TextModules.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.TextModules.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Material.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Material.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MasterData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MasterData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Infrastructure.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Infrastructure.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Scheduler.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceDomain">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceDomain.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Clerk.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Clerk.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementStructure.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementStructure.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Layout.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Layout.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.BusinessPartner.Certificate.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.BusinessPartner.Certificate.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Characteristic.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Characteristic.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Characteristic.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Characteristic.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Infrastructure.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Infrastructure.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.BusinessPartner.Contact.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.BusinessPartner.Contact.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.BusinessPartner.Main.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.BusinessPartner.Main.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Unit.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Unit.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Customize.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Customize.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Import.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Import.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostGroups.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostGroups.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostGroups.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostGroups.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.AssetMaster.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.AssetMaster.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Localization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Boq.Main.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Boq.Main.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.Localization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Jint">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Jint.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Runtime.Caching.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Security.Cryptography.ProtectedData.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Permissions">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Security.Permissions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ControllingCostCodes.BusinessComponents, Version=1.0.1296.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ControllingCostCodes.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostCodes.BusinessComponents, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostCodes.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.IndexTable.BusinessComponents, Version=3.2.201.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.IndexTable.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.BillingSchema.BusinessComponents, Version=24.2.53.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.BillingSchema.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core, Version=24.2.211.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.TaxCode.BusinessComponents, Version=25.1.145.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.TaxCode.BusinessComponents.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.Common\RIB.Visual.Procurement.Common.Common.csproj">
      <Project>{00008849-3AB3-4636-86DE-C0430490CAD5}</Project>
      <Name>RIB.Visual.Procurement.Common.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Common.Core\RIB.Visual.Procurement.Common.Core.csproj">
      <Project>{2BEA7191-C82A-4F53-99D9-6C756C089011}</Project>
      <Name>RIB.Visual.Procurement.Common.Core</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Common.Localization\RIB.Visual.Procurement.Common.Localization.csproj">
      <Project>{ABA42D58-77FF-4319-83CC-0E0668495AA5}</Project>
      <Name>RIB.Visual.Procurement.Common.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="EntityModel\DbContext.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <DevartEntityDeploy Include="EntityModel\EntityModel.edml">
      <Generator>DevartEfGenerator</Generator>
      <LastGenOutput>EntityModel.info</LastGenOutput>
      <SubType>Designer</SubType>
    </DevartEntityDeploy>
    <None Include="EntityModel\DataTransferObject.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.edps">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.info">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.MainDiagram.view">
      <DependentUpon>EntityModel.edml</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="EntityModel\Validation.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="RIBvisual.snk">
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Common.MetaModel.xml" />
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F&#xD;&#xA;" />
  </Target>
</Project>