/*
 * Copyright (c) RIB Software GmbH
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using RIB.Visual.Basics.Core.Core.Basics;
using RIB.Visual.Platform.BusinessComponents;

#nullable enable

// ReSharper disable once CheckNamespace
namespace RIB.Visual.Basics.Common.BusinessComponents
{
	/// <summary>
	/// Manages the list of active collaborators.
	/// </summary>
	[Export(typeof(IActiveCollaboratorsManager))]
	public partial class ActiveCollaboratorsLogic : LogicBase, IActiveCollaboratorsManager
	{
		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// Returns the list of active users in a given area and context.
		/// </summary>
		/// <param name="area">The area identifier.</param>
		/// <param name="context">The context identifier.</param>
		/// <param name="ignoreLoginCompany">Flag specifying whether to check activity of users from same company</param>
		/// <returns>The active users.</returns>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public IEnumerable<ActiveUserInfoEntity> GetActiveUsers(String area = "", String context = "", Boolean ignoreLoginCompany = false)
		{
			using var dbCtx = CreateDbContext();

			var users = dbCtx.Entities<ActiveUserInfoVEntity>().Where(au => au.Area == area && (au.Context == context || au.Context == ""));

			if (!ignoreLoginCompany)
			{
				var loginCompany = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId;
				users = users.Where(au => au.BasCompanyFk == loginCompany);
			}

			return [.. users.Select(au => new ActiveUserInfoEntity
			{
				DisplayName = au.Description,
				Id = au.FrmUserFk,
				EMail = au.Email
			})];
		}

		#region activity logging

		private static readonly Queue<(Action RegisterActivity, DateTime RequestTime)> NextActivitiesToLog = new();

		private static Task<Boolean>? _runner;

		/// <summary>
		/// Registers a user as recently having been active in one or more given contexts.
		/// </summary>
		/// <param name="userId">The user ID.</param>
		/// <param name="contextInfoFunc">A function that returns any number of context information tuples.</param>
		/// <exception cref="ArgumentNullException"><paramref name="contextInfoFunc"/> is <see langword="null"/>.</exception>
		public void LogActivity(Int32 userId, Func<IEnumerable<(String Area, String Context)>> contextInfoFunc)
		{
			ArgumentNullException.ThrowIfNull(contextInfoFunc);

			lock (NextActivitiesToLog)
			{
				NextActivitiesToLog.Enqueue((() =>
				{
					using var dbCtx = CreateDbContext();

					var allContextInfo = contextInfoFunc().ToArray();
					var loginCompany = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId;

					foreach (var ci in allContextInfo)
					{
						dbCtx.ExecuteStoredProcedure("BAS_LOG_ACTIVE_USER_SP",
							new SqlParameter { ParameterName = "@userId", SqlDbType = SqlDbType.Int, Value = userId },
							new SqlParameter { ParameterName = "@area", SqlDbType = SqlDbType.NVarChar, Size = 100, Value = ci.Area },
							new SqlParameter { ParameterName = "@context", SqlDbType = SqlDbType.NVarChar, Size = 100, Value = ci.Context },
							new SqlParameter { ParameterName = "@basCompanyFk", SqlDbType = SqlDbType.Int, Value = loginCompany });
					}
				}, DateTime.UtcNow));

				_runner ??= Task.Run(() =>
				{
					do
					{
						(Action RegisterActivity, DateTime RequestTime) currentRequest;
						lock (NextActivitiesToLog)
						{
							if (!NextActivitiesToLog.TryDequeue(out currentRequest))
							{
								_runner = null;
								break;
							}
						}

						if (DateTime.UtcNow > currentRequest.RequestTime.AddMinutes(10))
						{
							// discard requests that have been waiting for at least 10 minutes - data may already be outdated, anyway
							continue;
						}

						currentRequest.RegisterActivity();
					} while (true);

					return true;
				});
			}
		}

		#endregion

		private static readonly Lazy<ActiveCollaboratorsLogic> DefaultInstance = new();

		/// <summary>
		/// Returns a default instance of the class that may be used instead of instantiating a new instance.
		/// </summary>
		public static ActiveCollaboratorsLogic Default => DefaultInstance.Value;

		[GeneratedRegex(@"^RIB\.Visual\.(?<main>[^\.]+)\.(?<sub>[^\.]+)\.")]
		private static partial Regex GetModuleNamespacePattern();

		private static readonly Lazy<Regex> ModuleNamespacePattern = new(GetModuleNamespacePattern);

		/// <summary>
		/// Determines the default area identifier to retrieve possibly conflicting collaboration activities.
		/// </summary>
		/// <param name="obj">The object whose area identifier should be determined.</param>
		/// <returns>The area identifier.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="obj"/> is <see langword="null"/>.</exception>
		public static String DetermineDefaultArea(Object obj)
		{
			ArgumentNullException.ThrowIfNull(obj);

			var ns = obj.GetType().Namespace ?? "";
			var match = ModuleNamespacePattern.Value.Match(ns);

			if (match.Success)
			{
				return $"{match.Groups["main"].Value}.{match.Groups["sub"].Value}".ToLowerInvariant();
			}

			return "";
		}
	}
}
