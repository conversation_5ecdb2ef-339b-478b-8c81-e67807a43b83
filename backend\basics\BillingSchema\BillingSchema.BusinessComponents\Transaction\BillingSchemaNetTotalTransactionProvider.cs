using RIB.Visual.Basics.BillingSchema.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class BillingSchemaNetTotalTransactionProvider
	{
		/// <summary>
		/// 
		/// </summary>
		public string[] LineTypes { get { return new string[] { "1", "2" }; } }

		/// <summary>
		/// 
		/// </summary>
		public ITransactionPostingScope Scope { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<ITransactionEntity> Transactions { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSchema"></param>
		/// <returns></returns>
		public virtual decimal EvalDiscountAmount(IBillingSchemaPostingEntity billingSchema)
		{
			decimal discountAmount = 0.0m;
			var billingSchemas = this.Scope.Eaches["BillingSchemas"] as IEnumerable<IBillingSchemaPostingEntity>;

			if (billingSchema.LineType.HasValue && billingSchema.LineType.Value == 1)
			{
				var header = this.Scope.CurrentHeader;
				discountAmount = billingSchemas.Where(o => o.BillingLineTypeFk == (int)BillingLineType.EarlyPaymentDiscount || o.BillingLineTypeFk == (int)BillingLineType.EarlyPaymentDiscountRebate).Sum(o => o.ResultOc * (o.Factor.HasValue ? o.Factor.Value : 1));
			}

			return discountAmount;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="billingSchema"></param>
		public virtual void FillTransactionCommon(ITransactionEntity transaction, IBillingSchemaPostingEntity billingSchema)
		{
			var header = this.Scope.CurrentHeader;
			var scope = this.Scope as TransactionPostingScope;

			transaction.HeaderFk = header.Id;
			transaction.LineType = billingSchema.LineType.HasValue ? billingSchema.LineType.Value.ToString() : " ";
			transaction.DiscountAmount = this.EvalDiscountAmount(billingSchema);
			transaction.Amount = scope.GetValueRound(scope.EvalAmount(billingSchema));

			if (billingSchema.ControllingUnitFk.HasValue)
			{
				var controllingUnitEntity = scope.GetControllingUnit(billingSchema.ControllingUnitFk.Value);
				scope.FillControllingUnit(transaction, billingSchema.ControllingUnitFk.Value);
				scope.FillPostingType(transaction, controllingUnitEntity.Isassetmanagement);
			}

			transaction.IsSuccess = false;
			transaction.TransactionId = this.Scope.TransactionId;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSchemas"></param>
		/// <returns></returns>
		public virtual decimal EvalTargetVat(IEnumerable<IBillingSchemaPostingEntity> billingSchemas)
		{
			decimal targetVat = 0;
			var currentInvBillingSchemas = this.Scope.Eaches["BillingSchemas"] as IEnumerable<IBillingSchemaPostingEntity>;

			foreach (var item in billingSchemas)
			{
				if (item.BillingSchemaDetailTaxFk.HasValue)
				{
					var bs = currentInvBillingSchemas.FirstOrDefault(o => o.Id == item.BillingSchemaDetailTaxFk.Value);
					if (bs != null)
					{
						targetVat += bs.ResultOc;
					}
				}
			}

			return targetVat;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public virtual IEnumerable<IBillingSchemaPostingEntity> Filter(IEnumerable<IBillingSchemaPostingEntity> entities)
		{
			return entities.Where(e => e.LineType == 1 || e.LineType == 2).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> Create(IIdentifyable headerEntity)
		{
			var header = this.Scope.CurrentHeader;
			var entities = this.ProvideEntities(headerEntity);
			var transactions = new List<ITransactionEntity>();
			var result = new List<ITransactionEntity>();
			var scope = this.Scope as TransactionPostingScope;

			this.Scope.Eaches["BillingSchemas"] = entities;

			if (entities != null && entities.Any())
			{
				var targetBillingSchemas = this.Filter(entities);

				foreach (var billingSchema in targetBillingSchemas)
				{
					if (!scope.CheckBillingSchemaControllingUnit(billingSchema))
					{
						continue;
					}

					var transaction = this.Scope.TransactionLogic.Create(header.Id);

					this.FillTransactionCommon(transaction, billingSchema);
					this.FillTransactionOther(transaction, billingSchema);

					transactions.Add(transaction);
				}

				this.Scope.Eaches["TargetVat"] = this.EvalTargetVat(targetBillingSchemas);

				var groups = transactions.Select(e => new TransactionDecorator(e).DecorateControllingGroupSet(scope))
				.GroupBy(e => new { e.Entity.LineType, e.Entity.DocumentType, e.Entity.ControllingUnitFk, e.Entity.ControllingUnitCode, e.Entity.NominalAccount, e.Entity.CodeRetention, e.Entity.NominalDimension, e.Entity.NominalDimension2, e.Entity.NominalDimension3, e.Entity.VatCode, e.Entity.CompanyDeferalTypeFk, e.Entity.DateDeferalStart, e.ControllinggroupdetailText, e.ControllinggroupText, e.Entity.MatchText });

				// sum for AMOUNT, QUANTITY, VAT_AMOUNT group by all other fields 
				foreach (var group in groups)
				{
					var firstItem = group.First();
					var transaction = firstItem.Entity;

					transaction.Amount = group.Sum(e => e.Entity.Amount);
					transaction.Quantity = group.Sum(e => e.Entity.Quantity);
					transaction.VatAmount = group.Sum(e => e.Entity.VatAmount);

					if (firstItem.ControllingGrpSetDetails != null && firstItem.ControllingGrpSetDetails.Any())
					{
						scope.CreateControllingGrpSet(transaction, firstItem.ControllingGrpSetDetails);
					}

					if (this.CheckTransaction(transaction))
					{
						result.Add(transaction);
					}
				}
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <param name="transactions"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> AfterCreate(IIdentifyable headerEntity, IEnumerable<ITransactionEntity> transactions)
		{
			return transactions;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <returns></returns>
		public abstract IEnumerable<IBillingSchemaPostingEntity> ProvideEntities(IIdentifyable headerEntity);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <returns></returns>
		public abstract bool CheckTransaction(ITransactionEntity transaction);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="billingSchema"></param>
		public abstract void FillTransactionOther(ITransactionEntity transaction, IBillingSchemaPostingEntity billingSchema);

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public abstract int? ProvideHeaderControllingUnit();
	}
}
