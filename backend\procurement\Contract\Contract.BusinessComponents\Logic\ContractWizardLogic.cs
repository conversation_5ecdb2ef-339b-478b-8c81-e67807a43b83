using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RVPB = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Basics.Reporting.BusinessComponents;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using System.Collections.Concurrent;
using RIB.Visual.Procurement.RfQ.BusinessComponents;
using RIB.Visual.Platform.Common;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Procurement.Contract.BusinessComponents.Workflow;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Procurement.Contract.BusinessComponents.Entities;
using RIB.Visual.Procurement.Contract.BusinessComponents.Logic;
using System.Transactions;
using RIB.Visual.Boq.Wic.BusinessComponents;
using RIB.Visual.Basics.BillingSchema.BusinessComponents;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Basics.Clerk.BusinessComponents;
using RIB.Visual.BusinessPartner.Main.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Procurement.Contract.BusinessComponents
{
	/// <summary>GetPrcHeaderIdsFromFilteredContract
	///
	/// </summary>
	[Export(typeof(IContractWizardLogic))]
	public class ContractWizardLogic : IContractWizardLogic
	{
		/// <summary>
		///
		/// </summary>
		/// <param name="subsidiaryId"></param>
		/// <param name="statusIds"></param>
		/// <param name="DateFrom"></param>
		/// <param name="DateTo"></param>
		/// <returns></returns>
		public IEnumerable<int> GetPrcHeaderIdsFromFilteredContract(int subsidiaryId, IEnumerable<int> statusIds, DateTime? DateFrom, DateTime? DateTo)
		{
			IEnumerable<ConHeaderEntity> contracts = null;
			using (var dbContext = new RVPB.DbContext(ModelBuilder.DbModel))
			{
				var currentClientId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				var query = dbContext.Entities<ConHeaderEntity>().Where(e => e.CompanyFk == currentClientId && e.SubsidiaryFk == subsidiaryId && statusIds.Contains(e.ConStatusFk));
				if (DateFrom.HasValue)
				{
					query = query.Where(e => e.DateOrdered >= DateFrom.Value);
				}
				if (DateTo.HasValue)
				{
					query = query.Where(e => e.DateOrdered <= DateTo.Value);
				}

				contracts = query.ToList();
			}

			if (contracts == null || !contracts.Any())
			{
				return new List<int>();
			}

			return contracts.CollectIds(e => e.PrcHeaderFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// <param name="reportType"></param>
		/// <returns></returns>
		public IEnumerable<ReportEntity> GetReportsByConHeaderId(int conHeaderId, int reportType)
		{
			var reportList = new List<ReportEntity>();
			var reportsDict = GetContractReports(conHeaderId, reportType);
			var reportDictList = (List<Dictionary<string, object>>)reportsDict;

			foreach (var reportDict in reportDictList)
			{
				var reportParameterDictList = (List<Dictionary<string, object>>)reportDict["Parameter"];
				var reportParameterEntityList = new List<ReportParameterEntity>();

				foreach (var param in reportParameterDictList)
				{
					var reportParameterEntity = new ReportParameterEntity();

					if (param.ContainsKey("ParameterName"))
					{
						reportParameterEntity.ParameterName = param["ParameterName"].ToString();
					}

					if (param.ContainsKey("DataType"))
					{
						reportParameterEntity.DataType = param["DataType"].ToString();
					}

					if (param.ContainsKey("IsVisible"))
					{
						Boolean.TryParse(param["IsVisible"].ToString(), out var isVisible);
						reportParameterEntity.IsVisible = isVisible;
					}

					if (param.ContainsKey("Description"))
					{
						reportParameterEntity.DescriptionInfo = new DescriptionTranslateType(param["Description"].ToString());
					}

					if (param.ContainsKey("Value "))
					{
						reportParameterEntity.Default = param["Value "]?.ToString();
					}

					if (param.ContainsKey("Id"))
					{
						int.TryParse(param["Id"].ToString(), out var id);
						reportParameterEntity.Id = id;
					}

					reportParameterEntityList.Add(reportParameterEntity);
				}

				int.TryParse(reportDict["ReportID"].ToString(), out var reportID);

				var isDefault = false;
				if (reportDict.ContainsKey("IsDefault"))
				{
					bool.TryParse(reportDict["IsDefault"].ToString(), out isDefault);
				}

				var isMandatory = false;
				if (reportDict.ContainsKey("IsMandatory"))
				{
					bool.TryParse(reportDict["IsMandatory"].ToString(), out isMandatory);
				}

				var isNotPerso = false;
				if (reportDict.ContainsKey("IsNotPerso"))
				{
					bool.TryParse(reportDict["IsNotPerso"].ToString(), out isNotPerso);
				}

				var isCoverLetter = false;
				if (reportType == 1)
				{
					isCoverLetter = true;
				}
				object desc;
				reportDict.TryGetValue("Description", out desc);


				if (desc == null)
				{
					reportDict.TryGetValue("TemplateName", out desc);
				}
				var report = new RfqBidderWizardReportEntity
				{
					Name = new DescriptionTranslateType(reportDict["TemplateName"].ToString()),
					Description = new DescriptionTranslateType(desc.ToString()),
					FileName = reportDict["FileName"].ToString(),
					FilePath = reportDict["TemplatePath"].ToString(),
					Id = reportID,
					ReportParameterEntities = reportParameterEntityList,
					IsDefault = isDefault,
					IsMandatory = isMandatory,
					IsCoverLetter = isCoverLetter,
					IsNotPerso = isNotPerso
				};

				reportList.Add(report);
			}

			return reportList;
		}

		/// <summary>
		/// Gets contract reports extended with mandatory parameters
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// <param name="reportType"></param>
		/// <returns></returns>
		private IEnumerable<Dictionary<string, object>> GetContractReports(int conHeaderId, int reportType)
		{
			List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();
			ReportLogic reportLogic = new ReportLogic();
			var conLogic = new ConHeaderLogic();
			var conHeader = conLogic.GetById(new RIB.Visual.Platform.Core.IdentificationData() { Id = conHeaderId }); //.GetByLongId(conHeaderId);
			var prcHeader = new PrcHeaderLogic().GetById(new RIB.Visual.Platform.Core.IdentificationData() { Id = conHeader.PrcHeaderFk });
			var prcConfig2Reports = new PrcConfig2ReportLogic().GetSearchList(e => e.PrcConfigurationFk == prcHeader.ConfigurationFk && e.ReportType == reportType).ToList();

			if (prcConfig2Reports.Any())
			{
				foreach (var item in prcConfig2Reports)
				{
					Dictionary<string, object> result = new Dictionary<string, object>();
					List<Dictionary<string, object>> reportParams = new List<Dictionary<string, object>>();
					var report = reportLogic.GetReportById(item.BasReportFk);
					var reportParameters = new ReportParameterLogic().GetParameterByReportIdFiltered(item.BasReportFk);
					if (reportParameters.Any())
					{
						foreach (var param in reportParameters)
						{
							Dictionary<string, object> reportParam = new Dictionary<string, object>
							{
								{ "ParameterName", param.ParameterName },
								{ "DataType", param.DataType },
								{ "IsVisible", param.IsVisible },
								{ "Description", param.DescriptionInfo.Translated ?? param.DescriptionInfo.Description ?? param.ParameterName },
								{ "Id", param.Id }
							};
							switch (param.SysContext)
							{
								case ReportingSysContextItems.Parameter: //0
									reportParam.Add("Value ", param.Default);
									break;
								case ReportingSysContextItems.Company: //1
									reportParam.Add("Value ", BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId);
									break;
								case ReportingSysContextItems.ProfitCenter: //2
									reportParam.Add("Value ", BusinessApplication.BusinessEnvironment.CurrentContext.ClientId);
									break;
								case ReportingSysContextItems.Project: //3
									reportParam.Add("Value ", conHeader.ProjectFk);
									break;
								case ReportingSysContextItems.MainEntityId: //4
									reportParam.Add("Value ", conHeaderId);
									break;
								case ReportingSysContextItems.MainEntityIds: //5
									List<int> mainEntityIds = new List<int>
									{
										conHeaderId
									};
									reportParam.Add("Value ", mainEntityIds);
									break;
								case ReportingSysContextItems.UserId: //6
									reportParam.Add("Value ", BusinessApplication.BusinessEnvironment.CurrentContext.UserId);
									break;
								case ReportingSysContextItems.UserName: //7
									using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
									{
										string sqlForGetUserInfo = "SELECT ID,LOGONNAME,NAME FROM FRM_USER WHERE ID=" + BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
										var user = dbcontext.SqlQuery<UserInfoForReportParam>(sqlForGetUserInfo).ToList().FirstOrDefault();
										reportParam.Add("Value ", user.LogonName);
									}
									break;
								case ReportingSysContextItems.UserDescription: //8
									using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
									{
										string sqlForGetUserInfo = "SELECT ID,LOGONNAME,NAME FROM FRM_USER WHERE ID=" + BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
										var user = dbcontext.SqlQuery<UserInfoForReportParam>(sqlForGetUserInfo).ToList().FirstOrDefault();
										reportParam.Add("Value ", user.Name);
									}
									break;
								default:
									break;
							}
							reportParams.Add(reportParam);
						}
					}

					if (report != null)
					{
						reportLogic.Translate(new List<ReportEntity>() { report });
						result.Add("TemplateName", report.Name.Translated ?? report.Name.Description);
						result.Add("Description", report.Description.Translated ?? report.Description.Description);
						result.Add("FileName", report.FileName);
						result.Add("TemplatePath", report.FilePath);
						result.Add("ReportID", report.Id);
						result.Add("Parameter", reportParams);
						result.Add("IsDefault", item.IsDefault);
						if (reportType == 2)
						{
							result.Add("IsMandatory", item.IsMandatory);
							result.Add("IsNotPerso", item.IsNotPerSo);
						}
					}
					results.Add(result);
				}
			}
			return results;
		}

		/// <summary>
		///
		/// </summary>
		public class UserInfoForReportParam
		{
			/// <summary>
			///
			/// </summary>
			public int Id { get; set; }
			/// <summary>
			///
			/// </summary>
			public string LogonName { get; set; }
			/// <summary>
			///
			/// </summary>
			public string Name { get; set; }
		}

		/// <summary>
		/// GetDocumentsForContractConfirm
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// /// <param name="reqHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<DocumentResult> GetDocumentsForContractConfirm(int conHeaderId, int reqHeaderId = 0)
		{
			var executeParameters = new Dictionary<string, object>();
			var documentList = new List<DocumentResult>();

			executeParameters.Add("ConHeaderId", conHeaderId);
			executeParameters.Add("ReqId", reqHeaderId);

			IAction getDocumentsAction = new ContractConfirmDocumentsAction();
			var resultDic = getDocumentsAction.Execute(executeParameters);
			if (!(resultDic != null && resultDic.ContainsKey("Result")))
			{
				return documentList;
			}

			var result = resultDic["Result"];
			if (result == null)
			{
				return documentList;
			}

			var documents = result as List<DocumentResult>;
			if (documents == null)
			{
				return documentList;
			}

			return documents;
		}

		/// <summary>
		/// can not create WIC Catalog when not PrcBoq or more than 1 PrcBoq
		/// </summary>
		/// <param name="conHeaderFk"></param>
		/// <returns></returns>
		public (ConHeaderEntity, PrcBoqEntity) GetAndCheckConHeaderForWicCatalogCreate(int conHeaderFk)
		{
			var conHeader = new ConHeaderLogic().GetById(new Platform.Core.IdentificationData(conHeaderFk));
			var prcBoqLogic = new PrcBoqLogic();
			var prcBoqList = prcBoqLogic.GetList(conHeader.PrcHeaderFk);
			if (!prcBoqList.Any())
			{
				throw new Exception("There is no BoQ existed!");
			}

			if (prcBoqList.Count() > 1)
			{
				throw new Exception("There are more than one BoQ existed and not allow to create WIC catalog!");
			}

			return (conHeader, prcBoqList.FirstOrDefault());
		}

		/// <summary>
		/// CreateWicCatalog
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		public IWicBoqEntity CreateWicCatalog(CreateWicCatalogParameter parameter)
		{
			var (conHeader, prcBoq) = GetAndCheckConHeaderForWicCatalogCreate(parameter.ConHeaderFk);

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				var wicCatBoq = Injector.Get<IWicBoqLogic>().CreateWicBoqByContract(parameter.WicGroupFk, prcBoq.BoqHeaderFk, conHeader.BusinessPartnerFk, conHeader.SubsidiaryFk,
																													conHeader.SupplierFk, null, parameter.ValidFrom, parameter.ValidTo, parameter.PaymentTermPaFk, parameter.ClerkPrcFk,
																													parameter.WicTypeFk, parameter.PaymentTermFiFk, parameter.PaymentTermAdFk);

				var conHeader2BoqWicCatBoqLogic = new ConHeader2BoqWicCatBoqLogic();
				var entity = conHeader2BoqWicCatBoqLogic.GetByFilter(r => r.BoqHeaderFk == prcBoq.BoqHeaderFk && r.ConHeaderFk == parameter.ConHeaderFk && r.BoqWicCatBoqFk == wicCatBoq.Id).FirstOrDefault();
				if (entity == null)
				{
					entity = conHeader2BoqWicCatBoqLogic.Create(prcBoq.BoqHeaderFk, parameter.ConHeaderFk, wicCatBoq.Id);
					conHeader2BoqWicCatBoqLogic.Save(entity);
				}

				transaction.Complete();

				return wicCatBoq;
			}

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		public IWicBoqEntity UpdateWicCatalog(CreateWicCatalogParameter parameter)
		{
			var (conHeader, prcBoq) = GetAndCheckConHeaderForWicCatalogCreate(parameter.ConHeaderFk);
			var wicCatBoqLogic = new WicBoqLogic();
			var wicCatBoqEntity = wicCatBoqLogic.GetItemById(parameter.WicCatBoqId);
			wicCatBoqEntity.MdcWicTypeFk = parameter.WicTypeFk.Value;
			wicCatBoqEntity.ValidFrom = parameter.ValidFrom;
			wicCatBoqEntity.ValidTo = parameter.ValidTo;
			wicCatBoqEntity.BasClerkFk = parameter.ClerkPrcFk;
			wicCatBoqEntity.BasPaymentTermAdFk = parameter.PaymentTermAdFk;
			wicCatBoqEntity.BasPaymentTermFiFk = parameter.PaymentTermFiFk;
			wicCatBoqEntity.BasPaymentTermFk = parameter.PaymentTermPaFk;
			wicCatBoqLogic.UpdateWicBoqByContract(wicCatBoqEntity, prcBoq.BoqHeaderFk);
			return wicCatBoqEntity;
		}

		/// <summary>w
		/// get BoqItem
		/// </summary>
		/// <param name="conHeaderFk"></param>
		/// <returns></returns>
		public BoqItemEntity GetBoqItemById(int conHeaderFk)
		{
			var (conHeader, prcBoq) = GetAndCheckConHeaderForWicCatalogCreate(conHeaderFk);
			var rootItem = new BoqItemLogic().GetSearchList(e => e.BoqHeaderFk == prcBoq.BoqHeaderFk && e.BoqLineTypeFk == (int)BoqLineType.Root).FirstOrDefault();

			return rootItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		public ContractCreateComplete CreateContract(CreateContractWizardParameter parameter)
		{
			var conheaderLogic = new ConHeaderLogic();
			ContractComplete contractComplete = new ContractComplete();
			ContractCreateComplete contractCompleteEntity = new ContractCreateComplete();

			switch (parameter.CreateType)
			{
				//create new Contract
				case PurchaseOrders.PurchaseOrder:
					contractCompleteEntity = CreateEntityByWizard(parameter, contractComplete);
					return contractCompleteEntity;
				//create Call Of
				case PurchaseOrders.CallOf:
					return CreateCallOf(parameter);
				//create Change Order
				case PurchaseOrders.ChangeOrder:
					return CreateChangeOrder(parameter);
				//create FrameWork Contract
				case PurchaseOrders.FrameWorkCallOff:
					contractCompleteEntity = CreateEntityByWizard(parameter, contractComplete);
					CreateFrameWork(parameter, contractCompleteEntity, contractComplete);
					break;
			}
			contractComplete.ConHeader = contractCompleteEntity.ConHeaderEntity;
			//general billing schema
			contractComplete.BillingSchemaToSave = GeneralBillingSchema(contractComplete.ConHeader);
			//general Characteristics Data
			CharacteristicDataParam dataParam = new CharacteristicDataParam
			{
				mainItemId = contractCompleteEntity.ConHeaderEntity.Id,
				sourceHeaderId = contractCompleteEntity.ConHeaderEntity.PrcHeaderEntity.ConfigurationFk,
				targetHeaderId = contractCompleteEntity.ConHeaderEntity.Id
			};
			GenerateCertificateGenerals(contractComplete, parameter.CreateType);
			conheaderLogic.Update(contractComplete);
			GeneralCharacterData(dataParam, contractComplete.ConHeader);
			contractCompleteEntity.ConHeaderEntity.PurchaseOrders = (int?)parameter.CreateType;
			contractCompleteEntity.ConHeaderEntity.ContractHeaderFk = parameter.ConHeaderEntity.ConHeaderFk ?? null;
			return contractCompleteEntity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <param name="contractComplete"></param>
		private static ContractCreateComplete CreateEntityByWizard(CreateContractWizardParameter parameter, ContractComplete contractComplete)
		{
			var conheaderLogic = new ConHeaderLogic();
			int? configurationFk = parameter.ConHeaderEntity.ConfigurationFk;

			ContractCreateComplete contractCompleteEntity = conheaderLogic.CreateEntity(parameter.ConHeaderEntity.ProjectFk, null, configurationFk, parameter.ConHeaderEntity.BusinessPartnerFk, false, null, false, null, null, parameter.ConHeaderEntity.SupplierFk);
			contractComplete.MainItemId = contractCompleteEntity.ConHeaderEntity.Id;
			contractComplete.TotalToSave = contractCompleteEntity.ConTotals;
			contractComplete.PrcHeaderblobToSave = contractCompleteEntity.PrcHeaderBlob;
			contractCompleteEntity.ConHeaderEntity.SubsidiaryFk = parameter.ConHeaderEntity.SubsidiaryFk;
			contractCompleteEntity.ConHeaderEntity.SupplierFk = parameter.ConHeaderEntity.SupplierFk;
			contractCompleteEntity.ConHeaderEntity.ControllingUnitFk = parameter.ConHeaderEntity.ControllingUnitFk;

			contractCompleteEntity.ConHeaderEntity.ContactFk = parameter.ConHeaderEntity.ContactFk;
			contractCompleteEntity.ConHeaderEntity.ClerkPrcFk = parameter.ConHeaderEntity.ClerkPrcFk;
			contractCompleteEntity.ConHeaderEntity.ClerkReqFk = parameter.ConHeaderEntity.ClerkReqFk;
			if (parameter.CreateType == PurchaseOrders.PurchaseOrder)
			{
				contractCompleteEntity.ConHeaderEntity.Code = parameter.ConHeaderEntity.Code;
				contractCompleteEntity.ConHeaderEntity.PrcHeaderEntity.StructureFk = parameter.StructureFk;
				contractCompleteEntity.ConHeaderEntity.TaxCodeFk = parameter.ConHeaderEntity.TaxCodeFk;
				contractCompleteEntity.ConHeaderEntity.BpdVatGroupFk = parameter.ConHeaderEntity.BpdVatGroupFk;
				contractCompleteEntity.ConHeaderEntity.BankFk = parameter.ConHeaderEntity.BankFk;
				contractCompleteEntity.ConHeaderEntity.ContactFk = parameter.ConHeaderEntity.ContactFk;
				contractCompleteEntity.ConHeaderEntity.Description = parameter.ConHeaderEntity.Description;
				contractCompleteEntity.ConHeaderEntity.PrcHeaderBlob = contractCompleteEntity.PrcHeaderBlob;
				contractCompleteEntity.ConHeaderEntity.ConTotals = contractCompleteEntity.ConTotals;
				GenerateCertificateAndGenerals(contractCompleteEntity.ConHeaderEntity);
				GetClerkFk(contractCompleteEntity.ConHeaderEntity);
				return contractCompleteEntity;
			}

			if (contractCompleteEntity.ConHeaderEntity.PrcHeaderEntity != null)
			{
				contractCompleteEntity.ConHeaderEntity.PrcHeaderEntity.StructureFk = parameter.StructureFk;
			}
			return contractCompleteEntity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="conHeaderEntity"></param>
		private static void GetClerkFk(ConHeaderEntity conHeaderEntity)
		{
			var prcHeaderLogic = new PrcHeaderLogic();
			int? structureFk = conHeaderEntity.PrcHeaderEntity.StructureFk;
			if (structureFk.HasValue)
			{
				int? prcClerk = prcHeaderLogic.GetEntityPrcClerkFk(structureFk.Value, conHeaderEntity.ProjectFk, conHeaderEntity.CompanyFk);
				int? reqClerk = prcHeaderLogic.GetEntityReqClerkFk(structureFk.Value, conHeaderEntity.ProjectFk, conHeaderEntity.CompanyFk);
				conHeaderEntity.ClerkPrcFk = prcClerk.HasValue ? prcClerk : conHeaderEntity.ClerkPrcFk;
				conHeaderEntity.ClerkReqFk = prcClerk.HasValue ? reqClerk : conHeaderEntity.ClerkReqFk;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contract"></param>
		private static void GenerateCertificateAndGenerals(ConHeaderEntity contract)
		{
			//Generals
			var generalsLogic = new PrcGeneralsLogic();
			var generalItems = generalsLogic.ReloadGenerals(contract.Id, contract.PrcHeaderEntity.ConfigurationFk, contract.PrcHeaderEntity.StructureFk, contract.ProjectFk, contract.ControllingUnitFk);
			foreach (var item in generalItems)
			{
				item.PrcHeaderFk = contract.PrcHeaderFk;
			}
			contract.PrcGenerals = generalItems;

			//Certificate
			var certificateLogic = new PrcCertificateLogic();
			var certificateEntities = certificateLogic.CreateCertificatesFormConfig(contract.Id, contract.PrcHeaderEntity.ConfigurationFk, contract.PrcHeaderEntity.StructureFk);
			foreach (var item in certificateEntities)
			{
				item.PrcHeaderFk = contract.PrcHeaderFk;
			}
			contract.PrcCertificate = certificateEntities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		private ContractCreateComplete CreateCallOf(CreateContractWizardParameter parameter)
		{
			var conheaderLogic = new ConHeaderLogic();
			var callOfEntity = conheaderLogic.GetContractById(parameter.ConHeaderEntity.ConHeaderFk.Value);
			callOfEntity.ConHeaderFk = parameter.ConHeaderEntity.ConHeaderFk.Value;
			callOfEntity.ProjectChangeFk = null;
			callOfEntity.DateOrdered = DateTime.UtcNow;
			var callOfComplete = conheaderLogic.DoCreateDeepCopy(callOfEntity, false, "", callOfEntity.Description, false, true);
			callOfComplete.ConHeader.PurchaseOrders = (int?)PurchaseOrders.CallOf;
			ContractCreateComplete contractCreate = new ContractCreateComplete
			{
				ConHeaderEntity = callOfComplete.ConHeader
			};
			return contractCreate;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		private ContractCreateComplete CreateChangeOrder(CreateContractWizardParameter parameter)
		{
			var conheaderLogic = new ConHeaderLogic();
			var changeOrderEntity = conheaderLogic.GetContractById(parameter.ConHeaderEntity.ConHeaderFk.Value);
			changeOrderEntity.ConHeaderFk = parameter.ConHeaderEntity.ConHeaderFk.Value;
			changeOrderEntity.ProjectChangeFk = parameter.ConHeaderEntity.ProjectChangeFk;
			changeOrderEntity.DateOrdered = DateTime.UtcNow;
			var changeOrderComplete = conheaderLogic.DoCreateDeepCopy(changeOrderEntity, false, "", changeOrderEntity.Description, false, true);
			changeOrderComplete.ConHeader.PurchaseOrders = (int?)PurchaseOrders.ChangeOrder;
			ContractCreateComplete contractCreate = new ContractCreateComplete
			{
				ConHeaderEntity = changeOrderComplete.ConHeader
			};
			return contractCreate;

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <param name="contractCompleteEntity"></param>
		/// <param name="contract"></param>
		private static void CreateFrameWork(CreateContractWizardParameter parameter, ContractCreateComplete contractCompleteEntity, ContractComplete contract)
		{
			var prcGeneralsLogic = new PrcGeneralsLogic();
			contractCompleteEntity.ConHeaderEntity.ConHeaderFk = parameter.ConHeaderEntity.ConHeaderFk;
			contractCompleteEntity.ConHeaderEntity.BoqWicCatFk = parameter.ConHeaderEntity.BoqWicCatFk;
			contractCompleteEntity.ConHeaderEntity.MaterialCatalogFk = parameter.ConHeaderEntity.MaterialCatalogFk;
			contractCompleteEntity.ConHeaderEntity.BoqWicCatBoqFk = parameter.ConHeaderEntity.BoqWicCatBoqFk;
			contractCompleteEntity.ConHeaderEntity.PaymentTermPaFk = parameter.ConHeaderEntity.PaymentTermPaFk;
			contractCompleteEntity.ConHeaderEntity.PaymentTermFiFk = parameter.ConHeaderEntity.PaymentTermFiFk;
			contractCompleteEntity.ConHeaderEntity.PaymentTermAdFk = parameter.ConHeaderEntity.PaymentTermAdFk;
			if (parameter.ConHeaderEntity.MaterialCatalogFk.HasValue)
			{
				contractCompleteEntity.ConHeaderEntity.PrcCopyModeFk = (int)CopyMode.OnlyAllowedCatalogs;
			}
			contractCompleteEntity.ConHeaderEntity.IncotermFk = parameter.ConHeaderEntity.IncotermFk;
			contractCompleteEntity.ConHeaderEntity.BpdVatGroupFk = parameter.ConHeaderEntity.BpdVatGroupFk;
			contractCompleteEntity.ConHeaderEntity.BankFk = parameter.ConHeaderEntity.BankFk;
			if (parameter.ConHeaderEntity.MaterialCatalogFk.HasValue)
			{
				GenerateRestrictions(contract, contractCompleteEntity);
			}
			var generals = prcGeneralsLogic.ReloadGeneralsByBp(contractCompleteEntity.ConHeaderEntity.PrcHeaderFk, parameter.ConHeaderEntity.BusinessPartnerFk,null,null);
			contract.PrcGeneralsToSave = generals;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="conHeader"></param>
		/// <returns></returns>
		private static IEnumerable<CommonBillingSchemaEntity> GeneralBillingSchema(ConHeaderEntity conHeader)
		{
			var logic = new CommonBillingSchemaLogic("procurement.contract.billingschmema");
			var entities = logic.Create(conHeader.Id, conHeader.BillingSchemaFk, conHeader.RubricCategoryFk);
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dataParam"></param>
		/// <param name="conHeader"></param>
		/// <returns></returns>
		private static void GeneralCharacterData(CharacteristicDataParam dataParam, ConHeaderEntity conHeader)
		{
			var logic = new CharacteristicDataLogic();

			dataParam.sectionId = (int)CharacteristicSec.Characteristic1SectionId;
			dataParam.targetSectionId = (int)CharacteristicSec.Characteristic1SectionId;
			dataParam.sourceSectionId = (int)CharacteristicSec.Characteristic1SourceSectionId;
			List<CharacteristicDataEntity> characteristicDataEntities = GetCharacterDataList(dataParam);

			var defaultCharacterData = GetDefaultCharacterData(dataParam);
			AddRangeUniqueByField(characteristicDataEntities, defaultCharacterData.ToList());
			var characterDataBySec = GetDefaultCharacterDataBySec((int)CharacteristicSec.Characteristic2SectionId);
			AddRangeUniqueByField(characteristicDataEntities, characterDataBySec.ToList());

			CharacteristicDataCompleteEntity characteristicDataCompleteEntity = new CharacteristicDataCompleteEntity()
			{
				CharacteristicDataToSave = characteristicDataEntities,
				MainItemId = conHeader.Id
			};
			logic.UpdateEntity(characteristicDataCompleteEntity);


			dataParam.sectionId = (int)CharacteristicSec.Characteristic2SectionId;
			dataParam.targetSectionId = (int)CharacteristicSec.Characteristic2SectionId;
			dataParam.sourceSectionId = (int)CharacteristicSec.Characteristic2SourceSectionId;

			List<CharacteristicDataEntity> characteristicData2Entities = GetCharacterDataList(dataParam);

			var defaultCharacter2Data = GetDefaultCharacterData(dataParam);

			AddRangeUniqueByField(characteristicData2Entities, defaultCharacter2Data.ToList());

			CharacteristicDataCompleteEntity characteristicData2CompleteEntity = new CharacteristicDataCompleteEntity()
			{
				CharacteristicDataToSave = characteristicData2Entities,
				MainItemId = conHeader.Id
			};
			logic.UpdateEntity(characteristicData2CompleteEntity);

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="list"></param>
		/// <param name="newItems"></param>
		public static void AddRangeUniqueByField(List<CharacteristicDataEntity> list, List<CharacteristicDataEntity> newItems)
		{
			var existingIds = new HashSet<int>(list.Select(p => p.CharacteristicFk));
			foreach (var item in newItems.Where(item => item.Id > 0 && !existingIds.Contains(item.CharacteristicFk)))
			{
				list.Add(item);
				existingIds.Add(item.CharacteristicFk);
			}
		}

		/// <summary>
		/// Get Default CharacterData By Section
		/// </summary>
		/// <param name="sectionId"></param>
		/// <returns></returns>
		private static IEnumerable<CharacteristicDataEntity> GetDefaultCharacterDataBySec(int sectionId)
		{
			var logic = new CharacteristicDataLogic();
			var entities = logic.GetDefaults(sectionId).OrderBy(o => o.CharacteristicEntity.Code);
			return entities;
		}

		/// <summary>
		///  get characteristic data
		/// </summary>
		/// <param name="dataParam"></param>
		/// <returns></returns>
		private static IEnumerable<CharacteristicDataEntity> GetDefaultCharacterData(CharacteristicDataParam dataParam)
		{
			var logic = new CharacteristicDataLogic();
			var dtos = logic.GetDefaults(dataParam.sectionId, new IdentificationData()
			{
				Id = dataParam.mainItemId,
				PKey1 = dataParam.pKey1,
				PKey2 = dataParam.pKey2,
				PKey3 = dataParam.pKey3
			}).OrderBy(o => o.CharacteristicEntity.Code);
			return dtos;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dataParam"></param>
		/// <returns></returns>
		private static List<CharacteristicDataEntity> GetCharacterDataList(CharacteristicDataParam dataParam)
		{
			var logic = new CharacteristicDataLogic();
			var entity = logic.CopyCharacteristicData(new IdentificationData
			{
				Id = dataParam.sourceHeaderId,
				PKey1 = dataParam.sourcePKey1,
				PKey2 = dataParam.sourcePKey2,
				PKey3 = dataParam.sourcePKey3
			}, new IdentificationData
			{
				Id = dataParam.targetHeaderId,
				PKey1 = dataParam.targetPKey1,
				PKey2 = dataParam.targetPKey2,
				PKey3 = dataParam.targetPKey3
			}, dataParam.sourceSectionId, dataParam.targetSectionId);

			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contract"></param>
		/// <param name="createType"></param>
		private static void GenerateCertificateGenerals(ContractComplete contract, PurchaseOrders createType)
		{
			//Generals
			if (createType != PurchaseOrders.FrameWorkCallOff)
			{
				var generalsLogic = new PrcGeneralsLogic();
				var generalItems = generalsLogic.ReloadGenerals(contract.ConHeader.Id, contract.ConHeader.PrcHeaderEntity.ConfigurationFk, contract.ConHeader.PrcHeaderEntity.StructureFk, contract.ConHeader.ProjectFk, contract.ConHeader.ControllingUnitFk);
				foreach (var item in generalItems)
				{
					item.PrcHeaderFk = contract.ConHeader.PrcHeaderFk;
				}
				contract.PrcGeneralsToSave = generalItems;
			}

			//Certificate
			var certificateLogic = new PrcCertificateLogic();
			var certificateEntities = certificateLogic.CreateCertificatesFormConfig(contract.ConHeader.Id, contract.ConHeader.PrcHeaderEntity.ConfigurationFk, contract.ConHeader.PrcHeaderEntity.StructureFk);
			foreach (var item in certificateEntities)
			{
				item.PrcHeaderFk = contract.ConHeader.PrcHeaderFk;
			}
			contract.PrcCertificateToSave = certificateEntities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contract"></param>
		/// <param name="contractCompleteEntity"></param>
		private static void GenerateRestrictions(ContractComplete contract, ContractCreateComplete contractCompleteEntity)
		{
			var conMasterRestriction = new ConMasterRestrictionLogic().CreateNewItem(contract.MainItemId);
			conMasterRestriction.CopyType = (int)CopyType.Material;
			conMasterRestriction.MdcMaterialCatalogFk = contractCompleteEntity.ConHeaderEntity.MaterialCatalogFk;
			contract.ConMasterRestrictionToSave = new[] { conMasterRestriction };
		}

		/// <summary>
		/// get default clerk and vat group
		/// </summary>
		/// <param name="supplierFk"></param>
		/// <returns></returns>
		public ConHeaderEntity GetVatGroupBySupplier(int? supplierFk)
		{
			ConHeaderEntity conHeader = new ConHeaderEntity();
			var vatGroupLogic = Injector.Get<IVatGroupLogic>();
			var loginClerk = new BasicsClerkLogic().GetLoginClerk();

			if (loginClerk != null)
			{
				conHeader.ClerkPrcFk = loginClerk.Id;
			}

			var vatGroupDefault = vatGroupLogic.GetVatGroupDefault();
			if (vatGroupDefault != null)
			{
				conHeader.BpdVatGroupFk = vatGroupDefault.Id;
			}

			if (supplierFk.HasValue)
			{
				conHeader.BpdVatGroupFk = GetSupplierVatGroup(supplierFk.Value);
			}

			return conHeader;
		}

		private static int? GetSupplierVatGroup(int supplierFk)
		{
			var supplierCompanyLogic = new SupplierCompanyLogic();
			var clientId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;

			var suppliers = BusinessApplication.BusinessEnvironment
				 .GetExportedValue<ISupplierInfoProviderLogic>()
				 .GetSupplierByIDs(new[] { supplierFk });

			var companySuppliers = supplierCompanyLogic
				 .GetSearchList(e => e.BpdSupplierFk == supplierFk && e.BasCompanyFk == clientId);

			var supplier = suppliers.FirstOrDefault(e => e.Id == supplierFk);
			var companySupplier = companySuppliers.FirstOrDefault(e => e.BpdSupplierFk == supplierFk);

			return DetermineVatGroup(companySupplier, supplier);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="companySupplier"></param>
		/// <param name="supplier"></param>
		/// <returns></returns>
		private static int? DetermineVatGroup(SupplierCompanyEntity companySupplier, ISupplierEntity supplier)
		{
			return companySupplier?.VatGroupFk ?? supplier?.VatGroupFk;
		}
	}

	/// <summary>
	///
	/// </summary>
	public class CharacteristicDataParam
	{
		/// <summary>
		///
		/// </summary>
		public int sourceHeaderId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? sourcePKey1 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? sourcePKey2 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? sourcePKey3 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int targetHeaderId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? targetPKey1 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? targetPKey2 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? targetPKey3 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int sourceSectionId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int targetSectionId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int sectionId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int mainItemId { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? pKey1 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? pKey2 { get; set; }
		/// <summary>
		///
		/// </summary>
		public int? pKey3 { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public enum CharacteristicSec
	{
		/// <summary>
		///
		/// </summary>
		Characteristic1SectionId = 8,
		/// <summary>
		///
		/// </summary>
		Characteristic1TargetSectionId = 8,
		/// <summary>
		///
		/// </summary>
		Characteristic1SourceSectionId = 32,

		/// <summary>
		///
		/// </summary>
		Characteristic2SectionId = 46,
		/// <summary>
		///
		/// </summary>
		Characteristic2TargetSectionId = 46,
		/// <summary>
		///
		/// </summary>
		Characteristic2SourceSectionId = 55,
	}
	/// <summary>
	///
	/// </summary>
	public enum CopyMode
	{
		/// <summary>
		///
		/// </summary>
		NoRestrictions = 1,
		/// <summary>
		///
		/// </summary>
		CurrentPackageOnly = 2,
		/// <summary>
		///
		/// </summary>
		OnlyAllowedCatalogs = 3,
		/// <summary>
		///
		/// </summary>
		NoRestrictionsForStandardUser = 4,
	}
}
