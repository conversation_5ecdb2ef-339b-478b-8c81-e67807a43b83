using System.Collections.Generic;
using RVPC = RIB.Visual.Platform.Core;

namespace RIB.Visual.Basics.Core.Core
{
	/// <summary>
	/// 
	/// </summary>
	public interface IPackageGenerateBoQWizardLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		int GeneratePackageBoQFromProjectBoQ(GeneratePackageBoQFromBoQParam param);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		int GeneratePackageBoQFromWicBoQ(GeneratePackageBoQFromBoQParam param);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="packageId"></param>
		/// <param name="prcHeaderId"></param>
		/// <param name="isControllingUnitAsTitle"></param>
		/// <param name="considerBoqQtyRelation"></param>
		/// <param name="uniqueFields"></param>
		/// <param name="lineItemsWithoutResource"></param>
		/// <param name="newResources"></param>
		/// <param name="isMarkupCostCode">
		/// true - when summing up the cost total, count in the cost total of resources of which COSTCODE.IsCost is false if there are such kind of resuorces.
		/// false - ignore such kind of resources.
		/// </param>
		int CreatePackageBoqFromLineItem(
			int packageId,
			int prcHeaderId,
			bool isControllingUnitAsTitle,
			bool considerBoqQtyRelation,
			IEnumerable<IUniqueFieldProfileEntity> uniqueFields,
			IEnumerable<IEstLineItemEntity> lineItemsWithoutResource,
			IEnumerable<IScriptEstResource> newResources,
			bool isMarkupCostCode);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="packageId"></param>
		/// <param name="prcHeaderId"></param>
		/// <param name="newResources"></param>
		/// <param name="isMarkupCostCode"></param>
		/// <param name="boqStructure4SourceResource"></param>
		/// <param name="isSkipBoqPositionAsDivisionBoq"></param>
		/// <param name="estHeader"></param>
		/// <returns></returns>
		int CreatePackageBoqFromResource(
		 	int packageId,
			int prcHeaderId,
			IEnumerable<IScriptEstResource> newResources,
			int boqStructure4SourceResource,
			bool isSkipBoqPositionAsDivisionBoq,
			IEstHeaderEntity estHeader
		 );
	}

	/// <summary>
	/// 
	/// </summary>
	public class DragNDropInfo
	{
		/// <summary>
		/// 
		/// </summary>
		public int ProjectId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstHeaderId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsForcedToCreateBoq { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int SourceBoqHeaderId { get; set; }

		/// <summary>
		/// This is for the case that new package boq item is created.
		/// </summary>
		public IDictionary<int, int> SourceBoqItemId2NewBoqItemIdMap { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int TargetBoqHeaderId { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public class GeneratePackageBoQFromBoQParam
	{
		/// <summary>
		/// 
		/// </summary>
		public int PackageId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int PrcHeaderId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string QuantityTransferFrom { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool ConsiderBoqQtyRelation { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<IEstLineItemEntity> LineItemsWithoutResource { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<IScriptEstResource> NewResources { get; set; }

		/// <summary>
		/// true - when summing up the cost total, count in the cost total of resources of which COSTCODE.IsCost is false if there are such kind of resuorces.
		/// false - ignore such kind of resources.
		/// </summary>
		public bool IsMarkupCostCode { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<IBoqItemBaseInfo> NodeBoqItemsToSave { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DragNDropInfo DragNDropInfo { get; set; }
	}
}
