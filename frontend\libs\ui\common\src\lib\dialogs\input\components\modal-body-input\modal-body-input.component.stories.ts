/*
 * Copyright(c) RIB Software GmbH
 */

import { moduleMetadata } from '@storybook/angular';
import { Story, Meta } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ModalBodyInputComponent, INPUT_DLG_OPTIONS_TOKEN } from './modal-body-input.component';
import { IInputDialogOptions } from '../../model/input-dialog-options.interface';
import { ICustomDialog, getCustomDialogDataToken } from '../../../base';

export default {
  title: 'UI Common/Dialogs/Input/ModalBodyInput',
  component: ModalBodyInputComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FormsModule],
      declarations: [ModalBodyInputComponent],
      providers: [
        {
          provide: INPUT_DLG_OPTIONS_TOKEN,
          useValue: {
            headerText: 'Input Context ID',
            bodyText: 'Please Enter A value',
            pattern: '[a-zA-Z ]*',
            width: '30%',
            maxLength: 20,
            type: 'text',
          } as IInputDialogOptions
        },
        {
          provide: getCustomDialogDataToken<string, ModalBodyInputComponent>(),
          useValue: {
            value: '',
            get body(): ModalBodyInputComponent {
              return {} as ModalBodyInputComponent;
            },
            close() {},
            setAlarmConfig() {}
          } as ICustomDialog<string, ModalBodyInputComponent>
        }
      ]
    })
  ],
} as Meta<ModalBodyInputComponent>;

const Template: Story<ModalBodyInputComponent> = (args) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {};

export const WithValue = Template.bind({});
WithValue.args = {
  value: 'Sample input value'
};

export const WithNumericPattern = Template.bind({});
WithNumericPattern.args = {};
WithNumericPattern.decorators = [
  moduleMetadata({
    providers: [
      {
        provide: INPUT_DLG_OPTIONS_TOKEN,
        useValue: {
          headerText: 'Enter PIN',
          bodyText: 'Please Enter 4 numbers',
          pattern: '^\\d{4}$',
          maxLength: 4,
          type: 'text',
        } as IInputDialogOptions
      }
    ]
  })
];

export const WithPlaceholder = Template.bind({});
WithPlaceholder.args = {};
WithPlaceholder.decorators = [
  moduleMetadata({
    providers: [
      {
        provide: INPUT_DLG_OPTIONS_TOKEN,
        useValue: {
          headerText: 'Enter Email',
          bodyText: 'Please enter your email address',
          placeholder: '<EMAIL>',
          pattern: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$',
          type: 'email',
        } as IInputDialogOptions
      }
    ]
  })
];

export const WithPassword = Template.bind({});
WithPassword.args = {};
WithPassword.decorators = [
  moduleMetadata({
    providers: [
      {
        provide: INPUT_DLG_OPTIONS_TOKEN,
        useValue: {
          headerText: 'Enter Password',
          bodyText: 'Please enter your password',
          type: 'password',
          maxLength: 16,
        } as IInputDialogOptions
      }
    ]
  })
];


