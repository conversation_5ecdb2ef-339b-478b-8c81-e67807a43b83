using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class BillingSchemaSubTotalTransactionProvider
	{
		/// <summary>
		/// 
		/// </summary>
		public string[] LineTypes { get { return new string[] { "3", "4", "5", "6", }; } }

		/// <summary>
		/// 
		/// </summary>
		public abstract string Description { get; }

		/// <summary>
		/// 
		/// </summary>
		public ITransactionPostingScope Scope { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<ITransactionEntity> Transactions { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> ProvideRealCostTransactions()
		{
			var scope = this.Scope as TransactionPostingScope;
			var successTransactions = scope.GetCurrentSuccessTransactions();

			return scope.TransactionsToSave.Concat(successTransactions)
					.Where(e => e.Amount != 0 &&
					!string.IsNullOrWhiteSpace(e.LineType) &&
					(Convert.ToInt32(e.LineType) == 10 || Convert.ToInt32(e.LineType) == 11 || Convert.ToInt32(e.LineType) == 12)
					).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public virtual IEnumerable<IBillingSchemaPostingEntity> Filter(IEnumerable<IBillingSchemaPostingEntity> entities)
		{
			return entities.Where(e => e.LineType >= 3 && e.LineType <= 6).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="billingSchema"></param>
		public virtual void FillTransactionCommon(ITransactionEntity transaction, IBillingSchemaPostingEntity billingSchema)
		{
			var header = this.Scope.CurrentHeader;
			var scope = this.Scope as TransactionPostingScope;

			transaction.HeaderFk = header.Id;
			transaction.IsSuccess = false;
			transaction.TransactionId = this.Scope.TransactionId;
			transaction.LineType = billingSchema.LineType.ToString();

			if (billingSchema.TaxCodeFk.HasValue)
			{
				scope.FillTaxCode(transaction, billingSchema.TaxCodeFk.Value);
			}

			if (!string.IsNullOrWhiteSpace(billingSchema.AccountNo))
			{
				transaction.NominalAccount = billingSchema.AccountNo;
				scope.ClearSubtotalInfo(transaction);
			}

			if (billingSchema.ControllingUnitFk.HasValue)
			{
				var controllingUnitEntity = scope.GetControllingUnit(billingSchema.ControllingUnitFk.Value);
				scope.FillControllingUnit(transaction, billingSchema.ControllingUnitFk.Value);
				scope.FillPostingType(transaction, controllingUnitEntity.Isassetmanagement);
			}

			transaction.CompanyDeferalTypeFk = null;
			transaction.DateDeferalStart = null;
		}

		private void SetLineReference(IEnumerable<ITransactionEntity> realCostTransactions)
		{
			var scope = this.Scope as TransactionPostingScope;
			var realCostGroups = realCostTransactions.Select(e => ProvideSubTotalDecorator(e))
				.GroupBy(e => new { e.Entity.DocumentType, e.Entity.ControllingUnitFk, e.Entity.ControllingUnitCode, e.Entity.NominalAccount, e.Entity.CodeRetention, e.Entity.NominalDimension, e.Entity.NominalDimension2, e.Entity.NominalDimension3, e.Entity.VatCode, e.Entity.CompanyDeferalTypeFk, e.Entity.DateDeferalStart, e.ControllinggroupdetailText, e.ControllinggroupText, e.Entity.MatchText, e.ControllingUnitIcFk, e.ItemFk });

			foreach (var realCostGroup in realCostGroups)
			{
				string lineReference = "";
				var referenceItem = realCostGroup.Select(e => e.Entity as ITransactionReferenceEntity).FirstOrDefault(e => e.LineReference != null && e.LineReference.Length > 0);

				if (referenceItem != null)
				{
					lineReference = referenceItem.LineReference;
				}
				else
				{
					lineReference = scope.NextLineReference();
				}

				foreach (var item in realCostGroup)
				{
					var referenceEntity = item.Entity as ITransactionReferenceEntity;

					if(referenceEntity.LineReference != lineReference)
					{
						referenceEntity.LineReference = lineReference;

						if (item.Entity.IsSuccess)
						{
							scope.SuccessTransactionsToSave.Add(item.Entity.Clone() as ITransactionEntity);
						}
					}
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> Create(IIdentifyable headerEntity)
		{
			var result = new List<ITransactionEntity>();
			var transactions = new List<ITransactionEntity>();
			var entities = this.Scope.Eaches["BillingSchemas"] as IEnumerable<IBillingSchemaPostingEntity>;
			var scope = this.Scope as TransactionPostingScope;

			if (entities != null && entities.Any())
			{
				// Get the rebates or global deductions.
				var billingSchemas = this.Filter(entities).Select(e => new BillingSchemaPostingDecorator(e)).ToList();
				// Create a new array (1) with all INV_TRANSACTION of line type 10,11,12 of the current invoice (all records, independent of ISSUCCESS)
				var realCostTransactions = this.ProvideRealCostTransactions();

				if (scope.BuildLineReference)
				{
					this.SetLineReference(realCostTransactions);
				}

				foreach (var billingSchema in billingSchemas)
				{
					billingSchema.Entity.Amount = scope.GetValueRound(scope.EvalAmount(billingSchema.Entity));

					if (billingSchema.Entity.Amount == 0)
					{
						continue;
					}

					if (!scope.CheckBillingSchemaControllingUnit(billingSchema.Entity))
					{
						continue;
					}

					billingSchema.Decorate(scope);

					// Create a new array (1) with all INV_TRANSACTION of line type 10,11,12 of the current invoice (all records, independent of ISSUCCESS)
					var realCostTransactionClones = realCostTransactions.Select(e => e.Clone() as ITransactionEntity).ToList();
					var realCostAmount = realCostTransactionClones.Sum(e => e.Amount);

					// IF the total of AMOUNT over the new array is equal to zero then message to INV_VALIDATION and reject generating transactions
					// This is a very unlikely / nonsense use case (invoice was canceled but for some reason there is still a rebate / retention)
					if (realCostAmount == 0)
					{
						throw new TransactionAbortedException((int)BasMessageEnum.RejectTransaction);
					}

					if (billingSchema.Entity.BillingLineTypeFk == (int)RIB.Visual.Basics.BillingSchema.Core.BillingLineType.EarlyPaymentDiscountRebate)//23
					{
						SetVatAmountInEarlyPaymentDiscountRebate(scope, billingSchema, realCostTransactionClones, realCostAmount);
					}
					else
					{
						SetVatAmount(scope, billingSchema, realCostTransactionClones, realCostAmount);
					}

					transactions.AddRange(realCostTransactionClones);
				}

				if (!scope.IsCumulativeTransaction())
				{
					foreach (var transaction in transactions)
					{
						if (transaction.LineType == "3" || transaction.LineType == "5")
						{
							scope.DeductChainAmount(transaction, scope.IsTheSame);
						}
					}

					transactions.AddRange(scope.CreateChainTransaction("3"));
					transactions.AddRange(scope.CreateChainTransaction("5"));
				}


				var groups = transactions.Select(e => ProvideSubTotalDecorator(e))
				.GroupBy(e => new { e.Entity.LineType, e.Entity.DocumentType, e.Entity.ControllingUnitFk, e.Entity.ControllingUnitCode, e.Entity.NominalAccount, e.Entity.CodeRetention, e.Entity.NominalDimension, e.Entity.NominalDimension2, e.Entity.NominalDimension3, e.Entity.VatCode, e.Entity.CompanyDeferalTypeFk, e.Entity.DateDeferalStart, e.ControllinggroupdetailText, e.ControllinggroupText, e.Entity.MatchText, e.ControllingUnitIcFk, e.ItemFk });

				// sum for AMOUNT, QUANTITY, VAT_AMOUNT group by all other fields 
				foreach (var group in groups)
				{
					var newTransaction = this.Scope.TransactionLogic.Create(headerEntity.Id);
					var firstItem = group.First();
					var transaction = firstItem.Entity;

					newTransaction.Amount = group.Sum(e => e.Entity.Amount);
					newTransaction.Quantity = group.Sum(e => e.Entity.Quantity);
					newTransaction.VatAmount = group.Sum(e => e.Entity.VatAmount);

					transaction.Amount = newTransaction.Amount;
					transaction.VatAmount = newTransaction.VatAmount;
					transaction.Quantity = newTransaction.Quantity;
					transaction.Id = newTransaction.Id;
					scope.ClearVersion(transaction);

					if (scope.BuildLineReference)
					{
						(transaction as ITransactionReferenceEntity).LineReference = String.Join(", ", group.Select(e => e.Entity as ITransactionReferenceEntity).Where(e => !string.IsNullOrEmpty(e.LineReference)).Select(e => e.LineReference).Distinct());
					}

					if (firstItem.ControllingGrpSetDetails != null && firstItem.ControllingGrpSetDetails.Any())
					{
						scope.CreateControllingGrpSet(transaction, firstItem.ControllingGrpSetDetails);
					}

					scope.DeductCurrentAmount(transaction, scope.IsTheSame);

					if (this.CheckTransaction(transaction))
					{
						result.Add(transaction);
					}
				}

				var newGroups = result.GroupBy(e => e.LineType);

				foreach (var newGroup in newGroups)
				{
					if (newGroup.Sum(e => e.Amount) == 0 && newGroup.Sum(e => e.VatAmount) == 0)
					{
						foreach (var item in newGroup)
						{
							result.Remove(item);
							string description = this.Description + string.Format(", LineType={0}", newGroup.Key) + ": ";
							this.Scope.CreateValidation((int)BasMessageEnum.NotGeneralTransactionWhenAmountIsZero, description);
						}
					}
				}
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		protected virtual TransactionDecorator ProvideSubTotalDecorator(ITransactionEntity entity)
		{
			var scope = this.Scope as TransactionPostingScope;
			var decorator = new TransactionDecorator(entity);

			this.ConfigDecorator(decorator);
			decorator.DecorateControllingGroupSet(scope);

			return decorator;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected virtual void ConfigDecorator(TransactionDecorator decorator)
		{

		}

		private void SetVatAmountInEarlyPaymentDiscountRebate(TransactionPostingScope scope, BillingSchemaPostingDecorator billingSchema, IEnumerable<ITransactionEntity> realCostTransactions, decimal realCostAmount)
		{
			var targetVatAmount = GetTargetVatAmountInEarlyPaymentDiscountRebate(scope, realCostTransactions, billingSchema.Entity.ResultOc);

			foreach (var realCostTransaction in realCostTransactions)
			{
				this.FillTransactionCommon(realCostTransaction, billingSchema.Entity);
				this.FillTransactionOther(realCostTransaction, billingSchema.Entity);
				realCostTransaction.Amount = scope.GetValueRound((realCostTransaction.Amount / realCostAmount) * billingSchema.Entity.ResultOc);
				scope.EvalVatAmount(realCostTransaction);
				realCostTransaction.Amount = 0;
			}

			var totalVatAmount = realCostTransactions.Sum(e => e.VatAmount);

			if (totalVatAmount != targetVatAmount)
			{
				var lastItem = realCostTransactions.Last();
				lastItem.VatAmount += (targetVatAmount - totalVatAmount);
			}
		}

		/// <summary>
		/// calculate target vat amount from contract, pes and others.
		/// </summary>
		/// <param name="scope"></param>
		/// <param name="realCostTransactions"></param>
		/// <param name="targetAmount"></param>
		/// <returns></returns>
		private decimal GetTargetVatAmountInEarlyPaymentDiscountRebate(TransactionPostingScope scope, IEnumerable<ITransactionEntity> realCostTransactions, decimal targetAmount)
		{
			decimal result = 0;
			var filterList = realCostTransactions.Where(e => !e.IsSuccess).ToList();
			var realCostAmount = filterList.Sum(e => e.Amount);
			var groups = filterList.GroupBy(e => new { e.TaxCodeFk, e.TaxCodeMatrixFk, e.VatCode });

			foreach(var group in groups)
			{
				var amount = group.Sum(e => e.Amount);
				var vatPercent = scope.EvalVatPercent(group.First());

				if (vatPercent != 0)
				{
					result += (amount / realCostAmount) * targetAmount * vatPercent / 100;
				}
			}

			return scope.GetValueRound(result);
		}

		private void SetVatAmount(TransactionPostingScope scope, BillingSchemaPostingDecorator billingSchema, IEnumerable<ITransactionEntity> realCostTransactions, decimal realCostAmount)
		{
			//var targetVatAmount = GetTargetVatAmountInEarlyPaymentDiscountRebate(scope, realCostTransactions, billingSchema.Entity.Amount);

			// update properties of real cost transations in the array with billing shcema
			foreach (var realCostTransaction in realCostTransactions)
			{
				this.FillTransactionCommon(realCostTransaction, billingSchema.Entity);
				this.FillTransactionOther(realCostTransaction, billingSchema.Entity);
				realCostTransaction.Amount = scope.GetValueRound((realCostTransaction.Amount / realCostAmount) * billingSchema.Entity.Amount);
				scope.EvalVatAmount(realCostTransaction);
			}

			var totalAmount = realCostTransactions.Sum(e => e.Amount);
			// If the new sum for AMOUNT in the array is not equal to RESULT_OC of the current billing schema line then adjust this in the last line of the array. 
			if (totalAmount != billingSchema.Entity.Amount)
			{
				var lastItem = realCostTransactions.Last();
				lastItem.Amount += (billingSchema.Entity.Amount - totalAmount);
			}

			// Fix Defect #115701: don't adjust vat amount for items whose line type is not 23
			//var totalVatAmount = realCostTransactions.Sum(e => e.VatAmount);
			//if (totalVatAmount != targetVatAmount)
			//{
			//	var lastItem = realCostTransactions.Last();
			//	lastItem.VatAmount += (targetVatAmount - totalVatAmount);
			//}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <param name="transactions"></param>
		/// <returns></returns>
		public virtual IEnumerable<ITransactionEntity> AfterCreate(IIdentifyable headerEntity, IEnumerable<ITransactionEntity> transactions)
		{
			return transactions;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public abstract int? ProvideHeaderTaxCode();

		/// <summary>
		/// The same must work for the Controlling Unit if MDC_BILLING_SCHEMA_DETAIL_FK.HASCONTROLLINGUNIT = FALSE then do not look for BIL_HEADER.MDC_CONTROLLINGUNIT_FK but use the MDC_CONTROLLINGUNIT_FK from the net positions and split the amount by amount
		/// </summary>
		/// <returns></returns>
		public abstract int? ProvideHeaderControllingUnit();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <param name="billingSchema"></param>
		public abstract void FillTransactionOther(ITransactionEntity transaction, IBillingSchemaPostingEntity billingSchema);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="transaction"></param>
		/// <returns></returns>
		public abstract bool CheckTransaction(ITransactionEntity transaction);
	}
}
