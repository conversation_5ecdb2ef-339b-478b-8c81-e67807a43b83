using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Entity;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Transactions;
using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Platform.Common;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using System.Data.Entity.Infrastructure;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using System.ComponentModel.Composition;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using System.Collections.Concurrent;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.Core.Core.Basics;
using System.Globalization;

namespace RIB.Visual.Basics.EfbSheets.BusinessComponents
{
	/// <summary>
	/// EfbSheets CrewMix Business Logic should be placed here and class derived from platform LogicBase Class
	/// </summary>
	//[Export(typeof(IEstCrewMixLogic))]
	public class BasicsEstCrewMixLogic : EntityUpdateLogic<EstCrewMixEntity, RIB.Visual.Platform.Core.IdentificationData>//, IEstCrewMixLogic
	{
		/// <summary>
		/// The singleton identifier instance for the type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<EstCrewMixEntity>> IdentifierInstance = IdentifierFactory.Create<EstCrewMixEntity>("Id");

		int? _mdcContextFk;

		/// <summary>
		/// Basics Est CrewMix Guid
		/// </summary>
		public string BasicsEstCrewMixGuid { get { return "c4feed8d0ff34ff3a540c3ed642cf67c"; } }

		/// <summary>
		/// Initializes the logic instance.
		/// </summary>
		public BasicsEstCrewMixLogic()
		{
			Identifier = IdentifierInstance.Value;
			PermissionGUID = BasicsEstCrewMixGuid;
			_mdcContextFk = this.CompanyInfo.GetMasterDataContext();
		}

		/// <summary>
		/// Translation
		/// </summary>
		/// <param name="entities">entities which need Translation</param>
		public IEnumerable<EstCrewMixEntity> Translate(IEnumerable<EstCrewMixEntity> entities)
		{
			return entities.Translate(this.UserLanguageId, new Func<EstCrewMixEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
		}

		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <param name="data"></param>
		/// <returns/>
		public EstCrewMixEntity Create(BasicsEfbSheetsCrewMixCreationData data)
		{
			if (data == null)
			{
				throw new ArgumentNullException("data");
			}

			if (data.projectFk >= 1)
			{
				_mdcContextFk = null;
			}

			Permission.Ensure(PermissionGUID, Permissions.Create);

			using (new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				Int32 newId = SequenceManager.GetNext("EST_CREWMIX");

				var newEntity = new EstCrewMixEntity
				{
					Id = newId,
					MdcContextFk = _mdcContextFk,
					ProjectFk = data.projectFk
				};

				return newEntity;
			}
		}

		/// <summary>
		/// Update the complete crewmix data
		/// </summary>
		/// <param name="completeData"></param>
		/// <returns></returns>
		public void Update(EstCrewMixCompleteEntity completeData)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);

			var estAverageWageLogic = new EstAverageWageLogic();
			var estCrewmixAfLogic = new EstCrewMixAfLogic();
			var estCrewmixAfsnLogic = new EstCrewMixAfsnLogic();
			var estCrewMixCostCodeLogic = new EstCrewMixCostCodeLogic();
			var estNonWageCostsLogic = new EstNonwageCostsLogic();

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				using (var transactionScope = TransactionScopeFactory.Create())
				{
					if (completeData.EstCrewMix != null)
					{
						completeData.EstCrewMix.SaveTranslate<EstCrewMixEntity>(this.UserLanguageId, new Func<EstCrewMixEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
						if (completeData.EstCrewMix.Id >= 1)
						{
							dbcontext.Save(completeData.EstCrewMix);
						}
					}

					if (completeData.EstCrewMixes != null)
					{
						ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(completeData.EstCrewMixes));
						completeData.EstCrewMixes.SaveTranslate<EstCrewMixEntity>(this.UserLanguageId, new Func<EstCrewMixEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
						dbcontext.Save(completeData.EstCrewMixes);
					}

					/* collect EstCrewMix2CostCodes to Delete */
					if (completeData.EstCrewMix2CostCodeToDelete != null && completeData.EstCrewMix2CostCodeToDelete.Any())
					{
						var estCrewMix2CostCodeToDelete = completeData.EstCrewMix2CostCodeToDelete.ToList();
						estCrewMixCostCodeLogic.Delete(estCrewMix2CostCodeToDelete);
					}

					/* collect EstCrewMix2CostCodes to save */
					if (completeData.EstCrewMix2CostCodeToSave != null && completeData.EstCrewMix2CostCodeToSave.Any())
					{
						var estCrewMix2CostCodeToSave = completeData.EstCrewMix2CostCodeToSave.Where(e => e.EstCrewMix2CostCode != null && e.EstCrewMix2CostCode.MdcCostCodeFk.HasValue).Select(e => e.EstCrewMix2CostCode).ToList();
						estCrewMixCostCodeLogic.Save(estCrewMix2CostCodeToSave);
						UpdateCostCodes(completeData, estCrewMix2CostCodeToSave);
					}

					/* collect CrewmixAFSN to Delete */
					if (completeData.EstCrewMixAfsnToDelete != null && completeData.EstCrewMixAfsnToDelete.Any())
					{
						var estCrewmixAfsnToDelete = completeData.EstCrewMixAfsnToDelete.ToList();
						estCrewmixAfsnLogic.Delete(estCrewmixAfsnToDelete);
					}

					/* collect CrewmixAFSN to save */
					if (completeData.EstCrewMixAfsnToSave != null && completeData.EstCrewMixAfsnToSave.Any())
					{
						var crewmixAfsnsToSave = completeData.EstCrewMixAfsnToSave.Where(e => e.EstCrewMixAfsn != null && e.EstCrewMixAfsn.MdcWageGroupFk.HasValue).Select(e => e.EstCrewMixAfsn).ToList();
						estCrewmixAfsnLogic.Save(crewmixAfsnsToSave);
					}

					/* collect CrewmixAF to Delete */
					if (completeData.EstCrewMixAfToDelete != null && completeData.EstCrewMixAfToDelete.Any())
					{
						var estCrewmixAfToDelete = completeData.EstCrewMixAfToDelete.ToList();
						estCrewmixAfLogic.Delete(estCrewmixAfToDelete);
					}

					/* collect CrewmixAF to save */
					if (completeData.EstCrewMixAfToSave != null && completeData.EstCrewMixAfToSave.Any())
					{
						var crewmixAfsToSave = completeData.EstCrewMixAfToSave.Where(e => e.EstCrewMixAf != null && e.EstCrewMixAf.MdcWageGroupFk.HasValue).Select(e => e.EstCrewMixAf).ToList();
						estCrewmixAfLogic.Save(crewmixAfsToSave);
					}

					/* collect AverageWages to Delete */
					if (completeData.EstAverageWageToDelete != null && completeData.EstAverageWageToDelete.Any())
					{
						var estAverageWagesToDelete = completeData.EstAverageWageToDelete.ToList();
						estAverageWageLogic.Delete(estAverageWagesToDelete);
					}

					/* collect AverageWages to save */
					if (completeData.EstAverageWageToSave != null && completeData.EstAverageWageToSave.Any())
					{
						var estAverageWagesToSave = completeData.EstAverageWageToSave.Where(e => e.EstAverageWage != null && e.EstAverageWage.MdcWageGroupFk.HasValue).Select(e => e.EstAverageWage).ToList();
						estAverageWageLogic.Save(estAverageWagesToSave);
					}

					if (completeData.EstNonwageCostsToDelete != null && completeData.EstNonwageCostsToDelete.Any())
					{
						var estNonWageCostsToDelete = completeData.EstNonwageCostsToDelete.ToList();
						estNonWageCostsLogic.Delete(estNonWageCostsToDelete);
					}

					if (completeData.EstNonwageCostsToSave != null && completeData.EstNonwageCostsToSave.Any())
					{
						var estNonWageCostsToSave = completeData.EstNonwageCostsToSave.Where(e => e.EstNonwageCosts != null && e.EstNonwageCosts.MdcWageGroupFk > 0).Select(e => e.EstNonwageCosts).ToList();

						if (estNonWageCostsToSave.Any())
						{
							estNonWageCostsLogic.Save(estNonWageCostsToSave);
						}
					}
					transactionScope.Complete();
				}
			}
		}


		/// <summary>
		/// Get DbModel
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Get list of crewmixes for loading
		/// </summary>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> GetList()
		{
			var entities = this.GetListByFilter(e => e.ProjectFk == null && e.MdcContextFk == _mdcContextFk);
			if (entities == null)
			{
				return null;
			}

			var translatedEntities = this.Translate(entities);
			return translatedEntities;
		}

		/// <summary>
		/// Get list of crewmixes for loading
		/// </summary>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> GetListByProject(BasicsEfbSheetsCrewMixCreationData data)
		{
			var entities = this.GetListByFilter(e => e.ProjectFk == data.projectFk);
			if (entities == null)
			{
				return null;
			}

			var translatedEntities = this.Translate(entities);
			return translatedEntities;
		}


		/// <summary>
		/// Get list of crewmixes based on user input search string
		/// </summary>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> GetListByFilter(string filterValue)
		{
			IEnumerable<EstCrewMixEntity> entities;

			if (filterValue == null)
			{
				entities = this.GetListByFilter(e => e.ProjectFk == null && e.MdcContextFk == _mdcContextFk);
			}
			else
			{
				entities = this.GetListByFilter(e => e.ProjectFk == null && (e.Code.ToLower().Contains(filterValue) || e.DescriptionInfo.Description.ToLower().Contains(filterValue)));
			}

			if (entities == null)
			{
				return null;
			}

			var translatedEntities = this.Translate(entities);
			return translatedEntities;
		}

		/// <summary>
		/// GetListByFilterRequest method is used to get filtered list of crew mixes
		/// </summary>
		/// <param name="filterIn"></param>
		/// <param name="filterOut"></param>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> GetListByFilterRequest(FilterRequest<Int32> filterIn, ref FilterResponse<Int32> filterOut)
		{
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<EstCrewMixEntity> query = dbContext.Entities<EstCrewMixEntity>();

				query = query.Where(e => e.ProjectFk == null && e.MdcContextFk == _mdcContextFk);

				if (!string.IsNullOrWhiteSpace(filterIn.Pattern))
				{
					query = query.Where(e => e.Code.Contains(filterIn.Pattern) || e.DescriptionInfo.Description.Contains(filterIn.Pattern));
				}

				if (filterIn.PKeys != null)
				{
					query = query.Where(e => filterIn.PKeys.Contains(e.Id));
				}

				var entities = filterIn.OrderBy == null
					? FilterRequest<Int32>.RetrieveEntities(filterIn, filterOut, query, e => e.Code, e => e.Id)
					: FilterRequest<Int32>.RetrieveEntities(filterIn, filterOut, query);

				this.Translate(entities);
				this.PostProcess(entities);

				return entities;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="MainitemIds"></param>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> GetItemsById(List<int?> MainitemIds)
		{
			var entities = this.GetFilteredList<EstCrewMixEntity>(GetDbModel(), (e => MainitemIds.Contains(e.Id)));

			return entities;
		}


		/// <summary>
		/// Delete an existing Master EstCrewMixes with all children
		/// </summary>
		/// <param name="estCrewMixes">List of EstCrewMixes to be deleted</param>
		/// <returns></returns>
		public void DeleteMasterCrewMix(IEnumerable<EstCrewMixEntity> estCrewMixes)
		{
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					foreach (var estCrewMixEntity in estCrewMixes)
					{
						DeleteCrewmixDependentItems(estCrewMixEntity, dbContext);
						dbContext.Delete(estCrewMixEntity);
					}
					dbContext.SaveChanges();
				}
				transaction.Complete();
			}
		}

		/// <summary>
		/// Deletes Crew mix and all other dependent items
		/// </summary>
		/// <param name="estCrewmixEntity"></param>
		/// <param name="dbContext"></param>
		private void DeleteCrewmixDependentItems(EstCrewMixEntity estCrewmixEntity, RVPBC.DbContext dbContext)
		{
			//Get All Average Wages of CrewMix Entity
			var estAverageWages = dbContext.GetFiltered<EstAverageWageEntity>(e => e.EstCrewMixFk == estCrewmixEntity.Id);
			if (estAverageWages != null && estAverageWages.Any())
			{
				dbContext.Delete(estAverageWages);
			}

			//Get All CrewMix AFs of CrewMix Entity
			var estCrewMixAfs = dbContext.GetFiltered<EstCrewMixAfEntity>(e => e.EstCrewMixFk == estCrewmixEntity.Id);
			if (estCrewMixAfs != null && estCrewMixAfs.Any())
			{
				dbContext.Delete(estCrewMixAfs);
			}

			//Get All CrewMix AFSNs of CrewMix Entity
			var estCrewmixAfsns = dbContext.GetFiltered<EstCrewMixAfsnEntity>(e => e.EstCrewMixFk == estCrewmixEntity.Id);
			if (estCrewmixAfsns != null && estCrewmixAfsns.Any())
			{
				dbContext.Delete(estCrewmixAfsns);
			}

			//Get All Crewmix2costcodes of CrewMix Entity
			var estCrewMix2CostCodes = dbContext.GetFiltered<EstCrewMix2CostCodeEntity>(e => e.EstCrewMixFk == estCrewmixEntity.Id);
			if (estCrewMix2CostCodes != null && estCrewMix2CostCodes.Any())
			{
				dbContext.Delete(estCrewMix2CostCodes);
			}
		}

		/// <summary>
		/// Updates cost codes rate from CrewMixAfsn
		/// </summary>
		/// <param name="completeData"></param>
		/// <param name="crewmix2CostCodes"></param>
		public void UpdateCostCodes(EstCrewMixCompleteEntity completeData, IEnumerable<EstCrewMix2CostCodeEntity> crewmix2CostCodes)
		{
			var baseCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();
			List<ICostCodeEntity> baseCostCodes = new List<ICostCodeEntity>();

			if (completeData.EstCrewMixes != null)
			{
				foreach (var crewMix in completeData.EstCrewMixes)
				{
					if (crewMix.CrewMixAfsn != null)
					{
						foreach (var crewmixCostCode in crewmix2CostCodes)
						{
							if (crewmixCostCode.MdcCostCodeFk != null)
							{
								var baseCostCode = baseCostCodeLogic.GetCostCodesById(crewmixCostCode.MdcCostCodeFk.Value);

								if (baseCostCode != null && baseCostCode.IsLabour || baseCostCode.IsRate)
								{
									baseCostCode.Rate = (decimal)crewMix.CrewMixAfsn;
									baseCostCodes.Add(baseCostCode);
								}
							}
						}
					}
				}

				baseCostCodeLogic.SaveEntities(baseCostCodes);
			}

			if (completeData.EstCrewMix != null && completeData.EstCrewMix.Id >= 1 && completeData.EstCrewMix.CrewMixAfsn != null)
			{
				foreach (var crewmixCostCode in crewmix2CostCodes)
				{
					if (crewmixCostCode.MdcCostCodeFk != null)
					{
						var baseCostCode = baseCostCodeLogic.GetCostCodesById(crewmixCostCode.MdcCostCodeFk.Value);

						if (baseCostCode != null && baseCostCode.IsLabour || baseCostCode.IsRate)
						{
							baseCostCode.Rate = (decimal)completeData.EstCrewMix.CrewMixAfsn;
							baseCostCodes.Add(baseCostCode);
						}
					}
				}
				baseCostCodeLogic.SaveEntities(baseCostCodes);
			}
		}

		private static readonly LazyExportedValue<IActiveCollaboratorsManager> ActiveCollaboratorsMgr = new();

		/// <summary>
		///  Save Bulk EstCrewMixes
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="dbContext"></param>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> BulkSave(IEnumerable<EstCrewMixEntity> entities, RVPBC.DbContext dbContext)
		{
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(entities));

			foreach (var entity in entities)
			{
				entity.SaveTranslate(UserLanguageId, new Func<EstCrewMixEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
				var returnEntities = dbContext.Save(entity);
			}
			return entities;
		}

		/// <summary>
		/// Get All WageGroups from MDC_WAGE_GROUP
		/// </summary>
		/// <returns></returns>
		public IEnumerable<BasicsCustomizeWageGroupEntity> GetAllWageGroups()
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				BasicsCustomizeWageGroupLogic wageGroupLogic = new BasicsCustomizeWageGroupLogic();

				// Fetch all wage group data from the entity without additional filtering
				var wageGroups = wageGroupLogic.GetAllItems<BasicsCustomizeWageGroupEntity>(wageGroupLogic.GetDbModel()).ToList();

				// Translate DescriptionInfo and return the full list of wage groups
				return wageGroups.Translate(this.UserLanguageId, new Func<BasicsCustomizeWageGroupEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
			}
		}

		/// <summary>
		/// Get all wage rate type from BAS_WAGE_RATE_TYPE
		/// </summary>
		/// <returns></returns>
		public IEnumerable<BasicsCustomizeWageRateTypeEntity> GetAllWageRates()
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				BasicsCustomizeWageRateTypeLogic wageRateTypeLogic = new BasicsCustomizeWageRateTypeLogic();

				// Retrieve the list of wage rate type entities
				var wageRateTypes = wageRateTypeLogic.GetAllItems<BasicsCustomizeWageRateTypeEntity>(wageRateTypeLogic.GetDbModel()).ToList();

				// Apply the translation method with the appropriate DescriptionInfo translation
				return wageRateTypes.Translate(this.UserLanguageId, new Func<BasicsCustomizeWageRateTypeEntity, DescriptionTranslateType>[]
				{
				entity => entity.DescriptionInfo
				}).ToList();
			}
		}

		/// <summary>
		/// Recalculate and update wages
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public IEnumerable<EstCrewMixEntity> CalculateAndUpdateWages(BasicsEfbSheetsCrewMixUpdateWagesInputData data)
		{
			var estAverageWageLogic = new EstAverageWageLogic();
			var estCrewMixAfLogic = new EstCrewMixAfLogic();
			var estCrewMixAfsnLogic = new EstCrewMixAfsnLogic();
			var estNonwageCostsLogic = new EstNonwageCostsLogic();

			var allWageGroups = GetAllWageGroups().ToList();
			var allWageRateType = GetAllWageRates().ToList();

			string wageRateType = "Wage Group";

			var id = allWageRateType.Where(x => x.DescriptionInfo.Description == wageRateType).Select(x => x.Id).FirstOrDefault();

			var filteredwageGroups = allWageGroups.Where(e => e.WageRateTypeFk == id).ToList();

			var estAverageWageEntities = data.CrewMixScope == CREW_MIX_SCOPE.ALL_CREWMIX ? estAverageWageLogic.GetAllItems<EstAverageWageEntity>(estAverageWageLogic.GetDbModel()).ToList() : estAverageWageLogic.GetItemsById(data.MainitemId);
			var estCrewMixAfEntities = data.CrewMixScope == CREW_MIX_SCOPE.ALL_CREWMIX ? estCrewMixAfLogic.GetAllItems<EstCrewMixAfEntity>(estCrewMixAfLogic.GetDbModel()).ToList() : estCrewMixAfLogic.GetItemsById(data.MainitemId);
			var estCrewMixAfSnEntities = data.CrewMixScope == CREW_MIX_SCOPE.ALL_CREWMIX ? estCrewMixAfsnLogic.GetAllItems<EstCrewMixAfsnEntity>(estCrewMixAfsnLogic.GetDbModel()).ToList() : estCrewMixAfsnLogic.GetItemsById(data.MainitemId);
			var estCrewMixesEntities = data.CrewMixScope == CREW_MIX_SCOPE.ALL_CREWMIX ? this.GetAllItems<EstCrewMixEntity>(this.GetDbModel()).ToList() : this.GetItemsById(data.MainitemId);
			var estNonwageCostsEntities = data.CrewMixScope == CREW_MIX_SCOPE.ALL_CREWMIX ? estNonwageCostsLogic.GetAllItems<EstNonwageCostsEntity>(estNonwageCostsLogic.GetDbModel()).ToList() : estNonwageCostsLogic.GetItemsById(data.MainitemId);

			var updatedCrewMixes = estAverageWageLogic.CalculateBasedOnAverageWage(allWageGroups, estAverageWageEntities, estCrewMixesEntities).ToList();
			updatedCrewMixes = estCrewMixAfLogic.CalculateBasedOnCrewMixAF(allWageGroups, estCrewMixAfEntities, estCrewMixesEntities).ToList();
			updatedCrewMixes = estCrewMixAfsnLogic.CalculateBasedOnCrewMixAFSN(allWageGroups, estCrewMixAfSnEntities, estCrewMixesEntities).ToList();
			updatedCrewMixes = estNonwageCostsLogic.CalculateBasedOnNonwageCosts(allWageGroups, estNonwageCostsEntities, estCrewMixesEntities).ToList();

			return updatedCrewMixes.Translate(UserLanguageId, new Func<EstCrewMixEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
		}

		/// <summary>
		/// Returns the collaboration contexts for a given set of complete update entities.
		/// </summary>
		/// <param name="entities">The complete update entities.</param>
		/// <returns>The collaboration context(s).</returns>
		protected override IEnumerable<(String Area, String Context)> GetCollaborationContexts(IEnumerable<IIdentifyable> entities)
		{
			ArgumentNullException.ThrowIfNull(entities);

			var firstId = entities.FirstOrDefault()?.Id;

			return new List<(String Area, String Context)>() { new("basics.efbsheets", firstId.Value.ToString(CultureInfo.InvariantCulture)) };

		}
	}
}
