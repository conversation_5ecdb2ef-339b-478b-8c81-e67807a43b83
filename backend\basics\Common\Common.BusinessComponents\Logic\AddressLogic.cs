using RIB.Visual.Basics.Common.BusinessComponents.MapCalculations.Factory;
using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Common.Core;
using System.Threading.Tasks;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Cloud.Common.BusinessComponents;
using Newtonsoft.Json.Linq;
using Microsoft.Graph;

namespace RIB.Visual.Basics.Common.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class AddressCreationDto
	{
		/// <summary/>
		public int? CountryId { get; set; }
		/// <summary/>
		public string Street { get; set; }
		/// <summary/>
		public string ZipCode { get; set; }
		/// <summary/>
		public string City { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string County { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? StateId { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IGetAddressLogic))]
	public partial class AddressLogic : EntityLogicLegacyAdapter<AddressEntity>, IGetAddressLogic
	{

		/// <summary>
		/// 
		/// </summary>
		public AddressLogic()
		{
			this.PermissionGUID = "c597868e45f244c8b653ef384ab9e599";
			SetTempMatchingFunc<DdtempidsEntity>((e, tmp) => e.Id == tmp.Id);
			//this.EnablePermissionCheck = false;
		}

		#region NewOrClone
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public int GetNextId()
		{
			return SequenceManager.GetNext("BAS_ADDRESS");
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="count"></param>
		/// <returns></returns>
		public IList<int> GetNextIds(int count)
		{
			return SequenceManager.GetNextList("BAS_ADDRESS", count, "ID");
		}

		/// <summary>
		/// Creates an new Address Entity 
		/// and assigns a primary key via sequence manager
		/// 
		/// </summary>
		/// <param name="countryId">nullable, if present address is initialized with this country</param>
		/// <returns></returns>
		public AddressEntity GetNewElement(int? countryId = null)
		{
			AddressEntity result = new AddressEntity()
			{
				Id = GetNextId(),
				AddressModified = false
			};

			CountryEntity country = null;
			if (countryId.HasValue)
			{
				country = new CountryLogic().GetItemByKey(countryId);
			}
			if (country == null)
			{
				country = new CountryLogic().GetDefault();
			}
			if (country == null) { return result; }

			result.CountryDescription = country.DescriptionInfo.Translated;
			result.CountryFk = country.Id;
			var state = new StateLogic().GetDefault(country);
			if (state != null)
			{
				result.StateFk = state.Id;
			}

			return result;
		}

		/// <summary>
		/// This method creates an address entity and initializes it with data from addressCreationDto parameter object.
		/// 
		/// </summary>
		/// <param name="addressCreationDto"></param>
		public AddressEntity CreateAddress(AddressCreationDto addressCreationDto)
		{
			var result = GetNewElement(addressCreationDto == null ? null : addressCreationDto.CountryId);
			if (addressCreationDto != null)
			{
				result.Street = addressCreationDto.Street;
				result.City = addressCreationDto.City;
				result.ZipCode = addressCreationDto.ZipCode;
				result.County = addressCreationDto.County;
				result.StateFk = addressCreationDto.StateId;
				FormatAddressPropsByTemplate(result.CountryFk, result);
			}
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="creationData"></param>
		/// <returns></returns>
		public AddressEntity FromJObject(JObject creationData)
		{
			var result = new AddressEntity();
			if (creationData.TryGetValue("Id", StringComparison.InvariantCultureIgnoreCase, out JToken id))
			{
				result.Id = id.Value<int>();
			}
			if (creationData.TryGetValue("CountryFk", StringComparison.InvariantCultureIgnoreCase, out JToken value))
			{
				result.CountryFk = value.Value<int>();
			}
			if (creationData.TryGetValue("City", StringComparison.InvariantCultureIgnoreCase, out JToken value2))
			{
				result.City = value2.Value<string>();
			}
			if (creationData.TryGetValue("ZipCode", StringComparison.InvariantCultureIgnoreCase, out JToken value3))
			{
				result.ZipCode = value3.Value<string>();
			}
			if (creationData.TryGetValue("County", StringComparison.InvariantCultureIgnoreCase, out JToken value4))
			{
				result.County = value4.Value<string>();
			}
			if (creationData.TryGetValue("StateFk", StringComparison.InvariantCultureIgnoreCase, out JToken value5))
			{
				result.StateFk = value5.Value<int>();
			}
			if (creationData.TryGetValue("Address", StringComparison.InvariantCultureIgnoreCase, out JToken value6))
			{
				result.Address = value6.Value<string>();
			}
			if (creationData.TryGetValue("AddressLine", StringComparison.InvariantCultureIgnoreCase, out JToken value7))
			{
				result.AddressLine = value7.Value<string>();
			}
			if (creationData.TryGetValue("Street", StringComparison.InvariantCultureIgnoreCase, out JToken value8))
			{
				result.Street = value8.Value<string>();
			}
			if (creationData.TryGetValue("Supplement", StringComparison.InvariantCultureIgnoreCase, out JToken value9))
			{
				result.Supplement = value9.Value<string>();
			}

			return result;
		}

		/// <summary>
		/// Clone by matrixId
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public AddressEntity Clone(int id)
		{
			var result = GetItemByKey(id);
			if (result == null)
			{
				return null;
			}

			result.Id = GetNextId();
			result.AddressModified = false;

			result.Version = 0;
			result.InsertedAt = DateTime.UtcNow;
			result.InsertedBy = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			result.UpdatedAt = null;
			result.UpdatedBy = null;

			return result;
		}

		/// <summary>
		/// Clone by entity
		/// </summary>
		/// <param name="address"></param>
		/// <returns></returns>
		public AddressEntity Clone(AddressEntity address)
		{
			AddressEntity newAddress = null;
			if (address != null)
			{
				newAddress = address.Clone() as AddressEntity;
				newAddress.Version = 0;
				newAddress.Id = GetNextId();
			}
			return newAddress;
		}

		/// <summary>
		/// Clones the specified address entity and creates a specified number of copies, assigning new IDs to each copy
		/// </summary>
		/// <param name="id">The ID of the original address to clone</param>
		/// <param name="copyCount">The number of copies to create (default is 1)</param>
		/// <returns>A list containing all newly created address entities</returns>
		public List<AddressEntity> BulkCloneAddress(int id, int copyCount = 1)
		{
			var newAddressEntities = new List<AddressEntity>();
			if (copyCount <= 0)
			{
				return newAddressEntities;
			}

			var addressEntity = GetItemByKey(id);
			if (addressEntity == null)
			{
				return newAddressEntities;
			}

			var newIds = GetNextIds(copyCount);
			var userId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			foreach (var newId in newIds)
			{
				var addressCopy = addressEntity.Clone() as AddressEntity;
				addressCopy.Id = newId;
				addressCopy.AddressModified = false;
				addressCopy.Version = 0;
				addressCopy.InsertedAt = DateTime.UtcNow;
				addressCopy.InsertedBy = userId;
				addressCopy.UpdatedAt = null;
				addressCopy.UpdatedBy = null;
				newAddressEntities.Add(addressCopy);
			}
			return newAddressEntities;
		}
		#endregion

		#region LogicBase<TEntity>

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}


		/// <summary>
		/// PostProcess
		/// </summary>
		/// <param name="entities"></param>
		protected override void PostProcess(IEnumerable<AddressEntity> entities)
		{
			var countryIds = entities.Select(e => e.CountryFk);
			var countries = new CountryLogic().GetSearchList(e => countryIds.Contains(e.Id));

			foreach (AddressEntity entity in entities)
			{
				var country = countries.FirstOrDefault(x => x.Id == entity.CountryFk);
				if (null != country)
				{
					entity.CountryDescription = country.DescriptionInfo.Translated;
					entity.CountryISO2 = country.Iso2;
				}
			}

			this.FillCulture(entities);
		}

		#endregion

		/// <summary>
		/// Set an address entity to the passed entity for enrichment of data dispalyed in UI
		/// </summary>
		/// <param name="entity">The entity which is to be enriched by addess data</param>
		public void SetAddressTo(Addressable entity)
		{
			if (entity != null && entity.AddressFk.HasValue)
			{
				entity.AddressEntity = GetItemByKey(entity.AddressFk.Value);
			}
		}

		/// <summary>
		/// Set an address entity to all passed entities for enrichment of data dispalyed in UI
		/// </summary>
		/// <param name="entities">The list of entities which is to be enriched by addess data</param>
		public void SetAddressTo(IEnumerable<Addressable> entities)
		{
			IEnumerable<int> ids = entities.Where(e => e.AddressFk.HasValue).Select(e => e.AddressFk.Value);

			if (ids.Any())
			{
				IEnumerable<AddressEntity> addressEntities = GetItemsByKey(ids);
				Dictionary<int, AddressEntity> addressMap = new Dictionary<int, AddressEntity>();
				foreach (var adr in addressEntities)
				{
					addressMap.Add(adr.Id, adr);
				}

				foreach (var entity in entities)
				{
					if (entity.AddressFk.HasValue && addressMap.TryGetValue(entity.AddressFk.Value, out var addrRef))
					{
						entity.AddressEntity = addrRef;
					}
				}
			}
		}

		/// <summary>
		/// FillAddressReferenceData
		/// </summary>
		/// <param name="addressFks"> </param>
		public IEnumerable<AddressEntity> FillAddressReferenceData(List<int> addressFks)
		{
			if (addressFks.IsNullOrEmpty())
			{
				return Array.Empty<AddressEntity>();
			}

			var uniqueIds = addressFks.Distinct().ToArray();
			return this.GetItemsByKey(uniqueIds);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		public AddressEntity UpdatePOChangeDeliveryAddress(AddressEntity dto)
		{
			using var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel);
			var item = dbcontext.Entities<AddressEntity>().Where(e => e.Id == dto.Id).FirstOrDefault();
			if (item == null)
			{
				return null;
			}

			item.ZipCode = dto.ZipCode;
			item.CountryFk = dto.CountryFk;
			item.Street = dto.Street;
			item.City = dto.City;
			item.County = dto.County;
			item.StateFk = dto.StateFk;
			item.Latitude = dto.Latitude;
			item.Longitude = dto.Longitude;
			item.AddressLine = dto.AddressLine;
			item.Address = dto.Address;
			item.AddressModified = dto.AddressModified;
			item.UpdatedAt = dto.UpdatedAt;
			item.UpdatedBy = dto.UpdatedBy;
			return dbcontext.Save(item);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="addressId"></param>
		/// <param name="save"></param>
		/// <returns></returns>
		public AddressGeoCodingEntity GetAddressGeoLocation(int addressId, bool save)
		{
			AddressEntity address = new AddressLogic().GetItemByKey(addressId);
			return address != null ? GetAddressGeoLocation(address, true) : null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="address"></param>
		/// <param name="save"></param>
		/// <returns></returns>
		public AddressGeoCodingEntity GetAddressGeoLocation(AddressEntity address, bool save)
		{
			var provider = CreateGeoCodingProvider();
			var result = GetAddressGeoLocation(address, provider);
			if (result.Success && save)
			{
				result.Address = this.Save(result.Address);
			}
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="addresses"></param>
		/// <returns></returns>
		public IEnumerable<AddressGeoCodingEntity> GetAddressGeoLocation(IEnumerable<AddressEntity> addresses)
		{
			var taskList = new List<Task<AddressGeoCodingEntity>>();

			var provider = CreateGeoCodingProvider();

			foreach (var address in addresses)
			{
				var task = Task.Factory.StartNew<AddressGeoCodingEntity>((addr) =>
				{
					return GetAddressGeoLocation((AddressEntity)addr, provider);
				}, address);
				taskList.Add(task);
			}
			Task.WaitAll(taskList.ToArray());
			return taskList.Select(e => e.Result);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="addresses"></param>
		/// <returns></returns>
		public IEnumerable<AddressGeoCodingEntity> UpdateAddressGeoLocation(IEnumerable<AddressEntity> addresses)
		{
			var addressList = new List<AddressGeoCodingEntity>();
			var count = addresses.Count();
			for (int i = 0; i < count; i++)
			{
				var item = GetAddressGeoLocation(addresses.ElementAt(i), false);
				addressList.Add(item);
				//To prevent third-party map APIs exceptions
				if (i != 0 && i % 500 == 0)
				{
					Task.Delay(1000);
				}
			}
			var result = addressList.Where(r => r.Success).Select(r => r.Address);
			this.Save(result);

			return addressList;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="pageNumber"></param>
		/// <param name="pageSize"></param>
		/// <returns></returns>
		public IEnumerable<AddressEntity> GetInvalidGeoAddressList(int pageNumber, int pageSize)
		{
			using var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel);
			var list = dbcontext.Entities<AddressEntity>()
								.Where(r => (!r.Latitude.HasValue || !r.Longitude.HasValue) || (r.Longitude == 0 && r.Latitude == 0))
								.OrderBy(r => r.Id)
								.Skip(pageNumber)
								.Take(pageSize)
								.ToList();
			this.PostProcess(list);
			return list;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public int GetInvalidGeoAddressCount()
		{
			using var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel);
			var count = dbcontext.Entities<AddressEntity>()
								 .Where(r => (!r.Latitude.HasValue || !r.Longitude.HasValue) || (r.Longitude == 0 && r.Latitude == 0))
								 .Count();
			return count;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="address"></param>
		/// <param name="provider"></param>
		/// <returns></returns>
		private AddressGeoCodingEntity GetAddressGeoLocation(AddressEntity address, GeoCoding.Core.IGeoCodingProvider provider)
		{
			var result = new AddressGeoCodingEntity() { Success = false, Address = address };

			if (address == null)
			{
				result.Message = "address is null.";
				return result;
			}

			IEnumerable<GeoCoding.Core.Location> locations = null;

			try
			{
				if (address.AddressModified)
				{
					if (string.IsNullOrWhiteSpace(address.Address))
					{
						result.Message = "address is empty.";
					}
					else
					{
						locations = provider.Geocode(address.Address);
					}
				}
				else
				{
					string street = address.Street;
					string city = address.City;
					string county = address.County;
					string state = address.StateDescription;
					string postalCode = address.ZipCode;
					string country = address.CountryDescription;

					locations = provider.Geocode(street, city, county, state, postalCode, country);
				}
			}
			catch (Exception ex)
			{
				var realEx = ex;
				while (realEx.InnerException != null)
				{
					realEx = realEx.InnerException;
				}
				result.Message = realEx.Message;
				return result;
			}

			if (locations == null || !locations.Any())
			{
				result.Message = "address not found.";
				return result;
			}
			else
			{
				var location = locations.First();
				result.Address.Latitude = (decimal?)location.Latitude;
				result.Address.Longitude = (decimal?)location.Longitude;
				result.Success = true;
			}
			return result;
		}

		#region Map Providers Creation

		/// <summary>
		/// Create map provider factory for GeoCoding
		/// </summary>
		/// <returns></returns>

		private GeoCoding.Core.IGeoCodingProvider CreateGeoCodingProvider()
		{
			// default use google.
			var context = new MapContext() { Provider = "google" };

			SystemOptionLogic optionLogic = new SystemOptionLogic();

			// value will take from database, not from cache.
			var providerEntity = optionLogic.GetItemByKey(SystemOption.MapProviderId);
			context.Provider = providerEntity != null ? providerEntity.ParameterValue : context.Provider;

			// value will take from system option cache.
			context.BingKey = optionLogic.GetStringValue(SystemOption.BingMapKeyId);
			context.GoogleKey = optionLogic.GetStringValue(SystemOption.GoogleMapWebServerKeyId);
			context.BaiduKey = optionLogic.GetStringValue(SystemOption.BaiduMapKeyId);

			// test in local internal network.
			// todo:lst to be removed.
			//context.Provider = "test";

			var provider = GeoCodingProviderFactory.Create(context);

			return provider;
		}

		/// <summary>
		/// Create map provider factory for RouteDistanceProvider
		/// </summary>
		/// <returns></returns>
		private MapCalculations.Core.IRouteDistanceProvider CreateRouteDistanceProvider()
		{
			// default use google.
			var context = new MapContext() { Provider = "" };

			SystemOptionLogic optionLogic = new SystemOptionLogic();

			// value will take from database, not from cache.
			var providerEntity = optionLogic.GetItemByKey(SystemOption.MapProviderId);
			context.Provider = providerEntity != null ? providerEntity.ParameterValue : context.Provider;

			// value will take from system option cache.
			context.BingKey = optionLogic.GetStringValue(SystemOption.BingMapKeyId);
			context.GoogleKey = optionLogic.GetStringValue(SystemOption.GoogleMapWebServerKeyId);
			context.BaiduKey = optionLogic.GetStringValue(SystemOption.BaiduMapKeyId);

			var provider = MapCalculationsProviderFactory.Create(context);

			return provider;
		}

		#endregion

		#region IGetItemLogic members

		/// <summary>
		/// Explicit implementation of GetItemByDatasourceFky
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		IIdentifyable IGetItemLogic<IIdentifyable>.GetItemByKey(int id)
		{
			return GetItemByKey(id);
		}
		/// <summary>
		/// Explicit implementation of GetItemsByTableId
		/// </summary>
		/// <param name="keys"></param>
		/// <returns></returns>
		IEnumerable<IIdentifyable> IGetItemLogic<IIdentifyable>.GetItemsByKey(IEnumerable<int?> keys)
		{
			return GetItemsByKey(keys);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		IEnumerable<IAddressEntity> IGetAddressLogic.GetAddressByIds(IEnumerable<int> ids)
		{
			return GetItemsByKey(ids);
		}

		#endregion

		/// <summary>
		/// Delete by ids
		/// </summary>
		public void DeleteByIds(List<int> addressIds)
		{
			try
			{
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					IEnumerable<AddressEntity> result = dbContext.Entities<AddressEntity>().Where(e => addressIds.Contains(e.Id)).ToList();
					dbContext.Delete(result);
				}
			}
			catch (Exception e)
			{
				throw new BusinessLayerException("Cannot be deleted because referenced", e) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		public void FillCulture(IEnumerable<AddressEntity> entities)
		{
			var languages = new LanguageLogic().GetLanguages().ToDictionary(e => e.Id);

			foreach (var entity in entities)
			{
				var languageId = entity.LanguageFk != null ? entity.LanguageFk.Value : this.DataBaseLanguageId;
				if (languages.TryGetValue(languageId, out var language))
				{
					entity.Culture = language.Culture;
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="saveList"></param>
		/// <returns></returns>
		public List<AddressEntity> BulkSave(List<AddressEntity> saveList)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				this.BulkSave(ModelBuilder.DbModel, saveList);
			}
			return saveList;
		}

		/// <summary>
		/// GetSearchListComplete  [Obsolete("This method is no longer used, please use GetSearchList instead")]
		/// </summary>
		/// <param name="filterExpression"></param>
		/// <param name="canSearch"></param>
		/// <param name="sortFunc"></param>
		/// <returns></returns>
		public IEnumerable<AddressEntity> GetSearchListComplete(Expression<Func<AddressEntity, Boolean>> filterExpression, Boolean canSearch = true, Func<IQueryable<AddressEntity>, IQueryable<AddressEntity>> sortFunc = null)
		{
			return this.GetSearchList(filterExpression, canSearch, sortFunc);
		}

		#region Override base Get relative functions

		/// <summary>
		/// Get a list of translated entities with via filterExpression.
		/// </summary>
		/// <param name="filterExpression"></param>
		/// <param name="canSearch"></param>
		/// <param name="sortFunc"></param>
		/// <returns></returns>
		public override IEnumerable<AddressEntity> GetSearchList(Expression<Func<AddressEntity, Boolean>> filterExpression, Boolean canSearch = true, Func<IQueryable<AddressEntity>, IQueryable<AddressEntity>> sortFunc = null)
		{
			var entities = base.GetSearchList(filterExpression, canSearch, sortFunc);
			this.PostProcess(entities);
			return entities;
		}

		#endregion
	}
}
