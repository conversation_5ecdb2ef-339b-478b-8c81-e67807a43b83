/*
 * $Id: ProcurementCommonBoqController.cs 634401 2021-04-28 07:51:55Z alm $
 * Copyright(c): RIB Software SE
 */

using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Main.ServiceFacade.WebApi;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Procurement.Common.BusinessComponents;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Web.Http;
using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.BusinessComponents;
using BCC = RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Common.Core.Final;
using RVPC = RIB.Visual.Platform.Core;

namespace RIB.Visual.Procurement.Common.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("procurement/common/boq")]
	public class ProcurementCommonBoqController : ApiControllerBase
	{

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[Route("getinfo")]
		public string GetInfo()
		{
			return ApiControllerBase.GetDefaultInfo();
		}

		/// <summary>GetList</summary>
		[HttpGet]
		[Route("list")]
		public IEnumerable<PrcBoqExtendedDto> GetList(int prcHeaderFk, decimal? exchangeRate, bool filterBackups)
		{
			var ret = new List<PrcBoqExtendedDto>();

			foreach (var prcBoqExtendedEntity in new PrcBoqLogic().GetPrcBoqExtendeds(prcHeaderFk, filterBackups))
			{
				var prcBoqExtendedDto = new PrcBoqExtendedDto(prcBoqExtendedEntity);

				ret.Add(prcBoqExtendedDto);
			}

			return ret;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="module"></param>
		/// <param name="headerId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getboqitemsbymodule")]
		public IEnumerable<int> GetBoqItemsByModule(ProcurementModuleConstants module, int headerId)
		{
			var prcBoqLogic = new PrcBoqLogic();
			var prjItemIs = prcBoqLogic.GetBoqItemsByModule(module, headerId).Where(e => e.BoqItemPrjItemFk.HasValue).Select(e => e.BoqItemPrjItemFk.Value).Distinct();
			return prjItemIs;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prcBoqId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getprcboqextendeditem")]
		public PrcBoqExtendedDto GetBoqExtendedItem(int prcBoqId)
		{
			var prcBoqLogic = new PrcBoqLogic();
			var prcBoqItem = prcBoqLogic.GetSearchList(x => x.Id == prcBoqId).First();
			if (prcBoqItem != null)
			{
				var prcBoqExtended = prcBoqLogic.GetPrcBoqExtended(prcBoqItem);
				return new PrcBoqExtendedDto(prcBoqExtended);

			}
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getboqitemsbyconheaderid")]
		public List<BoqItemLookupDto> GetBoqItemsByConHeaderId(int conHeaderId)
		{
			var contractLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<BCC.IContractHeaderInfoProvider>();
			var contract = contractLogic.GetConHeaderById(conHeaderId);
			var prcBoqList = new PrcBoqLogic().GetList(contract.PrcHeaderFk);
			var boqIds = prcBoqList.Select(x => (int)x.Id).Distinct().ToList();
			var boqItems = new BoqItemLogic().GetSearchList(e=>boqIds.Contains(e.BoqHeaderFk)).ToList();
			return boqItems.Select(e => new BoqItemLookupDto(e)).ToList();
		}

		/// <summary>
		///	Updates an existing PRC_BOQ record
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("update")]
		public PrcBoqUpdateDto Update(PrcBoqUpdateDto dto)
		{
			var prcBoqExtendedDto = dto.PrcBoqExtended;

			if (prcBoqExtendedDto == null)
			{
				return null;
			}

			var prcBoqLogic = new PrcBoqLogic();
			var prcBoqExtended = prcBoqExtendedDto.Copy();
			prcBoqExtendedDto = new PrcBoqExtendedDto(prcBoqLogic.Update(prcBoqExtended));

			if (dto.NeedUpdateUcFromPackage == true && dto.PackageId.HasValue)
			{
				prcBoqLogic.UpdateControllingUnitBasePackage(prcBoqExtended, dto.PackageId.Value);
			}

			// return changed entities
			dto.PrcBoqExtended.PrcBoq = prcBoqExtendedDto.PrcBoq;
			dto.PrcBoqExtended.BoqHeader = prcBoqExtendedDto.BoqHeader;
			dto.PrcBoqExtended.BoqRootItem = prcBoqExtendedDto.BoqRootItem;
			dto.entities = 0;
			return dto;
		}

		/// <summary>
		/// This class holds the parameters sent by a creation post from the client
		/// </summary>
		public class PrcBoqCreationData
		{
			/// <summary>
			/// Get / set foreign key of the selected prc header
			/// </summary>
			public int PrcHeaderFk
			{
				get;
				set;
			}

			/// <summary>
			/// gets and sets PackageFk
			/// </summary>
			public int? PackageFk
			{
				get;
				set;
			}

			/// <summary>
			/// gets and sets Reference number of boq root item
			/// </summary>
			public string Reference
			{
				get;
				set;
			}

			/// <summary>
			/// gets and sets BriefInfo
			/// </summary>
			public RIB.Visual.Platform.Common.DescriptionTranslateTypeDto BriefInfo { get; set; }

			/// <summary>
			/// gets and sets BoqItemPrjBoqFk
			/// </summary>
			public int? BoqItemPrjBoqFk { get; set; }

			/// <summary>
			/// gets and sets BoqItemPrjItemFk
			/// </summary>
			public int? BoqItemPrjItemFk { get; set; }

			/// <summary>
			/// gets and sets CreateVersionBoq
			/// </summary>
			public bool CreateVersionBoq { get; set; }

			/// <summary>
			/// gets and sets Package2HeaderFk
			/// </summary>
			public int PrcHeaderFkOriginal { get; set; }

			/// <summary>
			/// gets and sets BasCurrencyFk
			/// </summary>
			public int BasCurrencyFk { get; set; }

			///// <summary>
			///// 
			///// </summary>
			//public bool UseExistedBoq { get; set; }

			/// <summary>
			/// 1: Take Over Entire Boq 2: Take Over Boq Header Only
			/// </summary>
			public int TakeOverOption { get; set; }

			///// <summary>
			///// 
			///// </summary>
			//public bool UseExistedWicBoq { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? WicGroupId { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? BoqHeaderId { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int BoqSource { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? WicBoqId { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int SubPackageId { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public string TargetModuleName { get; set; }

			/// <summary>
			/// If not null, the source boq header including boq config will be copied to the new boq header
			/// </summary>
			public int? SourceBoqHeaderIdToCopy {  get; set; }

			///// <summary>
			///// 
			///// </summary>
			//public decimal? ExchangeRate { get; set; }
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("create")]
		public PrcBoqExtendedDto Create(PrcBoqCreationData data)
		{
			var boqItemLogic = new BoqItemLogic();
			var prcBoqLogic = new PrcBoqLogic();
			var boqPriceConditionLogic = new BoqPriceconditionLogic();

			if ((data.PackageFk != null) && (data.PrcHeaderFkOriginal > 0) && data.CreateVersionBoq)//..create prc boq from req or contract module
			{
				BCC.IBoqCopyData boqs = null;
				if (data.BoqSource == 1 || data.BoqSource != 2)
				{
					// Make sure to have a unique reference number set, also in case of multi user issues
					var uniqueReferenceNumber = data.Reference;

					// We have the attributes for a base boq given and we try to find it or create a new one if the search with the given parameters is not successful.
					var packageFk = (int)data.PackageFk;
					var prcBaseBoq = prcBoqLogic.AssertPrcBaseBoqPresent(packageFk, data.PrcHeaderFkOriginal, uniqueReferenceNumber,
						new Platform.Common.DescriptionTranslateType(data.BriefInfo), data.BasCurrencyFk);//save prc boq and boq item to selected package
					var boqItemCopyInfo =
						new BoqItemCopyInfo()
						{
							// TargetModuleHeaderId = ReqHeader.Id or ConHeader.Id or ...              // TODO: BOQ-Copy
							TargetModuleName = data.TargetModuleName,
							CopyBoqRootItemOnly = (data.BoqSource == 1 && data.TakeOverOption == 2),
							CurrencyId = data.BasCurrencyFk
						};

					boqs = boqItemLogic.CopyBoq(prcBaseBoq.BoqHeader.Id, boqItemCopyInfo);// copy boq from package boq
				}
				else if (data.BoqSource == 2)
				{
					if (data.PackageFk.HasValue && data.WicBoqId.HasValue && data.SubPackageId > 0)
					{
						BCC.IPrcPackage2HeaderLogic prcPackage2HeaderLogic = Injector.Get<BCC.IPrcPackage2HeaderLogic>();
						var packageLogic = Injector.Get<BCC.IPrcPackageLogic>();
						var subpackage = prcPackage2HeaderLogic.GetItemById(data.SubPackageId);
						var prcHeaderId = subpackage.PrcHeaderFk;
						var boqItem = boqItemLogic.GetBoqItemById(data.WicBoqId.Value);
						var package = packageLogic.GetPackageById(data.PackageFk.Value);

						if (boqItem != null)
						{
							var wicRootBoqHeaderId = boqItem.BoqHeaderFk;
							var wicRootBoqBriefInfo = boqItem.BriefInfo;
							var wicBoqItemTree = boqItemLogic.GetBoqItems(wicRootBoqHeaderId);
							var baseBoqPriceConditionsToSave = new List<BoqPriceconditionEntity>();

							var boqItemCopyInfo =
								new BoqItemCopyInfo()
								{
									CopyBoqRootItemOnly = (data.TakeOverOption == 2),
									CurrencyId = data.BasCurrencyFk,
									TargetModuleName = data.TargetModuleName
								};

							if (wicBoqItemTree != null && wicBoqItemTree.Any())
							{
								var wicBoqItems = wicBoqItemTree.ElementAt(0).BoqItemChildren.ToList();
								// Make sure to have a unique reference number set, also in case of multi user issues
								var uniqueReferenceNumber = data.Reference;
								// create new base boq header and root boq
								var prcBaseBoq = prcBoqLogic.AssertPrcBaseBoqPresent(data.PackageFk.Value, prcHeaderId, uniqueReferenceNumber, wicRootBoqBriefInfo,package.CurrencyFk);
								ProcurementCommonWizardLogic procurementCommonWizardLogic = new ProcurementCommonWizardLogic();
								var exchangeRate = procurementCommonWizardLogic.GetBoqSource2TargetExchangeRate(wicRootBoqHeaderId, prcBaseBoq.BoqHeader.Id);
								var exchangeRateValue = exchangeRate.GetValueOrDefault(1m);
								var wicBoqItemList = new List<BoqItemEntity>();
								// copy the wic boq to base boq
								foreach (var item in wicBoqItems)
								{
									var temp = item.Clone(true);
									temp.BoqItemChildren = item.BoqItemChildren;
									wicBoqItemList.Add(temp);
									ForEachBoqItem(temp, b =>
									{
										b.Version = 0;
										b.IsWicItem = true;
										b.BoqCharacterContentEntity_BoqCharacterContentPrjFk = null;
										b.BoqCharacterContentEntity_BoqCharacterContentWorkFk = null;
										b.BoqItem2CostGroupEntities = new List<BoqItem2CostGroupEntity>();
										b.BoqItemBasisParent = null;
										b.BoqTextComplementEntities = new List<BoqTextComplementEntity>();
									});
								}
								boqItemLogic.SetCurrentExchangeRateHc2Oc(package.ExchangeRate);
								var newComplete = boqItemLogic.CopyOrMoveNode(prcBaseBoq.BoqRootItem.Id, true, false, wicBoqItemList, keepOriginRefNo: true, identicalProjectAssignment: false, syncBaseBoq: false);

								if (newComplete != null)
								{
									var boqItemCalculateLogic = new BoqItemCalculateLogic("procurement.package", package.Id, package.ExchangeRate);
									foreach (var complete in newComplete)
									{
										if (complete.BoqItem == null)
										{
											continue;
										}
										 foreach(var item in complete.BoqItems)
										{
											ForEachBoqItem(item, b =>
											{
												b.PriceOc = b.PriceOc * exchangeRateValue;
											});
										}
										var calculateResult = boqItemCalculateLogic.CalculateBoqTreeAndPriceconditions(complete.BoqItem);
										boqItemLogic.SaveBoqTree(complete.BoqItem);
										if (calculateResult != null && calculateResult.BoqPriceconditionChanged != null && calculateResult.BoqPriceconditionChanged.Any())
										{
											baseBoqPriceConditionsToSave.AddRange(calculateResult.BoqPriceconditionChanged);
										}
									}

									if (baseBoqPriceConditionsToSave.Any())
									{
										boqPriceConditionLogic.SaveEntities(baseBoqPriceConditionsToSave);
									}
								}
					
								// copy the base boq to pes boq
								boqs = boqItemLogic.CopyBoq(prcBaseBoq.BoqHeader.Id, boqItemCopyInfo);// copy boq from package boq
							}
						}
					}
				}

				if (boqs != null)
				{
					var boqRootItem = boqs.BoqRootItem as BoqItemEntity;
					if (boqRootItem != null)
					{
						var prcBoq = prcBoqLogic.Create(data.PrcHeaderFk, boqs.BoqHeader.Id, data.PackageFk);
						prcBoq = prcBoqLogic.Save(prcBoq, boqs.BoqHeader as BoqHeaderEntity, data.PrcHeaderFkOriginal);
						boqRootItem.BoqItemChildren.Clear();
						var prcBoqExtended = new PrcBoqExtendedEntity(prcBoq, boqs.BoqHeader as BoqHeaderEntity, boqRootItem);
						return new PrcBoqExtendedDto(prcBoqExtended);
					}
				}
				return null;
			}
			else//create prc boq from package module
			{
				// first create an boq header with a root item
				BoqItemEntity boqitem = null;
				BoqHeaderEntity boqheader = null;
				if (data.SourceBoqHeaderIdToCopy.HasValue)
				{
					var boqCopyInfo = new BoqItemCopyInfo
					{
						MaintainBaseBoqLink = false,
						TargetModuleName = "procurement.package",
						CurrencyId = data.BasCurrencyFk,
						CopyBoqRootItemOnly = true
					};
					var copyData = boqItemLogic.CopyBoq(data.SourceBoqHeaderIdToCopy.Value, boqCopyInfo);
					if (copyData != null)
					{
						boqitem = copyData.BoqRootItem as BoqItemEntity;
						boqheader = copyData.BoqHeader as BoqHeaderEntity;
					}
				}
				else
				{
					var creationParam = new BoqHeaderLogic.BoqHeaderCreationParam { BasCurrencyFk = data.BasCurrencyFk };
					boqheader = new BoqHeaderLogic().CreateEntity(true, creationParam); // #!# !! mp20150429: at the moment, header is stored in db after creation, ...otherwise problems with boq properties (boq-structure/structure details are still stored immediately in db and require a stored boq-header) -> have to correct...
					boqitem = boqItemLogic.CreateBoqRootItem(boqheader, false, data.Reference);					
				}

				if (null != data.BriefInfo)
				{
					boqitem.BriefInfo = new Platform.Common.DescriptionTranslateType(data.BriefInfo);
				}

				// Make sure to have a unique reference number set, also in case of multi user issues
				var uniqueReferenceNumber = data.Reference;
				boqitem.SetRootBoqItemReference(uniqueReferenceNumber);

				boqItemLogic.SaveEntity(boqitem);

				var prcBoq = prcBoqLogic.Create(data.PrcHeaderFk, boqheader.Id, data.PackageFk);
				prcBoq = prcBoqLogic.Save(prcBoq, boqheader, data.PrcHeaderFkOriginal);	// write new entity into the database: BH: As mentioned above, currently we have to create the boq header and the boq root item and save it instantly. To avoid having saved these two instances without the prc boq we save it after creation, too -> this has to be corrected, too.

				var prcBoqExtended = new PrcBoqExtendedEntity(prcBoq, boqheader, boqitem);
				return new PrcBoqExtendedDto(prcBoqExtended);
			}
		}

		/// <summary>Creates the deep copy of a procurement package boq.</summary>
		[HttpPost]
		[Route("createdeepcopy")]
		public void CreateDeepCopy(Int32 prcHeaderId, Int32 packageId, Int32 boqHeaderId, string targetModuleName)
		{
			new PrcBoqLogic().CreateDeepCopy(prcHeaderId, packageId, boqHeaderId, targetModuleName);
		}

		/// <summary>Creates a backup of a BOQ.</summary>
		[HttpPost]
		[Route("createbackup")]
		public Object CreateBackup(Int32 boqId, String description, String comment)
		{
			new PrcBoqLogic().CreateBackup(boqId, description, comment, out var hasDependentBoqs, out var dependentEstimates, out var qtoErrorMessage);
			return new { HasDependentBoqs = hasDependentBoqs, DependentEstimates = dependentEstimates, QtoErrorMessage = qtoErrorMessage };
		}

		/// <summary>Restores a backup of a BOQ.</summary>
		[HttpPost]
		[Route("restorebackup")]
		public Object RestoreBackup(Int32 boqHeaderId)
		{
			new PrcBoqLogic().RestoreBackup(boqHeaderId, out var hasDependentBoqs, out var dependentEstimates);
			return new { HasDependentBoqs = hasDependentBoqs, DependentEstimates = dependentEstimates };
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("delete")]
		public void Delete(PrcBoqExtendedDto dto)
		{
			new PrcBoqLogic().DeleteBoq(dto.PrcBoq.Copy(), dto.BoqHeader.Copy());
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dtos"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("updateboqrootitem")]
		public IEnumerable<BoqItemDto> UpdateBoqRootItem(IEnumerable<BoqItemDto> dtos)
		{
			var entities = dtos.Select(e => e.Copy()).ToList();
			var boqItemEntities = new PrcBoqLogic().UpdateBoqRootItem(entities);
			return boqItemEntities.Select(e => new BoqItemDto(e));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id">the id of PRC_BOQ</param>
		/// <returns></returns>
		[HttpGet]
		[Route("getboqrootitem")]
		public BoqItemDto GetBoqRootItem(long id)
		{
			var boqItem = new PrcBoqLogic().GetBoqRootItem(id);
			if (boqItem != null)
			{
				return new BoqItemDto(boqItem);
			}
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getmergedboqrootitem")]
		public MergedBoqItemInfo GetMergedBoqRootItem(GetMergedBoqRootItemRequest request)
		{
			var mergedBoqInfo = new PrcBoqLogic().GetMergedBoqRootItem(request.ConHeaderId, request.PrcBoqId);
			if (mergedBoqInfo != null && mergedBoqInfo.BoqItem != null)
			{
				var boqItem = new BoqItemDto(mergedBoqInfo.BoqItem);
				var result = new MergedBoqItemInfo();
				result.BoqItem = boqItem;
				result.MergedFinalPrice = mergedBoqInfo.MergedFinalPrice;
				result.MergedFinalPriceOc = mergedBoqInfo.MergedFinalPriceOc;
				return result;
			}
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getboq")]
		public PrcBoqDto GetBoqItem(int id)
		{
			return new PrcBoqDto(new PrcBoqLogic().GetItemByKey(id));
		}

		/// <summary>
		/// Check boq reference is is unique
		/// </summary>
		/// <param name="id"></param>
		/// <param name="prcHeaderFk"></param>
		/// <param name="reference"></param>
		/// <param name="isPackage"></param>
		/// <param name="packageFk"></param>
		/// <returns></returns>
		[Route("isunique")]
		[HttpGet]
		public bool IsUnique(int id, int prcHeaderFk, string reference, bool isPackage, int packageFk = 0)
		{
			return new PrcBoqLogic().IsUnique(id, prcHeaderFk, reference, isPackage, packageFk);
		}

		/// <summary>
		/// get boq reference
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="packageFk"></param>
		/// <returns></returns>
		[Route("getuniquereference")]
		[HttpGet]
		public long GetUniqueReference(int prcHeaderFk, int packageFk = 0)
		{
			return new PrcBoqLogic().GetUniqueBoqRootReference(prcHeaderFk, packageFk);
		}

		/// <summary>
		/// Return the base boq root items for the given package
		/// </summary>
		/// <param name="packageId">package id for which the base boq root items are to be determined</param>
		/// <returns></returns>
		[HttpGet]
		[Route("getprcbaseboqs")]
		public IEnumerable<PrcBoqExtendedDto> GetPrcBaseBoqs(int packageId)
		{
			var baseBoqRootItems = new PrcBoqLogic().GetPrcBaseBoqs(packageId).ToList();
			return baseBoqRootItems.Any() ? baseBoqRootItems.Select(e => new PrcBoqExtendedDto(e)).ToList() : null;
		}

		/// <summary>
		/// Check if base boqs given by packageFk have corresponding version boqs
		/// </summary>
		/// <param name="packageFk"></param>
		/// <returns></returns>
		[Route("checkforversionboqs")]
		[HttpGet]
		public bool CheckForVersionBoqs(int packageFk)
		{
			return new PrcBoqLogic().CheckForVersionBoqs(packageFk);
		}

		/// <summary>
		/// Check if procurement boqs given by prcHeaderFk have related version boqs
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <returns></returns>
		[Route("checkforrelatedversionboqs")]
		[HttpGet]
		public bool CheckForRelatedVersionBoqs(int prcHeaderFk)
		{
			return new PrcBoqLogic().CheckForRelatedVersionBoqs(prcHeaderFk);
		}

		/// <summary>
		///  Retrieves model object IDs for a given set of item IDs.
		/// </summary>
		/// <param name="modelObjectRequest"></param>
		/// <returns></returns>
		[Route("QueryModelObjects")]
		[HttpPost]
		public String QueryModelObjects(ModelObjectRequest modelObjectRequest)
		{
			return new QueryModelObjectLogic().QueryByBoQ(modelObjectRequest.itemIds, modelObjectRequest.modelId).CreateCompressedString();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjBoqFk"></param>
		/// <param name="prjItemFk"></param>
		/// <param name="prcBoqFk"></param>
		/// <param name="contractFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("checkboqitemsbyotherboqheader")]
		public bool IsFromContractBoqItems(int prjBoqFk, int prjItemFk, int prcBoqFk, int contractFk)
		{
			var prcBoq = new PrcBoqLogic().GetSearchList(e => e.Id == prcBoqFk).FirstOrDefault();
			var contractLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<BCC.IContractHeaderInfoProvider>();
			var contract = contractLogic.GetConHeaderById(contractFk);
			if (prcBoq != null && contract != null && prcBoq.PrcHeaderFk != contract.PrcHeaderFk)
			{
				return false;
			}

			if (prcBoq != null)
			{
				var specialBoqItem = new BoqItemLogic().GetSearchList(e => e.BoqHeaderFk == prcBoq.BoqHeaderFk && e.BoqItemPrjBoqFk == prjBoqFk && e.BoqItemPrjItemFk == prjItemFk).FirstOrDefault();

				if (specialBoqItem == null)
				{
					return false;
				}

				return true;
			}

			return false;
		}

		private void ForEachBoqItem(BoqItemEntity item, Action<BoqItemEntity> action)
		{
			if (item != null)
			{
				if (action != null)
				{
					action(item);
				}

				var children = item.BoqItemChildren;
				var boqItemChildren = new List<BoqItemEntity>();
				if (children != null && children.Any())
				{
					foreach(var child in children)
					{
						var temp = child.Clone(true);
						temp.BoqItemChildren = child.BoqItemChildren;
						boqItemChildren.Add(temp);
						ForEachBoqItem(temp, action);
					}
				}
				item.BoqItemChildren = boqItemChildren;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		[Route("RecalculationBoQ")]
		[HttpGet]
		public void RecalculationBoQ(int headerId, int? vatGroupFk, string sourceType, int? taxCodeFk)
		{
			var prcHeaderFks = new List<int>();
			var boqHeaderIds = new List<int>();
			Dictionary<int, int> boqHeaderFkMapMdcTaxCodeFk = null;
			var moduleName = string.Empty;

			switch (sourceType)
			{
				case "contract":
					var contractHeaderLogic = Injector.Get<IContractHeaderInfoProvider>();
					var contractHeader = contractHeaderLogic.GetItemByKey(headerId);
					if (contractHeader != null)
					{
						var contractHeaderEntity = (IContractHeaderData)contractHeader;
						var prcHeaderFk = contractHeaderEntity.PrcHeaderFk;
						prcHeaderFks.Add(prcHeaderFk);
						taxCodeFk = contractHeaderEntity.TaxCodeFk;
					}
					moduleName = "procurement.contract";
					break;
				case "requisition":
					IGetReqHeaderLogic reqHeaderLogic = Injector.Get<IGetReqHeaderLogic>();
					var reqtractHeader = reqHeaderLogic.GetItemByKey(headerId);
					if (reqtractHeader != null)
					{
						var rfqtractHeaderEntity = (IRequistionHeaderData)reqtractHeader;
						var prcHeaderFk = rfqtractHeaderEntity.PrcHeaderFk;
						prcHeaderFks.Add(prcHeaderFk);
					}
					moduleName = "procurement.requisition";
					break;
				case "quote":
					using (var dbcontext = new DbContext(RIB.Visual.Procurement.Common.BusinessComponents.ModelBuilder.DbModel))
					{
						var qtnRequisitionEntity = dbcontext.Entities<QtnRequisitionEntity>().Where(e => e.QtnHeaderFk == headerId).ToList();
						if (qtnRequisitionEntity.Any())
						{
							prcHeaderFks = qtnRequisitionEntity.Select(e => e.PrcHeaderFk).ToList();
						}
					}
					moduleName = "procurement.quote";
					break;
				case "pes":
					var pesBoqLogic = Injector.Get<IPesBoqLogic>();
					var pesBoqEntities = pesBoqLogic.GetByPesHeaders(new List<int> { headerId });
					if (pesBoqEntities.Any())
					{
						boqHeaderIds = pesBoqEntities.Where(e => e.BoqHeaderFk.HasValue).Select(e => e.BoqHeaderFk.Value).Distinct().ToList();
						boqHeaderFkMapMdcTaxCodeFk = pesBoqEntities.Where(e => e.BoqHeaderFk.HasValue).ToDictionary(e => e.BoqHeaderFk.Value, e => e.MdcTaxCodeFk);
					}
					moduleName = "procurement.pes";
					break;
				default:
					break;
			}
			if ((prcHeaderFks.Any() || boqHeaderIds.Any()) && !string.IsNullOrEmpty(moduleName))
			{
				var prcBoqLogic = new PrcBoqLogic();
				var boqList = prcBoqLogic.GetList(e => prcHeaderFks.Contains(e.PrcHeaderFk)).ToList();
				var boqHeaderFks = boqList.Select(e => e.BoqHeaderFk).ToList();
				boqHeaderFks.AddRange(boqHeaderIds);
				var boqItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IBoqItemLogic>();
				boqItemLogic.CalcTaxGrossForBoqItemTree(boqHeaderFks, vatGroupFk, taxCodeFk, boqHeaderFkMapMdcTaxCodeFk);

				var recalculateTotalLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcRecalculateTotalsLogic>(moduleName);
				if (moduleName == "procurement.quote")
				{
					foreach (var prcHeaderId in prcHeaderFks)
					{
						recalculateTotalLogic.RecalculateTotalsByHeaderIdAfterRecalculatingBoq(prcHeaderId);
					}
				}
				else
				{
					recalculateTotalLogic.RecalculateTotalsByHeaderIdAfterRecalculatingBoq(headerId);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="updateParam"></param>
		[Route("SyncBaseBoQ")]
		[HttpPost]
		public void SyncBaseBoQ(UpdatePackageBoqDto updateParam)
		{
			int headerId = updateParam.HeaderId;
			string sourceType = updateParam.SourceType;
			SyncBoqItemOptions options = updateParam.Options;
			var prcHeaderFks = new List<int>();
			var boqHeaderIds = new List<int>();
			Dictionary<int, int> boqHeaderFkMapMdcTaxCodeFk = null;
			var moduleName = string.Empty;
			decimal? sourceRate = null;
			RVPC.IIdentifyable procurementHeader = null;
			IGetReqHeaderLogic reqHeaderLogic = Injector.Get<IGetReqHeaderLogic>();
			switch (sourceType)
			{
				case "contract":
					var contractHeaderLogic = Injector.Get<IContractHeaderInfoProvider>();
					var contractHeader = contractHeaderLogic.GetConHeaderById(headerId);
					if (contractHeader != null)
					{
						procurementHeader = contractHeader;
						var contractHeaderEntity = (IContractHeaderData)contractHeader;
						var prcHeaderFk = contractHeaderEntity.PrcHeaderFk;
						sourceRate = contractHeaderEntity.ExchangeRate;
						prcHeaderFks.Add(prcHeaderFk);
					}
					moduleName = "procurement.contract";
					break;
				case "requisition":
					var reqtractHeaders = reqHeaderLogic.GetReqHeaderByIds(new List<int> { headerId }.ToArray());
					if (reqtractHeaders.Any())
					{
						procurementHeader = reqtractHeaders.First();
						var rfqtractHeaderEntity = (IRequistionHeaderData)reqtractHeaders.FirstOrDefault();
						sourceRate = rfqtractHeaderEntity.ExchangeRate;
						var prcHeaderFk = rfqtractHeaderEntity.PrcHeaderFk;
						prcHeaderFks.Add(prcHeaderFk);
					}
					moduleName = "procurement.requisition";
					break;
				case "quote":
					using (var dbcontext = new DbContext(RIB.Visual.Procurement.Common.BusinessComponents.ModelBuilder.DbModel))
					{
						var qtnRequisitionEntity = dbcontext.Entities<QtnRequisitionEntity>().Where(e => e.QtnHeaderFk == headerId).ToList();
						if (qtnRequisitionEntity.Any())
						{
							prcHeaderFks = qtnRequisitionEntity.Select(e => e.PrcHeaderFk).ToList();
							var reqHeaderIds = qtnRequisitionEntity.CollectIds(e => e.ReqHeaderFk);
							procurementHeader = reqHeaderIds.Any() ? reqHeaderLogic.GetItemByKey(reqHeaderIds.FirstOrDefault()) : null;
						}
					}
					moduleName = "procurement.quote";
					break;
				case "pes":
					var pesBoqLogic = Injector.Get<IPesBoqLogic>();
					prcHeaderFks = new List<int> { headerId };
					var pesBoqEntities = pesBoqLogic.GetByPesHeaders(new List<int> { headerId });
					if (pesBoqEntities.Any())
					{
						boqHeaderIds = pesBoqEntities.Where(e => e.BoqHeaderFk.HasValue).Select(e => e.BoqHeaderFk.Value).Distinct().ToList();
						boqHeaderFkMapMdcTaxCodeFk = pesBoqEntities.Where(e => e.BoqHeaderFk.HasValue).ToDictionary(e => e.BoqHeaderFk.Value, e => e.MdcTaxCodeFk);
					}
					moduleName = "procurement.pes";
					break;
				default:
					break;
			}
			var prcBoqLogic = new PrcBoqLogic();
			if (sourceType != "quote")
			{
				prcBoqLogic.UpdateSyncBaseBoqAndRel(prcHeaderFks, sourceType, sourceRate, true, options, null, procurementHeader);
			}
			else
			{

				var quoteOptions = updateParam.QtoOptions;
				UpdatePackageOption updateOption = new UpdatePackageOption();
				updateOption.AddNewItem = quoteOptions.AddNewItem;
				updateOption.UpdateExistItem = quoteOptions.UpdateExistItem;
				if (quoteOptions.AddNewItem)
				{
					updateOption.AddOption.IncludeQuantities = quoteOptions.AddQuantity;
					updateOption.AddOption.IncludePrices = quoteOptions.AddPrice;
				}
				if (quoteOptions.UpdateExistItem|| quoteOptions.UpdateQuantity|| quoteOptions.UpdatePrice)
				{
					updateOption.UpdateOption.IncludeQuantities = quoteOptions.UpdateQuantity;
					updateOption.UpdateOption.IncludePrices = quoteOptions.UpdatePrice;
				}
				prcBoqLogic.UpdateSyncBaseBoqAndRel(prcHeaderFks, sourceType, sourceRate, true, null, updateOption, procurementHeader);

			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="requests"></param>
		/// <returns></returns>
		[Route("getmergedprcboqsbyfilter"), HttpPost]
		public IEnumerable<PrcBoqLookupVEntity> GetMergedPrcBoqsByFilter(IEnumerable<LookupSearchRequest> requests)
		{
			return new PrcBoqLookupVLogic().GetMergedPrcBoqsByLookupRquests(requests);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="req"></param>
		/// <returns></returns>
		[Route("getboqrootitmes")]
		[HttpPost]
		public List<BoqItemEntity> GetBoqRootItmes(GetBoqRootItemsRequest req)
		{
			var boqItems = new List<BoqItemEntity>();

			var prcBoqLogic = new PrcBoqLogic();
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqItemLogic = new BoqItemLogic();
			var prcBoqList = prcBoqLogic.GetSearchList(e => req.prcHeaderIds.Contains(e.PrcHeaderFk), req.prcHeaderIds.Any());

			if (prcBoqList != null && prcBoqList.Any())
			{
				foreach (var prcBoq in prcBoqList)
				{
					var boqHeader = boqHeaderLogic.GetEntityById(prcBoq.BoqHeaderFk);
					var boqRootItem = boqItemLogic.GetBoqRootItemByHeaderId(boqHeader.Id);
					boqItems.Add(boqRootItem);
				}
			}
			return boqItems;
		}

		/// <summary>
		/// 
		/// </summary>
		public class GetBoqRootItemsRequest
		{
			/// <summary>
			/// 
			/// </summary>
			public List<int> prcHeaderIds { get; set; }
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="updateVersionBoqDto"></param>
		/// <returns></returns>
		[Route("updateversionboq"), HttpPost]
		public UpdateVersionBoqResultDto UpdateVersionBoQ(UpdateVersionBoqDto updateVersionBoqDto)
		{
			var entity = updateVersionBoqDto.Copy();
			var resultList = new UpdateVersionBoQLogic(entity).UpdateVersionBoq();
			var updateVersionBoqResultDto = new UpdateVersionBoqResultDto();
			updateVersionBoqResultDto.updateVersionBoqDto = updateVersionBoqDto;
			updateVersionBoqResultDto.processResults = resultList;
			return updateVersionBoqResultDto;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[Route("getboqitemidsiscontracted"), HttpPost]
		public IEnumerable<RVPC.IdentificationData> GetBoqItemIdsIsContracted(GetBoqItemIdsIsContractedRequest request)
		{
			if (request == null)
			{
				throw new HttpResponseException(System.Net.HttpStatusCode.BadRequest);
			}

			var boqItems = new BoqItemLogic().GetSimpleBoqItems(request.BoqHeaderId);
			var map = new PrcBoqLogic().SetBoqItemsIsContracted(boqItems, request.ModuleName);
			return map.Where(e => e.Value).Select(e => e.Key).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		public class UpdateVersionBoqResultDto
		{
			/// <summary>
			/// 
			/// </summary>
			public UpdateVersionBoqDto updateVersionBoqDto { set; get; }
			/// <summary>
			/// 
			/// </summary>
			public IEnumerable<ProcessResult> processResults { set; get; }
		}

		/// <summary>
		/// 
		/// </summary>
		public class MergedBoqItemInfo
		{
			/// <summary>
			/// 
			/// </summary>
			public BoqItemDto BoqItem { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public decimal MergedFinalPrice { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public decimal MergedFinalPriceOc { get; set; }
		}
	}
}