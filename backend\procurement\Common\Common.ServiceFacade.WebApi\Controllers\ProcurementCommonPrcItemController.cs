/*
 * $Id$
 * Copyright (c) RIB Software SE
 */

using System.Linq;
using System.Linq.Expressions;
using System.Web.Http;
using System.Net.Http;
using System.Collections.Generic;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Basics.Material.ServiceFacade.WebApi;
using System;
using RIB.Visual.Basics.Unit.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi;
using RIB.Visual.Basics.Common.BusinessComponents.Logic;
using RIB.Visual.Basics.PriceCondition.BusinessComponents;
using RIB.Visual.Basics.PriceCondition.ServiceFacade.WebApi;
using System.Web.Http.ModelBinding;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Platform.Core;
using RIB.Visual.Procurement.Common.ServiceFacade.WebApi.Controllers;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Basics.MaterialCatalog.BusinessComponents;
using RIB.Visual.Procurement.Common.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;

namespace RIB.Visual.Procurement.Common.ServiceFacade.WebApi
{
	/// <summary>
	/// Base class of WebApi service facades
	/// </summary>
	[RoutePrefix("procurement/common/prcitem")]
	public class ProcurementRequisitionPrcItemController : ApiControllerBase<PrcItemLogic>
	{
		/// <summary>
		/// </summary>
		private const string AIMaterialAlternativePermissionDescriptor = "77a641750f774c71a05f263977b287b1";

		private Func<DbContext, Expression<Func<PrcItemEntity, Boolean>>> GenerateFilterExpressionFactory(
			Expression<Func<PrcItemEntity, Boolean>> filterExpression, Int32? selModelId, String selModelObjectIds)
		{
			Func<DbContext, Expression<Func<PrcItemEntity, Boolean>>> filterExpressionFactory = null;
			if (selModelObjectIds != null)
			{
				var selectedObjectIds = ModelElementIdCompressor.ParseCompressedString(selModelObjectIds).ToArray();

				Expression<Func<PrcItem2MdlObjectVEntity, Boolean>> modelFilterExpression = null;
				if (selectedObjectIds.Length > 0)
				{
					modelFilterExpression = selectedObjectIds.CreateEntityFilterExpression<PrcItem2MdlObjectVEntity>(e => e.ObjectFk);
				}
				else
				{
					if (selModelId.HasValue)
					{
						var subModelIds =
							BusinessApplication.BusinessEnvironment.GetExportedValue<ISubModelLogic>()
								.GetSubModelIdsForModel(selModelId.Value);
						modelFilterExpression = e => subModelIds.Contains(e.ModelFk);
					}
				}

				if (modelFilterExpression != null)
				{
					filterExpressionFactory = dbCtx => new[]
					{
						filterExpression,
						e => dbCtx.Entities<PrcItem2MdlObjectVEntity>().Where(i2O => i2O.Id == e.Id).Any(modelFilterExpression)
					}.JoinWithAnd();
				}
			}
			if (filterExpressionFactory == null)
			{
				filterExpressionFactory = dbCtx => filterExpression;
			}

			return filterExpressionFactory;
		}

		/// <summary>
		/// Get the prcitems corresponding to prcHeaderId supplied by parameter
		/// </summary>
		/// <param name="mainItemId">prcHeaderId</param>
		/// <param name="projectId"></param>
		/// <param name="selModelId">Optionally, the ID of the selected model.</param>
		/// <param name="selModelObjectIds">Optionally, the IDs of the selected model objects as a compressed string.</param>
		/// <param name="moduleName">module name, like procurement.package, procurement.quote</param>
		/// <returns></returns>
		[Route("list")]
		[HttpGet]
		public Dictionary<string, object> GetList([System.Web.Http.FromUri] int mainItemId, [System.Web.Http.FromUri] int? projectId, Int32? selModelId = null,
			String selModelObjectIds = null, string moduleName = null)
		{
			var filter = GenerateFilterExpressionFactory(x => x.PrcHeaderFk == mainItemId, selModelId, selModelObjectIds);
			var PrcItemEntities = Logic.GetSearchListCompleteWithExpressionFactory(filter, true, e => e.OrderBy(x => x.Itemno)).ToList();

			Logic.PopulatePrcItemFields(PrcItemEntities, mainItemId, moduleName);

			var PrcItemDtos = PrcItemEntities.ConvertAll(e => new PrcItemDto(e));

			var jsData = PrcItemDtos.CollectLookups<PrcItemDto>(collercotr => collercotr
				.Add("PrcPackage", x => x.PrcPackageFk)
				.Add("UoM", x => x.BasUomPriceUnitFk, x => x.BasUomFk)
				.Add("PrcStructure", x => x.PrcStructureFk)
				.Add("PrcIncoterm", x => x.PrcIncotermFk)
				.Add("MaterialCommodity", x => x.MdcMaterialFk)
				.Add("PaymentTerm", x => x.BasPaymentTermPaFk)
				.Add("PrcPricecondition", x => x.PrcPriceConditionFk)
				.Add("Controllingunit", x => x.MdcControllingunitFk)
				.Add("PrcItemStatus", x => x.PrcItemstatusFk)
				.Add("TaxCode", x => x.MdcTaxCodeFk)
				.Add("PrcItemEvaluation", x => x.PrcItemEvaluationFk)
				);

			//#93204 implement the PRJ_STOCK_FK And PRJ_STOCKLOCATION_FK UI (lookup and the same read only controll as PES_ITEM)
			jsData.AddRange(PrcCommon2Stock.GetStockCache());

			// cost group
			var itemIds = PrcItemDtos.CollectIds(e => e.Id).ToList();
			var assembly2CostGroups = new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP").GetByFilter(e => itemIds.Contains(e.MainItemId64.Value)).ToList();
			var costGroupCats = new CostGroupCatLogic().GetCostGroupCatsByModule(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Procurement, projectId);

			//tax code matrix
			var taxCodeMatrixes = new TaxCodeMatrixLogic().GetSearchList(e => e.TaxCodeFk > 0).ToList();

			jsData.Add("PrcItem2CostGroups", assembly2CostGroups);
			jsData.Add("CostGroupCats", costGroupCats);
			jsData.Add("TaxCodeMatrix", taxCodeMatrixes);

			if (moduleName == ProcurementCommonModuleDataConfiguration.QuoteModuleIdentifier)
			{
				var requisitionPrcItemsItems = Logic.GetRequisitionPrcItemsItems(mainItemId).ToList();
				jsData.Add("RequisitionPrcItemsItems", requisitionPrcItemsItems);
			}

			if (moduleName == ProcurementCommonModuleDataConfiguration.ContractModuleIdentifier)
			{
				var callOffItemLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ICallOffItemLogic>();
				var relatedPrcItems = callOffItemLogic?.GetRelatedPrcItems(mainItemId);
				if (relatedPrcItems?.Any() == true)
				{
					jsData.Add("RelatedPrcItems", relatedPrcItems);
				}
			}
			return jsData;
		}

		/// <summary>
		/// package item material mapping by AI
		/// </summary>
		/// <param name="companyCurrencyId"></param>
		/// <param name="prcHeaderFk"></param>
		/// <returns></returns>
		[Route("mtwoai/itemmaterialmapping")]
		[HttpGet]
		[Permission(Permissions.Execute, AIMaterialAlternativePermissionDescriptor)]
		public Dictionary<string, object> ItemMaterialMapping([System.Web.Http.FromUri] int companyCurrencyId, [System.Web.Http.FromUri] int prcHeaderFk)
		{
			var mainEntities = Logic.ItemMaterialMapping(companyCurrencyId, prcHeaderFk);

			// PrcItemAIMappingDto
			IEnumerable<PrcItemEntity> prcItemEntities = mainEntities["PrcItems"] as IEnumerable<PrcItemEntity>;
			var prcItemAIMappingDto = prcItemEntities.ToDtos(x => new PrcItemAIMappingDto(x));

			foreach (PrcItemAIMappingDto dto in prcItemAIMappingDto)
			{
				dto.OriginalMaterialFk = dto.MdcMaterialFk;
				dto.IsCheckAi = false;
				if (dto.SuggestedMaterialFks != null && dto.SuggestedMaterialFks.Count > 0 &&
					dto.SuggestedMaterialFks[0] != dto.MdcMaterialFk)
				{
					dto.IsCheckAi = true;
					dto.MdcMaterialFk = dto.SuggestedMaterialFks[0];
				}
			}

			//MaterialDto
			IEnumerable<MaterialEntity> materialEntities = mainEntities["Materials"] as IEnumerable<MaterialEntity>;
			var materialDtos = materialEntities.ToDtos(x => new MaterialDto(x));

			// AIReplaceMaterialDto
			IEnumerable<AIReplaceMaterialEntity> alternativesEntities =
				mainEntities["Alternatives"] as IEnumerable<AIReplaceMaterialEntity>;
			var alternativesDtos = alternativesEntities.ToDtos(x => new AIReplaceMaterialDto(x));

			var jsData = new Dictionary<string, object>();
			jsData["Main"] = prcItemAIMappingDto;
			jsData["Materials"] = materialDtos;
			jsData["Alternatives"] = alternativesDtos;

			return jsData;
		}

		/// <summary>
		/// Replace the material of procurement items with AI suggestion
		/// </summary>
		/// <param name="updateDatas"></param>
		/// <returns></returns>
		[Route("mtwoai/aireplaceitemmaterial")]
		[HttpPost]
		[Permission(Permissions.Execute, AIMaterialAlternativePermissionDescriptor)]
		public bool AIReplaceItemMaterial(AIReplaceMaterialCompleteDto updateDatas)
		{
			IEnumerable<AIReplaceMaterialEntity> entities = updateDatas.AIReplaceMaterials.ToEntities(x => x.Copy());
			return Logic.AIReplaceItemMaterial(updateDatas.CompanyCurrencyId, updateDatas.ProjectId, entities);
		}

		/// <summary>
		/// package item material mapping by AI - two
		/// </summary>
		/// <param name="prcHeaderFk"></param>
		/// <param name="selectList"></param>
		/// <returns></returns>
		[Route("mtwoai/materialalternativemapping")]
		[HttpGet]
		[Permission(Permissions.Execute, AIMaterialAlternativePermissionDescriptor)]
		public Dictionary<string, object> MaterialAlaternativeMapping([System.Web.Http.FromUri] int prcHeaderFk, [System.Web.Http.FromUri] List<long> selectList)
		{
			var mainEntities = Logic.MaterialAlternativeMapping(prcHeaderFk, selectList);

			// PrcItemAIMappingDto
			IEnumerable<AIUpdateMaterialEntity> aiUpdateEntities = mainEntities["Main"] as IEnumerable<AIUpdateMaterialEntity>;
			var aiUpdateMaterialDto = aiUpdateEntities.ToDtos(x => new AIUpdateMaterialDto(x));

			var jsData = new Dictionary<string, object>();
			jsData["Main"] = aiUpdateMaterialDto;

			return jsData;
		}

		/// <summary>
		/// Replace the material of procurement items with AI suggestion -- two
		/// </summary>
		/// <param name="updateDatas"></param>
		/// <returns></returns>
		[Route("mtwoai/aiupdatematerial")]
		[HttpPost]
		[Permission(Permissions.Execute, AIMaterialAlternativePermissionDescriptor)]
		public bool AIUpdateMaterial(AIUpdateMaterialCompleteDto updateDatas)
		{
			var itemNos =
				Logic.GetSearchList(prcItem => prcItem.PrcHeaderFk == updateDatas.Param.PrcHeaderFk).Select(x => x.Itemno);
			var totalNos = updateDatas.Param.Itemnos.Union(itemNos);

			//need to consider the maximum line no from basis contract and all already existing change orders
			if (updateDatas.Param.IsContract && updateDatas.Param.FrmHeaderFk != null &&
				updateDatas.Param.ContractHeaderFk != null)
			{
				var itemNosFromBasiceContract = GetItemNosByBasicContract(updateDatas.Param);
				totalNos = totalNos.Union(itemNosFromBasiceContract);
			}

			foreach (var updateData in updateDatas.AIUpdatePrcItems)
			{
				var prcItem = Logic.GetItemByKey((int)updateData.PrcItemId);
				//Covert nomal into base
				if (1 == prcItem.BasItemType2Fk)
				{
					var updateItemList = new List<PrcItemEntity>();
					prcItem.BasItemType2Fk = 2;
					prcItem.PrcItemAltFk = prcItem.Id;
					updateItemList.Add(prcItem);
					Logic.Save(updateItemList, false);
				}

				#region According to diffent materials, create new alternative item and set field

				var maxNo = Logic.GetMaxItemNo(updateDatas.Param.PrcHeaderFk, totalNos);
				var entity = Logic.CreateNew(updateDatas.Param.PrcHeaderFk, updateDatas.Param.ConfigurationFk,
					updateDatas.Param.ProjectFk, maxNo, true);
				Logic.CreateAddress(updateDatas.Param.FrmHeaderFk, updateDatas.Param.FrmStyle, entity);

				entity.PrcHeaderFk = prcItem.PrcHeaderFk;
				entity.PrcItemstatusFk = prcItem.PrcItemstatusFk;
				entity.AlternativeUomFk = entity.BasUomFk = prcItem.BasUomFk;
				entity.BasUomPriceUnitFk = prcItem.BasUomPriceUnitFk;
				entity.PriceUnit = prcItem.PriceUnit;
				entity.AlternativeQuantity = entity.Quantity = prcItem.Quantity;
				entity.PrcPriceConditionFk = prcItem.PrcPriceConditionFk;
				if (updateDatas.Param.InstanceId != null)
				{
					entity.InstanceId = updateDatas.Param.InstanceId.Value;
				}
				if (updateDatas.Param.PrcPackageFk != null)
				{
					entity.PrcPackageFk = updateDatas.Param.PrcPackageFk;
				}

				if (updateDatas.Param.TaxCodeFk.HasValue && entity.PrcStructureFk == null)
				{
					entity.MdcTaxCodeFk = updateDatas.Param.TaxCodeFk;
				}
				entity.BasPaymentTermFiFk = updateDatas.Param.BasPaymentTermFiFk;
				entity.BasPaymentTermPaFk = updateDatas.Param.BasPaymentTermPaFk;

				//set to alternatives status and Userdefined1
				entity.BasItemType2Fk = 5;
				entity.PrcItemAltFk = prcItem.Id;
				entity.Userdefined1 = updateData.Top;

				# endregion

				// keep maxNo
				totalNos.ToList().Add(maxNo);

				//save items
				List<PrcItemEntity> items = new List<PrcItemEntity>();
				items.Add(entity);
				var saveEntity = Logic.Save(items.AsEnumerable(), true);

				//caculate and replace material and save
				bool status = Logic.AIUpdateMaterial(updateDatas.Param.ProjectFk, updateDatas.CompanyCurrencyId,
					updateDatas.Param.PrcHeaderFk, saveEntity.FirstOrDefault(), updateData.SuggestedMaterialId);
			}
			return true;
		}

		/// <summary>
		/// Get the item data
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="selModelId">Optionally, the ID of the selected model.</param>
		/// <param name="selModelObjectIds">Optionally, the IDs of the selected model objects as a compressed string.</param>
		/// <returns></returns>
		[Route("tree")]
		[HttpGet]
		public Object GetTree(int mainItemId, Int32? selModelId = null, String selModelObjectIds = null)
		{
			if (mainItemId < 0)
			{
				return null;
			}

			var filterExpressionFactory =
				GenerateFilterExpressionFactory(
					prcItem => prcItem.PrcHeaderFk == mainItemId && prcItem.PrcReplacementItemFk == null, selModelId, selModelObjectIds);
			var prcItems = this.Logic.GetSearchListCompleteWithExpressionFactory(filterExpressionFactory, true,
				e => e.OrderBy(x => x.Itemno));
			var replacementItemIds = prcItems.Select(e => e.Id);
			var replacementItems =
				this.Logic.GetSearchListComplete(
					prcItem => prcItem.PrcReplacementItemFk != null && replacementItemIds.Contains(prcItem.PrcReplacementItemFk.Value),
					replacementItemIds.Any());


			var uomIds = new List<int>();
			var prcpriceconditionIds = new List<int>();
			var materialIds = new List<int>();
			var addressIds =
				replacementItems.Where(e => e.BasAddressFk.HasValue).Select(e => e.BasAddressFk.Value).Distinct().ToList();
			var prcItemstatusIds = new List<int>();
			var reqStatusIds = new List<int>();
			var prcIds = new List<int>();
			var prcItemEvalutionIds = new List<int>();

			foreach (var item in prcItems)
			{
				uomIds.AddItemNoRepeatByEquals(item.BasUomFk);
				uomIds.AddItemNoRepeatByEquals(item.BasUomPriceUnitFk);

				prcpriceconditionIds.AddItemNoRepeatByEquals(item.PrcPriceConditionFk);

				materialIds.AddItemNoRepeatByEquals(item.MdcMaterialFk);

				addressIds.AddItemNoRepeatByEquals(item.BasAddressFk);

				prcItemstatusIds.AddItemNoRepeatByEquals(item.PrcItemstatusFk);

				prcItemEvalutionIds.AddItemNoRepeatByEquals(item.PrcItemEvaluationFk);
			}

			var fillAddress = new Func<IEnumerable<PrcItemEntity>, IEnumerable<int>, IEnumerable<AddressEntity>>((items, ids) =>
			{
				var addresses = new AddressLogic().GetSearchList(e => ids.Contains(e.Id), ids.Any());
				foreach (var item in items)
				{
					if (item.BasAddressFk.HasValue)
					{
						item.AddressEntity = addresses.FirstOrDefault(e => e.Id == item.BasAddressFk.Value);
					}
				}
				return addresses;
			});

			var prcHeaders = new PrcHeaderLogic().GetSearchList(entity => prcIds.Contains(entity.Id), prcIds.Any());
			var address =
				fillAddress(prcItems.Concat(replacementItems), addressIds).Select(entity => new AddressDto(entity)).ToList();
			var main =
				prcItems.Select(
					e =>
						new PrcItemDto(e)
						{
							ReplacementItems =
								replacementItems.Where(x => x.PrcReplacementItemFk == e.Id).Select(x => new PrcItemDto(x)).ToList()
						}).ToList();
			var uom =
				new BasicsUomLookupLogic().GetSearchList(e => uomIds.Contains(e.Id), uomIds.Any())
					.Select(entity => new RIB.Visual.Basics.Unit.ServiceFacade.WebApi.UomDto(entity))
					.ToList();
			var prcpricecondition =
				new PriceConditionLogic().GetSearchList(e => prcpriceconditionIds.Contains(e.Id), prcpriceconditionIds.Any())
					.Select(entity => new PriceConditionDto(entity))
					.ToList();
			var material =
				new MaterialLogic().GetSearchList(e => materialIds.Contains(e.Id), materialIds.Any())
					.Select(entity => new MaterialDto(entity))
					.ToList();
			var prcItemstatus =
				new PrcItemstatusLogic().GetSearchList(e => prcItemstatusIds.Contains(e.Id), prcItemstatusIds.Any())
					.Select(entity => new PrcItemstatusDto(entity))
					.ToList();
			var prcItemEvalutions =
				new PrcItemEvalutionLogic().GetSearchList(e => prcItemEvalutionIds.Contains(e.Id), prcItemEvalutionIds.Any())
					.Select(entity => new PrcItemEvaluationDto(entity))
					.ToList();

			return new
			{
				Main = main,
				Uom = uom,
				Prcpricecondition = prcpricecondition,
				MaterialRecord = material,
				Address = address,
				PrcItemStatus = prcItemstatus,
				PrcItemEvalution = prcItemEvalutions
			};
		}


		/// <summary>
		/// Returns the JSON schema description of the getprcitemschema class
		/// </summary>
		/// <returns></returns>
		[Route("schema")]
		[HttpGet]
		public HttpResponseMessage GetSchema()
		{
			return GetJsonSchema(typeof(PrcItemEntity));
		}

		/// <summary>
		/// update header delivery date to prcitem
		/// </summary>
		/// <returns></returns>
		[Route("updateDeliveryDateToItem")]
		[HttpPost]
		public void UpdateDeliveryDateToItem(UpdateDeliveryDateToItemDto entityToItem)
		{
			if (entityToItem == null)
			{
				return;
			}

			var items = Logic.GetSearchList(e => e.PrcHeaderFk == entityToItem.prcHeaderFK);
			var itemDeliveryLogic = new PrcItemdeliveryLogic();

			foreach (var item in items)
			{
				item.DateRequired = entityToItem.newDate;
				if (entityToItem.updateFlag)
				{
					item.PriceExtraOc = entityToItem.expressFree;
				}

				if (item.Hasdeliveryschedule == true)
				{
					item.Hasdeliveryschedule = false;
					var itemDeliveryEntities = itemDeliveryLogic.GetSearchList(e => e.PrcItemFk == item.Id);

					if (itemDeliveryEntities != null && itemDeliveryEntities.Any())
					{
						itemDeliveryLogic.DeleteEntities(itemDeliveryEntities);
					}
				}
			}

			Logic.Save(items, false);
		}

		/// <summary>
		/// update header delivery date to prcitem
		/// </summary>
		/// <returns></returns>
		[Route("getTotalLeadTime")]
		[HttpPost]
		public decimal getTotalLeadTime(PrcItemEntity prcItem)
		{
			var prcItemLogic = new PrcItemLogic();
			decimal totalLeadTime = 0;
			var prcItems = prcItemLogic.GetPrcItemsByPrcHeader(prcItem.PrcHeaderFk);
			if (prcItems != null && prcItems.Any())
			{
				totalLeadTime = prcItems.FirstOrDefault().TotalLeadTime;
			}

			return totalLeadTime;
		}

		/// <summary>
		/// GetNewPrcItem
		/// </summary>
		/// <returns></returns>
		[Route("create")]
		[HttpPost]
		public PrcItemDto Create(PrcItemCreateParameter param)
		{
			var prcHeaderLogic = new PrcHeaderLogic();
			var prcItemLogic = new PrcItemLogic();
			var totalNos = param.Itemnos;

			// need to consider the maximum line no from basis contract and all already existing change orders
			if (param.IsContract && param.FrmHeaderFk != null && param.ContractHeaderFk != null && !param.IsCallOffContract)
			{
				totalNos = totalNos.Union(GetItemNosByBasicContract(param));
			}

			int? itemNumber = null;
			if (param.FrmStyle == (int)FrmStyle.FromPriceComparisonQuoteReq)
			{
				if (param.InsertOptions != null)
				{
					var selectedItem = param.InsertOptions.SelectedItem?.Copy();
					var prcItems = param.InsertOptions.PrcItems?.Select(e => e.Copy());
					itemNumber = this.Logic.TryCreateInsertItemNumber(selectedItem, prcItems, param.InsertOptions.InsertBefore);
				}
				else
				{
					totalNos = prcItemLogic.GetSearchList(e => e.PrcHeaderFk == param.PrcHeaderFk).CollectIds(e => e.Itemno);
				}
			}

			itemNumber ??= Logic.GetMaxItemNo(param.PrcHeaderFk, totalNos);

			// #94630 - In REQ & CON module, when mannually add PRC_ITEM, fill in the PRC_ITEM_FK and make it same as PRC_ITEM.ID.
			var isSelfRef = param.IsPackage || param.FrmStyle is (int)FrmStyle.FromReq or (int)FrmStyle.FromContract or (int)FrmStyle.FromQuote;
			var entity = Logic.CreateNew(param.PrcHeaderFk, param.ConfigurationFk, param.ProjectFk, itemNumber.Value, isSelfRef, param.PrcPackageFk);

			if (param.IsCallOffContract)
			{
				var prcItemDict = BusinessApplication.BusinessEnvironment.GetExportedValue<ICallOffItemLogic>()?.GetRelatedPrcItems(param.PrcHeaderFk);
				if (itemNumber != null && prcItemDict?.TryGetValue(itemNumber.Value, out var prcItem) == true)
				{
					var originalPrcItem = Logic.GetItemByKey(prcItem.Id);

					originalPrcItem.Id = entity.Id;
					originalPrcItem.PrcHeaderFk = entity.PrcHeaderFk;
					originalPrcItem.PrcItemFk = entity.PrcItemFk;
					originalPrcItem.Itemno = entity.Itemno;
					originalPrcItem.Version = entity.Version;
					originalPrcItem.InsertedAt = entity.InsertedAt;
					originalPrcItem.InsertedBy = entity.InsertedBy;

					originalPrcItem.Quantity = prcItem.Quantity;
					originalPrcItem.MdcMaterialFk = prcItem.MdcMaterialFk;

					entity = originalPrcItem;
					if (originalPrcItem.BasBlobsSpecificationFk != null)
					{
						entity.BasBlobsSpecificationFk = new BlobLogic().CopyBlob((int)originalPrcItem.BasBlobsSpecificationFk);
					}
					Logic.CopyAddressToPrcItem(entity);
				}
			}
			Logic.CreateAddress(param.FrmHeaderFk, param.FrmStyle, entity);
			if (param.ParentId != null)
			{
				var prcItem = Logic.GetItemByKey(param.ParentId.Value);
				var newPrcHeader = prcHeaderLogic.Save(prcHeaderLogic.CreateEntity(RubricConstant.Quotation));
				entity.PrcHeaderFk = newPrcHeader.Id;
				entity.PrcReplacementItemFk = prcItem.Id;
				entity.PrcItemstatusFk = prcItem.PrcItemstatusFk;
				entity.AlternativeUomFk = entity.BasUomFk = prcItem.BasUomFk;
				entity.BasUomPriceUnitFk = prcItem.BasUomPriceUnitFk;
				entity.PriceUnit = prcItem.PriceUnit;
			}

			Logic.UpdatePrcItemAltFkByItemType2(entity);
			Logic.UpdateFieldsByCreateParam(entity, param);

			if (entity.MdcMaterialFk.HasValue)
			{
				entity.Material2Uoms = new Material2basUomLogic().GetMaterial2UomItems(entity.MdcMaterialFk);
			}
			prcItemLogic.UpdateAlternativeQuantity([entity]);

			return new PrcItemDto(entity)
			{
				PrcItemIncreaseStep = Logic.GetPrcItemIncreaseStep()
			};
		}

		private IEnumerable<int> GetItemNosByBasicContract(PrcItemCreateParameter param)
		{
			var conHeaderLookupViewLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IConHeaderLookupViewLogic>();
			var filterValue = " (ConHeaderFk=" + param.ContractHeaderFk + " || Id=" + param.ContractHeaderFk + " )";
			var prcHeaderIds =
				conHeaderLookupViewLogic.GetListOfInterface(filterValue, 0, null).Select(x => x.PrcHeaderId).ToList();
			var itemNos = Logic.GetPrcItemsByPrcHeader(prcHeaderIds).Select(x => x.Itemno);
			return itemNos;
		}

		/// <summary>
		/// Get latest currency rate
		/// </summary>
		/// <param name="currencyForeignId">currencyForeignId</param>
		/// <returns>Lastest  Currency Rate</returns>
		[Route("getlastestcurrencyrate")]
		[HttpGet]
		public decimal GetLatestCurrencyRate(int currencyForeignId)
		{
			return Logic.GetLatestCurrencyRate(currencyForeignId);
		}

		/// <summary>
		/// Get Factor By Source UoM and Target UoM
		/// </summary>
		/// <param name="sourceUoMId">source uom id</param>
		/// <param name="targetUoMId">target uom id</param>
		/// <returns>Lastest  Currency Rate</returns>
		[Route("getfactorbysourcetargetuom")]
		[HttpGet]
		public decimal? GetFactorBySourceTargetUoM(int? sourceUoMId, int? targetUoMId)
		{
			if (!sourceUoMId.HasValue || !targetUoMId.HasValue)
			{
				return null;
			}
			return new UoMConversionExtension().GetUomConvertRate(sourceUoMId.Value, targetUoMId.Value);
		}

		/// <summary>
		/// Get Items those need to be created by filter
		/// </summary>
		/// <param name="filters"></param>
		/// <returns></returns>
		[Route("getitems4create")]
		[HttpPost]
		public IEnumerable<PrcItemLookupVDto> GetItems4Create(Dictionary<string, object> filters)
		{
			if (filters == null)
			{
				return new List<PrcItemLookupVDto>();
			}

			return new PrcItemLookupVLogic().GetItems4Create(filters).ToDtos(e => new PrcItemLookupVDto(e));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="externalsourceDesc"></param>
		/// <param name="externalCode"></param>
		/// <returns></returns>
		[Route("externalstatus"), HttpGet]
		public int? GetStatusIdByExternalCode(string externalsourceDesc, string externalCode)
		{
			return new PrcItemstatusLogic().GetItemStatusByExternalCode(externalCode, externalsourceDesc);
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[Route("getdeliveredstatus")]
		[HttpGet]
		public IEnumerable<PrcItemstatusDto> GetDeliveredStatus()
		{
			return new PrcItemstatusLogic().GetSearchList(e => e.IsDelivered == true).ToDtos(e => new PrcItemstatusDto(e));
		}

		/// <summary>
		/// Retrieves model object IDs for a given set of item IDs.
		/// </summary>
		/// <param name="itemIds">The procurement item IDs.</param>
		/// <param name="modelId">An optional model ID whose objects are being queried.</param>
		/// <returns>The object IDs in compressed string format.</returns>
		[Route("QueryModelObjects")]
		[HttpGet]
		public String QueryModelObjects([ModelBinder(typeof(Int64ArrayModelBinder))] IEnumerable<Int64> itemIds,
			Int32? modelId = null)
		{
			return new QueryModelObjectLogic().Query("PRC_ITEM2MDL_OBJECT_V", itemIds, modelId).CreateCompressedString();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[Route("availableprcitemstatus")]
		[HttpGet]
		public IEnumerable<PrcItemstatusDto> GetAvailableStatus(string statusName, int statusFrom, int? projectId = null)
		{
			var availableStatusList = new StatusLogic(statusName).GetAvailableTargetStatus(statusFrom, projectId);
			if (availableStatusList != null && availableStatusList.Any())
			{
				return new PrcItemstatusLogic().GetPrcItemStatusEntities(availableStatusList).Select(e => new PrcItemstatusDto(e));
			}

			return null;
		}

		/// <summary>
		/// get prcItem with the same itemNo by basis contract or a previous change order
		/// </summary>
		/// <param name="itemNo"></param>
		/// <param name="prcHeaderFk"></param>
		/// <param name="conHeaderFk"></param>
		/// <param name="inContractModule"></param>
		/// <returns></returns>
		[Route("getpreviewprcItembyitemno")]
		public Dictionary<string, object> GetPreviewPrcItemByItemNo(int conHeaderFk, int itemNo, int prcHeaderFk, bool inContractModule = false)
		{
			var conHeaderLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IConHeaderLookupViewLogic>();
			var prcHeaderIds = conHeaderLogic
				 .GetListOfInterface($"(ConHeaderFk={conHeaderFk} || Id={conHeaderFk})", 0, null)
				 .Select(x => x.PrcHeaderId)
				 .ToList();

			var item = Logic.GetPrcItemsByPrcHeader(prcHeaderIds)
				 .Where(x => x.Itemno == itemNo && !x.IsDisabled)
				 .OrderBy(x => x.Id)
				 .FirstOrDefault();

			if (item == null)
			{
				return [];
			}


			var contract = conHeaderLogic.GetSearchList(e => e.Id == conHeaderFk).FirstOrDefault();
			if (contract != null)
			{
				item.MainExchangeRate = contract.Exchangerate;
				item.MainVatPercent = new PrcCommonGetVatPercentLogic().GetVatPercent(item.MdcTaxCodeFk, contract.BpdVatGroupFk);
			}

			if (inContractModule)
			{
				var prcItemDict = BusinessApplication.BusinessEnvironment.GetExportedValue<ICallOffItemLogic>()?.GetRelatedPrcItems(prcHeaderFk);
				if (prcItemDict?.TryGetValue(itemNo, out var prcItem) == true)
				{
					var original = Logic.GetItemByKeyComplete(prcItem.Id);
					Logic.PopulatePrcItemFields([original], prcHeaderFk);

					original.Quantity = prcItem.Quantity;
					original.MdcMaterialFk = prcItem.MdcMaterialFk;
					item = original;
					if (original.BasBlobsSpecificationFk != null)
					{
						item.BasBlobsSpecificationFk = new BlobLogic().CopyBlob((int)original.BasBlobsSpecificationFk);
					}
					Logic.CopyAddressToPrcItem(item);
				}

				var items = new List<PrcItemEntity> { item };
				Logic.GetRelatedContractsAndItems(items, out var conHeadersOfItems, out var relatedConHeaders, out var relatedItems);
				Logic.SetContractGrandQuantityTotalCallOffQuantity(item, conHeadersOfItems, relatedConHeaders, relatedItems);
			}

			var priceConditions = new PrcItemPriceConditionLogic()
				 .CreateFromPrcItem(item)
				 .ToDtos(e => new PrcItemPriceConditionDto(e as PrcItemPriceConditionEntity));

			return new Dictionary<string, object>
			{
				["Main"] = item,
				["PriceConditions"] = priceConditions
			};
		}


		/// <summary>
		/// get prcItems by basis contract or a previous change order
		/// </summary>
		/// <param name="conHeaderFk"></param>
		/// <returns></returns>
		[Route("getprcitemsbyheader")]
		public IEnumerable<PrcItemEntity> GetPrcItemsByHeader(int conHeaderFk)
		{
			var conHeaderLookupViewLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IConHeaderLookupViewLogic>();
			var filterValue = " (ConHeaderFk=" + conHeaderFk + " || Id=" + conHeaderFk + " )";
			var prcHeaderIds =
				conHeaderLookupViewLogic.GetListOfInterface(filterValue, 0, null).Select(x => x.PrcHeaderId).ToList();
			var parentItems = Logic.GetPrcItemsByPrcHeader(prcHeaderIds).ToList();
			return parentItems;
		}

		/// <summary>
		/// Get prc item by id
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		[Route("getbyid")]
		[HttpGet]
		public PrcItemDto GetPrcItemsById(int id)
		{
			PrcItemDto dto = null;
			var item = Logic.GetSearchListComplete(e => e.Id == id).FirstOrDefault();
			if (item != null)
			{
				dto = new PrcItemDto(item);
				var materialId = dto.MdcMaterialFk;
				if (materialId.HasValue)
				{
					var material = new MaterialLogic().GetSearchList(e => e.Id == dto.MdcMaterialFk, true).FirstOrDefault();
					dto.MaterialCode = material == null ? null : material.Code;
					dto.MaterialDescription = material == null ? null : material.Description1;
					var materialCatalog = Logic.GetMaterialCatalogByMaterialGroupId(material.MaterialGroupFk);
					if (materialCatalog != null)
					{
						dto.MaterialCatalogCode = materialCatalog.MaterialCatalogCode;
						dto.MaterialCatalogDescription = materialCatalog.MaterialCatalogDescription;
						dto.MaterialCatalogTypeFk = materialCatalog.MaterialCatalogTypeFk;
						dto.MaterialCatalogSupplier = materialCatalog.MaterialCatalogSupplier;
					}
				}

				var orderTextBlobEntities =
					new PrcItemblobLogic().GetSearchList(e => e.PrcItemFk == dto.Id && e.PrcTexttypeFk == 1);
				if (orderTextBlobEntities != null && orderTextBlobEntities.Any())
				{
					var blobEntity = orderTextBlobEntities.First();
					dto.OrderText = blobEntity.PlainText;
				}

				var supplierTextBlobEntities =
					new PrcItemblobLogic().GetSearchList(e => e.PrcItemFk == dto.Id && e.PrcTexttypeFk == 8);
				if (supplierTextBlobEntities != null && supplierTextBlobEntities.Any())
				{
					var blobEntity = supplierTextBlobEntities.First();
					dto.SupplierText = blobEntity.PlainText;
				}
				if (dto.MdcMaterialFk.HasValue)
				{
					dto.Material2Uoms = new Material2basUomLogic().GetMaterial2UomItems(dto.MdcMaterialFk);
				}
			}
			return dto;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="prcItemFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("creategrpset")]
		public IEnumerable<IControllingGrpSetDTLDto> CreateGrpSet(int mainItemId, int prcItemFk)
		{
			var entities = Logic.getGrpSet(mainItemId, prcItemFk);
			return Injector.Get<IControllingGrpSetDTLController>().ToDtos(entities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerIds"></param>
		/// <returns></returns>
		[Route("listbyheaders"), HttpGet]
		public IEnumerable<PrcItemDto> List([ModelBinder(typeof(Int32ArrayModelBinder))] IEnumerable<int> headerIds)
		{
			var filterExpressionFactory = GenerateFilterExpressionFactory(x => headerIds.Contains(x.PrcHeaderFk), null, null);
			var entities = Logic.GetSearchListCompleteWithExpressionFactory(filterExpressionFactory, true, e => e.OrderBy(x => x.Itemno));
			return entities.Select(e => new PrcItemDto(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="conHeaderId"></param>
		/// <returns></returns>
		[Route("listbyconheaderid"), HttpGet]
		public IEnumerable<PrcItemDto> List(int conHeaderId)
		{
			return Logic.GetPrcItemByConHeaderId(conHeaderId).Select(e => new PrcItemDto(e)).ToList();
		}

		/// <summary>
		/// Creates PRC items from materials with price conditions and certificates
		/// </summary>
		/// <param name="parameter">Input parameters for PRC items</param>
		/// <returns>Result containing created items, price conditions and certificates</returns>
		[HttpPost]
		[Route("getmaterialstoprcitem")]
      public PrcItemsCreateFromMaterialsResult GetMaterialsToPrcItem(PrcItemFromMaterialCreateParameter parameter)
		{
			var prcItemParam = parameter.prcItemCreateParameter;
			var isSelfRef = (prcItemParam.IsPackage || prcItemParam.FrmStyle == (int)FrmStyle.FromReq ||
						 prcItemParam.FrmStyle == (int)FrmStyle.FromContract || prcItemParam.FrmStyle == (int)FrmStyle.FromQuote);
			var materialIds = parameter.materialIds;
			var maxNo = parameter.maxNo;

			// Creates complete PRC items from materials including price conditions from materials
			var prcItemCompletes = Logic.CreatePrcItemsCompleteFromMaterials(prcItemParam, materialIds, parameter.priceListIds, maxNo, isSelfRef);

			// Copy certificates from materials
			var materialCerts = new Material2CertificateLogic().GetSearchList(e => materialIds.Contains(e.MaterialFk)).ToList();
			var existedPrcCerts = new PrcCertificateLogic().GetSearchList(e => e.PrcHeaderFk == prcItemParam.PrcHeaderFk).ToList();
			var certTypeIds = existedPrcCerts.CollectIds(e => e.BpdCertificateTypeFk).ToList();
			var copyCertsResult = new PrcCertificateLogic().CopyCertificatesFromMaterialCerts(prcItemParam.PrcHeaderFk, materialCerts, certTypeIds);

			// Reuslt
			var prcItemEntities = prcItemCompletes.Select(e => e.PrcItem);
			var result = new PrcItemsCreateFromMaterialsResult() {
				PrcItems = prcItemEntities.ToDtos(e => new PrcItemDto(e)),
				PriceConditions = prcItemCompletes.SelectMany(e => e.PriceConditionToSave).ToDtos(e => new PrcItemPriceConditionDto(e)),
				CopyCertificatesResult = copyCertsResult
			};

		  return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="parameter"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("copyprcitemvalue")]
		public PrcItemDto CopyPrcItemValue(CopyPrcItemValueParameter parameter)
		{
			var targetPrcItem = parameter.targetPrcItem;
			var originPrcItem = parameter.originPrcItem;

			targetPrcItem = Logic.CopyPrcItemValues(targetPrcItem, originPrcItem);
			return new PrcItemDto(targetPrcItem);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcItemFk"></param>
		/// <returns></returns>
		[Route("copyprcitemblobid"), HttpGet]
		public int CopyPrcItemBlobsSpecificationFk(int prcItemFk)
		{
			var item = Logic.GetSearchListComplete(e => e.Id == prcItemFk).FirstOrDefault();
			if (item != null)
			{
				var blobLogic = new Cloud.Common.BusinessComponents.BlobLogic();
				if (item.BasBlobsSpecificationFk != null)
				{
					var blobId = blobLogic.CopyBlob((int)item.BasBlobsSpecificationFk);
					return blobId;
				}
				else if (item.MdcMaterialFk != null)
				{
					var mdcCommoditySearchLogic = new MdcCommoditySearchVLogic();
					var mdcCommodity = mdcCommoditySearchLogic.GetCommodityById((int)item.MdcMaterialFk);
					if (mdcCommodity != null && mdcCommodity.BasBlobsSpecificationFk != null)
					{
						var blobId = blobLogic.CopyBlob((int)mdcCommodity.BasBlobsSpecificationFk);
						return blobId;
					}
				}
			}
			return 0;
		}
		/// <summary>
		/// Clone Address For PrcItem
		/// </summary>
		/// <param name="prcItemFk"></param>
		/// <returns></returns>
		[Route("cloneprcitemaddress"), HttpGet]
		public AddressEntity CloneAddressForPrcItem(int prcItemFk)
		{
			var item = Logic.GetSearchListComplete(e => e.Id == prcItemFk).FirstOrDefault();
			if (item?.BasAddressFk != null)
			{
				Logic.CopyAddressToPrcItem(item);
				return item.AddressEntity;
			}
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[Route("updatePrcItemGroup"), HttpPost]
		public void UpdatePrcItem(IEnumerable<PrcItemDto> request)
		{
			IEnumerable<PrcItemEntity> entities = request.Select(i => i.Copy()).ToList();
			Logic.Save(entities);
		}
		/// <summary>
		/// get cost group by prciItem id
		/// </summary>
		/// <param name="prcItemFk"></param>
		/// <returns></returns>
		[Route("getcostgroup")]
		[HttpGet]
		public Object GetCostGroupCats(int prcItemFk)
		{
		   var costGroups = 	new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP").GetByFilter(e => prcItemFk == e.MainItemId64.Value);
			return costGroups;
		}

		/// <summary>
		/// Update package (material) with additional items.
		/// Note: currently only support update package from requisition additional items.
		/// Similar function Refer to: [Route("SyncBaseBoQ")].
		/// </summary>
		/// <param name="updateParam"></param>
		[Route("updatePackageMaterial")]
		[HttpPost]
		public UpdatePackageMaterialResponse UpdatePackageWithAdditionalItem(UpdatePackageMaterialRequest updateParam)
		{
			return Logic.UpdatePackageWithAdditionalItem(updateParam);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		[Route("getprcitemsbyids"), HttpPost]
		public IEnumerable<PrcItemDto> GetPrcItemsByIds(IEnumerable<long> ids)
		{
			if (ids.IsNullOrEmpty())
			{
				return new List<PrcItemDto>();
			}
			var entities = this.Logic.GetSearchList(e => ids.Contains(e.Id));
			return entities.ToDtos(e => new PrcItemDto(e));
		}
	}
}
