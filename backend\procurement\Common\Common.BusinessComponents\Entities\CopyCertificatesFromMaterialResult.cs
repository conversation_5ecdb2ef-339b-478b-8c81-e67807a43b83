using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Material.BusinessComponents;

namespace RIB.Visual.Procurement.Common.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class CopyCertificatesFromMaterialResult
	{
		/// <summary>
		/// 
		/// </summary>
		public List<PrcCertificateEntity> NewPrcCertificates { set; get; }

		/// <summary>
		/// 
		/// </summary>
		public List<Material2CertificateEntity> ExistedMaterial2Certificates { set; get; }
	}
}
