﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Controlling.PublicApi.BusinessComponents;

namespace RIB.Visual.Controlling.PublicApi.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key1)
                    .HasColumnName(@"KEY1")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key2)
                    .HasColumnName(@"KEY2")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key3)
                    .HasColumnName(@"KEY3")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region ControllingGrpSetApiEntity

            modelBuilder.Entity<ControllingGrpSetApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_CONTROLLINGGRPSETAPI_V");
            // Properties:
            modelBuilder.Entity<ControllingGrpSetApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingGrpSetApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ControllingGrpSetApiEntity>(modelBuilder);

            #endregion

            #region ControllingGrpSetDtlApiEntity

            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_CONTROLLINGGRPSETDTLAPI_V");
            // Properties:
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGrpSetId)
                    .HasColumnName(@"MDC_CONTROLLINGGRPSET_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGroupId)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGroupCode)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGroupDesc)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGrpDetailId)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllingGrpDetailCode)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.ControllinggGrpDetailDesc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingGrpSetDtlApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ControllingGrpSetDtlApiEntity>(modelBuilder);

            #endregion

            #region ControllingUnitApiEntity

            modelBuilder.Entity<ControllingUnitApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_CONTROLLINGUNITAPI_V");
            // Properties:
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingUnitStatusId)
                    .HasColumnName(@"MDC_CONTRUNITSTATUS_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingUnitStatusDesc)
                    .HasColumnName(@"MDC_CONTRUNITSTATUS_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingUnitId)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingUnitCode)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingTemplateId)
                    .HasColumnName(@"MDC_CONTROLTEMPLATE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingTemplateUnitId)
                    .HasColumnName(@"MDC_CONTROLTEMPLATE_UNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingUnitDesc)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ContextId)
                    .HasColumnName(@"MDC_CONTEXT_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ContextDesc)
                    .HasColumnName(@"MDC_CONTEXT_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ProjectId)
                    .HasColumnName(@"PRJ_PROJECT_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ProjectCode)
                    .HasColumnName(@"PRJ_PROJECT_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ProjectDesc)
                    .HasColumnName(@"PRJ_PROJECT_DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UomId)
                    .HasColumnName(@"BAS_UOM_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UomDesc)
                    .HasColumnName(@"BAS_UOM_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsBillingElement)
                    .HasColumnName(@"ISBILLINGELEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsAccountingElement)
                    .HasColumnName(@"ISACCOUNTINGELEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsPlanningElement)
                    .HasColumnName(@"ISPLANNINGELEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsStockManagement)
                    .HasColumnName(@"ISSTOCKMANAGEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.StockId)
                    .HasColumnName(@"PRJ_STOCK_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.StockCode)
                    .HasColumnName(@"PRJ_STOCK_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.StockDesc)
                    .HasColumnName(@"PRJ_STOCK_DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsAssetManagement)
                    .HasColumnName(@"ISASSETMANAGEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsPlantManagement)
                    .HasColumnName(@"ISPLANTMANAGEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlantId)
                    .HasColumnName(@"ETM_PLANT_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlantCode)
                    .HasColumnName(@"ETM_PLANT_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlantDesc)
                    .HasColumnName(@"ETM_PLANT_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlannedStart)
                    .HasColumnName(@"PLANNED_START")
                    .HasColumnType("date");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlannedEnd)
                    .HasColumnName(@"PLANNED_END")
                    .HasColumnType("date");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.PlannedDuration)
                    .HasColumnName(@"PLANNED_DURATION")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment01)
                    .HasColumnName(@"ASSIGNMENT01")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail01Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL01_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail01Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL01_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail01Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL01_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment02)
                    .HasColumnName(@"ASSIGNMENT02")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail02Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL02_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail02Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL02_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail02Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL02_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment03)
                    .HasColumnName(@"ASSIGNMENT03")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDdetail03Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL03_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail03Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL03_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail03Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL03_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment04)
                    .HasColumnName(@"ASSIGNMENT04")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail04Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL04_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail04Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL04_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail04Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL04_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment05)
                    .HasColumnName(@"ASSIGNMENT05")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail05Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL05_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail05Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL05_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail05Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL05_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment06)
                    .HasColumnName(@"ASSIGNMENT06")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail06Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL06_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail06Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL06_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail06Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL06_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment07)
                    .HasColumnName(@"ASSIGNMENT07")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail07Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL07_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail07Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL07_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail07Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL07_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment08)
                    .HasColumnName(@"ASSIGNMENT08")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail08Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL08_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail08Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL08_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail08Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL08_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment09)
                    .HasColumnName(@"ASSIGNMENT09")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail09Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL09_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail09Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL09_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail09Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL09_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Assignment10)
                    .HasColumnName(@"ASSIGNMENT10")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail10Id)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL10_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail10Code)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL10_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingGrpDetail10Desc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL10_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UserDefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UserDefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UserDefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UserDefined4)
                    .HasColumnName(@"USERDEFINED4")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.UserDefined5)
                    .HasColumnName(@"USERDEFINED5")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.Budget)
                    .HasColumnName(@"BUDGET")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsFixedBudget)
                    .HasColumnName(@"ISFIXED_BUDGET")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.BudgetDifference)
                    .HasColumnName(@"BUDGET_DIFFERENCE")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.EstimateCost)
                    .HasColumnName(@"ESTIMATE_COST")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.BudgetCostDiff)
                    .HasColumnName(@"BUDGET_COST_DIFF")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.IsIntercompany)
                    .HasColumnName(@"ISINTERCOMPANY")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingCatId)
                    .HasColumnName(@"BAS_CONTROLLINGCAT_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ControllingCatDesc)
                    .HasColumnName(@"BAS_CONTROLLINGCAT_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ClerkId)
                    .HasColumnName(@"BAS_CLERK_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ClerkCode)
                    .HasColumnName(@"BAS_CLERK_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ClerkDesc)
                    .HasColumnName(@"BAS_CLERK_DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ProfitCenterId)
                    .HasColumnName(@"BAS_COMPANY_RESPONSIBLE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ProfitCenterCode)
                    .HasColumnName(@"BAS_COMPANY_RESPONSIBLE_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.CompanyId)
                    .HasColumnName(@"BAS_COMPANY_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.CompanyCode)
                    .HasColumnName(@"BAS_COMPANY_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.ForTimekeeping)
                    .HasColumnName(@"ISTIMEKEEPINGELEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ControllingUnitApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ControllingUnitApiEntity>(modelBuilder);

            #endregion

            #region ControllingUnitGroupApiEntity

            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_CONTROLLINGUNITGROUPAPI_V");
            // Properties:
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingUnitId)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingUnitCode)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_CODE")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingUnitDesc)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGroupId)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGroupCode)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGroupDesc)
                    .HasColumnName(@"MDC_CONTROLLINGGROUP_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGrpDetailId)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGrpDetailCode)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.ControllingGrpDetailDesc)
                    .HasColumnName(@"MDC_CONTROLLINGGRPDETAIL_DESC")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ControllingUnitGroupApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ControllingUnitGroupApiEntity>(modelBuilder);

            #endregion

            #region ActualsCostDataApiEntity

            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_COMPANY_COSTDATAAPI_V");
            // Properties:
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CompanyCostHeaderId)
                    .HasColumnName(@"BAS_COMPANY_COSTHEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CompanyCostHeaderCode)
                    .HasColumnName(@"BAS_COMPANY_COSTHEADER_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingUnitId)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingUnitDescription)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingUnitCode)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CostCodeId)
                    .HasColumnName(@"MDC_COST_CODE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CostCodeDescription)
                    .HasColumnName(@"MDC_COST_CODE_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CostCode)
                    .HasColumnName(@"COST_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingCostCodeId)
                    .HasColumnName(@"MDC_CONTR_COST_CODE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingCostCodeDescription)
                    .HasColumnName(@"MDC_CONTR_COST_CODE_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.ControllingCostCode)
                    .HasColumnName(@"CONTR_COST_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.AccountId)
                    .HasColumnName(@"BAS_ACCOUNT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.AccountDescription)
                    .HasColumnName(@"BAS_ACCOUNT_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.AccountCode)
                    .HasColumnName(@"BAS_ACCOUNT_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CurrencyId)
                    .HasColumnName(@"BAS_CURRENCY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.CurrencyDescription)
                    .HasColumnName(@"BAS_CURRENCY_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.BasUomId)
                    .HasColumnName(@"BAS_UOM_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.UomDescription)
                    .HasColumnName(@"BAS_UOM_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.Uom)
                    .HasColumnName(@"UOM")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.Amount)
                    .HasColumnName(@"AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.AmountOc)
                    .HasColumnName(@"AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.Comment)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.NominalDimension1)
                    .HasColumnName(@"NOMINAL_DIMENSION1")
                    .HasMaxLength(64)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.NominalDimension2)
                    .HasColumnName(@"NOMINAL_DIMENSION2")
                    .HasMaxLength(64)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.NominalDimension3)
                    .HasColumnName(@"NOMINAL_DIMENSION3")
                    .HasMaxLength(64)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.AmountProject)
                    .HasColumnName(@"AMOUNT_PROJECT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostDataApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ActualsCostDataApiEntity>(modelBuilder);

            #endregion

            #region ActualsCostHeaderApiEntity

            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_COMPANY_COSTHEADERAPI_V");
            // Properties:
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyId)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyCode)
                    .HasColumnName(@"BAS_COMPANY_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyYearId)
                    .HasColumnName(@"BAS_COMPANY_YEAR_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.TradingYear)
                    .HasColumnName(@"TRADING_YEAR")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyYearStartDate)
                    .HasColumnName(@"YEAR_START_DATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyYearEndDate)
                    .HasColumnName(@"YEAR_END_DATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.CompanyPeriodId)
                    .HasColumnName(@"BAS_COMPANY_PERIOD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.TradingPeriod)
                    .HasColumnName(@"TRADING_PERIOD")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.TradingPeriodStartDate)
                    .HasColumnName(@"PERIOD_START_DATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.TradingPeriodEndDate)
                    .HasColumnName(@"PERIOD_END_DATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.ValueTypeId)
                    .HasColumnName(@"BAS_VALUETYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.ValueType)
                    .HasColumnName(@"BAS_VALUETYPE_DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.ProjectId)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.Project)
                    .HasColumnName(@"PROJECTNO")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.ProjectName)
                    .HasColumnName(@"PROJECT_NAME")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.HasCostCode)
                    .HasColumnName(@"HASCOSTCODE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.HasAccount)
                    .HasColumnName(@"HASACCOUNT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.HasControllingCostCode)
                    .HasColumnName(@"HASCONTCOSTCODE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.Comment)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.Total)
                    .HasColumnName(@"TOTAL")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.TotalOc)
                    .HasColumnName(@"TOTAL_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.IsFinal)
                    .HasColumnName(@"ISFINAL")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ActualsCostHeaderApiEntity>()
                .Property(p => p.LanguageId)
                    .HasColumnName(@"LANGUAGE_ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ActualsCostHeaderApiEntity>(modelBuilder);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpSetApiEntity in the schema.
        /// </summary>
        public DbSet<ControllingGrpSetApiEntity> ControllingGrpSetApiEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingGrpSetDtlApiEntity in the schema.
        /// </summary>
        public DbSet<ControllingGrpSetDtlApiEntity> ControllingGrpSetDtlApiEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitApiEntity in the schema.
        /// </summary>
        public DbSet<ControllingUnitApiEntity> ControllingUnitApiEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitGroupApiEntity in the schema.
        /// </summary>
        public DbSet<ControllingUnitGroupApiEntity> ControllingUnitGroupApiEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ActualsCostDataApiEntity in the schema.
        /// </summary>
        public DbSet<ActualsCostDataApiEntity> ActualsCostDataApiEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ActualsCostHeaderApiEntity in the schema.
        /// </summary>
        public DbSet<ActualsCostHeaderApiEntity> ActualsCostHeaderApiEntities { get; set; }
    }
}
