using System.Collections.Generic;

namespace RIB.Visual.Procurement.Common.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class PrcItemCreateParameterEntity
	{
		/// <summary>
		/// gets and sets InstanceId
		/// </summary>
		public int? InstanceId { get; set; }

		/// <summary>
		/// gets and sets PrcHeaderFk
		/// </summary>
		public int PrcHeaderFk { get; set; }

		/// <summary>
		/// gets and sets ProjectFk
		/// </summary>
		public int ProjectFk { get; set; }

		/// <summary>
		/// gets and sets ConfigurationFk
		/// </summary>
		public int ConfigurationFk { get; set; }

		/// <summary>
		/// gets and sets PrcPackageFk
		/// </summary>
		public int? PrcPackageFk { get; set; }

		/// <summary>
		/// set items taxcode
		/// </summary>
		public int? TaxCodeFk { get; set; }

		/// <summary>
		///  gets and sets BasPaymentTermFiFk
		/// </summary>
		public int? BasPaymentTermFiFk { get; set; }

		/// <summary>
		///  gets and sets BasPaymentTermPaFk
		/// </summary>
		public int? BasPaymentTermPaFk { get; set; }

		/// <summary>
		///  gets and sets PrcIncotermFk
		/// </summary>
		public int? PrcIncotermFk { get; set; }

		/// <summary>
		/// gets and sets ContractHeaderFk, It just for contract module
		/// </summary>
		public int? ContractHeaderFk { get; set; }

		/// <summary>
		/// gets and sets FrmHeaderFk
		/// </summary>
		public int? FrmHeaderFk { get; set; }

		/// <summary>
		/// gets and sets FrmStyle
		/// </summary>
		public int? FrmStyle { get; set; }
	}
}
