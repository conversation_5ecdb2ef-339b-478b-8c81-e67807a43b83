import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UiCommonMessageBoxService } from '@libs/ui/common';
import { SalesWipWipsDataService } from '../../services/sales-wip-wips-data.service';
import { PlatformConfigurationService } from '@libs/platform/common';
import { IWipHeaderEntity } from '@libs/sales/interfaces';

/**
 * Response interface for update direct cost per unit API call
 */
interface IUpdateDirectCostPerUnitResponse {
    errorCode?: string;
    wipHeader?: IWipHeaderEntity;
}

@Injectable({
    providedIn: 'root'
})
/**
 * Service for handling sales billing wizards.
 */
export class SalesWipWizardService {
    private readonly http = inject(HttpClient);
    private readonly messageBoxService = inject(UiCommonMessageBoxService);
    private readonly configService = inject(PlatformConfigurationService);
    private readonly wipHeaderDataService = inject(SalesWipWipsDataService);

    public updateDirectCostPerUnit() {
        this.wipHeaderDataService.updateAndExecute(() => {
            const selectedWip = this.wipHeaderDataService.getSelectedEntity();
            if (!selectedWip) {
                return;
            }
            
            const wipHeaderId = selectedWip.Id;
            if (wipHeaderId > 0) {
                this.http.post(`${this.configService.webApiBaseUrl}sales/wip/updatedirectcostsperunit`, wipHeaderId)
                    .subscribe((response: IUpdateDirectCostPerUnitResponse) => {
                        if (!response.errorCode) {
                            const updatedWipHeader = response.wipHeader;
                            this.wipHeaderDataService.updateEntities([updatedWipHeader]);
                            // Refresh BOQ data if needed
                            // TODO: Implement equivalent of $injector.get('salesWipBoqStructureService').refreshBoqData();
                            this.messageBoxService.showInfoBox('sales.billing.updateDirectCostPerUnitSuccessMessage');
                        } else {
                            if (response.errorCode === 'noBoqs') {
                                // Handle no BOQs error
                                this.messageBoxService.showInfoBox('sales.billing.noBoqsMessage', 'Info', true);
                            }
                        }
                    });
            }
        });
    }
}
