using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Procurement.Invoice.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(ITransactionProvider)), PartCreationPolicy(CreationPolicy.NonShared)]
	public class InvRejectionTransactionProvider : ITransactionProvider
	{
		/// <summary>
		/// 
		/// </summary>
		public string[] LineTypes { get { return new string[] { "13" }; } }

		/// <summary>
		/// 
		/// </summary>
		public int Sorting { get { return 5; } }

		/// <summary>
		/// 
		/// </summary>
		public string Description{ get { return "Rejection"; } }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="contractName"></param>
		/// <returns></returns>
		public bool IsValid(string contractName) { return contractName == "InvHeaderEntity"; }

		/// <summary>
		/// 
		/// </summary>
		public ITransactionPostingScope Scope { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<ITransactionEntity> Transactions { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <returns></returns>
		public IEnumerable<ITransactionEntity> Create(IIdentifyable headerEntity)
		{
			var transactions = new List<ITransactionEntity>();
			var scope = this.Scope as InvTransactionPostingScope;
			var rejectionReasonIds = new InvRejectionReasonLogic().GetSearchList(e => e.IsAwaitingCreditNote).Select(e => e.Id).ToArray();
			var entities = new InvRejectLogic().GetSearchList(e => e.InvHeaderFk == headerEntity.Id);
			var rejections = new List<InvRejectEntity>();
			var others = new List<InvRejectEntity>();
			var companyInfo = Injector.Get<ICompanyInfoProvider>();
			var ledgerContextFk = companyInfo.GetLedgerContext();
			var rejectionReasonAccs = new BasicsCustomizeInvoiceRejectionReasonAccLogic().GetListByFilter(e => rejectionReasonIds.Contains(e.RejectionreasonFk) && e.LedgerContextFk == ledgerContextFk);
			var header = headerEntity as InvHeaderEntity;

			foreach (var entity in entities)
			{
				if(rejectionReasonIds.Contains(entity.InvRejectionReasonFk))
				{
					rejections.Add(entity);
				}
				else
				{
					others.Add(entity);
				}
			}

			if (others.Any())
			{
				var parameter = string.Join(", ", others.Select(e => e.Description).ToArray());
				scope.CreateValidation((int)BasMessageEnum.IsNotAwaitingCreditNote, parameter);
			}

			var invTransactions = new List<InvTransactionEntity>();

			if(header.ControllingUnitFk.HasValue && !scope.IsValidControllingUnitId(header.ControllingUnitFk.Value))
			{
				return invTransactions;
			}

			foreach(var rejection in rejections)
			{
				var transaction = new InvTransactionEntity();

				// take from header controlling unit
				if (header.ControllingUnitFk.HasValue)
				{
					transaction.ControllingUnitFk = header.ControllingUnitFk;
				}

				scope.FillTaxCode(transaction, rejection.TaxCodeFk);
				transaction.NominalAccount = QueryNorminalAccount(rejectionReasonAccs, rejection);
				transaction.Amount = this.CalculateAmount(rejection);
				transaction.Quantity = rejection.Quantity;
				transaction.PostingNarritive = rejection.Description;

				invTransactions.Add(transaction);
			}

			if (invTransactions.Any())
			{
				var groups = invTransactions.GroupBy(e => new
				{
					e.NominalAccount,
					e.TaxCodeFk,
					e.TaxCodeMatrixFk,
					e.VatCode,
					e.ControllingUnitFk,
				});

				foreach(var group in groups)
				{
					var transaction = scope.TransactionLogic.Create(headerEntity.Id) as InvTransactionEntity;

					transaction.LineType = "13";
					transaction.NominalAccount = group.Key.NominalAccount;
					transaction.ControllingUnitFk = group.Key.ControllingUnitFk;
					transaction.VatCode = group.Key.VatCode;
					transaction.TaxCodeFk = group.Key.TaxCodeFk;
					transaction.TaxCodeMatrixFk = group.Key.TaxCodeMatrixFk;

					foreach (var item in group)
					{
						transaction.Amount += item.Amount;
						transaction.Quantity += item.Quantity;
					}

					scope.FillHeaderInfo(transaction);
					if (transaction.ControllingUnitFk.HasValue)
					{
						scope.FillControllingUnit(transaction, transaction.ControllingUnitFk.Value);
					}

					transaction.IsDebit = true;
					transaction.AmountAuthorized = 0;
					transaction.IsSuccess = false;
					transaction.HandoverId = 0;
					transaction.ReturnValue = null;
					transaction.PostingNarritive = group.First().PostingNarritive;
					transaction.TransactionId = scope.TransactionId;
					scope.EvalVatAmount(transaction);

					if (header.ConHeaderFk.HasValue)
					{
						scope.FillConHeaderInfo(transaction, header.ConHeaderFk.Value);
					}
					else
					{
						scope.CreateValidation((int)BasMessageEnum.IsNull, "Contract of", "rejection");
					}

					transactions.Add(transaction);
				}
			}

			return transactions;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerEntity"></param>
		/// <param name="transactions"></param>
		/// <returns></returns>
		public IEnumerable<ITransactionEntity> AfterCreate(IIdentifyable headerEntity, IEnumerable<ITransactionEntity> transactions)
		{
			return transactions;
		}

		private string QueryNorminalAccount(IEnumerable<BasicsCustomizeInvoiceRejectionReasonAccEntity> rejectionReasonAccs, InvRejectEntity rejection)
		{
			string result = null;
			var entities = rejectionReasonAccs.Where(e => e.RejectionreasonFk == rejection.InvRejectionReasonFk).ToList();

			if(entities.Any())
			{
				var entity = entities.FirstOrDefault(e => e.TaxCodeFk == rejection.TaxCodeFk);

				if(entity != null)
				{
					result = entity.Account;
				}
			}

			return result;
		}

		private decimal CalculateAmount(InvRejectEntity rejection)
		{
			var totalConfirmed = rejection.QuantityConfirmed * rejection.PriceConfirmed;
			var totalAskedFor = rejection.QuantityAskedFor * rejection.PriceAskedFor;
			var totalNet = totalConfirmed - totalAskedFor;
			var totalAmount = rejection.Quantity * rejection.AmountNet;
			return totalNet == 0 ? totalAmount : totalNet;
		}
	}
}
